import type { InsertAchievement } from '@shared/schema';

export interface AchievementData extends Omit<InsertAchievement, 'userId'> {
  // Added field to group achievements into categories
  category: string;
}

// Define achievement icon mapping for different types
const ICON_MAPPING: Record<string, string> = {
  archived: 'archive',
  replies: 'message-square',
  processed: 'inbox',
  completion: 'check-square',
  time: 'clock',
  composite: 'award',
};

// Define all achievements
export const ACHIEVEMENTS: AchievementData[] = [
  // Total Emails Archived
  {
    name: 'Archivist Beginner',
    description: 'Archive 10 emails.',
    type: 'archived',
    icon: ICON_MAPPING.archived,
    level: 1,
    progress: 0,
    maxProgress: 10,
    isComplete: false,
    category: 'Total Emails Archived',
  },
  {
    name: 'Inbox Archivist',
    description: 'Archive 50 emails.',
    type: 'archived',
    icon: ICON_MAPPING.archived,
    level: 2,
    progress: 0,
    maxProgress: 50,
    isComplete: false,
    category: 'Total Emails Archived',
  },
  {
    name: 'Mail Curator',
    description: 'Archive 100 emails.',
    type: 'archived',
    icon: ICON_MAPPING.archived,
    level: 3,
    progress: 0,
    maxProgress: 100,
    isComplete: false,
    category: 'Total Emails Archived',
  },
  {
    name: 'Digital Archivist',
    description: 'Archive 500 emails.',
    type: 'archived',
    icon: ICON_MAPPING.archived,
    level: 4,
    progress: 0,
    maxProgress: 500,
    isComplete: false,
    category: 'Total Emails Archived',
  },
  {
    name: 'File Master',
    description: 'Archive 1,000 emails.',
    type: 'archived',
    icon: ICON_MAPPING.archived,
    level: 5,
    progress: 0,
    maxProgress: 1000,
    isComplete: false,
    category: 'Total Emails Archived',
  },
  {
    name: 'Archive Pro',
    description: 'Archive 5,000 emails.',
    type: 'archived',
    icon: ICON_MAPPING.archived,
    level: 6,
    progress: 0,
    maxProgress: 5000,
    isComplete: false,
    category: 'Total Emails Archived',
  },
  {
    name: 'Ultimate Archivist',
    description: 'Archive 10,000 emails.',
    type: 'archived',
    icon: ICON_MAPPING.archived,
    level: 7,
    progress: 0,
    maxProgress: 10000,
    isComplete: false,
    category: 'Total Emails Archived',
  },

  // Total Email Replies
  {
    name: 'Reply Rookie',
    description: 'Send 10 email replies.',
    type: 'replies',
    icon: ICON_MAPPING.replies,
    level: 1,
    progress: 0,
    maxProgress: 10,
    isComplete: false,
    category: 'Total Email Replies',
  },
  {
    name: 'Conversational Starter',
    description: 'Send 50 replies.',
    type: 'replies',
    icon: ICON_MAPPING.replies,
    level: 2,
    progress: 0,
    maxProgress: 50,
    isComplete: false,
    category: 'Total Email Replies',
  },
  {
    name: 'Engagement Expert',
    description: 'Send 100 replies.',
    type: 'replies',
    icon: ICON_MAPPING.replies,
    level: 3,
    progress: 0,
    maxProgress: 100,
    isComplete: false,
    category: 'Total Email Replies',
  },
  {
    name: 'Communication Specialist',
    description: 'Send 250 replies.',
    type: 'replies',
    icon: ICON_MAPPING.replies,
    level: 4,
    progress: 0,
    maxProgress: 250,
    isComplete: false,
    category: 'Total Email Replies',
  },
  {
    name: 'Response Dynamo',
    description: 'Send 500 replies.',
    type: 'replies',
    icon: ICON_MAPPING.replies,
    level: 5,
    progress: 0,
    maxProgress: 500,
    isComplete: false,
    category: 'Total Email Replies',
  },
  {
    name: 'Master Communicator',
    description: 'Send 1,000 replies.',
    type: 'replies',
    icon: ICON_MAPPING.replies,
    level: 6,
    progress: 0,
    maxProgress: 1000,
    isComplete: false,
    category: 'Total Email Replies',
  },
  {
    name: 'Email Influencer',
    description: 'Send 5,000 replies.',
    type: 'replies',
    icon: ICON_MAPPING.replies,
    level: 7,
    progress: 0,
    maxProgress: 5000,
    isComplete: false,
    category: 'Total Email Replies',
  },

  // Total Emails Processed
  {
    name: 'Inbox Tamer',
    description: 'Process 20 emails.',
    type: 'processed',
    icon: ICON_MAPPING.processed,
    level: 1,
    progress: 0,
    maxProgress: 20,
    isComplete: false,
    category: 'Total Emails Processed',
  },
  {
    name: 'Mail Wrangler',
    description: 'Process 100 emails.',
    type: 'processed',
    icon: ICON_MAPPING.processed,
    level: 2,
    progress: 0,
    maxProgress: 100,
    isComplete: false,
    category: 'Total Emails Processed',
  },
  {
    name: 'Efficiency Expert',
    description: 'Process 250 emails.',
    type: 'processed',
    icon: ICON_MAPPING.processed,
    level: 3,
    progress: 0,
    maxProgress: 250,
    isComplete: false,
    category: 'Total Emails Processed',
  },
  {
    name: 'Productivity Pro',
    description: 'Process 500 emails.',
    type: 'processed',
    icon: ICON_MAPPING.processed,
    level: 4,
    progress: 0,
    maxProgress: 500,
    isComplete: false,
    category: 'Total Emails Processed',
  },
  {
    name: 'Inbox Zero Hero',
    description: 'Process 1,000 emails.',
    type: 'processed',
    icon: ICON_MAPPING.processed,
    level: 5,
    progress: 0,
    maxProgress: 1000,
    isComplete: false,
    category: 'Total Emails Processed',
  },
  {
    name: 'Email Overlord',
    description: 'Process 5,000 emails.',
    type: 'processed',
    icon: ICON_MAPPING.processed,
    level: 6,
    progress: 0,
    maxProgress: 5000,
    isComplete: false,
    category: 'Total Emails Processed',
  },
  {
    name: 'Inbox Immortal',
    description: 'Process 10,000 emails.',
    type: 'processed',
    icon: ICON_MAPPING.processed,
    level: 7,
    progress: 0,
    maxProgress: 10000,
    isComplete: false,
    category: 'Total Emails Processed',
  },

  // Completion Rate Achievements
  {
    name: 'Task Initiate',
    description: 'Achieve a 50% task completion rate.',
    type: 'completion',
    icon: ICON_MAPPING.completion,
    level: 1,
    progress: 0,
    maxProgress: 50,
    isComplete: false,
    category: 'Completion Rate Achievements',
  },
  {
    name: 'Task Terminator',
    description: 'Hit a 75% completion rate.',
    type: 'completion',
    icon: ICON_MAPPING.completion,
    level: 2,
    progress: 0,
    maxProgress: 75,
    isComplete: false,
    category: 'Completion Rate Achievements',
  },
  {
    name: 'Perfection Seeker',
    description: 'Reach a 90% completion rate.',
    type: 'completion',
    icon: ICON_MAPPING.completion,
    level: 3,
    progress: 0,
    maxProgress: 90,
    isComplete: false,
    category: 'Completion Rate Achievements',
  },
  {
    name: 'Completion Champion',
    description: 'Maintain a 100% completion rate for one day.',
    type: 'completion',
    icon: ICON_MAPPING.completion,
    level: 4,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Completion Rate Achievements',
  },
  {
    name: 'Daily Dynamo',
    description: 'Achieve 100% completion for 7 consecutive days.',
    type: 'completion',
    icon: ICON_MAPPING.completion,
    level: 5,
    progress: 0,
    maxProgress: 7,
    isComplete: false,
    category: 'Completion Rate Achievements',
  },
  {
    name: 'Consistency King',
    description: 'Hit 100% completion for 30 consecutive days.',
    type: 'completion',
    icon: ICON_MAPPING.completion,
    level: 6,
    progress: 0,
    maxProgress: 30,
    isComplete: false,
    category: 'Completion Rate Achievements',
  },

  // Time Saved Milestones
  {
    name: 'Time Saver Starter',
    description: 'Save 1 hour of time.',
    type: 'time',
    icon: ICON_MAPPING.time,
    level: 1,
    progress: 0,
    maxProgress: 60,
    isComplete: false,
    category: 'Time Saved Milestones',
  },
  {
    name: 'Quick Click',
    description: 'Save 5 hours in total.',
    type: 'time',
    icon: ICON_MAPPING.time,
    level: 2,
    progress: 0,
    maxProgress: 300,
    isComplete: false,
    category: 'Time Saved Milestones',
  },
  {
    name: 'Efficiency Enthusiast',
    description: 'Save 10 hours.',
    type: 'time',
    icon: ICON_MAPPING.time,
    level: 3,
    progress: 0,
    maxProgress: 600,
    isComplete: false,
    category: 'Time Saved Milestones',
  },
  {
    name: 'Time Management Guru',
    description: 'Save 25 hours.',
    type: 'time',
    icon: ICON_MAPPING.time,
    level: 4,
    progress: 0,
    maxProgress: 1500,
    isComplete: false,
    category: 'Time Saved Milestones',
  },
  {
    name: 'Clock Crusher',
    description: 'Save 50 hours.',
    type: 'time',
    icon: ICON_MAPPING.time,
    level: 5,
    progress: 0,
    maxProgress: 3000,
    isComplete: false,
    category: 'Time Saved Milestones',
  },
  {
    name: 'Temporal Titan',
    description: 'Save 100 hours.',
    type: 'time',
    icon: ICON_MAPPING.time,
    level: 6,
    progress: 0,
    maxProgress: 6000,
    isComplete: false,
    category: 'Time Saved Milestones',
  },
  {
    name: 'Time Lord',
    description: 'Save 500 hours.',
    type: 'time',
    icon: ICON_MAPPING.time,
    level: 7,
    progress: 0,
    maxProgress: 30000,
    isComplete: false,
    category: 'Time Saved Milestones',
  },
  {
    name: 'Ultimate Time Saver',
    description: 'Save 1,000 hours.',
    type: 'time',
    icon: ICON_MAPPING.time,
    level: 8,
    progress: 0,
    maxProgress: 60000,
    isComplete: false,
    category: 'Time Saved Milestones',
  },

  // Composite & Multi-Stat Challenges
  {
    name: 'Multi-Tasker',
    description: 'In one day, archive 100 emails, send 50 replies, and process 200 emails.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 1,
    progress: 0,
    maxProgress: 1, // Binary: either complete or not
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Email Master',
    description: 'Achieve a 100% completion rate while processing 100 emails in a single day.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 1,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Efficiency Expert',
    description: 'Save 25 hours in one week while processing 500 emails.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 2,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Communication Champion',
    description: 'Archive 50 emails and send 100 replies in one day.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 2,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Inbox Innovator',
    description: 'Process 250 emails and archive at least 75% of them in one day.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 3,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Reply & Archive Pro',
    description: 'In one week, send 250 replies and archive 100 emails.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 3,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Productivity Powerhouse',
    description:
      'Process 1,000 emails, send 500 replies, and maintain at least a 90% completion rate in one week.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 4,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Time-Saving Titan',
    description: 'Save 50 hours over a week while keeping a 95%+ daily completion rate.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 4,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Archival All-Star',
    description: 'Consistently archive at least 10% of processed emails every day for a month.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 5,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Efficiency Streak',
    description:
      'Process an average of 50 emails per day while maintaining a 100% completion rate for 30 consecutive days.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 5,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Inbox Overachiever',
    description: 'In a single day, process 200 emails, archive at least 100, and reply to 50.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 5,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Rapid Responder',
    description:
      'In one session, process and reply to 10 emails with an average response time under 5 minutes.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 2,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Time Master',
    description: 'Save a cumulative total of 200 hours in one year.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 6,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Digital Dynamo',
    description:
      'Unlock at least three achievement tiers (from different stat categories) within one month.',
    type: 'composite',
    icon: ICON_MAPPING.composite,
    level: 3,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
  {
    name: 'Ultimate Email Champion',
    description: 'Unlock every achievement available in the app.',
    type: 'composite',
    icon: 'trophy',
    level: 10,
    progress: 0,
    maxProgress: 1,
    isComplete: false,
    category: 'Composite & Multi-Stat Challenges',
  },
];

/**
 * Initialize achievements for a new user
 * @param userId User ID to create achievements for
 * @param storage Storage interface
 */
export async function initializeUserAchievements(userId: number, storage: any) {
  try {
    // Check if the user already has achievements to avoid duplication
    const existingAchievements = await storage.getAchievements(userId);

    if (existingAchievements && existingAchievements.length > 0) {
      return; // User already has achievements
    }

    // Create achievements for the user
    for (const achievement of ACHIEVEMENTS) {
      await storage.createAchievement({
        ...achievement,
        userId,
      });
    }
  } catch (error) {
    console.error('Failed to initialize achievements:', error);
  }
}

/**
 * Update achievement progress based on user activity
 * @param userId User ID
 * @param type Achievement type to update
 * @param progress Progress to add
 * @param storage Storage interface
 */
export async function updateAchievementProgress(
  userId: number,
  type: string,
  progress: number,
  storage: any
) {
  try {
    const userAchievements = await storage.getAchievements(userId);
    const relevantAchievements = userAchievements.filter(
      (ach: any) => ach.type === type && !ach.isComplete
    );

    for (const achievement of relevantAchievements) {
      const newProgress = achievement.progress + progress;
      const isComplete = newProgress >= achievement.maxProgress;
      const updateData: any = {
        progress: Math.min(newProgress, achievement.maxProgress),
        isComplete,
      };

      if (isComplete && !achievement.isComplete) {
        updateData.unlockedAt = new Date();
      }

      await storage.updateAchievement(achievement.id, updateData);
    }
  } catch (error) {
    console.error(`Failed to update ${type} achievements:`, error);
  }
}

/**
 * Check and update composite achievements that depend on multiple factors
 * @param userId User ID
 * @param stats User stats
 * @param storage Storage interface
 */
export async function checkCompositeAchievements(userId: number, _stats: any, storage: any) {
  try {
    const userAchievements = await storage.getAchievements(userId);
    const _compositeAchievements = userAchievements.filter(
      (ach: any) => ach.type === 'composite' && !ach.isComplete
    );

    // In a real app, this would check various conditions based on user stats, email activity, etc.
    // For now, we'll leave this as a placeholder for future implementation
  } catch (error) {
    console.error('Failed to check composite achievements:', error);
  }
}
