import {
  type UseMutateFunction,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import apiClient from '@/lib/apiClient';
import { useAuth } from '@/context/AuthContext';
import type {
  Achievement,
  AchievementsData,
  AchievementCategory,
} from '@/types/achievements';

/**
 * Type for creating a new achievement (without id and userId which are server-generated)
 */
type CreateAchievementInput = Omit<Achievement, 'id'>;

/**
 * Type for updating an existing achievement (id is required, other fields are optional)
 */
type UpdateAchievementInput = Partial<Achievement> & { id: string };

/**
 * Interface for the return value of useAchievements hook
 */
interface UseAchievementsResult {
  achievementsData: AchievementsData | undefined;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
  createAchievementMutation: {
    mutate: UseMutateFunction<Achievement, Error, CreateAchievementInput, unknown>;
    isPending: boolean;
    isError: boolean;
    error: Error | null;
  };
  updateAchievementMutation: {
    mutate: UseMutateFunction<Achievement, Error, UpdateAchievementInput, unknown>;
    isPending: boolean;
    isError: boolean;
    error: Error | null;
  };
}

/**
 * Hook for managing achievements data and operations
 * @returns Object containing achievements data, loading state, error state, and mutation functions
 */
export function useAchievements(): UseAchievementsResult {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { user } = useAuth();

  const {
    data: achievementsData,
    isLoading,
    error,
    refetch,
  } = useQuery<AchievementsData>({
    queryKey: ['achievements', user?.id],
    queryFn: async () => {
      const response = await apiClient.get<AchievementsData>('/api/achievements');
      return response;
    },
    enabled: !!user,
  });

  // Mutation for creating an achievement
  const createAchievementMutation = useMutation<Achievement, Error, CreateAchievementInput>({
    mutationFn: (achievement) => apiClient.post('/api/achievements', achievement),
    onSuccess: (newAchievement) => {
      queryClient.invalidateQueries({ queryKey: ['achievements', user?.id] });
      toast({
        title: 'Achievement created',
        description: `${newAchievement.name} has been created successfully.`,
      });
    },
    onError: (err) => {
      toast({
        title: 'Failed to create achievement',
        description: err.message,
        variant: 'destructive',
      });
    },
  });

  // Mutation for updating an achievement
  const updateAchievementMutation = useMutation<Achievement, Error, UpdateAchievementInput>({
    mutationFn: ({ id, ...achievement }) => apiClient.patch(`/api/achievements/${id}`, achievement),
    onSuccess: (updatedAchievement) => {
      queryClient.invalidateQueries({ queryKey: ['achievements', user?.id] });
      toast({
        title: 'Achievement unlocked!',
        description: `${updatedAchievement.name} has been completed.`,
        variant: 'default',
      });
    },
    onError: (err) => {
      toast({
        title: 'Failed to update achievement',
        description: err.message,
        variant: 'destructive',
      });
    },
  });

  return {
    achievementsData,
    isLoading,
    error: error ?? null,
    refetch,
    createAchievementMutation: {
      mutate: createAchievementMutation.mutate,
      isPending: createAchievementMutation.isPending,
      isError: createAchievementMutation.isError,
      error: createAchievementMutation.error ?? null,
    },
    updateAchievementMutation: {
      mutate: updateAchievementMutation.mutate,
      isPending: updateAchievementMutation.isPending,
      isError: updateAchievementMutation.isError,
      error: updateAchievementMutation.error ?? null,
    },
  };
}
