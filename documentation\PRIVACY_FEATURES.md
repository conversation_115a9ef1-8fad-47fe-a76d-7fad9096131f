# Privacy Features Implementation

## Overview

This document outlines the privacy improvements implemented in InboxZeroAI to enhance user data protection, provide GDPR compliance, and give users control over their personal data.

## 🔒 Key Privacy Features

### 1. Automatic Data Expiration
- **Content Expiration**: Email content automatically expires and is deleted based on user-configurable retention periods
- **Default Retention**: 30 days for email content, 90 days for metadata
- **User Control**: Users can set retention periods from 1 to 365 days
- **Graceful Degradation**: Expired content is removed while preserving essential metadata (sender, subject, dates)

### 2. Enhanced Encryption
- **AES-256-GCM Encryption**: All sensitive email content can be encrypted at rest
- **Selective Encryption**: Users can enable/disable encryption per their preference
- **Content Types Encrypted**:
  - Original email content
  - HTML email content
  - AI-generated summaries
  - AI-generated replies
- **Transparent Decryption**: Content is automatically decrypted when accessed by the user

### 3. Privacy Settings Management
- **Granular Controls**: Users can configure individual privacy settings
- **Consent Tracking**: GDPR-compliant consent management with versioning
- **Settings Available**:
  - Data retention period (1-365 days)
  - AI processing consent
  - Email content storage preference
  - Auto-deletion of processed emails
  - Encryption of sensitive data

### 4. GDPR Compliance Tools
- **Data Export**: Complete user data export in JSON format
- **Data Purge**: Selective or complete data deletion
- **Right to be Forgotten**: Full user data removal capabilities
- **Consent Management**: Tracked consent with timestamps and versions

## 🗄️ Database Schema Changes

### New Email Fields
```sql
-- Privacy and data retention fields
content_expires_at TIMESTAMP,           -- When content should be deleted
last_accessed TIMESTAMP DEFAULT NOW(),  -- Track access for privacy
is_content_encrypted BOOLEAN DEFAULT false, -- Encryption status
retention_days INTEGER DEFAULT 30       -- User-specific retention
```

### New Settings Fields
```sql
-- Enhanced privacy settings
data_retention_days INTEGER DEFAULT 30,
allow_ai_processing BOOLEAN DEFAULT true,
store_email_content BOOLEAN DEFAULT true,
auto_delete_processed_emails BOOLEAN DEFAULT false,
encrypt_sensitive_data BOOLEAN DEFAULT true,
consent_version TEXT DEFAULT '1.0',
consent_timestamp TIMESTAMP DEFAULT NOW(),
created_at TIMESTAMP DEFAULT NOW(),
updated_at TIMESTAMP DEFAULT NOW()
```

## 🚀 API Endpoints

### Privacy Settings Management
- `GET /api/privacy/settings` - Get current privacy settings
- `PUT /api/privacy/settings` - Update privacy settings

### Data Management
- `GET /api/privacy/data-stats` - Get user data statistics
- `POST /api/privacy/cleanup-expired` - Manual cleanup of expired content
- `POST /api/privacy/encrypt-emails` - Manual encryption of existing emails
- `GET /api/privacy/export-data` - Export user data (GDPR)
- `POST /api/privacy/purge-data` - Purge user data (GDPR)

## 🔄 Automated Background Tasks

### Scheduled Data Cleanup
- **Frequency**: Runs every hour
- **Content Expiration**: Removes expired email content while preserving metadata
- **Complete Deletion**: Removes emails older than metadata retention period (90 days) that are in trash
- **Task Queue Cleanup**: Removes completed tasks older than 7 days

### Encryption Application
- **Frequency**: Runs every 2 hours
- **Scope**: Applies encryption to unencrypted emails for users who have enabled it
- **Batch Processing**: Processes emails in batches to avoid system overload

## 🛡️ Security Features

### Encryption Implementation
- **Algorithm**: AES-256-GCM (authenticated encryption)
- **Key Management**: Environment-based encryption keys with rotation support
- **Backward Compatibility**: Supports both legacy and new encryption formats
- **Error Handling**: Graceful fallback when decryption fails

### Access Tracking
- **Last Accessed**: Tracks when emails are viewed
- **Privacy Audit**: Enables users to see their data access patterns
- **Retention Enforcement**: Helps enforce data retention policies

## 📋 Migration Guide

### For Existing Installations

1. **Run Database Migration**:
   ```bash
   npx tsx scripts/migrate-privacy-features.js
   ```

2. **Verify Migration**:
   - Check that new database fields are added
   - Confirm default privacy settings are applied
   - Verify content expiration is scheduled for existing emails

3. **Configure Environment**:
   ```bash
   # Optional: Set encryption key for enhanced security
   ENCRYPTION_KEY=your-base64-encryption-key
   ```

### For New Installations
Privacy features are automatically enabled with secure defaults:
- 30-day content retention
- Encryption enabled
- AI processing consent required
- Full GDPR compliance tools available

## 🎛️ Configuration Options

### Environment Variables
```bash
# Encryption (optional - auto-generated if not provided)
ENCRYPTION_KEY=base64-encoded-key

# Scheduled task intervals (optional)
DATA_CLEANUP_INTERVAL=60        # minutes
TASK_CLEANUP_INTERVAL=240       # minutes
ENCRYPTION_INTERVAL=120         # minutes
```

### User Settings
```json
{
  "dataRetentionDays": 30,
  "allowAIProcessing": true,
  "storeEmailContent": true,
  "autoDeleteProcessedEmails": false,
  "encryptSensitiveData": true,
  "consentVersion": "1.0",
  "consentTimestamp": "2024-01-01T00:00:00Z"
}
```

## 🔍 Monitoring and Observability

### Logging
- All privacy operations are logged with appropriate detail levels
- Data retention actions are logged for audit purposes
- Encryption/decryption operations are tracked
- User consent changes are logged

### Metrics
- Content expiration statistics
- Encryption application progress
- Data export/purge requests
- Privacy settings usage patterns

## 🚨 Important Considerations

### Performance Impact
- **Encryption**: Minimal performance impact on email processing
- **Scheduled Tasks**: Run during low-usage periods
- **Database Queries**: Optimized with appropriate indexes

### Data Recovery
- **Expired Content**: Cannot be recovered after expiration
- **Encrypted Data**: Requires valid encryption key for recovery
- **Purged Data**: Permanently deleted and cannot be recovered

### Compliance
- **GDPR Ready**: Full compliance with data subject rights
- **Audit Trail**: Complete logging of privacy-related actions
- **Consent Management**: Proper consent tracking and versioning

## 🔧 Troubleshooting

### Common Issues

1. **Migration Fails**:
   - Check database permissions
   - Verify DATABASE_URL is correct
   - Ensure no active connections during migration

2. **Encryption Errors**:
   - Verify ENCRYPTION_KEY is properly set
   - Check for corrupted encrypted data
   - Review encryption logs for details

3. **Scheduled Tasks Not Running**:
   - Check server startup logs
   - Verify scheduled tasks service is initialized
   - Review task execution logs

### Debug Commands
```bash
# Test encryption setup
node -e "const { testEncryption } = require('./dist/server.js'); testEncryption();"

# Check database schema
npx drizzle-kit studio

# View privacy settings
curl -H "Authorization: Bearer $TOKEN" http://localhost:5000/api/privacy/settings
```

## 📚 Best Practices

### For Developers
1. Always use the data retention service for new email creation
2. Respect user privacy settings in all processing
3. Log privacy-related operations appropriately
4. Test encryption/decryption flows thoroughly

### For Users
1. Review privacy settings regularly
2. Set appropriate retention periods for your needs
3. Enable encryption for sensitive email content
4. Use data export feature for backup purposes

## 🔮 Future Enhancements

### Planned Features
- **Selective Encryption**: Encrypt only emails matching certain criteria
- **Advanced Retention Rules**: Different retention periods by email category
- **Privacy Dashboard**: Visual interface for privacy settings management
- **Data Minimization**: Automatic reduction of stored data over time

### Integration Opportunities
- **External Key Management**: Integration with services like AWS KMS
- **Compliance Reporting**: Automated GDPR compliance reports
- **Privacy Metrics**: User dashboard showing privacy statistics
- **Advanced Anonymization**: Techniques for further data protection

---

This privacy implementation provides a strong foundation for user data protection while maintaining the functionality and performance of InboxZeroAI. The features are designed to be transparent to users while providing comprehensive control over their personal data. 