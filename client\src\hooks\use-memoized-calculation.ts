/**
 * Custom hook for memoizing expensive calculations
 *
 * This hook is designed to cache the results of expensive functions
 * using a combination of useMemo and a WeakMap cache to optimize performance
 * and prevent unnecessary recalculations.
 */

import { useMemo, useRef } from 'react';

type Calculation<T, R> = (input: T) => R;

/**
 * useMemoizedCalculation hook
 *
 * Provides memoization for expensive calculations with a WeakMap cache
 * for additional caching beyond React's useMemo lifecycle.
 *
 * @param calculation The calculation function to memoize
 * @returns A memoized version of the calculation function
 *
 * Example usage:
 * ```
 * const calculateExpensiveValue = useMemoizedCalculation((data) => {
 *   // Expensive calculation here
 *   return processedResult;
 * });
 *
 * // Later in component:
 * const result = calculateExpensiveValue(someData);
 * ```
 */
export function useMemoizedCalculation<T, R>(calculation: Calculation<T, R>): Calculation<T, R> {
  // Use a WeakMap to store calculations based on input values
  // WeakMap allows garbage collection when inputs are no longer referenced
  const cacheRef = useRef<WeakMap<any, R>>(new WeakMap());

  // Create a stable function reference with useMemo
  return useMemo(() => {
    const calculationWithCache = (input: T): R => {
      // Skip caching for non-object inputs (strings, numbers, etc.)
      if (input === null || typeof input !== 'object') {
        return calculation(input);
      }

      const cache = cacheRef.current;

      // Check if we already have a cached result
      if (cache.has(input)) {
        return cache.get(input)!;
      }

      // Calculate the new result
      const result = calculation(input);

      // Store in cache for future use
      cache.set(input, result);

      return result;
    };

    return calculationWithCache;
  }, [calculation]);
}

/**
 * useDebouncedCalculation hook
 *
 * Similar to useMemoizedCalculation but adds debouncing to prevent
 * rapid recalculations when inputs change quickly.
 *
 * @param calculation The calculation function to memoize and debounce
 * @param delay Debounce delay in milliseconds (default: 300ms)
 * @returns A memoized and debounced version of the calculation function
 */
export function useDebouncedCalculation<T, R>(
  calculation: Calculation<T, R>,
  delay = 300
): Calculation<T, R> {
  // Use a ref to track the timeout ID
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  // Use a ref to track the last input and result
  const lastInputRef = useRef<{ input: T | null; result: R | null }>({
    input: null,
    result: null,
  });

  // Get the basic memoized calculation
  const memoizedCalculation = useMemoizedCalculation(calculation);

  // Create a stable function reference with useMemo
  return useMemo(() => {
    return (input: T): R => {
      // If the input is the same as last time, return the cached result
      if (lastInputRef.current.input === input && lastInputRef.current.result !== null) {
        return lastInputRef.current.result as R;
      }

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // For the immediate response, calculate and return the result
      const result = memoizedCalculation(input);

      // Update the last input and result
      lastInputRef.current = { input, result };

      // Schedule a delayed recalculation (useful for UI updates)
      timeoutRef.current = setTimeout(() => {
        const updatedResult = memoizedCalculation(input);
        lastInputRef.current = { input, result: updatedResult };
      }, delay);

      return result;
    };
  }, [memoizedCalculation, delay]);
}
