import { useState } from 'react';
import EmailConnectionManager from '@/components/email/EmailConnectionManager';
import AppLayout from '@/components/layout/AppLayout';
import PageHeader from '@/components/layout/PageHeader';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAdmin } from '@/hooks/use-admin';
import AccountSettingsTab from './settings/AccountSettings';
import AdminToolsTab from './settings/AdminTools';

export default function SettingsPage() {
  const { isAdmin } = useAdmin();
  const [activeTab, setActiveTab] = useState('account');

  return (
    <AppLayout>
      <PageHeader
        title="Settings"
        description="Manage your account, preferences, and connections."
      />
      <div className="space-y-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="account">Account</TabsTrigger>
            <TabsTrigger value="connections">Connections</TabsTrigger>
            {isAdmin && <TabsTrigger value="admin">Admin Tools</TabsTrigger>}
          </TabsList>
          <TabsContent value="account">
            <AccountSettingsTab />
          </TabsContent>
          <TabsContent value="connections">
            <EmailConnectionManager />
          </TabsContent>
          {isAdmin && (
            <TabsContent value="admin">
              <AdminToolsTab />
            </TabsContent>
          )}
        </Tabs>
      </div>
    </AppLayout>
  );
}
