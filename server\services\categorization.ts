/**
 * Consolidated Email Categorization Service
 *
 * This service consolidates both the standard and optimized categorization approaches,
 * using a lightweight sentiment classifier for fast initial screening,
 * combined with rule-based categorization for better performance.
 *
 * This replaces the previous categorization.ts and categorization-optimized.ts files.
 */
import { type PipelineType, pipeline } from '@xenova/transformers';
import { getEnvVar } from '../lib/environmentValidator';
import logger from '../lib/logger';

// Define standard email categories
export const DEFAULT_EMAIL_CATEGORIES = [
  'Work',
  'Personal',
  'Finance',
  'Shopping',
  'Travel',
  'Social',
  'Updates',
  'Promotions',
  'Family',
  'Health',
  'Education',
  'Legal',
  'Urgent',
  'News',
  'Bills',
  'Invoice',
  'Events',
  'Subscription',
  'IT',
  'Meeting',
  'Project',
];

// Define categories that are typically low urgency
export const LOW_URGENCY_CATEGORIES = ['Promotions', 'Shopping', 'Updates', 'Social'];

// Define rule patterns for specific categories
export const CATEGORY_RULES: Record<string, RegExp[]> = {
  Promotions: [
    /\b(discount|sale|promo|offer|deal|save|coupon|off|%|limited time)\b/i,
    /\b(special offer|clearance|exclusive deal|only today|buy now|act fast)\b/i,
  ],
  Updates: [
    /\b(newsletter|update|latest|news|digest|weekly|monthly|bulletin)\b/i,
    /\b(we've updated|new feature|latest version|release note|now available)\b/i,
  ],
  Urgent: [
    /\b(urgent|asap|immediately|emergency|deadline|critical|time sensitive)\b/i,
    /\b(action required|attention needed|respond by|due today|final notice)\b/i,
  ],
  Social: [
    /\b(invitation|event|party|celebrate|join us|rsvp|meetup|gathering)\b/i,
    /\b(linkedin|facebook|twitter|instagram|social media|connection request)\b/i,
  ],
  Travel: [
    /\b(flight|hotel|booking|reservation|itinerary|travel|trip|vacation)\b/i,
    /\b(confirmation number|booking reference|check-in|departure|arrival)\b/i,
  ],
  News: [
    /\b(breaking news|alert|announcement|press release|latest headlines)\b/i,
    /\b(newsletter|news digest|weekly recap|industry news|trending)\b/i,
  ],
  Bills: [
    /\b(bill|invoice|payment due|statement|receipt|balance|amount due)\b/i,
    /\b(monthly statement|utility bill|payment reminder|autopay|subscription fee)\b/i,
  ],
  Invoice: [
    /\b(invoice|receipt|payment confirmation|order #|transaction|purchase)\b/i,
    /\b(payment received|billing info|tax invoice|payment processed)\b/i,
  ],
  Events: [
    /\b(webinar|conference|seminar|workshop|summit|registration|agenda)\b/i,
    /\b(online event|upcoming event|save the date|calendar|reminder)\b/i,
  ],
  Meeting: [
    /\b(meeting|call|sync|discussion|session|appointment|conference)\b/i,
    /\b(zoom|teams|google meet|calendar invite|agenda|schedule|reschedule)\b/i,
  ],
  Project: [
    /\b(project|task|milestone|deadline|status update|deliverable)\b/i,
    /\b(action item|follow-up|progress report|timeline|next steps)\b/i,
  ],
};

// Sender domain categorization patterns
export const DOMAIN_CATEGORIES: Record<string, string> = {
  'linkedin.com': 'Social',
  'facebook.com': 'Social',
  'twitter.com': 'Social',
  'instagram.com': 'Social',
  'amazon.com': 'Shopping',
  'ebay.com': 'Shopping',
  'paypal.com': 'Finance',
  'chase.com': 'Finance',
  'bankofamerica.com': 'Finance',
  'citibank.com': 'Finance',
  'wellsfargo.com': 'Finance',
  'capitalone.com': 'Finance',
  'amex.com': 'Finance',
  'expedia.com': 'Travel',
  'booking.com': 'Travel',
  'airbnb.com': 'Travel',
  'hotels.com': 'Travel',
  'outlook.com': 'Personal',
  'gmail.com': 'Personal',
  'yahoo.com': 'Personal',
  'hotmail.com': 'Personal',
};

// Category confidence thresholds (minimum confidence to assign category)
export const CATEGORY_THRESHOLDS: Record<string, number> = {
  Urgent: 0.75, // Higher threshold for urgent emails
  Work: 0.65, // Higher for important work emails
  Finance: 0.7, // Higher for financial matters
  Promotions: 0.55, // Lower for promotional content
  Updates: 0.6, // Lower for updates
  Social: 0.65, // Medium for social
  News: 0.6, // Lower for news content
  Bills: 0.7, // Higher for financial matters
  Invoice: 0.7, // Higher for financial matters
  Events: 0.65, // Medium for events
  Meeting: 0.7, // Higher for work-related meetings
  Project: 0.7, // Higher for work-related projects
  default: 0.65, // Default threshold for other categories
};

// Type definitions for categorization results
export interface CategoryResult {
  category: string; // The assigned category
  confidence: number; // Confidence score between 0 and 1
  allScores?: Record<string, number>; // Optional scores for all categories
  method?: string; // The method used for categorization (rules, ml, hybrid)
}

// Interface for feedback input
export interface CategoryFeedback {
  originalCategory: string;
  correctedCategory: string;
  subject: string;
  content: string;
  emailId?: number;
  userId?: number;
}

// Performance metrics for monitoring
interface PerformanceMetrics {
  totalCalls: number;
  fastPathCalls: number;
  fullModelCalls: number;
  ruleBasedResults: number;
  domainBasedResults: number;
  mlResults: number;
  totalProcessingTime: number;
  avgProcessingTime: number;
  errors: number;
}

// Initialize metrics
const metrics: PerformanceMetrics = {
  totalCalls: 0,
  fastPathCalls: 0,
  fullModelCalls: 0,
  ruleBasedResults: 0,
  domainBasedResults: 0,
  mlResults: 0,
  totalProcessingTime: 0,
  avgProcessingTime: 0,
  errors: 0,
};

// Cache for the zero-shot classification pipeline
let categorizationPipeline: any = null;

// Cache for the fast sentiment classifier
let fastClassifier: any = null;

// Model loading promises to prevent duplicate loading
let modelLoadingPromise: Promise<void> | null = null;
let fastModelLoadingPromise: Promise<void> | null = null;

// Model initialization status flags
let mainModelReady = false;
let fastModelReady = false;

/**
 * Initialize the fast sentiment classifier for initial screening
 */
export async function initFastClassifier(): Promise<void> {
  if (fastClassifier) return;

  if (!fastModelLoadingPromise) {
    fastModelLoadingPromise = (async () => {
      try {
        // Use a tiny model for fast inference
        fastClassifier = await pipeline(
          'sentiment-analysis' as PipelineType,
          'Xenova/distilbert-base-uncased-finetuned-sst-2-english'
        );
        fastModelReady = true;
      } catch (error) {
        console.error('[ERROR] Failed to load lightweight model:', error);
        fastModelLoadingPromise = null;
      }
    })();
  }

  return fastModelLoadingPromise;
}

/**
 * Initialize the zero-shot classification pipeline for detailed categorization
 */
export async function initCategorizationModel(): Promise<void> {
  if (categorizationPipeline) return;

  if (!modelLoadingPromise) {
    modelLoadingPromise = (async () => {
      try {
        categorizationPipeline = await pipeline(
          'zero-shot-classification' as PipelineType,
          'Xenova/distilbert-base-uncased-mnli'
        );
        mainModelReady = true;
      } catch (error) {
        console.error('[ERROR] Failed to initialize main email categorization model:', error);
        modelLoadingPromise = null;
      }
    })();
  }

  return modelLoadingPromise;
}

/**
 * Initialize both classification models (can be called during application startup)
 * This preloads models to avoid delays during first categorization request
 */
export async function initializeAllModels(): Promise<void> {
  // We no longer use the fast classifier, only the main categorization model.
  if (String(getEnvVar('SKIP_AI_MODEL_LOADING')) !== 'true') {
    await initCategorizationModel();
  }
}

/**
 * Perform quick sentiment classification to determine if full analysis is needed
 */
async function quickClassify(
  content: string,
  subject: string
): Promise<{
  needsDeepAnalysis: boolean;
  sentiment: 'positive' | 'negative' | 'neutral';
  preliminaryCategory?: string;
}> {
  // Check if we can use the fast path with rule-based categorization
  const ruleBasedResult = checkRuleBasedCategory(subject, content);

  // If we have a high-confidence rule match, we can take the fast path
  if (ruleBasedResult && ruleBasedResult.confidence > 0.85) {
    return {
      needsDeepAnalysis: false,
      sentiment: 'neutral',
      preliminaryCategory: ruleBasedResult.category,
    };
  }

  // Default response if fast model not available
  if (!fastClassifier || !fastModelReady) {
    return { needsDeepAnalysis: true, sentiment: 'neutral' };
  }

  try {
    // Combine subject and truncated content for better sentiment detection
    const combinedText = `${subject} ${content}`.substring(0, 512);

    // Get fast sentiment classification
    const result = await fastClassifier(combinedText);

    // Extract the sentiment
    const sentiment =
      result[0].label === 'POSITIVE'
        ? 'positive'
        : result[0].label === 'NEGATIVE'
          ? 'negative'
          : 'neutral';

    // Use sentiment as a proxy for whether deep analysis is needed
    // High confidence positive content is often marketing/promotional
    // Negative sentiment often correlates with urgency/importance
    const confidenceScore = result[0].score;

    if (sentiment === 'negative' && confidenceScore > 0.8) {
      // Negative with high confidence could be urgent
      return {
        needsDeepAnalysis: true,
        sentiment,
        preliminaryCategory: 'Urgent',
      };
    }
    if (sentiment === 'positive' && confidenceScore > 0.9) {
      // Very positive content is likely promotional
      return {
        needsDeepAnalysis: false,
        sentiment,
        preliminaryCategory: 'Promotions',
      };
    }

    // For emails with less clear sentiment, decide based on content length
    const needsDeepAnalysis =
      sentiment === 'negative' || // Negative sentiment might need deep analysis
      content.length > 1500; // Long content probably needs deep analysis

    return { needsDeepAnalysis, sentiment };
  } catch (error) {
    console.error('[ERROR] Quick classification failed:', error);
    return { needsDeepAnalysis: true, sentiment: 'neutral' };
  }
}

/**
 * Clean and preprocess email content to improve categorization accuracy
 */
function preprocessEmailContent(content: string): string {
  if (!content) return '';

  // Use a single-pass optimization approach with combined regex patterns
  const patternGroups = [
    // Remove email signatures (single regex for common patterns)
    {
      pattern: /--\s*[\r\n]+(.*(\r|\n|$))+|(\r|\n)--[\r\n]+.+$/im,
      replace: ' ',
    },

    // Remove quoted replies (combined pattern with alternation)
    { pattern: /^>.*$|On.*wrote:[\s\S]*/gm, replace: ' ' },

    // Remove URLs and email addresses in one pass
    {
      pattern: /https?:\/\/[^\s]+|[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
      replace: ' ',
    },
  ];

  // Apply all replacements in sequence
  let processedContent = content;
  for (const { pattern, replace } of patternGroups) {
    processedContent = processedContent.replace(pattern, replace);
  }

  // Final cleanup of whitespace - using more efficient trim
  return processedContent.replace(/\s+/g, ' ').trim();
}

/**
 * Extract sender domain from an email address
 */
function extractSenderDomain(sender: string): string | null {
  if (!sender) return null;

  const match = sender.match(/@([^@]+)$/);
  return match ? match[1].toLowerCase() : null;
}

/**
 * Check if an email matches rule-based category patterns
 */
export function checkRuleBasedCategory(
  subject: string,
  content: string
): { category: string; confidence: number } | null {
  if (!subject && !content) return null;

  // Prepare the text only once - lowercase for case-insensitive matching
  const lowerSubject = subject.toLowerCase();
  const lowerContent = content.toLowerCase();

  // First check the subject only (prioritize subject matches for both speed and accuracy)
  for (const [category, patterns] of Object.entries(CATEGORY_RULES)) {
    // Check if any pattern matches the subject (this is faster and has high precision)
    if (patterns.some((pattern) => pattern.test(lowerSubject))) {
      return { category, confidence: 0.9 }; // High confidence for subject matches
    }
  }

  // If no match in subject, construct combined text only when needed
  const combinedText = `${lowerSubject} ${lowerContent}`;

  // Use an early return approach to avoid unnecessary pattern checking
  for (const [category, patterns] of Object.entries(CATEGORY_RULES)) {
    let matchCount = 0;

    // Check each pattern until we have enough to make a decision
    for (const pattern of patterns) {
      if (pattern.test(combinedText)) {
        matchCount++;
        // If we already have multiple matches, we can exit early
        if (matchCount > 1) {
          return { category, confidence: 0.85 };
        }
      }
    }

    // If we found a single match for this category
    if (matchCount === 1) {
      return { category, confidence: 0.75 };
    }
  }

  return null;
}

/**
 * Check domain-based category
 */
export function checkDomainCategory(
  sender?: string
): { category: string; confidence: number } | null {
  if (!sender) return null;

  const domain = extractSenderDomain(sender);
  if (domain && DOMAIN_CATEGORIES[domain]) {
    return {
      category: DOMAIN_CATEGORIES[domain],
      confidence: 0.85,
    };
  }

  return null;
}

/**
 * Return a fallback category when models are unavailable
 */
function getFallbackCategory(subject: string, content: string, sender?: string): CategoryResult {
  // First try rule-based categorization
  const ruleBasedResult = checkRuleBasedCategory(subject, content);
  if (ruleBasedResult) {
    metrics.ruleBasedResults++;
    return {
      category: ruleBasedResult.category,
      confidence: ruleBasedResult.confidence,
      method: 'rules_fallback',
    };
  }

  // Then try domain-based
  if (sender) {
    const domainResult = checkDomainCategory(sender);
    if (domainResult) {
      metrics.domainBasedResults++;
      return {
        category: domainResult.category,
        confidence: domainResult.confidence,
        method: 'domain_fallback',
      };
    }
  }

  // Last resort fallback
  return {
    category: 'Uncategorized',
    confidence: 0.5,
    method: 'fallback',
  };
}

/**
 * Optimized email categorization function
 * Uses a two-tier approach: fast screening followed by detailed analysis only when needed
 */
export async function categorizeEmail(
  subject: string,
  content: string,
  sender?: string,
  categories: string[] = DEFAULT_EMAIL_CATEGORIES,
  includeAllScores = false
): Promise<CategoryResult> {
  const startTime = Date.now();
  metrics.totalCalls++;

  try {
    // --- Step 1: Rule-Based Categorization ---
    const ruleResult = checkRuleBasedCategory(subject, content);
    if (ruleResult) {
      metrics.ruleBasedResults++;
      return { ...ruleResult, method: 'rule' };
    }

    // --- Step 2: Domain-Based Categorization ---
    const domainResult = checkDomainCategory(sender);
    if (domainResult) {
      metrics.domainBasedResults++;
      return { ...domainResult, method: 'domain' };
    }

    // --- Step 3: ML-Based Categorization ---
    metrics.fullModelCalls++;
    if (!mainModelReady) {
      await initCategorizationModel();
    }
    
    if (!categorizationPipeline) {
      throw new Error('Categorization model not available.');
    }

    const preprocessedContent = preprocessEmailContent(content);
    const result = await categorizationPipeline(preprocessedContent, categories, {
      multi_label: true,
    });

    const primaryCategory = result.labels[0];
    const confidence = result.scores[0];

    const categoryThreshold = CATEGORY_THRESHOLDS[primaryCategory] ?? CATEGORY_THRESHOLDS.default;

    if (confidence < categoryThreshold) {
      return getFallbackCategory(subject, content, sender);
    }
    
    const allScores = includeAllScores
      ? result.labels.reduce((obj: Record<string, number>, label: string, index: number) => {
          obj[label] = result.scores[index];
          return obj;
        }, {})
      : undefined;

    return {
      category: primaryCategory,
      confidence,
      allScores,
      method: 'ml',
    };
  } catch (error) {
    metrics.errors++;
    logger.error('Error in categorizeEmail', {
      error: error instanceof Error ? error.message : String(error),
    });
    return getFallbackCategory(subject, content, sender);
  } finally {
    const duration = Date.now() - startTime;
    metrics.totalProcessingTime += duration;
    metrics.avgProcessingTime = metrics.totalProcessingTime / metrics.totalCalls;
  }
}

/**
 * Get categorization performance metrics
 */
export function getCategoryPerformanceMetrics(): PerformanceMetrics {
  return { ...metrics };
}

/**
 * Reset performance metrics (e.g., for testing)
 */
export function resetPerformanceMetrics(): void {
  Object.assign(metrics, {
    totalCalls: 0,
    fastPathCalls: 0,
    fullModelCalls: 0,
    ruleBasedResults: 0,
    domainBasedResults: 0,
    mlResults: 0,
    totalProcessingTime: 0,
    avgProcessingTime: 0,
    errors: 0,
  });
}

// Store feedback data for learning
const categoryFeedback: CategoryFeedback[] = [];

/**
 * Get multiple categories for an email (from original categorization.ts)
 */
export async function getMultipleCategories(
  subject: string,
  content: string,
  sender?: string,
  categories: string[] = DEFAULT_EMAIL_CATEGORIES,
  threshold = 0.1,
  maxCategories = 3
): Promise<{ categories: string[]; confidences?: number[] }> {
  try {
    const result = await categorizeEmail(subject, content, sender, categories, true);

    if (!result.allScores) {
      return { categories: [result.category], confidences: [result.confidence] };
    }

    // Sort scores and filter by threshold
    const sortedScores = Object.entries(result.allScores)
      .sort(([, a], [, b]) => b - a)
      .filter(([, score]) => score >= threshold)
      .slice(0, maxCategories);

    return {
      categories: sortedScores.map(([category]) => category),
      confidences: sortedScores.map(([, score]) => score),
    };
  } catch (error) {
    console.error('Error getting multiple categories:', error);
    return { categories: ['Uncategorized'] };
  }
}

/**
 * Detect urgency of an email (from original categorization.ts)
 */
export async function detectUrgency(
  subject: string,
  content: string,
  category?: string
): Promise<{ isUrgent: boolean; confidence: number; method?: string }> {
  try {
    // Check for urgent keywords first
    const urgentPatterns = CATEGORY_RULES.Urgent || [];
    const combinedText = `${subject} ${content}`.toLowerCase();

    for (const pattern of urgentPatterns) {
      if (pattern.test(combinedText)) {
        return {
          isUrgent: true,
          confidence: 0.9,
          method: 'keyword_match',
        };
      }
    }

    // If category is already Urgent, return high confidence
    if (category === 'Urgent') {
      return {
        isUrgent: true,
        confidence: 0.85,
        method: 'category_based',
      };
    }

    // Categories that are typically not urgent
    if (category && LOW_URGENCY_CATEGORIES.includes(category)) {
      return {
        isUrgent: false,
        confidence: 0.8,
        method: 'category_based',
      };
    }

    // Default to not urgent
    return {
      isUrgent: false,
      confidence: 0.6,
      method: 'default',
    };
  } catch (error) {
    console.error('Error detecting urgency:', error);
    return {
      isUrgent: false,
      confidence: 0,
      method: 'error',
    };
  }
}

/**
 * Submit category feedback for learning (from original categorization.ts)
 */
export async function submitCategoryFeedback(feedback: CategoryFeedback): Promise<boolean> {
  try {
    // Store feedback
    categoryFeedback.push({
      ...feedback,
      originalCategory: feedback.originalCategory,
      correctedCategory: feedback.correctedCategory,
    });

    // Log the feedback

    // Update rule patterns based on feedback if applicable
    updateRulePatternsFromFeedback(feedback);

    return true;
  } catch (error) {
    console.error('Error submitting category feedback:', error);
    return false;
  }
}

/**
 * Update rule patterns based on feedback (from original categorization.ts)
 */
function updateRulePatternsFromFeedback(_feedback: CategoryFeedback): void {
  try {
    // This is a simplified implementation - in a full system you'd want more sophisticated learning
    // For now, just log the feedback for manual review
    // In a production system, you might update weights or add new patterns
  } catch (error) {
    console.error('Error updating rule patterns from feedback:', error);
  }
}
