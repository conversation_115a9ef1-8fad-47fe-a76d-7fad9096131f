import axios from 'axios';
import { createContext, type ReactNode, useContext, useEffect, useRef, useState } from 'react';

/**
 * Network Status Types
 */
type NetworkStatus = 'online' | 'offline' | 'loading' | 'success';

interface NetworkStatusContextType {
  status: NetworkStatus;
  setStatus: (status: NetworkStatus) => void;
  activeRequests: number;
  lastError: Error | null;
  lastSuccessTime: Date | null;
  isReconnecting: boolean;
}

const NetworkStatusContext = createContext<NetworkStatusContextType | undefined>(undefined);

export function NetworkStatusProvider({ children }: { children: ReactNode }) {
  const [status, setStatus] = useState<NetworkStatus>('online');
  const [activeRequests, setActiveRequests] = useState<number>(0);
  const activeRequestsRef = useRef(0);
  const [lastError, setLastError] = useState<Error | null>(null);
  const [lastSuccessTime, setLastSuccessTime] = useState<Date | null>(null);
  const [isReconnecting, setIsReconnecting] = useState<boolean>(false);

  // Track network status via browser events
  useEffect(() => {
    const handleOnline = () => {
      setStatus('online');
      // Try to reconnect to services when coming back online
      setIsReconnecting(true);
      setTimeout(() => setIsReconnecting(false), 5000);
    };

    const handleOffline = () => {
      setStatus('offline');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial status
    setStatus(navigator.onLine ? 'online' : 'offline');

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Set up axios interceptors to track API requests
  useEffect(() => {
    // Request interceptor
    const requestInterceptor = axios.interceptors.request.use(
      (config) => {
        if (config.url && !config.url.includes('/ping')) {
          // Skip tracking ping requests
          activeRequestsRef.current += 1;
          setActiveRequests(activeRequestsRef.current);
          if (activeRequestsRef.current === 1) {
            setStatus('loading');
          }
        }
        return config;
      },
      (error) => {
        activeRequestsRef.current = Math.max(0, activeRequestsRef.current - 1);
        setActiveRequests(activeRequestsRef.current);
        if (activeRequestsRef.current === 0) {
          setStatus(navigator.onLine ? 'online' : 'offline');
        }
        setLastError(error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    const responseInterceptor = axios.interceptors.response.use(
      (response) => {
        if (response.config.url && !response.config.url.includes('/ping')) {
          activeRequestsRef.current = Math.max(0, activeRequestsRef.current - 1);
          setActiveRequests(activeRequestsRef.current);

          if (activeRequestsRef.current === 0) {
            // Show success briefly, then go back to online
            setStatus('success');
            setLastSuccessTime(new Date());
            setTimeout(() => {
              setStatus(navigator.onLine ? 'online' : 'offline');
            }, 1500);
          }
        }
        return response;
      },
      (error) => {
        if (error.config?.url && !error.config.url.includes('/ping')) {
          activeRequestsRef.current = Math.max(0, activeRequestsRef.current - 1);
          setActiveRequests(activeRequestsRef.current);

          if (activeRequestsRef.current === 0) {
            setStatus(navigator.onLine ? 'online' : 'offline');
          }

          setLastError(error);
        }
        return Promise.reject(error);
      }
    );

    // Clean up interceptors
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, []);

  const contextValue = {
    status,
    setStatus,
    activeRequests,
    lastError,
    lastSuccessTime,
    isReconnecting,
  };

  return (
    <NetworkStatusContext.Provider value={contextValue}>{children}</NetworkStatusContext.Provider>
  );
}

export function useNetworkStatus() {
  const context = useContext(NetworkStatusContext);
  if (context === undefined) {
    throw new Error('useNetworkStatus must be used within a NetworkStatusProvider');
  }
  return context;
}
