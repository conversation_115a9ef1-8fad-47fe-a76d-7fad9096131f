import { createContext, useCallback, useContext, useEffect, useState } from 'react';

// Define theme types
export type Theme = 'light' | 'dark' | 'system';

// Define the context type
export interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  actualTheme: 'light' | 'dark'; // The actual applied theme (never 'system')
}

// Create the context with a default value
export const ThemeContext = createContext<ThemeContextType>({
  theme: 'system',
  actualTheme: 'light',
  setTheme: () => {
    // Default implementation
  },
});

// Define provider props
interface ThemeProviderProps {
  children: React.ReactNode;
  storageKey?: string;
}

// Create the provider component
export function ThemeProvider({ children, storageKey = 'theme' }: ThemeProviderProps) {
  // Get initial theme from localStorage if available
  const getInitialTheme = (): Theme => {
    try {
      const storedTheme = localStorage.getItem(storageKey) as Theme;
      return storedTheme || 'system';
    } catch {
      // Ignore localStorage errors and fallback to system
      return 'system';
    }
  };

  // State initialized with values from localStorage if available
  const [theme, _setTheme] = useState<Theme>(getInitialTheme);
  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');

  // Function to get system theme preference
  const getSystemTheme = useCallback((): 'light' | 'dark' => {
    if (typeof window === 'undefined') return 'light';
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }, []);

  // Function to apply theme to document
  const applyThemeToDocument = useCallback((themeToApply: 'light' | 'dark') => {
    if (typeof document === 'undefined') return;

    // Skip application if the theme is already applied correctly
    if (document.documentElement.classList.contains(themeToApply)) {
      return;
    }

    // Set state for consumers
    setActualTheme(themeToApply);

    // Remove existing theme classes and add new one
    document.documentElement.classList.remove('light', 'dark');
    document.documentElement.classList.add(themeToApply);
  }, []);

  // Apply theme when theme state changes
  useEffect(() => {
    const themeToApply = theme === 'system' ? getSystemTheme() : theme;
    applyThemeToDocument(themeToApply);

    // Save theme preference to localStorage
    try {
      localStorage.setItem(storageKey, theme);
    } catch (_e) {
      // Ignore localStorage errors
    }
  }, [theme, getSystemTheme, applyThemeToDocument, storageKey]);

  // Listen for system theme changes when theme is set to 'system'
  useEffect(() => {
    if (theme !== 'system') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = () => {
      const systemTheme = getSystemTheme();
      applyThemeToDocument(systemTheme);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme, getSystemTheme, applyThemeToDocument]);

  // Function to set theme
  const setTheme = useCallback((newTheme: Theme) => {
    _setTheme(newTheme);
  }, []);

  const value = {
    theme,
    setTheme,
    actualTheme,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
