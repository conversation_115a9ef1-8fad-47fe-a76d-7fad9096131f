import {
  categorizeEmail,
  checkDomainCategory,
  checkRuleBasedCategory,
  DEFAULT_EMAIL_CATEGORIES,
} from '@server/services/categorization';

// Mock the transformers library to avoid downloading large models during tests
jest.mock('@xenova/transformers', () => ({
  pipeline: jest.fn().mockResolvedValue((_text: string, categories: string[]) =>
    Promise.resolve({
      labels: categories,
      scores: categories.map((c) => (c === 'Work' ? 0.9 : 0.1)), // Mock response
    })
  ),
  // Add a mock for the dispose function to prevent open handles
  dispose: jest.fn(),
}));

describe('Email Categorization Service', () => {
  describe('checkRuleBasedCategory', () => {
    it('should categorize an email as "Urgent" based on subject keywords', () => {
      const subject = 'URGENT: Action Required Immediately';
      const content = 'Please review the attached document.';
      const result = checkRuleBasedCategory(subject, content);
      expect(result).not.toBeNull();
      expect(result?.category).toBe('Urgent');
      expect(result?.confidence).toBe(0.9);
    });

    it('should categorize an email as "Invoice" based on content keywords', () => {
      const subject = 'Your order details';
      const content = 'Thank you for your purchase. Your transaction ID is 555-1234.';
      const result = checkRuleBasedCategory(subject, content);
      expect(result).not.toBeNull();
      expect(result?.category).toBe('Invoice');
    });

    it('should return null if no rule-based keywords are found', () => {
      const subject = 'Hello there';
      const content = 'Just checking in.';
      const result = checkRuleBasedCategory(subject, content);
      expect(result).toBeNull();
    });
  });

  describe('checkDomainCategory', () => {
    it('should categorize an email as "Social" from a LinkedIn sender', () => {
      const sender = '<EMAIL>';
      const result = checkDomainCategory(sender);
      expect(result).not.toBeNull();
      expect(result?.category).toBe('Social');
      expect(result?.confidence).toBe(0.85);
    });

    it('should categorize an email as "Finance" from a PayPal sender', () => {
      const sender = '<EMAIL>';
      const result = checkDomainCategory(sender);
      expect(result).not.toBeNull();
      expect(result?.category).toBe('Finance');
    });

    it('should return null for an unrecognized domain', () => {
      const sender = '<EMAIL>';
      const result = checkDomainCategory(sender);
      expect(result).toBeNull();
    });

    it('should return null for a missing sender', () => {
      const result = checkDomainCategory(undefined);
      expect(result).toBeNull();
    });
  });

  describe('categorizeEmail (Fast Path)', () => {
    // We need to reset the mock before each test in this describe block
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should use the rule-based category if one is found', async () => {
      const subject = 'Your bill for this month';
      const content = 'Payment is due soon.';
      const result = await categorizeEmail(subject, content);

      expect(result.category).toBe('Bills');
      expect(result.method).toBe('rules');
      // Ensure the ML model was NOT called
      expect(require('@xenova/transformers').pipeline).not.toHaveBeenCalled();
    });

    it('should use the domain-based category if one is found and no rule matches', async () => {
      const subject = 'Your friend sent you a message';
      const content = 'A new person is in your network.';
      const sender = '<EMAIL>';
      const result = await categorizeEmail(subject, content, sender);

      expect(result.category).toBe('Social');
      expect(result.method).toBe('domain');
      expect(require('@xenova/transformers').pipeline).not.toHaveBeenCalled();
    });

    it('should fall back to the ML model if no fast-path category is found', async () => {
      const subject = 'Following up';
      const content = 'Just wanted to touch base on our earlier conversation.';
      const sender = '<EMAIL>';

      const result = await categorizeEmail(subject, content, sender, DEFAULT_EMAIL_CATEGORIES);

      // The mock is set to return 'Work' with high confidence
      expect(result.category).toBe('Work');
      expect(result.method).toBe('ml');
      // Ensure the ML model WAS called
      expect(require('@xenova/transformers').pipeline).toHaveBeenCalled();
    });
  });
});
