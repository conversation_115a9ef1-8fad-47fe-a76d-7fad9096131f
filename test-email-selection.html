<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email Selection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Email Selection Debug Test</h1>
        <p>This page tests the email selection functionality by making direct API calls to the backend.</p>
        
        <div class="test-section">
            <h3>1. Test API Connection</h3>
            <button onclick="testApiConnection()">Test API Connection</button>
            <div id="api-status" class="status info">Click the button to test API connection</div>
        </div>

        <div class="test-section">
            <h3>2. Fetch Emails List</h3>
            <button onclick="fetchEmails()">Fetch Emails</button>
            <div id="emails-status" class="status info">Click the button to fetch emails</div>
            <div id="emails-list"></div>
        </div>

        <div class="test-section">
            <h3>3. Test Email Selection</h3>
            <button onclick="selectFirstEmail()">Select First Email</button>
            <div id="selection-status" class="status info">Click the button to test email selection</div>
            <div id="selected-email-details"></div>
        </div>

        <div class="test-section">
            <h3>Debug Log</h3>
            <button onclick="clearLog()">Clear Log</button>
            <div id="debug-log" class="log"></div>
        </div>
    </div>

    <script>
        let emails = [];
        
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        async function testApiConnection() {
            const statusDiv = document.getElementById('api-status');
            try {
                log('Testing API connection...');
                const response = await fetch('https://localhost:3000/api/health');
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `✅ API Connection successful: ${JSON.stringify(data)}`;
                    log('API connection successful');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ API Connection failed: ${error.message}`;
                log(`API connection failed: ${error.message}`);
            }
        }

        async function fetchEmails() {
            const statusDiv = document.getElementById('emails-status');
            const listDiv = document.getElementById('emails-list');
            
            try {
                log('Fetching emails...');
                const response = await fetch('https://localhost:3000/api/emails?limit=5');
                
                if (response.ok) {
                    const data = await response.json();
                    emails = data.emails || [];
                    
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `✅ Fetched ${emails.length} emails`;
                    
                    listDiv.innerHTML = '<h4>Emails:</h4>';
                    emails.forEach((email, index) => {
                        listDiv.innerHTML += `
                            <div style="border: 1px solid #ccc; padding: 10px; margin: 5px 0; border-radius: 4px;">
                                <strong>Email ${index + 1}:</strong><br>
                                <strong>Message ID:</strong> ${email.messageId || 'N/A'}<br>
                                <strong>Subject:</strong> ${email.subject || 'No subject'}<br>
                                <strong>Sender:</strong> ${email.sender || 'Unknown'}<br>
                                <button onclick="selectEmailByMessageId('${email.messageId}')">Select This Email</button>
                            </div>
                        `;
                    });
                    
                    log(`Successfully fetched ${emails.length} emails`);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Failed to fetch emails: ${error.message}`;
                log(`Failed to fetch emails: ${error.message}`);
            }
        }

        async function selectFirstEmail() {
            if (emails.length === 0) {
                alert('Please fetch emails first');
                return;
            }
            
            const firstEmail = emails[0];
            await selectEmailByMessageId(firstEmail.messageId);
        }

        async function selectEmailByMessageId(messageId) {
            const statusDiv = document.getElementById('selection-status');
            const detailsDiv = document.getElementById('selected-email-details');
            
            try {
                log(`Selecting email with messageId: ${messageId}`);
                
                const response = await fetch(`https://localhost:3000/api/emails/by-message-id/${encodeURIComponent(messageId)}`);
                
                if (response.ok) {
                    const emailData = await response.json();
                    
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `✅ Successfully selected email: ${emailData.subject || 'No subject'}`;
                    
                    detailsDiv.innerHTML = `
                        <h4>Selected Email Details:</h4>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 4px;">
                            <p><strong>ID:</strong> ${emailData.id}</p>
                            <p><strong>Message ID:</strong> ${emailData.messageId}</p>
                            <p><strong>Subject:</strong> ${emailData.subject || 'No subject'}</p>
                            <p><strong>Sender:</strong> ${emailData.sender || 'Unknown'}</p>
                            <p><strong>Received At:</strong> ${emailData.receivedAt || 'Unknown'}</p>
                            <p><strong>Summary:</strong> ${emailData.summary || 'No summary'}</p>
                        </div>
                    `;
                    
                    log(`Successfully selected email: ${emailData.subject}`);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Failed to select email: ${error.message}`;
                detailsDiv.innerHTML = '';
                log(`Failed to select email: ${error.message}`);
            }
        }

        // Initialize
        log('Email Selection Debug Test initialized');
    </script>
</body>
</html>
