/**
 * User Routes
 *
 * API endpoints for user profile management including:
 * - Getting user profile
 * - Updating user profile
 * - Managing user preferences
 */

import type { Request, Response } from 'express';
import { Router } from 'express';
import { storage } from '../storage';
import { catchAsync } from '../utils/errorHandler';

const router = Router();

// Get current user profile
router.get(
  '/me',
  catchAsync(async (req: Request, res: Response) => {
    if (!req.user) {
      return res.status(401).json({ message: 'Not authenticated' });
    }
    res.json({ success: true, user: req.user });
  })
);

// Update user settings
router.patch(
  '/me',
  catchAsync(async (req: Request, res: Response) => {
    if (!req.user) {
      return res.status(401).json({ message: 'Not authenticated' });
    }
    const { name, replyTone, theme } = req.body;
    const updatedUser = await storage.updateUser(req.user?.id, {
      name,
      replyTone,
      // theme settings are handled separately now
    });
    res.json({ success: true, user: updatedUser });
  })
);

export default router;
