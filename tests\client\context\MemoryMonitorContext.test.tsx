import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { act, renderHook } from '@testing-library/react';
import '@testing-library/jest-dom';

// ---------- mocks ------------
const mockGet = jest.fn();
const mockPost = jest.fn();
jest.mock('@/lib/apiClient', () => ({
  __esModule: true,
  default: { get: (...args: any[]) => mockGet(...args), post: (...args: any[]) => mockPost(...args) },
}));

jest.mock('@/hooks/use-toast', () => ({
  __esModule: true,
  useToast: () => ({ toast: jest.fn() }),
}));

import { MemoryMonitorProvider, useMemoryMonitor } from '@/components/memory-monitor/context/MemoryMonitorContext';

describe('Context/MemoryMonitor', () => {
  const wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const client = new QueryClient({ defaultOptions: { queries: { retry: false } } });
    return (
      <QueryClientProvider client={client}>
        <MemoryMonitorProvider>{children}</MemoryMonitorProvider>
      </QueryClientProvider>
    );
  };

  beforeEach(() => {
    mockGet.mockResolvedValue({});
  });

  it('toggleAutoRefresh flips state', async () => {
    const { result } = renderHook(() => useMemoryMonitor(), { wrapper });
    expect(result.current.autoRefresh).toBe(false);
    act(() => {
      result.current.toggleAutoRefresh();
    });
    expect(result.current.autoRefresh).toBe(true);
  });

  it('triggerGarbageCollection sends post request', async () => {
    mockPost.mockResolvedValueOnce({});
    const { result } = renderHook(() => useMemoryMonitor(), { wrapper });
    await act(async () => {
      await result.current.triggerGarbageCollection();
    });
    expect(mockPost).toHaveBeenCalledWith('/admin/memory/gc');
  });
}); 