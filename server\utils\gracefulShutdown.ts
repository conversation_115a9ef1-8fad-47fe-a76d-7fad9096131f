import type { Server } from 'node:http';
import { closeDatabase } from '../db';
import logger from '../lib/logger';

let isShuttingDown = false;

export async function shutdown(server: Server, exitCode = 0, signal?: string) {
  if (isShuttingDown) {
    logger.warn('Shutdown already in progress.');
    return;
  }
  isShuttingDown = true;

  const reason = signal ? `Received ${signal}.` : 'Shutdown initiated.';
  logger.info(`[SHUTDOWN] ${reason} Closing server gracefully.`);

  // 1. Stop accepting new connections
  server.close(async (err) => {
    if (err) {
      logger.error('[SHUTDOWN] Error while closing the server:', err);
    } else {
      logger.info('[SHUTDOWN] Server closed. No longer accepting new connections.');
    }

    // 2. Close database connections
    try {
      logger.info('[SHUTDOWN] Closing database connections...');
      await closeDatabase();
      logger.info('[SHUTDOWN] Database connections closed successfully.');
    } catch (dbError) {
      logger.error('[SHUTDOWN] Error closing database connections:', dbError);
    }

    // 3. Exit the process
    logger.info(`[SHUTDOWN] Graceful shutdown complete. Exiting with code ${exitCode}.`);
    process.exit(exitCode);
  });

  // Force exit after a timeout if server.close() callback doesn't run
  setTimeout(() => {
    logger.error('[SHUTDOWN] Graceful shutdown timed out. Forcing exit.');
    process.exit(1);
  }, 10000).unref(); // 10 second timeout
}
