#!/usr/bin/env node

import { dirname, join } from 'node:path';
import { fileURLToPath } from 'node:url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env file
dotenv.config({ path: join(__dirname, '../.env') });

const requiredEnvVars = {
  VITE_FIREBASE_API_KEY: process.env.VITE_FIREBASE_API_KEY,
  VITE_FIREBASE_AUTH_DOMAIN: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  VITE_FIREBASE_PROJECT_ID: process.env.VITE_FIREBASE_PROJECT_ID,
  VITE_FIREBASE_STORAGE_BUCKET: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  VITE_FIREBASE_MESSAGING_SENDER_ID: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  VITE_FIREBASE_APP_ID: process.env.VITE_FIREBASE_APP_ID,
  FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
  FIREBASE_PRIVATE_KEY: process.env.FIREBASE_PRIVATE_KEY,
  FIREBASE_CLIENT_EMAIL: process.env.FIREBASE_CLIENT_EMAIL,
  DATABASE_URL: process.env.DATABASE_URL,
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
  SESSION_SECRET: process.env.SESSION_SECRET,
};

console.log('🔍 Production Environment Validation');
console.log('=====================================\n');

let hasErrors = false;

console.log('📋 Required Environment Variables:');
Object.entries(requiredEnvVars).forEach(([key, value]) => {
  if (!value || value === 'undefined') {
    console.log(`❌ ${key}: MISSING`);
    hasErrors = true;
  } else {
    const displayValue =
      key.includes('SECRET') || key.includes('PRIVATE_KEY') || key.includes('CLIENT_SECRET')
        ? `${value.substring(0, 8)}...`
        : key.includes('API_KEY') || key.includes('APP_ID')
          ? `${value.substring(0, 10)}...`
          : value;
    console.log(`✅ ${key}: ${displayValue}`);
  }
});

console.log('\n📊 Validation Summary:');
if (hasErrors) {
  console.log('❌ VALIDATION FAILED - Missing required environment variables');
  console.log('🔧 Set all required environment variables in your deployment environment');
  process.exit(1);
} else {
  console.log('✅ ALL REQUIRED VARIABLES ARE SET');
  console.log('🚀 Environment is ready for production deployment');
}
