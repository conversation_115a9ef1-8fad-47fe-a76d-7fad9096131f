/**
 * Session Management Configuration
 *
 * Configures express-session with Redis support for a scalable,
 * secure session store. Falls back to memory store for development.
 */

import crypto from 'node:crypto';
import { RedisStore } from 'connect-redis';
import type { RequestHandler } from 'express';
import session from 'express-session';
import { getEnvVar } from '../lib/environmentValidator';
import logger from '../lib/logger';
import { getRedisClient } from '../services/redis';

// Session configuration interface
interface SessionConfig {
  secret: string;
  store: session.Store;
  cookie: {
    secure: boolean;
    httpOnly: boolean;
    maxAge: number;
    sameSite: 'lax' | 'strict' | 'none';
  };
  resave: boolean;
  saveUninitialized: boolean;
  name: string;
  rolling: boolean;
}

/**
 * Gets the Redis store if available, otherwise returns MemoryStore.
 * Uses the centralized Redis service for consistency.
 */
function getSessionStore(isProduction: boolean): session.Store {
  const redisUrl = getEnvVar('REDIS_URL');
  
  if (!redisUrl) {
    if (isProduction) {
      logger.error(
        'REDIS_URL is required for production session storage. Memory store is not suitable for production.'
      );
      throw new Error('Redis configuration required for production session management.');
    }
    logger.warn(
      'No REDIS_URL configured. Using in-memory session store (only suitable for development).'
    );
    return new session.MemoryStore();
  }

  try {
    const redisClient = getRedisClient();
    const store = new RedisStore({ 
      client: redisClient, 
      prefix: 'sess:', 
      ttl: 7 * 24 * 60 * 60 // 7 days in seconds
    });
    logger.info('Session store configured with Redis.');
    return store;
  } catch (error) {
    if (isProduction) {
      logger.error('Failed to initialize Redis session store in production:', error);
      throw new Error('Redis session store initialization failed in production.');
    }
    logger.warn('Redis not available, falling back to memory store for development:', error);
    return new session.MemoryStore();
  }
}

/**
 * Configures and returns the express-session middleware.
 */
export function configureSession(): RequestHandler {
  let sessionSecret = getEnvVar('SESSION_SECRET');
  const isProduction = getEnvVar('NODE_ENV') === 'production';

  if (!sessionSecret) {
    if (isProduction) {
      logger.error(
        'SESSION_SECRET environment variable is required for session management in production.'
      );
      throw new Error('SESSION_SECRET environment variable is not set.');
    }
    sessionSecret = crypto.randomBytes(32).toString('hex');
    logger.warn(
      'SESSION_SECRET not set in development. Using a temporary, auto-generated secret. This will invalidate sessions on each server restart.'
    );
  } else if (sessionSecret.length < 32 && isProduction) {
    logger.error(
      'SESSION_SECRET is too short. It must be at least 32 characters long for security in production.'
    );
    throw new Error('SESSION_SECRET is not strong enough for production use.');
  }

  const sessionConfig: SessionConfig = {
    secret: sessionSecret,
    name: 'inboxzero.sid',
    store: getSessionStore(isProduction),
    cookie: {
      secure: isProduction, // Use secure cookies in production
      httpOnly: true,
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
      sameSite: 'lax', // Recommended for OAuth callbacks
    },
    resave: false, // Don't save session if unmodified
    saveUninitialized: false, // Don't create session until something stored
    rolling: true, // Reset cookie expiration on every request
  };

  return session(sessionConfig as session.SessionOptions);
}
