/**
 * Integration Module
 *
 * This module integrates our new improvements with the existing codebase
 * in a backward-compatible way.
 */

import type { Express } from 'express';
import logger from '../lib/logger';
import startServer from '../serverStartup';
import { initializeEmailProcessors } from '../services/emailProcessor';
import { asyncHandler, catchAsync, globalErrorHandler } from './errorHandler';
import { validateRequest } from './inputValidator';

/**
 * Integrate our improved modules with the existing application
 * @param app Express application
 * @returns Promise that resolves when integration is complete
 */
export async function integrateImprovements(_app: Express): Promise<void> {
  logger.info('Integrating improved modules...');

  // Register our specialized Gmail error handler first
  // This needs to come before the global error handler to intercept Gmail-specific errors
  // DEPRECATED: This is now handled in server/index.ts
  // app.use(gmailErrorMiddleware);

  // Register our enhanced error handler for other errors
  // DEPRECATED: This is now handled in server/index.ts
  // app.use(globalErrorHandler);

  // Initialize services
  logger.info('Initializing email processors with improved error handling...');
  initializeEmailProcessors();

  // Make error utilities available globally for backward compatibility
  (global as any).catchAsync = catchAsync;
  (global as any).asyncHandler = asyncHandler;
  (global as any).validateRequest = validateRequest;

  logger.info('Improvements integrated successfully');
}

/**
 * Start the improved server with full backward compatibility
 * @param app Express application
 * @param port Port to listen on
 */
export async function startEnhancedServer(app: Express, port: number) {
  try {
    // Apply improvements
    await integrateImprovements(app);

    // Start server with our improved startup process
    return await startServer(app, port);
  } catch (error) {
    logger.error('Failed to start enhanced server:', error);
    throw error;
  }
}

/**
 * Fix circular import issues by providing direct access to all imports
 * This allows modules to import from here instead of creating circular dependencies
 */
export { globalErrorHandler, catchAsync, asyncHandler, validateRequest, startServer };
