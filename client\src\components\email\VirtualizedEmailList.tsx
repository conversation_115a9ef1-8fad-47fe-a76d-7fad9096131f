import { Loader2 } from 'lucide-react';
import type React from 'react';
import { useRef, useEffect } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import EmailItem from '@/components/email/EmailItem';
import type { Email } from '@/types/email';
import logger from '@/lib/logger';

interface VirtualizedEmailListProps {
  emails: Email[];
  selectedEmail: Email | null;
  selectEmail: (messageId: string | null) => void;
  isLoading?: boolean;
  /**
   * Optional estimated row height. Defaults to 110px which matches current CSS.
   */
  estimateSize?: number;
}

/**
 * High-performance email list leveraging @tanstack/react-virtual.
 * Only the items visible in the scroll viewport are actually rendered,
 * eliminating main-thread churn when the inbox contains hundreds/thousands
 * of messages.
 */
const VirtualizedEmailList: React.FC<VirtualizedEmailListProps> = ({
  emails,
  selectedEmail,
  selectEmail,
  isLoading = false,
  estimateSize = 110,
}) => {
  const parentRef = useRef<HTMLDivElement | null>(null);

  // Debug emails prop changes
  useEffect(() => {
    logger.info('[VirtualizedEmailList] Emails prop changed', {
      emailCount: emails.length,
      firstEmailSubject: emails[0]?.subject || 'none',
      lastEmailSubject: emails[emails.length - 1]?.subject || 'none',
      emailIds: emails.slice(0, 5).map(e => e.id), // First 5 email IDs
      timestamp: new Date().toISOString()
    });
  }, [emails]);

  // Setup virtualizer
  const rowVirtualizer = useVirtualizer({
    count: emails.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => estimateSize,
    overscan: 10, // render a few extra rows for smoother scrolling
  });

  // Debug virtualizer state changes
  useEffect(() => {
    logger.info('[VirtualizedEmailList] Virtualizer state changed', {
      totalSize: rowVirtualizer.getTotalSize(),
      virtualItemsCount: rowVirtualizer.getVirtualItems().length,
      emailCount: emails.length,
      timestamp: new Date().toISOString()
    });
  }, [rowVirtualizer.getTotalSize(), rowVirtualizer.getVirtualItems().length, emails.length]);

  // Re-measure after initial mount to handle hidden tabpanes that report zero size
  useEffect(() => {
    rowVirtualizer.measure();
  }, [rowVirtualizer]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!emails || emails.length === 0) {
    return (
      <div className="flex flex-col justify-center items-center h-full text-gray-500 p-8">
        <p className="text-lg">No emails found</p>
        <p className="text-sm mt-2">Try adjusting your filters or search criteria</p>
      </div>
    );
  }

  return (
    <div ref={parentRef} className="h-full flex-1 min-h-0 overflow-y-auto scrollbar-thin email-list">
      <div
        className="relative w-full"
        style={{ height: `${rowVirtualizer.getTotalSize()}px` }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualRow) => {
          const email = emails[virtualRow.index];
          if (!email) {
            logger.warn('[VirtualizedEmailList] Missing email at index', {
              index: virtualRow.index,
              emailsLength: emails.length,
              timestamp: new Date().toISOString()
            });
            return null;
          }
          return (
            <div
              key={email.id}
              data-index={virtualRow.index}
              ref={rowVirtualizer.measureElement}
              className="absolute top-0 left-0 w-full"
              style={{ transform: `translateY(${virtualRow.start}px)` }}
            >
              <EmailItem
                email={email}
                isSelected={selectedEmail?.messageId === email.messageId}
                onClick={() => selectEmail(email.messageId)}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default VirtualizedEmailList;
