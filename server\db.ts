import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from '../shared/schema';
import logger from './lib/logger';

interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean | { rejectUnauthorized: boolean };
}

interface HealthCheckCache {
  lastCheck: number;
  isHealthy: boolean;
  ttl: number; // Time to live in milliseconds
}

interface PoolMetrics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  queuedRequests: number;
  healthCheckHits: number;
  healthCheckMisses: number;
  connectionReuses: number;
  // Phase 3: Advanced metrics
  connectionLatencyMs: number;
  poolUtilization: number;
  connectionErrors: number;
  poolResizes: number;
  resourceCleanups: number;
  memoryPressureAdjustments: number;
}

// Phase 3: Connection Quality Metrics
interface ConnectionQualityMetrics {
  averageLatency: number;
  errorRate: number;
  uptime: number;
  lastHealthCheck: number;
  consecutiveFailures: number;
  performanceScore: number; // 0-100 score based on latency and reliability
}

// Phase 3: Resource Usage Tracking
interface ResourceUsageMetrics {
  memoryUsageMB: number;
  connectionPoolSize: number;
  activeQueries: number;
  queueDepth: number;
  cacheHitRate: number;
  resourcePressure: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Database Connection Pool Manager
 * Prevents connection leaks and manages pool lifecycle with atomic operations
 * Phase 1 Optimizations: Enhanced pool configuration, cached health checks, memory-aware sizing
 */
class DatabasePoolManager {
  private activePool: postgres.Sql | null = null;
  private drizzleInstance: ReturnType<typeof drizzle<typeof schema>> | null = null;
  private connectionPromise: Promise<postgres.Sql> | null = null;
  private isClosing = false;
  private isConnecting = false;
  private connectionAttempts = 0;
  private readonly maxConnectionAttempts = 3;
  private readonly connectionTimeout = 20000; // Reduced from 30s to 20s
  private readonly healthCheckTimeout = 5000; // Reduced from 10s to 5s for faster feedback
  private readonly maxQueueSize = 75; // Increased from 50 to handle more concurrent requests
  private connectionQueue: Array<{
    resolve: (sql: postgres.Sql) => void;
    reject: (error: Error) => void;
  }> = [];

  // Health check caching to reduce overhead
  private healthCheckCache: HealthCheckCache = {
    lastCheck: 0,
    isHealthy: false,
    ttl: 30000, // 30 seconds cache TTL
  };

  // Pool metrics for monitoring
  private poolMetrics: PoolMetrics = {
    totalConnections: 0,
    activeConnections: 0,
    idleConnections: 0,
    queuedRequests: 0,
    healthCheckHits: 0,
    healthCheckMisses: 0,
    connectionReuses: 0,
    // Phase 3: Advanced metrics
    connectionLatencyMs: 0,
    poolUtilization: 0,
    connectionErrors: 0,
    poolResizes: 0,
    resourceCleanups: 0,
    memoryPressureAdjustments: 0,
  };

  // Memory manager integration
  private memoryManager: any = null;
  
  // Phase 3: Advanced connection management
  private connectionQualityMetrics: ConnectionQualityMetrics = {
    averageLatency: 0,
    errorRate: 0,
    uptime: Date.now(),
    lastHealthCheck: 0,
    consecutiveFailures: 0,
    performanceScore: 100,
  };
  
  private resourceUsageMetrics: ResourceUsageMetrics = {
    memoryUsageMB: 0,
    connectionPoolSize: 0,
    activeQueries: 0,
    queueDepth: 0,
    cacheHitRate: 0,
    resourcePressure: 'low',
  };
  
  private readonly latencyHistory: number[] = [];
  private readonly maxLatencyHistory = 100;
  private lastResourceCleanup = Date.now();
  private readonly resourceCleanupInterval = 300000; // 5 minutes

  // Dynamic pool configuration based on memory pressure
  private getOptimizedPoolConfig() {
    const baseConfig = {
      max: 20, // Increased from 10 to 20 for better concurrency
      idle_timeout: 120, // Reduced from 300 to 120 seconds (2 minutes)
      connect_timeout: 15, // Reduced from 30 to 15 seconds
      max_lifetime: 900, // Reduced from 1800 to 900 seconds (15 minutes)
      prepare: false, // Disable prepared statements for better connection reuse
      transform: { undefined: null }, // Handle undefined values efficiently
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    };

    // Integrate with memory manager if available
    try {
      if (!this.memoryManager) {
        const { memoryManager } = require('./utils/memoryManager');
        this.memoryManager = memoryManager;
      }

      if (this.memoryManager) {
        const memoryStats = this.memoryManager.getMemoryStats();
        const recommendation = this.memoryManager.getResourcePoolRecommendation();

        // Adjust pool size based on memory pressure
        switch (memoryStats.pressureLevel) {
          case 'CRITICAL':
            return { ...baseConfig, max: 8, idle_timeout: 60 }; // Minimal connections
          case 'HIGH':
            return { ...baseConfig, max: 12, idle_timeout: 90 }; // Reduced connections
          case 'MEDIUM':
            return { ...baseConfig, max: 16, idle_timeout: 120 }; // Slightly reduced
          default:
            return baseConfig; // Full capacity for low pressure
        }
      }
    } catch (error) {
      logger.debug('[DB] Memory manager not available, using default pool config');
    }

    return baseConfig;
  }

  private readonly poolConfig = this.getOptimizedPoolConfig();

  /**
   * Get or create database connection with atomic pool management
   */
  async getConnection(): Promise<postgres.Sql> {
    // Update queue metrics
    this.poolMetrics.queuedRequests = this.connectionQueue.length;

    // If already connecting, add to queue and wait
    if (this.connectionPromise) {
      logger.debug('[DB] Connection in progress, adding to queue');
      
      // Check queue size to prevent memory issues
      if (this.connectionQueue.length >= this.maxQueueSize) {
        throw new Error('Connection queue is full. Too many concurrent requests.');
      }
      
      return new Promise<postgres.Sql>((resolve, reject) => {
        this.connectionQueue.push({ resolve, reject });
      });
    }

    // If pool is closing, wait for closure and reconnect
    if (this.isClosing) {
      logger.debug('[DB] Pool is closing, waiting for closure completion');
      await this.waitForPoolClosure();
    }

    // Return existing healthy connection with cached health check
    if (this.activePool && !this.isClosing) {
      try {
        // Use cached health check to reduce overhead
        if (this.isCachedHealthCheckValid()) {
          logger.debug('[DB] Reusing connection with cached health check');
          this.poolMetrics.healthCheckHits++;
          this.poolMetrics.connectionReuses++;
          
          // Process any queued requests with the healthy connection
          this.processConnectionQueue(this.activePool);
          return this.activePool;
        }

        // Perform health check and cache result
        await this.testConnectionWithCache(this.activePool);
        logger.debug('[DB] Reusing existing healthy connection');
        this.poolMetrics.connectionReuses++;
        
        // Process any queued requests with the healthy connection
        this.processConnectionQueue(this.activePool);
        return this.activePool;
      } catch (healthError) {
        const errorMessage = healthError instanceof Error ? healthError.message : String(healthError);
        logger.warn('[DB] Existing connection unhealthy, creating new one:', errorMessage);
        
        // Invalidate health check cache
        this.invalidateHealthCheckCache();
        
        // Don't close the pool if it's just a timeout - the connection might still be usable
        if (!errorMessage.includes('Health check timeout')) {
          await this.closePoolSafely();
        }
      }
    }

    // Create new connection atomically
    this.connectionPromise = this.createNewConnection();

    try {
      const newPool = await this.connectionPromise;
      this.connectionPromise = null;
      
      // Process any queued requests
      this.processConnectionQueue(newPool);
      return newPool;
    } catch (error) {
      this.connectionPromise = null;
      
      // Reject all queued requests
      this.processConnectionQueue(null, error as Error);
      throw error;
    }
  }

  /**
   * Check if cached health check is still valid
   */
  private isCachedHealthCheckValid(): boolean {
    const now = Date.now();
    return (
      this.healthCheckCache.isHealthy &&
      (now - this.healthCheckCache.lastCheck) < this.healthCheckCache.ttl
    );
  }

  /**
   * Invalidate health check cache
   */
  private invalidateHealthCheckCache(): void {
    this.healthCheckCache.isHealthy = false;
    this.healthCheckCache.lastCheck = 0;
  }

  /**
   * Test connection with caching
   */
  private async testConnectionWithCache(sql: postgres.Sql): Promise<void> {
    // Check cache first
    if (this.isCachedHealthCheckValid()) {
      this.poolMetrics.healthCheckHits++;
      return;
    }

    // Perform actual health check
    this.poolMetrics.healthCheckMisses++;
    await this.testConnection(sql);
    
    // Cache the successful result
    this.healthCheckCache = {
      lastCheck: Date.now(),
      isHealthy: true,
      ttl: 30000, // 30 seconds
    };
  }

  /**
   * Process queued connection requests
   */
  private processConnectionQueue(connection: postgres.Sql | null, error?: Error): void {
    const queue = [...this.connectionQueue];
    this.connectionQueue = [];

    if (connection) {
      queue.forEach(({ resolve }) => resolve(connection));
    } else if (error) {
      queue.forEach(({ reject }) => reject(error));
    }

    // Update metrics
    this.poolMetrics.queuedRequests = this.connectionQueue.length;
  }

  /**
   * Atomically create new database connection
   */
  private async createNewConnection(): Promise<postgres.Sql> {
    // Double-check if we're already connecting to prevent race conditions
    if (this.isConnecting) {
      // Wait a bit and retry once
      await new Promise(resolve => setTimeout(resolve, 100));
      if (this.isConnecting) {
        throw new Error('Connection creation already in progress');
      }
    }

    this.isConnecting = true;
    this.connectionAttempts++;

    try {
      logger.info(
        `[DB] Creating new connection (attempt ${this.connectionAttempts}/${this.maxConnectionAttempts})`
      );

      if (this.connectionAttempts > this.maxConnectionAttempts) {
        throw new Error(`Maximum connection attempts (${this.maxConnectionAttempts}) exceeded`);
      }

      // Ensure old pool is fully closed before creating new one
      if (this.activePool && !this.isClosing) {
        logger.debug('[DB] Closing existing pool before creating new connection');
        await this.closePoolSafely();
      }

      const config = this.getDatabaseConfig();

      // Get adaptive pool config (may have changed due to memory pressure and load)
      const currentPoolConfig = this.getAdaptivePoolConfig();

      // Track connection creation latency
      const connectionStartTime = Date.now();

      // Create connection with timeout protection
      const connectionPromise = this.createConnectionWithTimeout(config, currentPoolConfig);
      const sql = await connectionPromise;

      // Test the new connection with caching
      await this.testConnectionWithCache(sql);

      // Atomically set as active pool
      this.activePool = sql;
      this.isClosing = false;
      this.connectionAttempts = 0; // Reset on success

      // Track connection latency
      const connectionLatency = Date.now() - connectionStartTime;
      this.trackConnectionLatency(connectionLatency);

      // Update metrics
      this.poolMetrics.totalConnections++;
      this.connectionQualityMetrics.consecutiveFailures = 0; // Reset on success

      logger.info(`[DB] Successfully created and tested new database connection (${connectionLatency}ms)`);
      return sql;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`[DB] Failed to create connection (attempt ${this.connectionAttempts}):`, errorMessage);

      // Track connection errors
      this.poolMetrics.connectionErrors++;
      this.connectionQualityMetrics.consecutiveFailures++;
      
      // Update error rate (exponential moving average)
      const currentErrorRate = this.connectionQualityMetrics.errorRate;
      this.connectionQualityMetrics.errorRate = currentErrorRate * 0.9 + 0.1; // Increase error rate

      // Invalidate health check cache on connection failure
      this.invalidateHealthCheckCache();

      // In development **or test**, provide a helpful error but allow fallback to memory storage
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        logger.warn(
          '[DB] Database connection failed in development mode, application will use memory storage'
        );
        this.connectionAttempts = 0; // Reset for future attempts
        throw new Error('DATABASE_UNAVAILABLE_FALLBACK_TO_MEMORY');
      }

      if (this.connectionAttempts >= this.maxConnectionAttempts) {
        this.connectionAttempts = 0; // Reset for future attempts
        throw new Error(
          `Database connection failed after ${this.maxConnectionAttempts} attempts: ${errorMessage}`
        );
      }

      // Exponential backoff for retry
      const backoffDelay = Math.min(1000 * 2 ** (this.connectionAttempts - 1), 10000);
      logger.info(`[DB] Retrying connection in ${backoffDelay}ms`);
      await new Promise((resolve) => setTimeout(resolve, backoffDelay));

      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  /**
   * Create connection with timeout protection and optimized configuration
   */
  private async createConnectionWithTimeout(config: DatabaseConfig, poolConfig: any): Promise<postgres.Sql> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Database connection timed out after ${this.connectionTimeout}ms`));
      }, this.connectionTimeout);

      try {
        const sql = postgres({
          host: config.host,
          port: config.port,
          database: config.database,
          username: config.username,
          password: config.password,
          ...poolConfig,
          onnotice: () => {}, // Suppress notices for cleaner logs
          onclose: (connection_id: number) => {
            logger.debug(`[DB] Connection ${connection_id} closed`);
            // Update metrics when connection closes
            this.poolMetrics.activeConnections = Math.max(0, this.poolMetrics.activeConnections - 1);
          },
          onparameter: (key: string, value: any) => {
            logger.debug(`[DB] Connection parameter changed: ${key}=${value}`);
          },
          onconnect: () => {
            // Update metrics when connection is established
            this.poolMetrics.activeConnections++;
          },
        });

        clearTimeout(timeout);
        resolve(sql);
      } catch (error) {
        clearTimeout(timeout);
        reject(error);
      }
    });
  }

  /**
   * Test connection health with robust error handling
   */
  private async testConnection(sql: postgres.Sql): Promise<void> {
    // Check if the connection is already being closed
    if (this.isClosing) {
      throw new Error('Connection is being closed');
    }

    try {
      // Add timeout protection for the health check
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Health check timeout')), this.healthCheckTimeout)
      );

      const healthCheckPromise = sql`SELECT 1 as health_check`;

      await Promise.race([healthCheckPromise, timeoutPromise]);
      logger.debug('[DB] Connection health check passed');
    } catch (error) {
      // Handle specific connection errors
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorCode = (error as any)?.code;

      // Handle CONNECTION_DESTROYED and similar connection errors gracefully
      if (
        errorCode === 'CONNECTION_DESTROYED' ||
        errorCode === 'ECONNRESET' ||
        errorCode === 'ENOTFOUND' ||
        errorMessage.includes('CONNECTION_DESTROYED') ||
        errorMessage.includes('connection terminated') ||
        errorMessage.includes('server closed the connection')
      ) {
        logger.warn('[DB] Connection was destroyed or lost during health check');
        throw new Error('Connection lost');
      }

      logger.error('[DB] Connection health check failed:', {
        message: errorMessage,
        code: errorCode,
        type: typeof error,
      });
      throw new Error(`Connection health check failed: ${errorMessage}`);
    }
  }

  /**
   * Get Drizzle instance with enhanced connection management and caching
   */
  async getDrizzleInstance(): Promise<ReturnType<typeof drizzle<typeof schema>>> {
    if (this.drizzleInstance && !this.isClosing) {
      // Use cached health check for Drizzle instance reuse
      try {
        if (this.isCachedHealthCheckValid()) {
          logger.debug('[DB] Reusing Drizzle instance with cached health check');
          this.poolMetrics.healthCheckHits++;
          this.poolMetrics.connectionReuses++;
          return this.drizzleInstance;
        }

        // Test health with caching
        await this.testConnectionWithCache(this.activePool!);
        this.poolMetrics.connectionReuses++;
        return this.drizzleInstance;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        // Handle different types of connection errors appropriately
        if (
          errorMessage.includes('Connection lost') ||
          errorMessage.includes('CONNECTION_DESTROYED') ||
          errorMessage.includes('Connection is being closed')
        ) {
          logger.warn('[DB] Drizzle instance found but connection is lost. Recreating.', {
            reason: errorMessage,
          });
          
          // Invalidate cache and close connection
          this.invalidateHealthCheckCache();
          
          // Close the unhealthy connection safely
          try {
            await this.closePoolSafely();
          } catch (closeError) {
            logger.warn('[DB] Error during unhealthy connection cleanup:', closeError);
          }
        } else if (errorMessage.includes('Health check timeout')) {
          // For timeouts, just log a warning but continue using the connection
          // The connection might still be functional, just slow
          logger.warn('[DB] Health check timed out, but continuing with existing connection', {
            reason: errorMessage,
          });
          return this.drizzleInstance;
        } else {
          // For other errors, log and try to recreate
          logger.warn('[DB] Drizzle instance health check failed. Recreating.', {
            error: errorMessage,
          });
          this.invalidateHealthCheckCache();
          await this.closePoolSafely();
        }
      }
    }

    const sql = await this.getConnection();
    this.drizzleInstance = drizzle(sql, { schema });
    return this.drizzleInstance;
  }

  /**
   * Safely close database pool with proper cleanup
   */
  async closePoolSafely(): Promise<void> {
    if (!this.activePool || this.isClosing) {
      return;
    }

    this.isClosing = true;
    logger.info('[DB] Starting graceful pool closure');

    try {
      // Clear Drizzle instance first
      this.drizzleInstance = null;

      // Close the pool with timeout protection and proper error handling
      const closePromise = this.activePool.end({ timeout: 10 }); // 10 second timeout
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Pool closure timed out')), 15000)
      );

      await Promise.race([closePromise, timeoutPromise]);

      this.activePool = null;
      logger.info('[DB] Pool closed successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorCode = (error as any)?.code;

      // Handle specific connection errors during closure
      if (
        errorCode === 'CONNECTION_DESTROYED' ||
        errorCode === 'ECONNRESET' ||
        errorMessage.includes('CONNECTION_DESTROYED') ||
        errorMessage.includes('connection terminated') ||
        errorMessage.includes('Pool closure timed out')
      ) {
        logger.warn('[DB] Pool closure encountered expected connection errors:', {
          message: errorMessage,
          code: errorCode,
        });
      } else {
        logger.error('[DB] Error during pool closure:', {
          message: errorMessage,
          code: errorCode,
          type: typeof error,
        });
      }

      // Force cleanup even if closure failed
      this.activePool = null;
      this.drizzleInstance = null;
    } finally {
      this.isClosing = false;
    }
  }

  /**
   * Wait for pool closure to complete
   */
  private async waitForPoolClosure(): Promise<void> {
    let attempts = 0;
    const maxAttempts = 50; // 5 seconds max wait

    while (this.isClosing && attempts < maxAttempts) {
      await new Promise((resolve) => setTimeout(resolve, 100));
      attempts++;
    }

    if (this.isClosing) {
      logger.warn('[DB] Pool closure timeout, forcing cleanup');
      this.activePool = null;
      this.drizzleInstance = null;
      this.isClosing = false;
    }
  }

  /**
   * Handle connection loss with automatic recovery
   */
  async handleConnectionLoss(): Promise<void> {
    logger.warn('[DB] Connection loss detected, initiating recovery');

    try {
      // Close existing pool safely
      await this.closePoolSafely();

      // Wait before attempting reconnection
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Create new connection
      await this.getConnection();

      logger.info('[DB] Connection recovery completed successfully');
    } catch (error) {
      logger.error('[DB] Connection recovery failed:', error);
      throw error;
    }
  }

  /**
   * Get database configuration from environment
   * Supports both DATABASE_URL format and individual environment variables
   */
  private getDatabaseConfig(): DatabaseConfig {
    // Try to use DATABASE_URL first (recommended for production)
    if (process.env.DATABASE_URL) {
      try {
        const url = new URL(process.env.DATABASE_URL);
        return {
          host: url.hostname,
          port: Number.parseInt(url.port) || 5432,
          database: url.pathname.slice(1), // Remove leading slash
          username: url.username,
          password: url.password,
          ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
        };
      } catch (error) {
        logger.warn(
          '[DB] Failed to parse DATABASE_URL, falling back to individual variables',
          error
        );
      }
    }

    // Fallback to individual environment variables
    const requiredEnvVars = [
      'DATABASE_HOST',
      'DATABASE_NAME',
      'DATABASE_USER',
      'DATABASE_PASSWORD',
    ];
    const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);

    if (missingVars.length > 0) {
      // In development **or test**, provide a helpful error but allow fallback to memory storage
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        logger.warn(
          `[DB] Database configuration incomplete. Missing: ${missingVars.join(', ')}. Using memory storage fallback.`
        );

        // Provide default development values that will fail gracefully
        return {
          host: process.env.DATABASE_HOST || 'localhost',
          port: Number.parseInt(process.env.DATABASE_PORT || '5432'),
          database: process.env.DATABASE_NAME || 'inboxzero_dev',
          username: process.env.DATABASE_USER || 'postgres',
          password: process.env.DATABASE_PASSWORD || '',
          ssl: false,
        };
      }

      const availableVars = process.env.DATABASE_URL
        ? 'DATABASE_URL is available but failed to parse. '
        : '';
      throw new Error(
        `${availableVars}Missing required environment variables: ${missingVars.join(', ')}. Please set either DATABASE_URL or the individual database variables.`
      );
    }

    return {
      host: process.env.DATABASE_HOST!,
      port: Number.parseInt(process.env.DATABASE_PORT || '5432'),
      database: process.env.DATABASE_NAME!,
      username: process.env.DATABASE_USER!,
      password: process.env.DATABASE_PASSWORD!,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    };
  }

  /**
   * Get enhanced pool statistics with performance metrics
   */
  getPoolStats(): {
    isConnected: boolean;
    isClosing: boolean;
    isConnecting: boolean;
    connectionAttempts: number;
    poolMetrics: PoolMetrics;
    healthCheckCacheStats: {
      isValid: boolean;
      lastCheck: number;
      ttl: number;
      hitRate: number;
    };
    currentPoolConfig: any;
    // Phase 3: Advanced metrics
    connectionQuality: ConnectionQualityMetrics;
    resourceUsage: ResourceUsageMetrics;
    adaptivePoolConfig: any;
  } {
    const totalHealthChecks = this.poolMetrics.healthCheckHits + this.poolMetrics.healthCheckMisses;
    const hitRate = totalHealthChecks > 0 ? this.poolMetrics.healthCheckHits / totalHealthChecks : 0;

    // Update resource metrics before returning
    this.updateResourceUsageMetrics();
    
    return {
      isConnected: !!this.activePool && !this.isClosing,
      isClosing: this.isClosing,
      isConnecting: this.isConnecting,
      connectionAttempts: this.connectionAttempts,
      poolMetrics: { ...this.poolMetrics },
      healthCheckCacheStats: {
        isValid: this.isCachedHealthCheckValid(),
        lastCheck: this.healthCheckCache.lastCheck,
        ttl: this.healthCheckCache.ttl,
        hitRate: Math.round(hitRate * 100) / 100, // Round to 2 decimal places
      },
      currentPoolConfig: this.getOptimizedPoolConfig(),
      // Phase 3: Advanced metrics
      connectionQuality: { ...this.connectionQualityMetrics },
      resourceUsage: { ...this.resourceUsageMetrics },
      adaptivePoolConfig: this.getAdaptivePoolConfig(),
    };
  }

  /**
   * Reset pool metrics (useful for monitoring)
   */
  resetPoolMetrics(): void {
    this.poolMetrics = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      queuedRequests: 0,
      healthCheckHits: 0,
      healthCheckMisses: 0,
      connectionReuses: 0,
      // Phase 3: Advanced metrics
      connectionLatencyMs: 0,
      poolUtilization: 0,
      connectionErrors: 0,
      poolResizes: 0,
      resourceCleanups: 0,
      memoryPressureAdjustments: 0,
    };
    logger.info('[DB] Pool metrics reset');
  }

  /**
   * Phase 3: Advanced connection latency tracking
   */
  private trackConnectionLatency(latency: number): void {
    this.latencyHistory.push(latency);
    if (this.latencyHistory.length > this.maxLatencyHistory) {
      this.latencyHistory.shift();
    }
    
    // Update average latency
    this.connectionQualityMetrics.averageLatency = 
      this.latencyHistory.reduce((sum, l) => sum + l, 0) / this.latencyHistory.length;
    
    // Update pool metrics
    this.poolMetrics.connectionLatencyMs = this.connectionQualityMetrics.averageLatency;
    
    // Update performance score (lower latency = higher score)
    const maxAcceptableLatency = 1000; // 1 second
    const latencyScore = Math.max(0, 100 - (this.connectionQualityMetrics.averageLatency / maxAcceptableLatency) * 50);
    const errorScore = Math.max(0, 100 - (this.connectionQualityMetrics.errorRate * 100));
    this.connectionQualityMetrics.performanceScore = (latencyScore + errorScore) / 2;
  }

  /**
   * Phase 3: Update resource usage metrics
   */
  private updateResourceUsageMetrics(): void {
    const memoryUsage = process.memoryUsage();
    this.resourceUsageMetrics.memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;
    this.resourceUsageMetrics.connectionPoolSize = this.poolMetrics.activeConnections;
    this.resourceUsageMetrics.queueDepth = this.poolMetrics.queuedRequests;
    
    // Calculate resource pressure
    const memoryPressure = memoryUsage.heapUsed / memoryUsage.heapTotal;
    const queuePressure = this.poolMetrics.queuedRequests / this.maxQueueSize;
    const overallPressure = Math.max(memoryPressure, queuePressure);
    
    if (overallPressure > 0.9) {
      this.resourceUsageMetrics.resourcePressure = 'critical';
    } else if (overallPressure > 0.7) {
      this.resourceUsageMetrics.resourcePressure = 'high';
    } else if (overallPressure > 0.5) {
      this.resourceUsageMetrics.resourcePressure = 'medium';
    } else {
      this.resourceUsageMetrics.resourcePressure = 'low';
    }
    
    // Update pool utilization
    const maxConnections = this.getOptimizedPoolConfig().max || 20;
    this.poolMetrics.poolUtilization = (this.poolMetrics.activeConnections / maxConnections) * 100;
  }

  /**
   * Phase 3: Intelligent resource cleanup
   */
  private async performResourceCleanup(): Promise<void> {
    const now = Date.now();
    if (now - this.lastResourceCleanup < this.resourceCleanupInterval) {
      return; // Too soon for cleanup
    }
    
    this.lastResourceCleanup = now;
    this.poolMetrics.resourceCleanups++;
    
    logger.debug('[DB] Performing intelligent resource cleanup');
    
    try {
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      // Clean up old latency history if it's getting too large
      if (this.latencyHistory.length > this.maxLatencyHistory * 1.5) {
        this.latencyHistory.splice(0, this.latencyHistory.length - this.maxLatencyHistory);
      }
      
      // Reset error rate if we've had good performance recently
      if (this.connectionQualityMetrics.consecutiveFailures === 0 && 
          this.connectionQualityMetrics.errorRate > 0) {
        this.connectionQualityMetrics.errorRate *= 0.9; // Gradually improve error rate
      }
      
      logger.debug('[DB] Resource cleanup completed successfully');
    } catch (error) {
      logger.warn('[DB] Resource cleanup encountered error:', error);
    }
  }

  /**
   * Phase 3: Adaptive pool sizing based on load and memory pressure
   */
  private getAdaptivePoolConfig(): any {
    const baseConfig = this.getOptimizedPoolConfig();
    const memoryUsage = process.memoryUsage();
    const memoryPressure = memoryUsage.heapUsed / memoryUsage.heapTotal;
    const queuePressure = this.poolMetrics.queuedRequests / this.maxQueueSize;
    
    // Adaptive sizing based on current load
    let adaptiveMax = baseConfig.max;
    let adaptiveIdle = baseConfig.idle_timeout;
    
    // If queue is building up, increase pool size (up to a limit)
    if (queuePressure > 0.5 && memoryPressure < 0.7) {
      adaptiveMax = Math.min(baseConfig.max * 1.5, 30);
      this.poolMetrics.poolResizes++;
      logger.debug(`[DB] Increasing pool size to ${adaptiveMax} due to queue pressure`);
    }
    
    // If memory pressure is high, reduce pool size and idle timeout
    if (memoryPressure > 0.8) {
      adaptiveMax = Math.max(Math.floor(baseConfig.max * 0.7), 5);
      adaptiveIdle = Math.floor(baseConfig.idle_timeout * 0.5);
      this.poolMetrics.memoryPressureAdjustments++;
      logger.debug(`[DB] Reducing pool size to ${adaptiveMax} due to memory pressure`);
    }
    
    return {
      ...baseConfig,
      max: adaptiveMax,
      idle_timeout: adaptiveIdle,
    };
  }

  /**
   * Phase 3: Get comprehensive connection and resource metrics
   */
  getAdvancedMetrics(): {
    connectionQuality: ConnectionQualityMetrics;
    resourceUsage: ResourceUsageMetrics;
    poolMetrics: PoolMetrics;
  } {
    this.updateResourceUsageMetrics();
    
    return {
      connectionQuality: { ...this.connectionQualityMetrics },
      resourceUsage: { ...this.resourceUsageMetrics },
      poolMetrics: { ...this.poolMetrics },
    };
  }

  /**
   * Force refresh pool configuration based on current memory pressure and performance
   */
  refreshPoolConfiguration(): void {
    // Update resource metrics first
    this.updateResourceUsageMetrics();
    
    // Perform cleanup if needed
    this.performResourceCleanup();
    
    // Get current memory usage
    const memoryUsage = process.memoryUsage();
    const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
    const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024;
    const memoryPressure = heapUsedMB / heapTotalMB;

    logger.debug(`[DB] Refreshing pool configuration - Memory pressure: ${(memoryPressure * 100).toFixed(1)}%, Performance score: ${this.connectionQualityMetrics.performanceScore.toFixed(1)}, Resource pressure: ${this.resourceUsageMetrics.resourcePressure}`);

    // Log current adaptive configuration
    const adaptiveConfig = this.getAdaptivePoolConfig();
    logger.info('[DB] Current adaptive pool config:', {
      max: adaptiveConfig.max,
      idle_timeout: adaptiveConfig.idle_timeout,
      connect_timeout: adaptiveConfig.connect_timeout,
      resourcePressure: this.resourceUsageMetrics.resourcePressure,
      performanceScore: this.connectionQualityMetrics.performanceScore.toFixed(1),
    });

    // If memory pressure is high, we should consider reducing pool size
    if (memoryPressure > 0.8) {
      logger.warn('[DB] High memory pressure detected, pool will be optimized on next connection');
    }
    
    // If performance is degrading, log warning
    if (this.connectionQualityMetrics.performanceScore < 70) {
      logger.warn(`[DB] Connection performance degraded (score: ${this.connectionQualityMetrics.performanceScore.toFixed(1)})`);
    }
    
    // If resource pressure is critical, trigger immediate cleanup
    if (this.resourceUsageMetrics.resourcePressure === 'critical') {
      logger.warn('[DB] Critical resource pressure detected, triggering immediate cleanup');
      this.performResourceCleanup();
    }
  }

  /**
   * Graceful shutdown for application termination
   */
  async shutdown(): Promise<void> {
    logger.info('[DB] Starting graceful database shutdown');

    try {
      // Cancel any pending connections
      this.connectionPromise = null;

      // Reject all queued connection requests
      this.processConnectionQueue(null, new Error('Database is shutting down'));

      // Close active pool
      await this.closePoolSafely();

      logger.info('[DB] Database shutdown completed');
    } catch (error) {
      logger.error('[DB] Error during database shutdown:', error);
    }
  }
}

// Global database pool manager instance
const poolManager = new DatabasePoolManager();

// Register database pool with memory manager for cleanup and monitoring
try {
  const { memoryManager } = require('./utils/memoryManager');
  
  // Register cleanup callback for database pool
  memoryManager.registerCleanupCallback('database-pool', async () => {
    logger.debug('[DB] Memory cleanup triggered for database pool');
    
    // Refresh pool configuration based on current memory pressure
    poolManager.refreshPoolConfiguration();
    
    // If memory pressure is high, we could potentially close idle connections
    // but postgres-js handles this automatically with our configuration
  });

  // Listen for memory pressure changes to adjust pool configuration
  memoryManager.on('pressureChange', (event: any) => {
    logger.info(`[DB] Memory pressure changed: ${event.previousLevel} → ${event.currentLevel}`);
    
    // Refresh pool configuration for next connection
    poolManager.refreshPoolConfiguration();
  });

  logger.info('[DB] Database pool integrated with memory manager');
} catch (error) {
  logger.debug('[DB] Memory manager not available during database initialization');
}

/**
 * Get database connection
 * This is the primary interface for getting database connections
 */
export async function getDbConnection(): Promise<postgres.Sql> {
  try {
    return await poolManager.getConnection();
  } catch (error) {
    logger.error('[DB] Failed to get database connection:', error);
    throw error;
  }
}

/**
 * Get Drizzle ORM instance
 * This is the primary interface for database operations
 */
export async function getDb(): Promise<ReturnType<typeof drizzle<typeof schema>>> {
  try {
    return await poolManager.getDrizzleInstance();
  } catch (error) {
    logger.error('[DB] Failed to get Drizzle instance:', error);

    // Attempt connection recovery on failure
    try {
      await poolManager.handleConnectionLoss();
      return await poolManager.getDrizzleInstance();
    } catch (recoveryError) {
      logger.error('[DB] Database recovery failed:', recoveryError);
      throw new Error(
        `Database connection failed: ${(error as Error).message}. Recovery failed: ${(recoveryError as Error).message}`
      );
    }
  }
}

/**
 * Handle connection loss (for external error handlers)
 */
export async function handleConnectionLoss(): Promise<void> {
  return poolManager.handleConnectionLoss();
}

/**
 * Get database pool statistics
 */
export function getPoolStats() {
  return poolManager.getPoolStats();
}

/**
 * Reset pool metrics for monitoring
 */
export function resetPoolMetrics(): void {
  return poolManager.resetPoolMetrics();
}

/**
 * Refresh pool configuration based on current memory pressure
 */
export function refreshPoolConfiguration(): void {
  return poolManager.refreshPoolConfiguration();
}

/**
 * Phase 3: Get advanced connection and resource metrics
 */
export function getAdvancedMetrics(): {
  connectionQuality: ConnectionQualityMetrics;
  resourceUsage: ResourceUsageMetrics;
  poolMetrics: PoolMetrics;
} {
  return poolManager.getAdvancedMetrics();
}

/**
 * Graceful shutdown function
 */
export async function closeDatabase(): Promise<void> {
  return poolManager.shutdown();
}

logger.info('[DB] Database module initialized with connection pool management');

/**
 * Test database connection
 * Used by consolidated routes for health checks
 */
export async function testConnection(): Promise<void> {
  try {
    // Check pool status first
    const poolStats = poolManager.getPoolStats();
    
    // If the pool is closing, don't attempt connection test
    if (poolStats.isClosing) {
      throw new Error('Database pool is closing');
    }

    const client = await getDbConnection();
    
    // Add timeout protection for the health check (use same timeout as internal health checks)
    const timeoutPromise = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('Database connection test timeout')), 10000)
    );

    const healthCheckPromise = client`SELECT 1 as health_check`;

    await Promise.race([healthCheckPromise, timeoutPromise]);
    logger.debug('[DB] Connection test passed');
  } catch (error) {
    // Handle specific connection errors
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorCode = (error as any)?.code;

    // Handle CONNECTION_DESTROYED and similar connection errors gracefully
    if (
      errorCode === 'CONNECTION_DESTROYED' ||
      errorCode === 'ECONNRESET' ||
      errorCode === 'ENOTFOUND' ||
      errorMessage.includes('CONNECTION_DESTROYED') ||
      errorMessage.includes('connection terminated') ||
      errorMessage.includes('server closed the connection') ||
      errorMessage.includes('Database pool is closing')
    ) {
      logger.warn('[DB] Connection test failed due to connection loss:', {
        message: errorMessage,
        code: errorCode,
      });
      throw new Error('Database connection unavailable');
    }

    logger.error('[DB] Connection test failed:', {
      message: errorMessage,
      code: errorCode,
      type: typeof error,
    });
    throw new Error(`Database connection test failed: ${errorMessage}`);
  }
}
