/**
 * CSRF Protection Middleware
 *
 * This module configures and provides CSRF protection using the `csrf-sync` package.
 * It must be initialized after the session middleware.
 */

import { csrfSync } from 'csrf-sync';
import type { Express, NextFunction, Request, Response } from 'express';

const { generateToken, csrfSynchronisedProtection } = csrfSync({
  // The function that returns the token submitted by the user.
  // We read it from the `x-csrf-token` header.
  getTokenFromRequest: (req: Request) => req.headers['x-csrf-token'] as string,

  // A function that returns a boolean value to determine whether to skip CSRF protection.
  skipCsrfProtection: (req: Request) => {
    const excludedPaths = ['/api/auth/firebase/verify', '/api/auth/register'];

    // The dev-only test endpoint should bypass CSRF only when NOT running in production.
    if (process.env.NODE_ENV !== 'production') {
      excludedPaths.push('/api/auth/test-post');
    }

    // Skip protection for specific POST requests. `csrf-sync` already ignores GET, HEAD, OPTIONS.
    return req.method === 'POST' && excludedPaths.includes(req.path);
  },
});

export function initializeCsrfProtection(app: Express): void {
  // 1. Endpoint for the client to fetch a CSRF token.
  app.get('/api/auth/csrf', (req, res) => {
    // The `generateToken` function will create a new token if one doesn't exist on the session,
    // or return the existing one.
    const token = generateToken(req);
    res.json({ csrfToken: token });
  });

  // 2. Apply the CSRF protection middleware to all subsequent routes.
  app.use(csrfSynchronisedProtection);

  // 3. Custom error handler for CSRF validation errors.
  // This must be the last piece of middleware added.
  app.use((err: any, _req: Request, res: Response, next: NextFunction) => {
    if (err.code === 'EBADCSRFTOKEN') {
      res.status(403).json({
        error: 'CSRF token validation failed. Please refresh and try again.',
        code: 'CSRF_ERROR',
      });
    } else {
      next(err);
    }
  });
}
