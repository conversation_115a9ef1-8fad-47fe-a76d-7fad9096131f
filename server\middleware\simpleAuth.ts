/**
 * Simplified Authentication Middleware
 *
 * This middleware provides a single, reliable authentication pattern that:
 * 1. Checks session authentication first (primary method)
 * 2. Falls back to Firebase token authentication if session is missing
 * 3. Restores session when Firebase auth succeeds
 * 4. Avoids complex token refresh logic that causes session corruption
 */

import type { User } from '@shared/schema';
import type { NextFunction, Request, Response } from 'express';
import { verifyFirebaseToken } from '../auth/firebase';
import logger, { updateLoggerContext } from '../lib/logger';
import { storage } from '../storage';

// Extend Express Request and Session to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
      authMethod?: 'session' | 'firebase';
    }
  }
}

export interface AuthOptions {
  required?: boolean;
}

/**
 * Atomic Authentication Operations
 * Ensures sequential execution to prevent race conditions within a single request
 */
class AtomicAuthenticator {
  /**
   * Perform session-based authentication
   */
  async authenticateViaSession(req: Request): Promise<{ user: User | null; method?: string }> {
    try {
      // Primary: Check session authentication
      if (req.session?.userId) {
        const sessionUser = await storage.getUser(req.session.userId);
        if (sessionUser) {
          logger.debug('User authenticated via session.userId', { userId: sessionUser.id });
          return { user: sessionUser, method: 'session' };
        }
      }

      return { user: null };
    } catch (error) {
      logger.error('Error during session authentication, this may indicate a database issue.', {
        error: error instanceof Error ? error.message : String(error),
        sessionId: req.sessionID,
      });
      // Re-throw the error to be caught by the main error handler
      throw error;
    }
  }

  /**
   * Perform Firebase-based authentication
   */
  async authenticateViaFirebase(
    req: Request
  ): Promise<{ user: User | null; error?: { status: number; message: string; code: string } }> {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader?.startsWith('Bearer ')) {
        return { user: null };
      }

      const idToken = authHeader.split('Bearer ')[1];
      const decodedToken = await verifyFirebaseToken(idToken);
      const firebaseUser = await storage.getUserByFirebaseUid(decodedToken.uid);

      if (firebaseUser) {
        // Atomic session restoration
        await this.restoreSessionFromFirebase(req, firebaseUser);
        logger.debug('User authenticated via Firebase', { userId: firebaseUser.id });
        return { user: firebaseUser };
      }

      return { user: null };
    } catch (error: any) {
      const errorMessage = error.message.toLowerCase();
      // Check for specific token errors that should result in a 401
      if (
        errorMessage.includes('invalid') ||
        errorMessage.includes('expired') ||
        errorMessage.includes('signature')
      ) {
        logger.warn('Invalid Firebase token received', { error: error.message });
        return {
          user: null,
          error: {
            status: 401,
            message: 'Invalid or expired Firebase token.',
            code: 'INVALID_TOKEN',
          },
        };
      }

      // For other errors, log them but don't halt the request with a 401
      logger.error('Error during Firebase authentication', { error: error.message });
      return { user: null };
    }
  }

  /**
   * Atomic session state synchronization
   */
  private async syncSessionState(req: Request, user: User): Promise<void> {
    if (req.session && req.session.userId !== user.id) {
      req.session.userId = user.id;
      logger.debug('Session synchronized with authenticated user', { userId: user.id });
    }
  }

  /**
   * Atomic session restoration from Firebase.
   * Ensures the session is always consistent with the authenticated user.
   */
  private async restoreSessionFromFirebase(req: Request, user: User): Promise<void> {
    if (req.session) {
      req.session.userId = user.id;
      logger.debug('Session restored/synchronized from Firebase auth', { userId: user.id });
    }
  }
}

// Global atomic authenticator instance
const atomicAuth = new AtomicAuthenticator();

/**
 * Simplified and reliable authentication middleware.
 * It sequentially attempts session and then Firebase token authentication.
 */
export function simpleAuth(options: AuthOptions = {}) {
  const { required = true } = options;

  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      let user: User | null = null;
      let authMethod: 'session' | 'firebase' | undefined;

      // Step 1: Try session-based authentication
      const sessionResult = await atomicAuth.authenticateViaSession(req);
      if (sessionResult.user) {
        user = sessionResult.user;
        authMethod = 'session';
      }

      // Step 2: Try Firebase authentication (only if session failed)
      if (!user) {
        const firebaseResult = await atomicAuth.authenticateViaFirebase(req);
        if (firebaseResult.error) {
          // If Firebase auth returned a specific error (e.g., invalid token), stop immediately.
          res.status(firebaseResult.error.status).json({
            success: false,
            message: firebaseResult.error.message,
            code: firebaseResult.error.code,
          });
          return;
        }

        if (firebaseResult.user) {
          user = firebaseResult.user;
          authMethod = 'firebase';
        }
      }

      // Set authenticated user and method on request
      if (user) {
        req.user = user;
        req.authMethod = authMethod;
        updateLoggerContext(user.id);

        logger.debug('Authentication successful', {
          userId: user.id,
          method: authMethod,
          path: req.path,
        });
      } else {
        updateLoggerContext('anonymous');
      }

      // Handle authentication requirements
      if (required && !user) {
        logger.debug('Authentication required but no valid user found', {
          hasSession: !!req.session?.userId,
          hasAuthHeader: !!req.headers.authorization,
          path: req.path,
        });

        res.status(401).json({
          success: false,
          message: 'Authentication required',
          code: 'AUTHENTICATION_REQUIRED',
        });
        return;
      }

      // Proceed to next middleware
      next();
    } catch (error) {
      logger.error('Authentication middleware error', error, {
        path: req.path,
        method: req.method,
      });

      if (required) {
        res.status(500).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTHENTICATION_ERROR',
        });
        return;
      }

      // Optional auth - continue without user
      next();
    }
  };
}

/**
 * Require authentication (shorthand)
 */
export const requireAuth = simpleAuth({ required: true });

/**
 * Optional authentication (shorthand)
 */
export const optionalAuth = simpleAuth({ required: false });
