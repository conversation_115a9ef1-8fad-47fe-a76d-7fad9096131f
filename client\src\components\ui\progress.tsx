'use client';

import * as ProgressPrimitive from '@radix-ui/react-progress';
import * as React from 'react';

import { cn } from '@/lib/utils';

// A utility hook to merge multiple refs into one, ensuring that both the forwarded
// ref and our internal ref are handled correctly.
function useComposedRefs<T>(...refs: (React.Ref<T> | undefined)[]) {
  return React.useCallback((node: T) => {
    for (const ref of refs) {
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref != null) {
        (ref as React.MutableRefObject<T>).current = node;
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, refs);
}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>
>(({ className, value, ...props }, ref) => {
  const internalRef = React.useRef<HTMLDivElement>(null);
  const composedRef = useComposedRefs(ref, internalRef);

  // We use an effect to programmatically set the CSS custom property.
  // This avoids rendering the inline `style` attribute in JSX, which is
  // blocked by the Content Security Policy.
  React.useEffect(() => {
    internalRef.current?.style.setProperty('--progress-value', `${value || 0}%`);
  }, [value]);

  return (
    <ProgressPrimitive.Root
      ref={composedRef}
      className={cn('relative h-2 w-full overflow-hidden rounded-full bg-secondary', className)}
      {...props}
    >
      <ProgressPrimitive.Indicator className="h-full w-[var(--progress-value)] flex-1 bg-primary transition-all" />
    </ProgressPrimitive.Root>
  );
});
Progress.displayName = ProgressPrimitive.Root.displayName;

export { Progress };
