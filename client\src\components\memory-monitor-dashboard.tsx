/**
 * Memory Monitor Dashboard
 *
 * A component for visualizing and monitoring memory usage in the application.
 * Displays real-time memory metrics, historical usage patterns, and recommendations.
 */

import { formatDistanceToNow } from 'date-fns';
import { Loader2 } from 'lucide-react';
import type React from 'react';
import { useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartTabs } from './memory-monitor/ChartTabs';
import {
  MemoryMonitorProvider,
  useMemoryMonitor,
} from './memory-monitor/context/MemoryMonitorContext';
import { StatsGrid } from './memory-monitor/StatsGrid';

function MemoryMonitorDashboardContent() {
  const { memoryStatsQuery, autoRefresh, toggleAutoRefresh, refreshAll } = useMemoryMonitor();

  const isLoading = memoryStatsQuery.isFetching && !memoryStatsQuery.isRefetching;

  // Format last update time
  const lastUpdateTime = useMemo(() => {
    if (!memoryStatsQuery.data?.timestamp) return 'Never';
    return formatDistanceToNow(new Date(memoryStatsQuery.data.timestamp), {
      addSuffix: true,
    });
  }, [memoryStatsQuery.data?.timestamp]);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Memory Monitor</CardTitle>
          <p className="text-sm text-muted-foreground">
            Real-time server memory and resource monitoring. Last updated: {lastUpdateTime}
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={toggleAutoRefresh}
            className={autoRefresh ? 'bg-green-100 dark:bg-green-900' : ''}
          >
            {autoRefresh ? 'Auto-refresh On' : 'Auto-refresh Off'}
          </Button>
          <Button onClick={refreshAll} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Refreshing...
              </>
            ) : (
              'Refresh Now'
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <StatsGrid />
        <ChartTabs />
      </CardContent>
    </Card>
  );
}

export const MemoryMonitorDashboard: React.FC = () => {
  return (
    <MemoryMonitorProvider>
      <MemoryMonitorDashboardContent />
    </MemoryMonitorProvider>
  );
};

export default MemoryMonitorDashboard;
