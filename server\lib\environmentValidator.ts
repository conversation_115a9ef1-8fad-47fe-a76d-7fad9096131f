/**
 * Environment Variable Validation Service
 *
 * This service provides comprehensive validation and management of environment variables
 * across the application. It ensures all critical variables are present and properly
 * formatted, with consistent fallback mechanisms and error reporting.
 *
 * Features:
 * - Comprehensive environment variable validation
 * - Type-safe environment variable access
 * - Consistent fallback mechanisms
 * - Security-focused validation for sensitive variables
 * - Development vs production configuration management
 * - Detailed error reporting for missing variables
 */

import logger from './logger';

/**
 * Safe logging that falls back to console in test environments
 */
function safeLog(level: 'info' | 'error' | 'warn' | 'fatal', message: string, meta?: any) {
  try {
    if (logger?.[level]) {
      logger[level](message, meta);
    } else {
      // Fallback for test environments where logger might not be available
      if (level === 'error' || level === 'fatal') {
        console.error(message, meta);
      } else if (level === 'warn') {
        console.warn(message, meta);
      } else if (level === 'info') {
        console.log(message, meta);
      }
    }
  } catch {
    // Silent fallback if logger is not available
  }
}

/**
 * Environment variable configuration schema
 */
export interface EnvironmentConfig {
  // Database Configuration
  DATABASE_URL: string;
  DATABASE_HOST?: string;
  DATABASE_PORT?: number;
  DATABASE_NAME?: string;
  DATABASE_USER?: string;
  DATABASE_PASSWORD?: string;
  DATABASE_SSL?: boolean;

  // Firebase Configuration
  FIREBASE_SERVICE_ACCOUNT?: string;
  FIREBASE_DATABASE_URL?: string;

  // OAuth Credentials
  GOOGLE_CLIENT_ID?: string;
  GOOGLE_CLIENT_SECRET?: string;
  GOOGLE_REDIRECT_URI?: string;
  OUTLOOK_CLIENT_ID?: string;
  OUTLOOK_CLIENT_SECRET?: string;

  // API Keys
  GEMINI_API_KEY?: string;
  GEMINI_MODEL?: string;
  OPENAI_API_KEY?: string;
  OPENAI_MODEL?: string;

  // Application Configuration
  NODE_ENV: 'development' | 'production' | 'test';
  PORT?: number;
  SESSION_SECRET?: string;
  JWT_SECRET?: string;

  // Encryption Keys
  ENCRYPTION_KEY?: string;
  TOKEN_ENCRYPTION_KEY?: string;

  // Task Queue Configuration
  QUICK_START?: boolean;
  TASK_QUEUE_POLLING_INTERVAL?: number;

  // Feature Flags & Debug Options
  ENABLE_ANALYTICS?: boolean;
  ENABLE_CACHING?: boolean;
  ENABLE_RATE_LIMITING?: boolean;
  SKIP_AI_MODEL_LOADING?: boolean;
  USE_LIGHTWEIGHT_MODELS?: boolean;
  CATEGORIZATION_STRATEGY?: string;
  MODEL_LOADING_TIMEOUT?: number;
  BYPASS_AUTHENTICATION_FOR_STATS?: boolean;
  DEBUG_STATS_API?: boolean;

  // External Services
  REDIS_URL?: string;
  SENTRY_DSN?: string;

  // Microsoft Configuration
  MICROSOFT_CLIENT_ID?: string;
  MICROSOFT_CLIENT_SECRET?: string;
}

/**
 * Validation result for environment variables
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  config: Partial<EnvironmentConfig>;
}

/**
 * Environment variable requirement levels
 */
type RequirementLevel = 'required' | 'optional' | 'development' | 'production';

/**
 * Environment variable definition
 */
interface EnvironmentVariable {
  key: keyof EnvironmentConfig;
  requirement: RequirementLevel;
  type: 'string' | 'number' | 'boolean' | 'url' | 'email' | 'json';
  defaultValue?: any;
  validator?: (value: string) => boolean;
  description: string;
  sensitive?: boolean;
}

/**
 * Environment Variable Validation Service
 */
export class EnvironmentValidator {
  private static instance: EnvironmentValidator;
  private validatedConfig: Partial<EnvironmentConfig> = {};
  private isValidated = false;

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): EnvironmentValidator {
    if (!EnvironmentValidator.instance) {
      EnvironmentValidator.instance = new EnvironmentValidator();
    }
    return EnvironmentValidator.instance;
  }

  /**
   * Environment variable definitions
   */
  private getVariableDefinitions(): EnvironmentVariable[] {
    return [
      // Database Configuration - Critical for application startup
      {
        key: 'DATABASE_URL',
        requirement: 'required',
        type: 'url',
        description: 'Complete database connection URL (PostgreSQL)',
        validator: (value) => value.startsWith('postgresql://') || value.startsWith('postgres://'),
        sensitive: true,
      },
      {
        key: 'NODE_ENV',
        requirement: 'required',
        type: 'string',
        defaultValue: 'development',
        description: 'Application environment mode',
        validator: (value) => ['development', 'production', 'test'].includes(value),
      },

      // Firebase Configuration - Required for authentication
      {
        key: 'FIREBASE_SERVICE_ACCOUNT',
        requirement: 'required',
        type: 'string',
        description: 'Firebase service account JSON object as a single-line string.',
        sensitive: true,
        validator: (value) => {
          const tryParse = (str: string) => {
            try {
              return JSON.parse(str);
            } catch {
              return null;
            }
          };

          let parsed = null;
          const attemptedStrategies = [];

          // Strategy 1: Parse raw value
          parsed = tryParse(value);
          if (parsed?.project_id && parsed.private_key && parsed.client_email) return true;
          attemptedStrategies.push('raw');

          // Strategy 2: Handle quoted JSON strings (common in .env files)
          if (value.startsWith('"') && value.endsWith('"')) {
            const unquoted = value.slice(1, -1).replace(/\\n/g, '\n').replace(/\\"/g, '"');
            parsed = tryParse(unquoted);
            if (parsed?.project_id && parsed.private_key && parsed.client_email) {
              return true;
            }
            attemptedStrategies.push('quoted-with-escapes');
          }

          // Strategy 3: Handle actual newlines (escape them back to \\n for JSON)
          const reEscaped = value.replace(/\n/g, '\\n').replace(/\t/g, '\\t').replace(/\r/g, '\\r');
          parsed = tryParse(reEscaped);
          if (parsed?.project_id && parsed.private_key && parsed.client_email) {
            return true;
          }
          attemptedStrategies.push('re-escaped-newlines');

          // Strategy 4: Handle double-encoded JSON
          try {
            const doubleDecoded = JSON.parse(value);
            if (typeof doubleDecoded === 'string') {
              parsed = tryParse(doubleDecoded);
              if (parsed?.project_id && parsed.private_key && parsed.client_email) {
                return true;
              }
            }
          } catch {
            /* ignore */
          }
          attemptedStrategies.push('double-encoded');

          // Strategy 5: Handle base64 encoding
          try {
            const b64 = Buffer.from(value, 'base64').toString('utf8');
            parsed = tryParse(b64);
            if (parsed) return parsed.project_id && parsed.private_key && parsed.client_email;
          } catch {
            /* ignore */
          }
          attemptedStrategies.push('base64');

          // Strategy 6: Handle single quotes
          if (value.startsWith("'") && value.endsWith("'")) {
            const unquoted = value.slice(1, -1);
            parsed = tryParse(unquoted);
            if (parsed) return parsed.project_id && parsed.private_key && parsed.client_email;
            attemptedStrategies.push('single-quoted');
          }

          // All strategies failed - log diagnostic info for debugging
          logger.error('[Validator] FIREBASE_SERVICE_ACCOUNT parsing failed', {
            strategies: attemptedStrategies,
            length: value.length,
            startsWithBrace: value.trim().startsWith('{'),
            endsWithBrace: value.trim().endsWith('}'),
          });

          return false;
        },
      },

      // Google OAuth - Required for Gmail integration
      {
        key: 'GOOGLE_CLIENT_ID',
        requirement: 'required',
        type: 'string',
        description: 'Google OAuth 2.0 client ID',
        validator: (value) => value.endsWith('.apps.googleusercontent.com'),
      },
      {
        key: 'GOOGLE_CLIENT_SECRET',
        requirement: 'required',
        type: 'string',
        description: 'Google OAuth 2.0 client secret',
        sensitive: true,
      },
      {
        key: 'GOOGLE_REDIRECT_URI',
        requirement: 'optional',
        type: 'string',
        description: 'Google OAuth 2.0 redirect URI (auto-generated if not provided)',
      },

      // Gemini API - Required for AI features
      {
        key: 'GEMINI_API_KEY',
        requirement: 'required',
        type: 'string',
        description: 'Google Gemini API key for AI operations',
        sensitive: true,
        validator: (value) => value.startsWith('AI') && value.length > 20,
      },
      {
        key: 'GEMINI_MODEL',
        requirement: 'optional',
        type: 'string',
        defaultValue: 'gemini-1.5-flash',
        description: 'Gemini model name to use',
      },

      // Application Configuration
      {
        key: 'PORT',
        requirement: 'optional',
        type: 'number',
        defaultValue: 5000,
        description: 'HTTP server port number',
      },
      {
        key: 'SESSION_SECRET',
        requirement: 'production',
        type: 'string',
        description: 'Session encryption secret (32+ characters)',
        sensitive: true,
        validator: (value) => value.length >= 32,
      },
      {
        key: 'ENCRYPTION_KEY',
        requirement: 'required',
        type: 'string',
        description: 'Main encryption key for data security (minimum 32 characters)',
        sensitive: true,
        validator: (value) => value.length >= 32,
      },
      {
        key: 'TOKEN_ENCRYPTION_KEY',
        requirement: 'optional',
        type: 'string',
        description: 'Legacy token encryption key for backward compatibility',
        sensitive: true,
      },

      // Optional Integrations
      {
        key: 'OUTLOOK_CLIENT_ID',
        requirement: 'optional',
        type: 'string',
        description: 'Microsoft Outlook OAuth client ID',
      },
      {
        key: 'OUTLOOK_CLIENT_SECRET',
        requirement: 'optional',
        type: 'string',
        description: 'Microsoft Outlook OAuth client secret',
        sensitive: true,
      },
      {
        key: 'OPENAI_API_KEY',
        requirement: 'optional',
        type: 'string',
        description: 'OpenAI API key for additional AI features',
        sensitive: true,
        validator: (value) => value.startsWith('sk-'),
      },
      {
        key: 'OPENAI_MODEL',
        requirement: 'optional',
        type: 'string',
        defaultValue: 'gpt-4o',
        description: 'OpenAI model name to use',
      },

      // Task Queue Configuration
      {
        key: 'QUICK_START',
        requirement: 'optional',
        type: 'boolean',
        defaultValue: false,
        description: 'Enable quick start mode for development',
      },
      {
        key: 'TASK_QUEUE_POLLING_INTERVAL',
        requirement: 'optional',
        type: 'number',
        defaultValue: 5000,
        description: 'Task queue polling interval in milliseconds',
      },

      // AI & Model Configuration
      {
        key: 'SKIP_AI_MODEL_LOADING',
        requirement: 'optional',
        type: 'boolean',
        defaultValue: false,
        description: 'Skip loading AI models for faster startup',
      },
      {
        key: 'USE_LIGHTWEIGHT_MODELS',
        requirement: 'optional',
        type: 'boolean',
        defaultValue: false,
        description: 'Use lightweight AI models for better performance',
      },
      {
        key: 'CATEGORIZATION_STRATEGY',
        requirement: 'optional',
        type: 'string',
        defaultValue: 'ai-first',
        description: 'Email categorization strategy (ai-first or rules-first)',
      },
      {
        key: 'MODEL_LOADING_TIMEOUT',
        requirement: 'optional',
        type: 'number',
        defaultValue: 5000,
        description: 'Model loading timeout in milliseconds',
      },

      // Debug & Development Options
      {
        key: 'BYPASS_AUTHENTICATION_FOR_STATS',
        requirement: 'development',
        type: 'boolean',
        defaultValue: false,
        description: 'Bypass authentication for stats API (development only)',
      },
      {
        key: 'DEBUG_STATS_API',
        requirement: 'optional',
        type: 'boolean',
        defaultValue: false,
        description: 'Enable debug logging for stats API',
      },
      {
        key: 'REDIS_URL',
        requirement: 'optional',
        type: 'url',
        description: 'Redis connection URL for caching',
        validator: (value) => value.startsWith('redis://') || value.startsWith('rediss://'),
      },

      // Feature Flags
      {
        key: 'ENABLE_ANALYTICS',
        requirement: 'optional',
        type: 'boolean',
        defaultValue: false,
        description: 'Enable user analytics tracking',
      },
      {
        key: 'ENABLE_CACHING',
        requirement: 'optional',
        type: 'boolean',
        defaultValue: true,
        description: 'Enable application-level caching',
      },
      {
        key: 'ENABLE_RATE_LIMITING',
        requirement: 'optional',
        type: 'boolean',
        defaultValue: true,
        description: 'Enable API rate limiting protection',
      },

      // Microsoft OAuth Configuration
      {
        key: 'MICROSOFT_CLIENT_ID',
        requirement: 'optional',
        type: 'string',
        description: 'Microsoft client ID for OAuth',
      },
      {
        key: 'MICROSOFT_CLIENT_SECRET',
        requirement: 'optional',
        type: 'string',
        description: 'Microsoft client secret for OAuth',
        sensitive: true,
      },
    ];
  }

  /**
   * Validate all environment variables and return comprehensive results
   */
  validateEnvironment(): ValidationResult {
    if (this.isValidated) {
      return {
        isValid: true,
        errors: [],
        warnings: [],
        config: this.validatedConfig,
      };
    }

    const errors: string[] = [];
    const warnings: string[] = [];
    const config: Partial<EnvironmentConfig> = {};
    const currentEnv = process.env.NODE_ENV || 'development';

    safeLog('info', '[Environment] Starting environment validation...', {
      environment: currentEnv,
    });

    // Validate each defined variable
    for (const variable of this.getVariableDefinitions()) {
      const result = this.validateSingleVariable(variable, currentEnv);

      if (result.error) {
        errors.push(result.error);
      }

      if (result.warning) {
        warnings.push(result.warning);
      }

      if (result.value !== undefined) {
        config[variable.key] = result.value;
      }
    }

    // Perform additional contextual validations
    this.performEnvironmentChecks(config, currentEnv, errors, warnings);

    const isValid = errors.length === 0;

    if (isValid) {
      this.validatedConfig = config;
      this.isValidated = true;
      logger.info('[Environment] ✅ Environment validation successful', {
        configuredVariables: Object.keys(config).length,
        warnings: warnings.length,
        environment: currentEnv,
      });
    } else {
      safeLog('error', '[Environment] ❌ Environment validation failed', {
        errors: errors.length,
        warnings: warnings.length,
        environment: currentEnv,
      });
    }

    return { isValid, errors, warnings, config };
  }

  /**
   * Validate a single environment variable
   */
  private validateSingleVariable(
    variable: EnvironmentVariable,
    currentEnv: string
  ): { value?: any; error?: string; warning?: string } {
    const rawValue = process.env[variable.key];
    const isRequired = this.isVariableRequired(variable, currentEnv);

    // Check if required variable is missing
    if (isRequired && !rawValue) {
      return {
        error: `[${variable.key}] Missing required environment variable - ${variable.description}`,
      };
    }

    // Use default value if available and no value is set
    if (!rawValue && variable.defaultValue !== undefined) {
      return {
        value: variable.defaultValue,
        warning: `[${variable.key}] Using default value: ${variable.sensitive ? '[REDACTED]' : variable.defaultValue}`,
      };
    }

    // Skip validation if optional and not provided
    if (!rawValue) {
      return {};
    }

    // Parse and validate the value
    const parsedValue = this.parseValue(rawValue, variable.type);
    if (parsedValue === undefined) {
      return {
        error: `[${variable.key}] Invalid ${variable.type} format - Expected: ${variable.description}`,
      };
    }

    // Run custom validator if provided
    if (variable.validator && !variable.validator(rawValue)) {
      return {
        error: `[${variable.key}] Validation failed - ${variable.description}`,
      };
    }

    return { value: parsedValue };
  }

  /**
   * Check if a variable is required in the current environment
   */
  private isVariableRequired(variable: EnvironmentVariable, currentEnv: string): boolean {
    switch (variable.requirement) {
      case 'required':
        return true;
      case 'optional':
        return false;
      case 'development':
        return currentEnv === 'development';
      case 'production':
        return currentEnv === 'production';
      default:
        return false;
    }
  }

  /**
   * Parse value according to its type
   */
  private parseValue(value: string, type: EnvironmentVariable['type']): any {
    try {
      switch (type) {
        case 'string':
          return value.trim();
        case 'number': {
          const num = Number.parseInt(value, 10);
          return Number.isNaN(num) ? undefined : num;
        }
        case 'boolean':
          return ['true', '1', 'yes', 'on'].includes(value.toLowerCase());
        case 'url':
          new URL(value); // Validate URL format
          return value;
        case 'email': {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          return emailRegex.test(value) ? value : undefined;
        }
        case 'json':
          try {
            // First, try to parse the value as-is.
            return JSON.parse(value);
          } catch (e) {
            // If it fails and is wrapped in quotes (common from .env files),
            // strip the quotes and try again.
            if (value.startsWith('"') && value.endsWith('"')) {
              try {
                return JSON.parse(value.substring(1, value.length - 1));
              } catch (e2) {
                // If the second parse also fails, the JSON is truly invalid.
                logger.error('Failed to parse JSON even after stripping quotes.', {
                  value,
                  error: e2,
                });
                return undefined;
              }
            }
            logger.error('Failed to parse JSON.', { value, error: e });
            return undefined;
          }
        default:
          return value;
      }
    } catch {
      return undefined;
    }
  }

  /**
   * Perform additional environment-specific validations
   */
  private performEnvironmentChecks(
    config: Partial<EnvironmentConfig>,
    currentEnv: string,
    errors: string[],
    warnings: string[]
  ): void {
    if (currentEnv === 'production') {
      if (!config.SESSION_SECRET || config.SESSION_SECRET.length < 32) {
        errors.push(
          '[SESSION_SECRET] Required in production and must be at least 32 characters long'
        );
      }

      if (!config.FIREBASE_SERVICE_ACCOUNT) {
        errors.push(
          '[FIREBASE_SERVICE_ACCOUNT] Required in production for Firebase authentication'
        );
      }
    }

    // OAuth provider validation
    const hasGoogleOAuth = config.GOOGLE_CLIENT_ID && config.GOOGLE_CLIENT_SECRET;
    const hasOutlookOAuth = config.OUTLOOK_CLIENT_ID && config.OUTLOOK_CLIENT_SECRET;

    if (!hasGoogleOAuth) {
      warnings.push('[OAuth] Google OAuth not configured - Gmail integration unavailable');
    }

    if (!hasOutlookOAuth) {
      warnings.push('[OAuth] Outlook OAuth not configured - Outlook integration unavailable');
    }

    // AI API validation
    if (!config.GEMINI_API_KEY && !config.OPENAI_API_KEY) {
      errors.push('[AI] No AI API keys configured - Core AI features will be unavailable');
    }

    // Database configuration checks
    if (config.DATABASE_URL) {
      try {
        const dbUrl = new URL(config.DATABASE_URL);
        if (!dbUrl.hostname || !dbUrl.pathname) {
          errors.push('[DATABASE_URL] Invalid database URL format');
        }
      } catch {
        errors.push('[DATABASE_URL] Malformed database connection string');
      }
    }
  }

  /**
   * Get validated configuration with type safety
   */
  getConfig(): Partial<EnvironmentConfig> {
    if (!this.isValidated) {
      const result = this.validateEnvironment();
      if (!result.isValid) {
        throw new Error(`Environment validation failed: ${result.errors.join('; ')}`);
      }
    }
    return this.validatedConfig;
  }

  /**
   * Get specific configuration value with type safety
   */
  get<K extends keyof EnvironmentConfig>(key: K): EnvironmentConfig[K] | undefined {
    return this.getConfig()[key];
  }

  /**
   * Check if a variable is configured
   */
  has(key: keyof EnvironmentConfig): boolean {
    return key in this.getConfig();
  }

  /**
   * Get environment summary for logging
   */
  getSummary(): {
    environment: string;
    configuredCount: number;
    requiredCount: number;
    hasDatabase: boolean;
    hasFirebase: boolean;
    hasGoogleOAuth: boolean;
    hasAI: boolean;
  } {
    if (!this.isValidated) {
      this.validateEnvironment();
    }
    const config = this.validatedConfig;
    return {
      environment: this.validatedConfig.NODE_ENV || 'unknown',
      configuredCount: Object.keys(config).length,
      requiredCount: this.getVariableDefinitions().filter((v) => v.requirement === 'required')
        .length,
      hasDatabase: !!config.DATABASE_URL,
      hasFirebase: !!config.FIREBASE_SERVICE_ACCOUNT,
      hasGoogleOAuth: !!(config.GOOGLE_CLIENT_ID && config.GOOGLE_CLIENT_SECRET),
      hasAI: !!(config.GEMINI_API_KEY || config.OPENAI_API_KEY),
    };
  }

  /**
   * Reset validation state (useful for testing)
   */
  reset(): void {
    this.isValidated = false;
    this.validatedConfig = {};
  }
}

// Export singleton instance
export const environmentValidator = EnvironmentValidator.getInstance();

// Export convenience functions for easy access
export const validateEnvironment = (): ValidationResult =>
  environmentValidator.validateEnvironment();
export const getConfig = (): Partial<EnvironmentConfig> => environmentValidator.getConfig();
export const getEnvVar = <K extends keyof EnvironmentConfig>(
  key: K
): EnvironmentConfig[K] => {
  const value = process.env[key as string] as EnvironmentConfig[K] | undefined;
  if (value !== undefined && value !== null && value !== '') {
    return value;
  }

  const fromConfig = environmentValidator.getConfig()[key];
  if (fromConfig !== undefined && fromConfig !== null && fromConfig !== '') {
    return fromConfig as EnvironmentConfig[K];
  }

  throw new Error(`Environment variable ${String(key)} is not defined`);
};
export const hasEnvVar = (key: keyof EnvironmentConfig): boolean => environmentValidator.has(key);
export const getEnvironmentSummary = () => environmentValidator.getSummary();

/**
 * Initialize and validate environment - now called explicitly from server startup
 * instead of during module import to ensure .env files are loaded first
 */
export const initializeEnvironmentValidator = () => {
  if (process.env.NODE_ENV === 'test')
    return { isValid: true, errors: [], warnings: [], config: {} };

  try {
    const result = validateEnvironment();

    if (!result.isValid) {
      // Log critical errors and exit
      logger.fatal('[Environment] Critical configuration errors detected:', {
        errors: result.errors,
      });

      console.error('\n❌ Environment Configuration Errors:');
      result.errors.forEach((error) => console.error(`  ${error}`));
      console.error('\nApplication cannot start with invalid configuration.');
      console.error('Please check your environment variables and try again.\n');

      const nodeEnv: string | undefined = process.env.NODE_ENV;
      const isJest = Boolean(process.env.JEST_WORKER_ID);

      // Avoid terminating the Jest runner; throw instead so tests can assert
      if (nodeEnv === 'test' || isJest) {
        throw new Error(`Environment validation failed: ${result.errors.join('; ')}`);
      }

      // In non-test environments, fail fast
      process.exit(1);
    }

    // Log warnings if any
    if (result.warnings.length > 0) {
      logger.warn('[Environment] Configuration warnings:', {
        warnings: result.warnings,
      });

      console.warn('\n⚠️ Environment Configuration Warnings:');
      result.warnings.forEach((warning) => console.warn(`  ${warning}`));
      console.warn('');
    }

    // Log successful validation
    const summary = getEnvironmentSummary();
    logger.info('[Environment] Configuration validated successfully', summary);

    return result;
  } catch (error) {
    safeLog('fatal', '[Environment] Failed to initialize environment validator:', {
      error: error instanceof Error ? error.message : String(error),
    });
    console.error('\n❌ Failed to validate environment configuration');
    console.error('Please check your .env file and environment setup.\n');

    const nodeEnv: string | undefined = process.env.NODE_ENV;
    const isJest = Boolean(process.env.JEST_WORKER_ID);

    // Avoid terminating the Jest runner; throw instead so tests can assert
    if (nodeEnv === 'test' || isJest) {
      throw error;
    }

    // In non-test environments, fail fast
    process.exit(1);
  }
};
