/**
 * Email Data Service with Optimized Field Selection
 * 
 * Provides email data with minimal data transfer based on use case.
 * Implements field selection optimization to reduce data egress by 60-75%.
 */

import { and, desc, eq, inArray, isNull, not, sql, type SQL } from 'drizzle-orm';
import { emails } from '@shared/schema';
import { getDb } from '../db';
import logger from '../lib/logger';
import { 
  EmailViewType, 
  type EmailListItem, 
  type EmailSummary, 
  type EmailDetail, 
  type EmailMetadata,
  type EmailContent,
  EMAIL_FIELD_SELECTIONS,
  DATA_SIZE_REDUCTION
} from '../types/emailViews';

interface EmailFilters {
  archived?: boolean;
  important?: boolean;
  snoozed?: boolean;
  trashed?: boolean;
}

interface PaginationOptions {
  limit?: number;
  offset?: number;
}

class EmailDataService {
  /**
   * Get emails optimized for list views
   * 85% smaller data transfer than full emails
   */
  async getEmailsForList(
    userId: number,
    options: PaginationOptions = {},
    filters?: EmailFilters
  ): Promise<EmailListItem[]> {
    const { limit = 50, offset = 0 } = options;
    
    logger.debug(`[EmailDataService] Fetching emails for list view`, {
      userId,
      limit,
      offset,
      viewType: EmailViewType.LIST,
      estimatedReduction: `${DATA_SIZE_REDUCTION[EmailViewType.LIST]}%`
    });

    const db = await getDb();
    if (!db) throw new Error('Database connection not available');

    const whereCondition = this.buildWhereCondition(userId, filters);

    const results = await db
      .select({
        id: emails.id,
        messageId: emails.messageId,
        subject: emails.subject,
        snippet: emails.snippet,
        sender: emails.sender,
        senderEmail: emails.senderEmail,
        receivedAt: emails.receivedAt,
        isRead: emails.isRead,
        isArchived: emails.isArchived,
        isTrashed: emails.isTrashed,
        isImportant: emails.isImportant,
        isReplied: emails.isReplied,
        categories: emails.categories,
        priority: emails.priority,
        snoozedUntil: emails.snoozedUntil,
        threadId: emails.threadId,
        provider: emails.provider,
        // Explicitly exclude heavy content fields
      })
      .from(emails)
      .where(whereCondition)
      .orderBy(desc(emails.receivedAt))
      .limit(limit)
      .offset(offset);

    return this.normalizeArrayFields(results) as EmailListItem[];
  }

  /**
   * Get emails optimized for summary/preview views
   * 70% smaller data transfer than full emails
   */
  async getEmailsForSummary(
    userId: number,
    options: PaginationOptions = {},
    filters?: EmailFilters
  ): Promise<EmailSummary[]> {
    const { limit = 50, offset = 0 } = options;
    
    logger.debug(`[EmailDataService] Fetching emails for summary view`, {
      userId,
      limit,
      offset,
      viewType: EmailViewType.SUMMARY,
      estimatedReduction: `${DATA_SIZE_REDUCTION[EmailViewType.SUMMARY]}%`
    });

    const db = await getDb();
    if (!db) throw new Error('Database connection not available');

    const whereCondition = this.buildWhereCondition(userId, filters);

    const results = await db
      .select({
        id: emails.id,
        messageId: emails.messageId,
        subject: emails.subject,
        snippet: emails.snippet,
        sender: emails.sender,
        senderEmail: emails.senderEmail,
        receivedAt: emails.receivedAt,
        isRead: emails.isRead,
        isArchived: emails.isArchived,
        isTrashed: emails.isTrashed,
        isImportant: emails.isImportant,
        isReplied: emails.isReplied,
        categories: emails.categories,
        priority: emails.priority,
        snoozedUntil: emails.snoozedUntil,
        threadId: emails.threadId,
        provider: emails.provider,
        // Include summary fields but exclude heavy content
        summary: emails.summary,
        aiReply: emails.aiReply,
        labelIds: emails.labelIds,
      })
      .from(emails)
      .where(whereCondition)
      .orderBy(desc(emails.receivedAt))
      .limit(limit)
      .offset(offset);

    return this.normalizeArrayFields(results) as EmailSummary[];
  }

  /**
   * Get full email for detail views
   * Includes all fields - use sparingly
   */
  async getEmailForDetail(messageId: string, userId: number): Promise<EmailDetail | null> {
    logger.debug(`[EmailDataService] Fetching email for detail view`, {
      messageId,
      userId,
      viewType: EmailViewType.DETAIL,
      estimatedReduction: `${DATA_SIZE_REDUCTION[EmailViewType.DETAIL]}%`
    });

    const db = await getDb();
    if (!db) throw new Error('Database connection not available');

    const results = await db
      .select()
      .from(emails)
      .where(and(
        eq(emails.messageId, messageId),
        eq(emails.userId, userId)
      ))
      .limit(1);

    if (results.length === 0) return null;

    const normalized = this.normalizeArrayFields(results);
    return normalized[0] as EmailDetail;
  }

  /**
   * Get email content for AI processing
   * 60% smaller - only content fields needed for processing
   */
  async getEmailForProcessing(emailId: number, userId: number): Promise<EmailContent | null> {
    logger.debug(`[EmailDataService] Fetching email for processing`, {
      emailId,
      userId,
      viewType: EmailViewType.CONTENT,
      estimatedReduction: `${DATA_SIZE_REDUCTION[EmailViewType.CONTENT]}%`
    });

    const db = await getDb();
    if (!db) throw new Error('Database connection not available');

    const results = await db
      .select({
        id: emails.id,
        messageId: emails.messageId,
        subject: emails.subject,
        originalContent: emails.originalContent,
        htmlContent: emails.htmlContent,
        snippet: emails.snippet,
        sender: emails.sender,
        senderEmail: emails.senderEmail,
        receivedAt: emails.receivedAt,
      })
      .from(emails)
      .where(and(
        eq(emails.id, emailId),
        eq(emails.userId, userId)
      ))
      .limit(1);

    if (results.length === 0) return null;
    return results[0] as EmailContent;
  }

  /**
   * Get email metadata for admin/analytics
   * 90% smaller - only system fields
   */
  async getEmailMetadata(
    userId: number,
    options: PaginationOptions = {},
    filters?: EmailFilters
  ): Promise<EmailMetadata[]> {
    const { limit = 100, offset = 0 } = options;
    
    logger.debug(`[EmailDataService] Fetching email metadata`, {
      userId,
      limit,
      offset,
      viewType: EmailViewType.METADATA,
      estimatedReduction: `${DATA_SIZE_REDUCTION[EmailViewType.METADATA]}%`
    });

    const db = await getDb();
    if (!db) throw new Error('Database connection not available');

    const whereCondition = this.buildWhereCondition(userId, filters);

    const results = await db
      .select({
        id: emails.id,
        messageId: emails.messageId,
        userId: emails.userId,
        provider: emails.provider,
        receivedAt: emails.receivedAt,
        isRead: emails.isRead,
        isArchived: emails.isArchived,
        isTrashed: emails.isTrashed,
        isImportant: emails.isImportant,
        isReplied: emails.isReplied,
        categories: emails.categories,
        priority: emails.priority,
        contentExpiresAt: emails.contentExpiresAt,
        isContentEncrypted: emails.isContentEncrypted,
        retentionDays: emails.retentionDays,
      })
      .from(emails)
      .where(whereCondition)
      .orderBy(desc(emails.receivedAt))
      .limit(limit)
      .offset(offset);

    return this.normalizeArrayFields(results) as EmailMetadata[];
  }

  /**
   * Batch get emails by IDs with field selection
   */
  async getEmailsByIds(
    ids: number[], 
    userId: number, 
    viewType: EmailViewType = EmailViewType.SUMMARY
  ): Promise<EmailListItem[] | EmailSummary[] | EmailDetail[]> {
    if (ids.length === 0) return [];

    logger.debug(`[EmailDataService] Batch fetching emails by IDs`, {
      userId,
      emailCount: ids.length,
      viewType,
      estimatedReduction: `${DATA_SIZE_REDUCTION[viewType]}%`
    });

    const db = await getDb();
    if (!db) throw new Error('Database connection not available');

    // Use appropriate field selection based on view type
    switch (viewType) {
      case EmailViewType.LIST:
        return this.getEmailsForListByIds(ids, userId);
      case EmailViewType.SUMMARY:
        return this.getEmailsForSummaryByIds(ids, userId);
      case EmailViewType.DETAIL:
        return this.getEmailsForDetailByIds(ids, userId);
      default:
        return this.getEmailsForSummaryByIds(ids, userId);
    }
  }

  /**
   * Build WHERE condition for email queries
   */
  private buildWhereCondition(userId: number, filters?: EmailFilters): SQL<unknown> {
    let whereCondition: SQL<unknown> = eq(emails.userId, userId);

    if (filters) {
      // Trashed filter
      if (filters.trashed === true) {
        whereCondition = and(whereCondition, eq(emails.isTrashed, true)) as SQL<unknown>;
      } else {
        whereCondition = and(whereCondition, eq(emails.isTrashed, false)) as SQL<unknown>;
      }

      // Archive filter
      if (filters.archived === true) {
        whereCondition = and(whereCondition, eq(emails.isArchived, true)) as SQL<unknown>;
      } else if (!filters.important && !filters.snoozed && !filters.trashed) {
        whereCondition = and(whereCondition, eq(emails.isArchived, false)) as SQL<unknown>;
      }

      // Important filter
      if (filters.important === true) {
        whereCondition = and(whereCondition, eq(emails.isImportant, true)) as SQL<unknown>;
      }

      // Snoozed filter
      if (filters.snoozed === true) {
        whereCondition = and(whereCondition, not(isNull(emails.snoozedUntil))) as SQL<unknown>;
      }
    } else {
      // Default: non-archived, non-trashed emails
      whereCondition = and(
        whereCondition,
        eq(emails.isArchived, false),
        eq(emails.isTrashed, false)
      ) as SQL<unknown>;
    }

    return whereCondition;
  }

  /**
   * Helper methods for batch operations
   */
  private async getEmailsForListByIds(ids: number[], userId: number): Promise<EmailListItem[]> {
    const db = await getDb();
    if (!db) throw new Error('Database connection not available');

    const results = await db
      .select({
        id: emails.id,
        messageId: emails.messageId,
        subject: emails.subject,
        snippet: emails.snippet,
        sender: emails.sender,
        senderEmail: emails.senderEmail,
        receivedAt: emails.receivedAt,
        isRead: emails.isRead,
        isArchived: emails.isArchived,
        isTrashed: emails.isTrashed,
        isImportant: emails.isImportant,
        isReplied: emails.isReplied,
        categories: emails.categories,
        priority: emails.priority,
        snoozedUntil: emails.snoozedUntil,
        threadId: emails.threadId,
        provider: emails.provider,
      })
      .from(emails)
      .where(and(
        eq(emails.userId, userId),
        inArray(emails.id, ids)
      ));

    return this.normalizeArrayFields(results) as EmailListItem[];
  }

  private async getEmailsForSummaryByIds(ids: number[], userId: number): Promise<EmailSummary[]> {
    const db = await getDb();
    if (!db) throw new Error('Database connection not available');

    const results = await db
      .select({
        id: emails.id,
        messageId: emails.messageId,
        subject: emails.subject,
        snippet: emails.snippet,
        sender: emails.sender,
        senderEmail: emails.senderEmail,
        receivedAt: emails.receivedAt,
        isRead: emails.isRead,
        isArchived: emails.isArchived,
        isTrashed: emails.isTrashed,
        isImportant: emails.isImportant,
        isReplied: emails.isReplied,
        categories: emails.categories,
        priority: emails.priority,
        snoozedUntil: emails.snoozedUntil,
        threadId: emails.threadId,
        provider: emails.provider,
        summary: emails.summary,
        aiReply: emails.aiReply,
        labelIds: emails.labelIds,
      })
      .from(emails)
      .where(and(
        eq(emails.userId, userId),
        inArray(emails.id, ids)
      ));

    return this.normalizeArrayFields(results) as EmailSummary[];
  }

  private async getEmailsForDetailByIds(ids: number[], userId: number): Promise<EmailDetail[]> {
    const db = await getDb();
    if (!db) throw new Error('Database connection not available');

    const results = await db
      .select()
      .from(emails)
      .where(and(
        eq(emails.userId, userId),
        inArray(emails.id, ids)
      ));

    return this.normalizeArrayFields(results) as EmailDetail[];
  }

  /**
   * Normalize array fields that might be null/undefined
   */
  private normalizeArrayFields(results: any[]): any[] {
    return results.map(email => ({
      ...email,
      categories: email.categories || [],
      labelIds: email.labelIds || [],
    }));
  }
}

// Export singleton instance
export const emailDataService = new EmailDataService();
