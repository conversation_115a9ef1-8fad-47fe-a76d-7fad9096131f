import dotenv from 'dotenv';
import pg from 'pg';

dotenv.config();

// Get user ID from command line argument
const userIdArg = process.argv[2];

if (!userIdArg || Number.isNaN(Number.parseInt(userIdArg))) {
  console.error('❌ Error: Please provide a valid user ID as an argument');
  console.error('Usage: node scripts/delete-user.js <user_id>');
  console.error('Example: node scripts/delete-user.js 123');
  process.exit(1);
}

const userId = Number.parseInt(userIdArg);

async function deleteUser() {
  // Create DB connection
  const client = new pg.Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Begin transaction
    await client.query('BEGIN');

    try {
      // First, delete related records in child tables to maintain referential integrity
      console.log(`Deleting records for User ID ${userId}...`);

      // Delete from settings
      const settingsResult = await client.query(
        'DELETE FROM settings WHERE user_id = $1 RETURNING id',
        [userId]
      );
      console.log(`Deleted ${settingsResult.rowCount} settings records`);

      // Delete from achievements
      const achievementsResult = await client.query(
        'DELETE FROM achievements WHERE user_id = $1 RETURNING id',
        [userId]
      );
      console.log(`Deleted ${achievementsResult.rowCount} achievement records`);

      // Delete from emails
      const emailsResult = await client.query(
        'DELETE FROM emails WHERE user_id = $1 RETURNING id',
        [userId]
      );
      console.log(`Deleted ${emailsResult.rowCount} email records`);

      // Get user info before deletion for confirmation
      const userInfoResult = await client.query('SELECT id, email, name FROM users WHERE id = $1', [
        userId,
      ]);

      if (userInfoResult.rowCount === 0) {
        console.log(`User ID ${userId} not found in the database`);
        await client.query('ROLLBACK');
        return;
      }

      const userInfo = userInfoResult.rows[0];
      console.log('\n⚠️  WARNING: About to delete user:');
      console.log(`   ID: ${userInfo.id}`);
      console.log(`   Email: ${userInfo.email}`);
      console.log(`   Name: ${userInfo.name || 'N/A'}`);

      // Simple confirmation (no external dependencies)
      console.log(`\nType "DELETE" to confirm (case-sensitive):`);
      process.stdout.write('> ');

      const confirmation = await new Promise((resolve) => {
        process.stdin.once('data', (data) => {
          resolve(data.toString().trim());
        });
      });

      if (confirmation !== 'DELETE') {
        console.log('❌ Deletion cancelled. User not deleted.');
        await client.query('ROLLBACK');
        return;
      }

      // Finally, delete the user
      const userResult = await client.query('DELETE FROM users WHERE id = $1 RETURNING id, email', [
        userId,
      ]);

      console.log(`✅ Successfully deleted User ID ${userId} (${userResult.rows[0].email})`);
      await client.query('COMMIT');
    } catch (error) {
      // If any error occurs, roll back the transaction
      await client.query('ROLLBACK');
      console.error('Error during user deletion:', error);
    }
  } catch (error) {
    console.error('Database connection error:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

// Run the delete function
deleteUser().catch(console.error);
