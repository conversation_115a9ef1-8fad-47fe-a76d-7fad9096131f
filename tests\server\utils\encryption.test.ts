// Mock the logger to prevent console noise and allow for spying
jest.mock('@server/lib/logger', () => ({
  error: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
}));

describe('Server Encryption Utilities', () => {
  const MOCK_ENCRYPTION_KEY = 'a'.repeat(64); // 64-char hex = 32 bytes
  const MOCK_LEGACY_KEY = 'b'.repeat(32); // 32 chars
  const plaintext = 'This is a super secret message!';

  let encryptionUtils: { encrypt?: any; decrypt: any; isLegacyEncrypted?: any; };

  // This function simulates the legacy encryption format for testing decryption.
  const createLegacyEncryptedString = (text: string): string => {
    const iv = require('node:crypto').randomBytes(12);
    const key = Buffer.from(MOCK_LEGACY_KEY);
    const cipher = require('node:crypto').createCipheriv('aes-256-gcm', key, iv);
    let encrypted = cipher.update(text, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    const authTag = cipher.getAuthTag();
    return `${iv.toString('base64')}:${authTag.toString('base64')}:${encrypted}`;
  };

  beforeEach(() => {
    // Reset modules before each test to clear cached keys from environment variables
    jest.resetModules();
    // Set up environment variables for the encryption keys
    process.env.ENCRYPTION_KEY = MOCK_ENCRYPTION_KEY;
    process.env.TOKEN_ENCRYPTION_KEY = MOCK_LEGACY_KEY;
    // Dynamically import the module to use the fresh environment variables
    encryptionUtils = require('@server/utils/encryption');
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.ENCRYPTION_KEY;
    delete process.env.TOKEN_ENCRYPTION_KEY;
  });

  describe('Modern Encryption and Decryption', () => {
    it('should correctly encrypt and then decrypt a string', () => {
      const { encrypt, decrypt } = encryptionUtils;
      const encrypted = encrypt(plaintext);
      expect(encrypted).not.toBe(plaintext);
      expect(encrypted.startsWith('enc:')).toBe(true);

      const decrypted = decrypt(encrypted);
      expect(decrypted).toBe(plaintext);
    });

    it('should handle encrypting and decrypting an empty string', () => {
      const { encrypt, decrypt } = encryptionUtils;
      const encrypted = encrypt('');
      expect(encrypted).not.toBe('');
      const decrypted = decrypt(encrypted);
      expect(decrypted).toBe('');
    });
  });

  describe('Legacy Decryption Compatibility', () => {
    it('should correctly decrypt a string encrypted with the legacy format', () => {
      const { decrypt } = encryptionUtils;
      const legacyEncrypted = createLegacyEncryptedString(plaintext);

      // Ensure it's not the new format
      expect(legacyEncrypted.startsWith('enc:')).toBe(false);

      const decrypted = decrypt(legacyEncrypted);
      expect(decrypted).toBe(plaintext);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should return the original string if it is not encrypted', () => {
      const { decrypt } = encryptionUtils;
      const notEncrypted = 'this is just a plain string';
      expect(decrypt(notEncrypted)).toBe(notEncrypted);
    });

    it('should throw an error if modern decryption fails due to tampered data', () => {
      const { encrypt, decrypt } = encryptionUtils;
      const encrypted = encrypt(plaintext);

      // Tamper with the auth tag specifically
      // Format is enc:iv:encrypted:authTag. After splitting by ':', parts[0] is 'enc', parts[1] is iv, etc.
      const parts = encrypted.split(':');
      // Tamper the auth tag, which is the last part
      parts[parts.length - 1] = `${parts[parts.length - 1].slice(0, -1)}z`;
      const tampered = parts.join(':');

      expect(() => decrypt(tampered)).toThrow('Failed to decrypt data.');
    });

    it('should throw an error if legacy decryption fails due to tampered data', () => {
      const { decrypt, isLegacyEncrypted } = encryptionUtils;
      const legacyEncrypted = createLegacyEncryptedString(plaintext);
      // Tamper with the encrypted data by changing a character.
      // Legacy format is iv:authTag:encrypted, all base64. Let's tamper the payload.
      const parts = legacyEncrypted.split(':');
      const tamperedPayload = `${Buffer.from(parts[2], 'base64').toString('hex').slice(0, -1)}z`;
      const tampered = `${parts[0]}:${parts[1]}:${Buffer.from(tamperedPayload, 'hex').toString('base64')}`;

      // It should be identified as legacy but fail decryption.
      expect(isLegacyEncrypted(tampered)).toBe(true);
      expect(() => decrypt(tampered)).toThrow('Failed to decrypt legacy data.');
    });

    it('should throw an error if the modern encryption key is missing', () => {
      delete process.env.ENCRYPTION_KEY;
      // We must re-import the module for the change to be detected
      const utils = require('@server/utils/encryption');
      expect(() => utils.encrypt(plaintext)).toThrow(
        'Application is not configured with an ENCRYPTION_KEY.'
      );
    });

    it('should throw an error for an invalid modern encryption key format', () => {
      process.env.ENCRYPTION_KEY = 'short-key';
      const utils = require('@server/utils/encryption');
      expect(() => utils.encrypt(plaintext)).toThrow('Invalid ENCRYPTION_KEY format.');
    });
  });
});
