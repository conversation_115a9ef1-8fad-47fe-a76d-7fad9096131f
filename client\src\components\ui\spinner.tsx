import { Loader2 } from 'lucide-react';
import type React from 'react';
import { cn } from '@/lib/utils';

type SpinnerSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

interface SpinnerProps {
  size?: SpinnerSize;
  className?: string;
}

/**
 * Spinner Component
 *
 * A reusable spinner component for loading states with multiple size options
 */
export const Spinner: React.FC<SpinnerProps> = ({ size = 'md', className }) => {
  const sizeClasses = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12',
  };

  return <Loader2 className={cn('animate-spin text-primary', sizeClasses[size], className)} />;
};

export default Spinner;
