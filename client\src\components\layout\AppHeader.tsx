import { motion } from 'framer-motion';
import { Mail, Menu } from 'lucide-react';
import React from 'react';
import { Link } from 'wouter';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { dropdownActionItems, dropdownNavItems } from '@/config/navigation';
import { useAuth } from '@/context/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { getUserInitials } from '@/lib/utils';
import { EnhancedConnectionStatus } from '../email/EnhancedConnectionStatus';

// Mobile menu context
interface MobileMenuContextType {
  setOpenMobile: (open: boolean) => void;
}

const MobileMenuContext = React.createContext<MobileMenuContextType | null>(null);

export const MobileMenuProvider: React.FC<{ children: React.ReactNode; setOpenMobile: (open: boolean) => void }> = ({ children, setOpenMobile }) => {
  return (
    <MobileMenuContext.Provider value={{ setOpenMobile }}>
      {children}
    </MobileMenuContext.Provider>
  );
};

const useMobileMenu = () => {
  const context = React.useContext(MobileMenuContext);
  return context;
};

const AppHeader: React.FC = () => {
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const mobileMenu = useMobileMenu();

  // Safely handle user data with proper fallbacks
  const userEmail: string = user?.email || '';
  const userName: string = user?.name || '';
  const userPicture: string = user?.picture || '';
  const userTier: string = user?.tier || 'free';
  const displayName: string = userName || (userEmail ? userEmail.split('@')[0] : 'User');

  return (
    <motion.header
      className="app-header w-full max-w-[100vw] px-2 sm:px-3 md:px-4 py-1.5 sm:py-2 overflow-hidden"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      // Reduced animation duration for performance
      transition={{ duration: 0.2, ease: 'easeOut' }}
    >
      <div className="flex items-center gap-1 sm:gap-1 overflow-hidden">
        {/* Mobile menu button */}
        {isMobile && mobileMenu && (
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 mr-2"
            onClick={() => mobileMenu.setOpenMobile(true)}
            aria-label="Open navigation menu"
          >
            <Menu className="h-4 w-4" />
          </Button>
        )}
        
        <motion.div
          className="text-primary flex items-center"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          transition={{ duration: 0.15 }}
        >
          <Mail className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 flex-shrink-0" aria-hidden="true" />
          <span className="ml-1 sm:ml-1.5 md:ml-2 font-semibold text-sm sm:text-base md:text-lg tracking-tight truncate">
            Inbox Zero
          </span>
        </motion.div>
        {user && (
          <div className="hidden md:flex items-center ml-2">
            <Badge
              variant="outline"
              className="bg-primary-muted text-primary text-xs font-medium px-2 py-0.5 rounded-full"
            >
              {userTier === 'free' ? 'Free Tier' : 'Pro Tier'}
            </Badge>
          </div>
        )}
      </div>

      <div className="flex items-center gap-1 sm:gap-2">
        {/* Show tier badge on mobile */}
        {user && isMobile && (
          <Badge
            variant="outline"
            className="bg-primary-muted text-primary text-[10px] font-medium px-1.5 py-0.5 rounded-full"
          >
            {userTier === 'free' ? 'Free' : 'Pro'}
          </Badge>
        )}

        {/* Theme toggle */}
        <ThemeToggle />

        {/* User profile dropdown */}
        {user && (
          <>
            <EnhancedConnectionStatus size="sm" />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="secondary"
                  className="flex items-center gap-1 sm:gap-2xs rounded-full p-1 pr-1 sm:pr-2 md:pr-3 transition-all duration-300 hover:bg-accent hover:shadow-sm"
                >
                  <Avatar className="h-5 w-5 sm:h-6 sm:w-6 md:h-7 md:w-7 border border-border">
                    {userPicture ? (
                      <AvatarImage src={userPicture} alt={userName || 'User'} />
                    ) : (
                      <AvatarFallback className="text-xs sm:text-sm bg-primary text-primary-foreground">
                        {userName ? getUserInitials(userName) : 'U'}
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <span className="text-xs sm:text-sm font-medium hidden md:block max-w-[100px] truncate">
                    {displayName}
                  </span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-52 sm:w-56 z-50">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />

                {dropdownNavItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <DropdownMenuItem key={item.name} asChild>
                      <Link href={item.path}>
                        <a className="cursor-pointer flex items-center w-full">
                          <Icon className="mr-2 h-4 w-4 flex-shrink-0" />
                          <span className="truncate">{item.name}</span>
                        </a>
                      </Link>
                    </DropdownMenuItem>
                  );
                })}

                <DropdownMenuSeparator />

                {dropdownActionItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <DropdownMenuItem key={item.name} asChild>
                      <Link href={item.path}>
                        <a className="cursor-pointer text-destructive flex items-center w-full">
                          <Icon className="mr-2 h-4 w-4 flex-shrink-0" />
                          <span className="truncate">{item.name}</span>
                        </a>
                      </Link>
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        )}
      </div>
    </motion.header>
  );
};

export default React.memo(AppHeader);
