/**
 * Authentication Repair Component
 *
 * This component automatically detects and attempts to repair authentication issues
 * when they occur. It runs in the background and provides user feedback.
 */

import { AlertCircle, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useProviderStatus } from '@/hooks/use-provider-status';
import { type AuthDiagnostics, attemptAuthRepair, diagnoseAuthentication } from '@/lib/authFixer';
import { getCurrentFirebaseUser } from '@/lib/firebaseAuth';

const REPAIR_COOLDOWN_MS = 30000;
const DIAGNOSTIC_DELAY_MS = 1000;

interface AuthenticationRepairProps {
  autoRepair?: boolean;
  showDiagnostics?: boolean;
}

export function AuthenticationRepair({
  autoRepair = true,
  showDiagnostics = false,
}: AuthenticationRepairProps) {
  const { user } = useAuth();
  const { providers, isLoading, refetch: refetchProviderStatus } = useProviderStatus();
  const { toast } = useToast();

  const [diagnostics, setDiagnostics] = useState<AuthDiagnostics | null>(null);
  const [isRepairing, setIsRepairing] = useState(false);
  const [lastRepairTime, setLastRepairTime] = useState<number>(0);

  // Detect authentication issues
  const hasAuthIssue =
    user &&
    ((!isLoading && providers.length === 0) ||
      providers.some((p) => p.provider === 'google' && !p.isConnected));

  // Run diagnostics
  const runDiagnostics = useCallback(async () => {
    if (!user) return;
    try {
      const firebaseUser = await getCurrentFirebaseUser();
      if (!firebaseUser) return null;
      const results = await diagnoseAuthentication(firebaseUser);
      setDiagnostics(results);
      return results;
    } catch (error) {
      console.error('[AuthRepair] Diagnostics failed:', error);
      return null;
    }
  }, [user]);

  // Attempt repair
  const runRepair = useCallback(async () => {
    if (isRepairing) return;

    setIsRepairing(true);
    setLastRepairTime(Date.now());

    try {
      const firebaseUser = await getCurrentFirebaseUser();
      const result = await attemptAuthRepair(firebaseUser);

      if (result.success) {
        toast({
          title: 'Authentication Repaired',
          description: result.message,
          variant: 'default',
        });

        // Re-run diagnostics to verify fix
        setTimeout(async () => {
          await refetchProviderStatus();
          runDiagnostics();
        }, DIAGNOSTIC_DELAY_MS);
      } else {
        toast({
          title: 'Repair Failed',
          description: result.message,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('[AuthRepair] Repair failed:', error);
      toast({
        title: 'Repair Error',
        description: 'An unexpected error occurred during repair',
        variant: 'destructive',
      });
    } finally {
      setIsRepairing(false);
    }
  }, [isRepairing, toast, runDiagnostics, refetchProviderStatus]);

  // Auto-repair logic
  useEffect(() => {
    if (!user || !hasAuthIssue || !autoRepair) return;

    // Don't attempt repair too frequently
    const now = Date.now();
    if (now - lastRepairTime < REPAIR_COOLDOWN_MS) return;

    runRepair();
  }, [user, hasAuthIssue, autoRepair, lastRepairTime, runRepair]);

  // Run diagnostics when auth state changes
  useEffect(() => {
    if (user && showDiagnostics) {
      runDiagnostics();
    }
  }, [user, showDiagnostics, runDiagnostics]);

  // Don't render if no user or no issues and not showing diagnostics
  if (!user || (!hasAuthIssue && !showDiagnostics)) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* Show alert for authentication issues */}
      {hasAuthIssue && (
        <Alert variant={diagnostics?.canAutoFix ? 'default' : 'destructive'}>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Authentication Issue Detected</AlertTitle>
          <AlertDescription className="space-y-2">
            <p>
              There appears to be an issue with your email provider connection.
              {diagnostics?.canAutoFix
                ? ' This can likely be fixed automatically.'
                : ' Manual intervention may be required.'}
            </p>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={runRepair}
                disabled={isRepairing}
                className="flex items-center gap-2"
              >
                {isRepairing ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                {isRepairing ? 'Repairing...' : 'Fix Authentication'}
              </Button>

              <Button size="sm" variant="outline" onClick={runDiagnostics}>
                Run Diagnostics
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Show diagnostics if requested */}
      {showDiagnostics && diagnostics && (
        <Alert variant="default">
          <CheckCircle className="h-4 w-4" />
          <AlertTitle>Authentication Diagnostics</AlertTitle>
          <AlertDescription>
            <div className="space-y-2 text-sm">
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center gap-2">
                  {diagnostics.hasFirebaseToken ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-3 w-3 text-red-500" />
                  )}
                  Firebase Auth
                </div>

                <div className="flex items-center gap-2">
                  {diagnostics.isSessionValid ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-3 w-3 text-red-500" />
                  )}
                  Session Valid
                </div>

                <div className="flex items-center gap-2">
                  {diagnostics.isBackendConnected ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-3 w-3 text-red-500" />
                  )}
                  Backend Connection
                </div>

                <div className="flex items-center gap-2">
                  {diagnostics.isGmailConnected ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-3 w-3 text-red-500" />
                  )}
                  Gmail Connection
                </div>
              </div>

              <div className="pt-2 border-t">
                <strong>Recommendation:</strong> {diagnostics.recommendedAction}
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

export default AuthenticationRepair;
