/**
 * Email Service – business-logic tests for AI processing pipeline.
 */

import { processEmailsWithAI } from '@server/services/email-v2';

// --------------------
// In-memory store mocks
// --------------------
const mockEmails: any = {
  1: {
    id: 1,
    userId: 42,
    summary: 'Processing...',
    subject: 'Project Update',
    sender: 'Alice',
    originalContent: 'This is a long enough message to trigger AI processing.',
  },
  2: {
    id: 2,
    userId: 42,
    summary: 'Processing...',
    subject: 'Hi',
    sender: 'Bob',
    originalContent: 'Short text', // < 20 chars triggers SHORT_MESSAGE shortcut
  },
};

const updatedEmails: Record<number, any> = {};

jest.mock('@server/storage', () => ({
  __esModule: true,
  storage: {
    getEmail: async (id: number) => mockEmails[id] ?? null,
    updateEmail: async (id: number, updates: any) => {
      updatedEmails[id] = { ...mockEmails[id], ...updates };
    },
    getSettings: async () => ({ replyTone: 'professional', customTone: null }),
  },
}));

// --------------------
// External dependency mocks
// --------------------
jest.mock('@server/services/rateLimiter', () => ({
  __esModule: true,
  checkRateLimit: jest.fn().mockResolvedValue(true), // always allow
}));

jest.mock('@server/services/gemini', () => ({
  __esModule: true,
  unifiedEmailProcessing: jest.fn(async () => ({ summary: 'Mock summary', reply: 'Mock reply' })),
}));

jest.mock('@server/services/categorization', () => ({
  __esModule: true,
  categorizeEmail: jest.fn(async () => ({ category: 'Work', confidence: 0.9 })),
}));

// Silence logger
jest.mock('@server/lib/logger', () => ({
  __esModule: true,
  default: { info: jest.fn(), warn: jest.fn(), error: jest.fn(), debug: jest.fn() },
}));

describe('processEmailsWithAI', () => {
  it('updates email with AI summary, reply, and categories', async () => {
    await processEmailsWithAI(42, [1]);

    expect(updatedEmails[1].summary).toBe('Mock summary');
    expect(updatedEmails[1].aiReply).toBe('Mock reply');
    expect(updatedEmails[1].categories).toEqual(['Work']);
  });

  it('handles short messages by setting SHORT_MESSAGE summary', async () => {
    await processEmailsWithAI(42, [2]);

    expect(updatedEmails[2].summary.toLowerCase()).toContain('short');
  });
}); 