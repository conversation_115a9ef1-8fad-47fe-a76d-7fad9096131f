import { useEffect } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useAuth } from '@/context/AuthContext';

/**
 * Logout page
 * This component handles the logout functionality by calling the logout
 * method from the AuthContext.
 */
export default function LogoutPage() {
  const { logout, loading } = useAuth();

  useEffect(() => {
    logout();
  }, [logout]);

  // While the logout function from the context is running,
  // it sets its own loading state. We can reflect that here.
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-lg">Logging out...</p>
        </div>
      </div>
    );
  }

  // The logout function will navigate away, so we can show a fallback
  // message in case the navigation is slow or fails.
  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center">
        <p className="text-lg">Redirecting to login...</p>
      </div>
    </div>
  );
}
