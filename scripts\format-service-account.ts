import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * This script reads a multi-line Google Cloud service account JSON file,
 * converts it to a single-line string, and prints a .env compatible
 * variable assignment to the console.
 */

// Path to your service account key file
const keyFilePath = path.resolve(__dirname, '..', 'service-account.json');

if (!fs.existsSync(keyFilePath)) {
  console.error(`\n❌ Error: Service account file not found at:\n${keyFilePath}`);
  console.error(
    '\nPlease place your downloaded service account JSON file in the project root and name it "service-account.json".\n'
  );
  process.exit(1);
}

try {
  // Read the file content
  const keyFileContent = fs.readFileSync(keyFilePath, 'utf8');

  // Parse the JSON to ensure it's valid
  const serviceAccount = JSON.parse(keyFileContent);

  // Validate that it's a service account file
  if (!serviceAccount.type || serviceAccount.type !== 'service_account') {
    console.error('\n❌ Error: This does not appear to be a valid service account file.');
    console.error('Expected "type": "service_account" in the JSON file.\n');
    process.exit(1);
  }

  // Stringify the JSON object into a single line
  const singleLineJson = JSON.stringify(serviceAccount);

  console.log('\n✅ Your service account key has been formatted successfully!');
  console.log(
    '\nThis is the final step. Please copy the following line and paste it into your .env file:'
  );
  console.log(
    '-------------------------------------------------------------------------------------'
  );
  console.log(`FIREBASE_SERVICE_ACCOUNT="${singleLineJson}"`);
  console.log(
    '-------------------------------------------------------------------------------------\n'
  );
} catch (error) {
  console.error('\n❌ Error: Failed to parse the service account file.', error);
  console.error('\nPlease ensure that "service-account.json" is a valid JSON file.\n');
  process.exit(1);
}
