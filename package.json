{"name": "inbox-zero-ai", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"clean": "rm -rf dist", "dev": "concurrently \"npm:dev:server\" \"npm:dev:client\"", "dev:server": "tsx server/index.ts", "dev:client": "vite", "build": "npm run clean && npm run build:client && npm run build:server", "build:client": "vite build", "build:server": "esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outfile=dist/server.js", "start": "cross-env NODE_ENV=production node -r dotenv/config dist/server.js", "start:dev": "cross-env NODE_ENV=development node -r dotenv/config dist/server.js", "preview": "vite preview", "type-check": "tsc --noEmit", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:setup": "node scripts/create-tables.js", "check-env": "node check-env.js", "test-db": "node scripts/test-db-connection.js", "lint": "biome lint .", "lint:fix": "biome lint --write .", "format": "biome format .", "format:fix": "biome format --write .", "check": "biome check .", "check:fix": "biome check --write .", "check-legacy": "npm run search-deprecated && npm run search-console-logs", "search-deprecated": "grep -r \"getUserById\" --include=\"*.ts\" . || echo \"No deprecated functions found\"", "search-console-logs": "grep -r \"console\\.log\" --include=\"*.ts\" server/ shared/ || echo \"No console.log found in production code\"", "validate-env": "node scripts/validate-production-env.js", "format-sa": "tsx scripts/format-service-account.ts", "test": "cross-env NODE_OPTIONS=--max_old_space_size=4096 jest --runInBand", "test:client": "cross-env NODE_OPTIONS=--max_old_space_size=4096 jest --runInBand --testPathPattern=tests/client", "test:server": "cross-env NODE_OPTIONS=--max_old_space_size=4096 jest --runInBand --testPathPattern=tests/server", "test:csrf": "tsx scripts/test-csrf-bypass.ts", "test:csrf:prod": "NODE_ENV=production tsx scripts/test-csrf-bypass.ts", "test:new": "tsx scripts/test-new-tests.ts", "validate:scripts": "node -e \"console.log('📋 Validating script integrity...'); const fs = require('fs'); const pkg = require('./package.json'); const scripts = pkg.scripts; Object.entries(scripts).forEach(([name, cmd]) => { if (cmd.includes('scripts/')) { const match = cmd.match(/scripts\\/([^\\s]+)/); if (match) { const file = match[1]; if (!fs.existsSync('scripts/' + file)) { console.error('❌ Missing script file:', file); process.exit(1); } } } }); console.log('✅ All script files exist');\""}, "dependencies": {"@google/genai": "^1.6.0", "@hookform/resolvers": "^5.1.1", "@microsoft/microsoft-graph-client": "^3.0.7", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.0", "@tanstack/react-virtual": "^3.13.10", "@types/compression": "^1.8.1", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "@xenova/transformers": "^2.17.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "compression": "^1.8.0", "connect-pg-simple": "^10.0.0", "connect-redis": "^9.0.0", "csrf-sync": "^4.2.1", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "drizzle-orm": "^0.31.2", "drizzle-zod": "^0.5.1", "embla-carousel-react": "^8.6.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "framer-motion": "^12.18.1", "googleapis": "^150.0.1", "input-otp": "^1.4.2", "lodash-es": "^4.17.21", "lucide-react": "^0.522.0", "openai": "^5.6.0", "pg": "^8.16.2", "postgres": "^3.4.7", "react": "^18.3.1", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.58.1", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.4", "redis": "^5.5.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "winston": "^3.17.0", "wouter": "^3.7.1", "ws": "^8.18.2", "zod": "^3.25.67", "zod-validation-error": "^3.5.2"}, "devDependencies": {"@biomejs/biome": "2.0.0", "@swc/jest": "^0.2.38", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^15.0.7", "@types/connect-pg-simple": "^7.0.3", "@types/express": "5.0.3", "@types/express-session": "^1.18.2", "@types/jest": "^29.5.12", "@types/lodash-es": "^4.17.12", "@types/node": "20.14.9", "@types/prompts": "^2.4.9", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/supertest": "^6.0.3", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "drizzle-kit": "^0.22.8", "esbuild": "^0.25.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jsdom": "^26.1.0", "mkcert": "^3.2.0", "postcss": "^8.5.6", "prompts": "^2.4.2", "supertest": "^7.1.1", "tailwindcss": "^4.1.10", "testing-library": "^0.0.2", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "5.8.3", "vite": "^6.3.5", "vite-plugin-mkcert": "^1.17.8", "vite-tsconfig-paths": "^5.1.4"}, "optionalDependencies": {"bufferutil": "^4.0.9"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "overrides": {"glob": "^10.3.10", "react": "^18.3.1", "@types/react": "^18.3.3"}}