import { AlertCircle, AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useProviderStatus } from '@/hooks/use-provider-status';
import type { EmailProviderStatus } from '@/lib/emailProviders';

/**
 * A component that prompts the user to connect their Gmail account
 * Displayed when a user has logged in with Firebase but hasn't connected Gmail
 */
export function GmailConnectionPrompt() {
  const { providers, isLoading } = useProviderStatus();

  if (isLoading) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Loading Connection Status...</AlertTitle>
        <AlertDescription>Please wait while we check your account connections.</AlertDescription>
      </Alert>
    );
  }

  const gmailStatus = providers.find((p) => p.provider === 'google') as
    | EmailProviderStatus
    | undefined;
  const needsReauth = gmailStatus?.tokenInvalid || false;

  const handleConnect = () => {
    // Redirect to the Google auth endpoint on the server
    window.location.href = '/api/auth/google';
  };

  if (!gmailStatus || !gmailStatus.isConnected) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Connect your Gmail account</AlertTitle>
        <AlertDescription>
          To get started, you need to connect your Gmail account to allow InboxZeroAI to access your
          emails.
          <div className="mt-4">
            <Button onClick={handleConnect}>Connect Gmail</Button>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (needsReauth) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Re-authentication needed</AlertTitle>
        <AlertDescription>
          Your connection to Gmail has expired or permissions have changed. Please reconnect your
          account.
          <div className="mt-4">
            <Button onClick={handleConnect}>Reconnect Gmail</Button>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  return null;
}

export default GmailConnectionPrompt;
