/**
 * Production Console.log Cleaner
 * 
 * Removes all console.log statements from production code paths
 * while preserving them in test files and development utilities.
 */

import fs from 'node:fs';
import path from 'node:path';
import { glob } from 'glob';

interface CleanupStats {
  filesProcessed: number;
  filesModified: number;
  logsRemoved: number;
  errors: string[];
}

interface FileProcessingResult {
  modified: boolean;
  removedCount: number;
  error?: string;
}

/**
 * Configuration for console.log removal
 */
const CONFIG = {
  // Files to process (production code)
  includePatterns: [
    'client/src/**/*.{ts,tsx,js,jsx}',
    'server/**/*.{ts,tsx,js,jsx}',
    'shared/**/*.{ts,tsx,js,jsx}',
  ],
  // Files to exclude (keep console.logs in these)
  excludePatterns: [
    '**/test/**',
    '**/tests/**',
    '**/*.test.*',
    '**/*.spec.*',
    '**/scripts/**',
    '**/utils/fix-auth-session.js', // Development utility
  ],
  // Console methods to remove
  consoleMethods: [
    'console.log',
    'console.debug',
    'console.info', // Remove info logs too for cleaner production
  ],
  // Console methods to keep (errors and warnings should stay)
  keepMethods: [
    'console.error',
    'console.warn',
  ],
  // Backup directory
  backupDir: 'backups/console-cleanup',
};

/**
 * Check if file should be excluded from processing
 */
function shouldExcludeFile(filePath: string): boolean {
  return CONFIG.excludePatterns.some(pattern => {
    // Convert glob pattern to regex
    const regexPattern = pattern
      .replace(/\*\*/g, '.*')
      .replace(/\*/g, '[^/]*')
      .replace(/\?/g, '.');
    
    const regex = new RegExp(regexPattern);
    return regex.test(filePath);
  });
}

/**
 * Create backup of file before modification
 */
function createBackup(filePath: string): void {
  const backupPath = path.join(CONFIG.backupDir, filePath);
  const backupDirectory = path.dirname(backupPath);
  
  // Ensure backup directory exists
  fs.mkdirSync(backupDirectory, { recursive: true });
  
  // Copy file to backup location
  fs.copyFileSync(filePath, backupPath);
}

/**
 * Remove console statements from file content
 */
function removeConsoleStatements(content: string): { 
  newContent: string; 
  removedCount: number; 
} {
  let removedCount = 0;
  let newContent = content;

  for (const method of CONFIG.consoleMethods) {
    // Match console statements with various patterns
    const patterns = [
      // Simple single-line console statements
      new RegExp(`^\\s*${method.replace('.', '\\.')}\\([^;]*\\);?\\s*$`, 'gm'),
      
      // Multi-line console statements
      new RegExp(`${method.replace('.', '\\.')}\\([^)]*(?:\\([^)]*\\)[^)]*)*\\);?`, 'g'),
      
      // Console statements with template literals
      new RegExp(`${method.replace('.', '\\.')}\\(\`[^`]*\`[^;]*\\);?`, 'g'),
      
      // Console statements with object literals
      new RegExp(`${method.replace('.', '\\.')}\\(\\{[^}]*\\}[^;]*\\);?`, 'g'),
    ];

    for (const pattern of patterns) {
      const matches = newContent.match(pattern);
      if (matches) {
        newContent = newContent.replace(pattern, '');
        removedCount += matches.length;
      }
    }
  }

  // Clean up empty lines that might be left after removal
  newContent = newContent.replace(/\n\s*\n\s*\n/g, '\n\n');
  
  return { newContent, removedCount };
}

/**
 * Process a single file
 */
function processFile(filePath: string): FileProcessingResult {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    const { newContent, removedCount } = removeConsoleStatements(originalContent);

    if (removedCount > 0) {
      // Create backup before modifying
      createBackup(filePath);
      
      // Write cleaned content
      fs.writeFileSync(filePath, newContent, 'utf8');
      
      return {
        modified: true,
        removedCount,
      };
    }

    return {
      modified: false,
      removedCount: 0,
    };

  } catch (error) {
    return {
      modified: false,
      removedCount: 0,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Get all files to process
 */
async function getFilesToProcess(): Promise<string[]> {
  const allFiles: string[] = [];

  for (const pattern of CONFIG.includePatterns) {
    const files = await glob(pattern, { 
      cwd: process.cwd(),
      ignore: CONFIG.excludePatterns 
    });
    allFiles.push(...files);
  }

  // Remove duplicates and filter excluded files
  const uniqueFiles = [...new Set(allFiles)];
  return uniqueFiles.filter(file => !shouldExcludeFile(file));
}

/**
 * Main cleanup function
 */
async function cleanupConsoleLogs(): Promise<CleanupStats> {
  console.log('🧹 Starting console.log cleanup for production code...\n');

  const stats: CleanupStats = {
    filesProcessed: 0,
    filesModified: 0,
    logsRemoved: 0,
    errors: [],
  };

  try {
    // Ensure backup directory exists
    fs.mkdirSync(CONFIG.backupDir, { recursive: true });

    // Get files to process
    const filesToProcess = await getFilesToProcess();
    console.log(`📁 Found ${filesToProcess.length} files to process\n`);

    // Process each file
    for (const filePath of filesToProcess) {
      stats.filesProcessed++;
      
      const result = processFile(filePath);
      
      if (result.error) {
        stats.errors.push(`${filePath}: ${result.error}`);
        console.log(`❌ ${filePath} - Error: ${result.error}`);
        continue;
      }

      if (result.modified) {
        stats.filesModified++;
        stats.logsRemoved += result.removedCount;
        console.log(`✅ ${filePath} - Removed ${result.removedCount} console statements`);
      } else {
        console.log(`⚪ ${filePath} - No console statements found`);
      }
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    stats.errors.push(`Global error: ${errorMessage}`);
    console.error('❌ Global error:', errorMessage);
  }

  return stats;
}

/**
 * Print cleanup summary
 */
function printSummary(stats: CleanupStats): void {
  console.log(`\n${'='.repeat(50)}`);
  console.log('📊 CONSOLE.LOG CLEANUP SUMMARY');
  console.log('='.repeat(50));
  console.log(`📁 Files processed: ${stats.filesProcessed}`);
  console.log(`✏️  Files modified: ${stats.filesModified}`);
  console.log(`🗑️  Console statements removed: ${stats.logsRemoved}`);
  console.log(`❌ Errors encountered: ${stats.errors.length}`);

  if (stats.errors.length > 0) {
    console.log('\n📋 Errors:');
    stats.errors.forEach(error => console.log(`   • ${error}`));
  }

  if (stats.filesModified > 0) {
    console.log(`\n💾 Backups created in: ${CONFIG.backupDir}`);
    console.log('   You can restore files from backups if needed.');
  }

  console.log('\n✅ Console.log cleanup completed!');
}

/**
 * Validate environment before cleanup
 */
function validateEnvironment(): boolean {
  // Ensure we're not running in test environment
  if (process.env.NODE_ENV === 'test') {
    console.error('❌ Cannot run console cleanup in test environment');
    return false;
  }

  // Check if we're in the right directory
  if (!fs.existsSync('package.json')) {
    console.error('❌ Not in project root directory (package.json not found)');
    return false;
  }

  // Check for git repository (recommended for safety)
  if (!fs.existsSync('.git')) {
    console.warn('⚠️  Warning: Not in a git repository. Consider committing changes first.');
  }

  return true;
}

/**
 * Run the cleanup with safety checks
 */
async function main(): Promise<void> {
  console.log('🔍 Console.Log Production Cleanup Tool');
  console.log('=====================================\n');

  // Validate environment
  if (!validateEnvironment()) {
    process.exit(1);
  }

  // Confirm with user
  const args = process.argv.slice(2);
  const isForced = args.includes('--force') || args.includes('-f');
  
  if (!isForced) {
    console.log('⚠️  This will remove console.log statements from production code.');
    console.log('   Backups will be created automatically.');
    console.log('   Run with --force to skip this confirmation.\n');
    
    // In a real scenario, you'd want to prompt for user confirmation
    // For now, we'll assume confirmation
    console.log('✅ Proceeding with cleanup...\n');
  }

  // Run cleanup
  const startTime = Date.now();
  const stats = await cleanupConsoleLogs();
  const duration = Date.now() - startTime;

  // Print results
  printSummary(stats);
  console.log(`⏱️  Cleanup completed in ${duration}ms\n`);

  // Exit with appropriate code
  process.exit(stats.errors.length > 0 ? 1 : 0);
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
}

export { cleanupConsoleLogs, CONFIG }; 