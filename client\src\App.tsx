import { TooltipProvider } from '@components/ui/tooltip';
import { QueryClientProvider } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';
import type React from 'react';
import { Suspense, useState, useCallback } from 'react';
import { He<PERSON>etProvider } from 'react-helmet-async';
import { Route, Switch } from 'wouter';
import AppInitializer from '@/components/AppInitializer';
import PrivateRoute from '@/components/auth/PrivateRoute';
import TokenExpirationHandler from '@/components/auth/TokenExpirationHandler';
import ErrorBoundary from '@/components/error/ErrorBoundary';
import FirebaseAuthErrorBoundary from '@/components/error/FirebaseAuthErrorBoundary';
import KeyboardShortcutsHelp from '@/components/ui/keyboard-shortcuts-help';
import { Toaster } from '@/components/ui/toaster';
import { AuthProvider } from '@/context/AuthContext';
import { EmailDetailProvider } from '@/context/EmailDetailContext';
import { EmailListProvider } from '@/context/EmailListContext';
import { NetworkStatusProvider } from '@/context/NetworkStatusContext';
import { ThemeProvider } from '@/context/ThemeContext';
import { UserSettingsProvider } from '@/context/UserSettingsContext';
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';
import { queryClient } from '@/lib/queryClient';
// Import lazy loading utilities
import { deferredLazyLoad, lazyLoad } from '@/utils/lazyLoad';

// Loading component for use with Suspense
const PageLoading = () => (
  <div className="flex items-center justify-center h-screen">
    <div className="flex flex-col items-center justify-center space-y-4">
      <Loader2 className="h-12 w-12 animate-spin text-primary" />
      <p className="text-gray-600">Loading page...</p>
    </div>
  </div>
);

// Core pages
const LoginPage = lazyLoad(() => import('@/pages/login'));
const DashboardPage = lazyLoad(() => import('@/pages/dashboard'));
const NotFound = lazyLoad(() => import('@/pages/not-found'));
const LogoutPage = lazyLoad(() => import('@/pages/logout'));

// Secondary pages - use standard lazy loading
const Settings = lazyLoad(() => import('@/pages/settings'));
const HeatmapPage = lazyLoad(() => import('@/pages/heatmap'));
const DigestPage = lazyLoad(() => import('@/pages/digest'));
const AchievementsPage = lazyLoad(() => import('@/pages/achievements'));
const CircuitBreakersPage = lazyLoad(() => import('@/pages/admin/CircuitBreakersPage'));
const MemoryMonitorPage = lazyLoad(() => import('@/pages/admin/MemoryMonitorPage'));

// Add mailbox pages
const ArchivePage = lazyLoad(() => import('@/pages/archive'));
const TrashPage = lazyLoad(() => import('@/pages/trash'));
const SnoozedPage = lazyLoad(() => import('@/pages/snoozed'));
const ImportantPage = lazyLoad(() => import('@/pages/important'));

// Lower priority pages - defer loading until after component mounts
const TokenManagementPage = lazyLoad(() => import('@/pages/token-management'));

function AppRouter() {
  // Keyboard shortcuts setup
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);
  
  // Memoize the callbacks to prevent unnecessary re-renders
  const showHelpModal = useCallback(() => setIsHelpModalOpen(true), []);
  const hideHelpModal = useCallback(() => setIsHelpModalOpen(false), []);
  const { shortcuts } = useKeyboardShortcuts(showHelpModal);

  return (
    <>
      {/* Use ErrorBoundary to catch any rendering errors in routes */}
      <ErrorBoundary>
        {/* Use Suspense to handle loading states for lazy-loaded components */}
        <Suspense fallback={<PageLoading />}>
          <Switch>
            <Route path="/login">
              <LoginPage />
            </Route>
            <Route path="/logout">
              <LogoutPage />
            </Route>
            <Route path="/dashboard">
              <PrivateRoute component={DashboardPage} />
            </Route>
            <Route path="/settings">
              <PrivateRoute component={Settings} />
            </Route>
            <Route path="/heatmap">
              <PrivateRoute component={HeatmapPage} />
            </Route>
            <Route path="/digest">
              <PrivateRoute component={DigestPage} />
            </Route>
            {/* Mailbox pages */}
            <Route path="/archive">
              <PrivateRoute component={ArchivePage} />
            </Route>
            <Route path="/trash">
              <PrivateRoute component={TrashPage} />
            </Route>
            <Route path="/snoozed">
              <PrivateRoute component={SnoozedPage} />
            </Route>
            <Route path="/important">
              <PrivateRoute component={ImportantPage} />
            </Route>
            <Route path="/achievements">
              <AchievementsPage />
            </Route>
            <Route path="/token-management">
              <PrivateRoute component={TokenManagementPage} />
            </Route>
            <Route path="/admin/circuit-breakers">
              <PrivateRoute component={CircuitBreakersPage} />
            </Route>
            <Route path="/admin/memory-monitor">
              <PrivateRoute component={MemoryMonitorPage} />
            </Route>

            {/* Fallback Route */}
            <Route>
              <PrivateRoute component={NotFound} />
            </Route>
          </Switch>
        </Suspense>
      </ErrorBoundary>

      {/* Keyboard shortcuts help modal */}
      <KeyboardShortcutsHelp
        shortcuts={shortcuts}
        isOpen={isHelpModalOpen}
        onClose={hideHelpModal}
      />
    </>
  );
}

function AppProviders({ children }: { children: React.ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <ThemeProvider>
          <FirebaseAuthErrorBoundary>
            <UserSettingsProvider>
              <NetworkStatusProvider>
                <EmailListProvider>
                  <EmailDetailProvider>
                    <AppInitializer />
                    {children}
                    <TokenExpirationHandler />
                    <Toaster />
                  </EmailDetailProvider>
                </EmailListProvider>
              </NetworkStatusProvider>
            </UserSettingsProvider>
          </FirebaseAuthErrorBoundary>
        </ThemeProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

function App() {
  return (
    <HelmetProvider>
      <ErrorBoundary>
        <AppProviders>
          <TooltipProvider>
            <AppRouter />
          </TooltipProvider>
        </AppProviders>
      </ErrorBoundary>
    </HelmetProvider>
  );
}

export default App;
