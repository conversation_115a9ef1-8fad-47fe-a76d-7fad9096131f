/**
 * Priority Color Utility
 *
 * This utility provides consistent color coding for email priorities
 * across the application, supporting both text-based and numeric priorities.
 * Colors are optimized for both light and dark modes using theme-aware functions.
 */

import { getThemeAdjustedColor, isDarkMode } from './utils';

// Type for priority color map
type PriorityColorMap = {
  [key: string]: string;
};

// Function to get HSL color values from CSS variables
export const getPriorityColor = (priority: string): string => {
  // Default fallback colors in hex format
  const fallbacks = {
    high: isDarkMode() ? '#dc2626' : '#ef4444', // red-500/600
    medium: isDarkMode() ? '#d97706' : '#f59e0b', // amber-500/600
    low: isDarkMode() ? '#10b981' : '#059669', // emerald-500/600
    none: isDarkMode() ? '#6b7280' : '#9ca3af', // gray-500/400
  };

  if (!priority) return fallbacks.none;

  switch (priority.toLowerCase()) {
    case 'high':
      return fallbacks.high;
    case 'medium':
      return fallbacks.medium;
    case 'low':
      return fallbacks.low;
    default:
      return fallbacks.none;
  }
};

// Function to get a lighter shade of priority color for backgrounds
export const getLighterColor2 = (priority: string): string => {
  const color = getPriorityColor(priority);
  const isDark = isDarkMode();

  return getThemeAdjustedColor(color, isDark);
};

// We'll keep this for backward compatibility
const PRIORITY_COLORS: PriorityColorMap = {
  high: getPriorityColor('high'),
  medium: getPriorityColor('medium'),
  low: getPriorityColor('low'),
  none: getPriorityColor('none'),
};

/**
 * Get a text color that will be readable on the given background color
 * This function is optimized for both light and dark mode with enhanced contrast
 *
 * @param bgColor The background color as a hex string
 * @returns Appropriate foreground color with good contrast for the current theme
 */
export function getContrastTextColor(bgColor: string): string {
  // Strip the hash if it exists
  const color = bgColor.charAt(0) === '#' ? bgColor.substring(1, 7) : bgColor;

  try {
    // Convert hex to RGB
    const r = Number.parseInt(color.substring(0, 2), 16); // Red
    const g = Number.parseInt(color.substring(2, 4), 16); // Green
    const b = Number.parseInt(color.substring(4, 6), 16); // Blue

    if (Number.isNaN(r) || Number.isNaN(g) || Number.isNaN(b)) {
      return isDarkMode() ? '#ffffff' : '#000000';
    }

    // Calculate luminance - https://www.w3.org/TR/WCAG20-TECHS/G17.html
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    const isDark = isDarkMode();

    // Dynamic threshold based on theme
    const threshold = isDark ? 0.7 : 0.5;

    if (luminance > threshold) {
      // Use a darker foreground for bright backgrounds
      return isDark ? '#000000' : '#000000';
    }
    // Use a lighter foreground for dark backgrounds
    return isDark ? '#ffffff' : '#ffffff';
  } catch (_error) {
    // Fallback to safe defaults if any calculation fails
    return isDarkMode() ? '#ffffff' : '#000000';
  }
}

/**
 * Get a modified version of a color (lighter or darker) based on theme
 * This enhanced version uses the getThemeAdjustedColor utility for better
 * theme-aware color handling with improved contrast in both modes.
 *
 * @param color The base color as a hex string
 * @param percent How much to lighten/darken (0-100)
 * @returns Modified color as a hex string
 */
export function getLighterColor(color: string, percent = 40): string {
  // First, ensure the color is theme-adjusted for proper starting point
  const themeAdjustedColor = getThemeAdjustedColor(color, isDarkMode());

  // Strip the hash if it exists
  const hex =
    themeAdjustedColor.charAt(0) === '#' ? themeAdjustedColor.substring(1, 7) : themeAdjustedColor;

  try {
    // Convert hex to RGB
    let r = Number.parseInt(hex.substring(0, 2), 16);
    let g = Number.parseInt(hex.substring(2, 4), 16);
    let b = Number.parseInt(hex.substring(4, 6), 16);

    if (Number.isNaN(r) || Number.isNaN(g) || Number.isNaN(b)) {
      return isDarkMode() ? '#555555' : '#cccccc';
    }

    // Calculate luminance to determine appropriate action
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    const isDark = isDarkMode();

    // In dark mode:
    // - Darken very bright colors to reduce eye strain
    // - Lighten dark colors with lower intensity to prevent them from becoming too bright
    //
    // In light mode:
    // - Simply lighten all colors as before

    if (isDark) {
      if (luminance > 0.7) {
        // For very bright colors in dark mode, darken to reduce eye strain
        const darkModeDarkenPercent = percent * 0.8;
        r = Math.max(0, r - Math.floor(r * (darkModeDarkenPercent / 100)));
        g = Math.max(0, g - Math.floor(g * (darkModeDarkenPercent / 100)));
        b = Math.max(0, b - Math.floor(b * (darkModeDarkenPercent / 100)));
      } else if (luminance < 0.2) {
        // For very dark colors in dark mode, lighten but with a smaller percent
        const darkModeLightenPercent = percent * 1.2; // Increase lightening for dark colors in dark mode
        r = Math.min(255, r + Math.floor((255 - r) * (darkModeLightenPercent / 100)));
        g = Math.min(255, g + Math.floor((255 - g) * (darkModeLightenPercent / 100)));
        b = Math.min(255, b + Math.floor((255 - b) * (darkModeLightenPercent / 100)));
      } else {
        // For mid-range colors in dark mode, make subtle adjustments based on color
        // Slightly lighten to ensure visibility but not too much to cause eye strain
        const darkModeDefaultPercent = percent * 0.5;
        r = Math.min(255, r + Math.floor((255 - r) * (darkModeDefaultPercent / 100)));
        g = Math.min(255, g + Math.floor((255 - g) * (darkModeDefaultPercent / 100)));
        b = Math.min(255, b + Math.floor((255 - b) * (darkModeDefaultPercent / 100)));
      }
    } else {
      // In light mode, lighten colors as before with some refinements
      if (luminance < 0.3) {
        // Very dark colors in light mode should be lightened more
        const lightModeLightenPercent = percent * 1.3;
        r = Math.min(255, r + Math.floor((255 - r) * (lightModeLightenPercent / 100)));
        g = Math.min(255, g + Math.floor((255 - g) * (lightModeLightenPercent / 100)));
        b = Math.min(255, b + Math.floor((255 - b) * (lightModeLightenPercent / 100)));
      } else {
        // Normal lightening for other colors
        r = Math.min(255, r + Math.floor((255 - r) * (percent / 100)));
        g = Math.min(255, g + Math.floor((255 - g) * (percent / 100)));
        b = Math.min(255, b + Math.floor((255 - b) * (percent / 100)));
      }
    }

    // Convert back to hex with padding for single digits
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  } catch (_error) {
    // Fallback on error
    return isDarkMode() ? '#555555' : '#cccccc';
  }
}

export default PRIORITY_COLORS;
