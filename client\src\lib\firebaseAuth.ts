/**
 * Firebase Authentication Functions
 *
 * Contains helper functions for Firebase authentication that interface with both
 * the Firebase SDK and our application's backend via the centralized `apiClient`.
 */

import type { User as FirebaseUser } from 'firebase/auth';
import {
  createUserWithEmailAndPassword,
  EmailAuthProvider,
  signOut as firebaseSignOut,
  updateProfile as firebaseUpdateProfile,
  GoogleAuthProvider,
  getAuth,
  onAuthStateChanged,
  reauthenticateWithCredential,
  sendPasswordResetEmail,
  signInWithEmailAndPassword,
  signInWithPopup,
  updateEmail,
  updatePassword,
} from 'firebase/auth';
import type { User as AppUser } from '@/types/email';
import apiClient from './apiClient';
import { type AppError, createAppError, ErrorCategory, handleError } from './errorHandler';
import { app } from './firebase';
import { queryClient } from './queryClient';

const auth = getAuth(app);

export const onAuthChange = (callback: (user: FirebaseUser | null) => void) => {
  return onAuthStateChanged(auth, callback);
};

export const signInWithEmail = async (email: string, password: string): Promise<FirebaseUser> => {
  try {
    const result = await signInWithEmailAndPassword(auth, email, password);
    const idToken = await result.user.getIdToken();
    await apiClient.post<AppUser>('/api/auth/firebase/verify', { idToken });
    return result.user;
  } catch (error: any) {
    let appError: AppError;
    if (error.code) {
      // Firebase specific error
      let message = 'Failed to sign in. Please check your credentials.';
      if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
        message = 'Invalid email or password.';
      } else if (error.code === 'auth/too-many-requests') {
        message = 'Too many failed login attempts. Please try again later.';
      }
      appError = createAppError(message, ErrorCategory.AUTHENTICATION, { originalError: error });
    } else {
      appError = handleError(error, { operation: 'signInWithEmail' }, false);
    }
    throw appError;
  }
};

export const signUpWithEmail = async (
  email: string,
  password: string,
  displayName: string
): Promise<FirebaseUser> => {
  try {
    const result = await createUserWithEmailAndPassword(auth, email, password);
    await firebaseUpdateProfile(result.user, { displayName });

    const idToken = await result.user.getIdToken();
    await apiClient.post('/api/auth/register', { idToken, email, displayName });

    return result.user;
  } catch (error: any) {
    let appError: AppError;
    if (error.code) {
      let message = 'Failed to create account.';
      if (error.code === 'auth/email-already-in-use') {
        message = 'An account with this email address already exists.';
      } else if (error.code === 'auth/weak-password') {
        message = 'Password is too weak. Please choose a stronger password.';
      }
      appError = createAppError(message, ErrorCategory.VALIDATION, { originalError: error });
    } else {
      appError = handleError(error, { operation: 'signUpWithEmail' }, false);
    }
    throw appError;
  }
};

export const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error: any) {
    let message = 'Failed to send password reset email. Please try again.';
    if (error.code === 'auth/user-not-found') {
      message = 'No account found with this email address.';
    }
    throw createAppError(message, ErrorCategory.AUTHENTICATION, { originalError: error });
  }
};

export const signInWithGoogle = async () => {
  const provider = new GoogleAuthProvider();
  return await signInWithPopup(auth, provider);
};

export const signOut = async () => {
  await apiClient.post('/api/auth/logout');
  await firebaseSignOut(auth);
  queryClient.clear();
};

export const verifyFirebaseToken = async (token: string) => {
  return await apiClient.post('/api/auth/firebase/verify', { token });
};

export const checkAuthStatus = async () => {
  return await apiClient.get('/api/auth/status');
};

export const getCurrentUser = (): Promise<FirebaseUser | null> => {
  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      unsubscribe();
      if (user) {
        resolve(user);
      } else {
        resolve(null);
      }
    });
  });
};

/**
 * Returns the raw Firebase User object, which includes methods like getIdToken.
 * This should be used when you need to interact with the Firebase User API.
 */
export const getCurrentFirebaseUser = (): Promise<FirebaseUser | null> => {
  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      unsubscribe();
      resolve(user);
    });
  });
};

export const isAuthenticated = async (): Promise<boolean> => {
  // Check both Firebase and backend session for a robust status.
  const firebaseUser = await getCurrentFirebaseUser();
  const { isAuthenticated: isSessionValid } = await apiClient.get<{ isAuthenticated: boolean }>(
    '/api/auth/status'
  );
  return !!firebaseUser && isSessionValid;
};

export const updateUserProfileAndBackend = async (
  displayName: string,
  photoURL?: string
): Promise<void> => {
  try {
    const user = auth.currentUser;
    if (!user) throw new Error('No authenticated user found.');

    await firebaseUpdateProfile(user, { displayName, photoURL });
    await apiClient.patch('/api/user/profile', { displayName, photoURL });
  } catch (error: any) {
    throw handleError(error, { operation: 'updateUserProfile' });
  }
};

export const changePassword = async (
  currentPassword: string,
  newPassword: string
): Promise<void> => {
  try {
    const user = auth.currentUser;
    if (!user || !user.email) throw new Error('No authenticated user.');

    const credential = EmailAuthProvider.credential(user.email, currentPassword);
    await reauthenticateWithCredential(user, credential);
    await updatePassword(user, newPassword);
  } catch (error: any) {
    let message = 'Failed to change password.';
    if (error.code === 'auth/wrong-password') {
      message = 'The current password you entered is incorrect.';
    } else if (error.code === 'auth/weak-password') {
      message = 'The new password is too weak.';
    }
    throw createAppError(message, ErrorCategory.VALIDATION, { originalError: error });
  }
};

export const updateUserEmailAndBackend = async (
  currentPassword: string,
  newEmail: string
): Promise<void> => {
  try {
    const user = auth.currentUser;
    if (!user || !user.email) throw new Error('No authenticated user.');

    const credential = EmailAuthProvider.credential(user.email, currentPassword);
    await reauthenticateWithCredential(user, credential);
    await updateEmail(user, newEmail);

    // After successfully updating with Firebase, update our backend.
    await apiClient.patch('/api/user/email', { newEmail, currentPassword });
  } catch (error: any) {
    let message = 'Failed to update email.';
    if (error.code === 'auth/wrong-password') {
      message = 'The current password you entered is incorrect.';
    } else if (error.code === 'auth/email-already-in-use') {
      message = 'This email is already in use by another account.';
    } else if (error.code === 'auth/invalid-email') {
      message = 'The email address is not valid.';
    }
    throw createAppError(message, ErrorCategory.VALIDATION, { originalError: error });
  }
};
