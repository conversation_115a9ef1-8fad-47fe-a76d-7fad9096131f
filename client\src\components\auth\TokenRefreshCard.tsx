/**
 * TokenRefreshCard.tsx
 *
 * A component that displays the current token status and provides
 * options to refresh or force refresh Gmail tokens when they become invalid.
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AlertCircle, CheckCircle, RefreshCw, XCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import apiClient from '@/lib/apiClient';
import type { TokenStatus } from '@/types/token'; // Import from shared types

interface TokenRefreshCardProps {
  provider: 'gmail'; // Can be expanded to other providers
}

// Helper function to fetch token status
const fetchTokenStatus = async (provider: string): Promise<TokenStatus> => {
  return await apiClient.get(`/api/email-providers/${provider}/status`);
};

// Helper function to force refresh a token
const forceRefreshToken = async (
  provider: string
): Promise<{ success: boolean; message?: string }> => {
  return await apiClient.post(`/api/email-providers/${provider}/refresh`);
};

// Status icon component
const StatusIcon = ({ status }: { status: string }) => {
  if (status === 'invalid' || status === 'expired') {
    return <XCircle className="h-8 w-8 text-red-500" />;
  }
  if (status === 'expiring_soon') {
    return <AlertCircle className="h-8 w-8 text-yellow-500" />;
  }
  return <CheckCircle className="h-8 w-8 text-green-500" />;
};

export function TokenRefreshCard({ provider }: TokenRefreshCardProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: tokenStatus,
    isLoading,
    error,
  } = useQuery<TokenStatus, Error>({
    queryKey: ['tokenStatus', provider],
    queryFn: () => fetchTokenStatus(provider),
  });

  const refreshMutation = useMutation({
    mutationFn: () => forceRefreshToken(provider),
    onSuccess: () => {
      toast({
        title: 'Token Refreshed',
        description: 'The token has been successfully refreshed.',
      });
      queryClient.invalidateQueries({ queryKey: ['tokenStatus', provider] });
    },
    onError: (err: Error) => {
      toast({
        title: 'Refresh Failed',
        description: err.message || 'An unexpected error occurred during refresh.',
        variant: 'destructive',
      });
    },
  });

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  if (isLoading) {
    return (
      <Card className="w-full shadow-sm">
        <CardHeader>
          <CardTitle>Token Status ({provider})</CardTitle>
          <CardDescription>Checking token status...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-4">
            <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !tokenStatus) {
    return (
      <Card className="w-full shadow-sm border-red-200 bg-red-50 dark:border-red-900 dark:bg-red-950">
        <CardHeader>
          <CardTitle>Error Fetching Token Status</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error?.message || 'An unknown error occurred.'}</p>
        </CardContent>
      </Card>
    );
  }

  const getStatusColor = () => {
    if (tokenStatus.status === 'invalid' || tokenStatus.status === 'expired')
      return 'border-red-200 bg-red-50 dark:border-red-900 dark:bg-red-950';
    if (tokenStatus.status === 'expiring_soon')
      return 'border-yellow-200 bg-yellow-50 dark:border-yellow-900 dark:bg-yellow-950';
    return 'border-green-200 bg-green-50 dark:border-green-900 dark:bg-green-950';
  };

  return (
    <Card className={`w-full shadow-sm ${getStatusColor()}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="capitalize">{provider} Token Status</CardTitle>
            <CardDescription>
              {tokenStatus.status === 'valid' && `Expires in ${tokenStatus.expiresIn || 'N/A'}`}
              {tokenStatus.status === 'expiring_soon' && 'Expiring soon'}
              {tokenStatus.status === 'expired' && 'Token has expired'}
              {tokenStatus.status === 'invalid' && 'Token is invalid'}
            </CardDescription>
          </div>
          <StatusIcon status={tokenStatus.status} />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="font-medium">Status:</div>
            <div className="capitalize">{tokenStatus.status}</div>
            <div className="font-medium">Expires At:</div>
            <div>{formatDate(tokenStatus.expiresAt)}</div>
            <div className="font-medium">Last Successful Refresh:</div>
            <div>{formatDate(tokenStatus.lastSuccessfulRefresh)}</div>
          </div>
          {tokenStatus.lastError && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Last Recorded Error</AlertTitle>
              <AlertDescription className="text-sm">
                <strong>{tokenStatus.lastError.error}</strong>: {tokenStatus.lastError.message}
                <div className="text-xs mt-1">{formatDate(tokenStatus.lastError.timestamp)}</div>
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button onClick={() => refreshMutation.mutate()} disabled={refreshMutation.isPending}>
          {refreshMutation.isPending ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            'Force Refresh'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
