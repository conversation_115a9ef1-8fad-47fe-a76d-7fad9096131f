import { formatDistanceToNow } from 'date-fns';
import { Calendar, Info, Loader2, RefreshCw, Settings } from 'lucide-react';
import { useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  type ConnectionStatus,
  ConnectionStatusIndicator,
} from '@/components/ui/connection-status-indicator';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useProviderStatus } from '@/hooks/use-provider-status';

interface EnhancedConnectionStatusProps {
  showPopover?: boolean;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Enhanced Connection Status Component
 *
 * Shows Gmail connection status with visual indicator and detailed information.
 * Features a popover with connection details and quick actions.
 */
export function EnhancedConnectionStatus({
  showPopover = true,
  showLabel = false,
  size = 'md',
  className = '',
}: EnhancedConnectionStatusProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Use the centralized hook for all provider data
  const { gmailProvider, isLoading, refetch, error } = useProviderStatus();

  // Determine connection status for indicator
  const status = useMemo((): ConnectionStatus => {
    if (isLoading) return 'refreshing';
    if (error || !gmailProvider) return 'unknown';

    if (gmailProvider.isConnected) {
      return 'connected';
    }
    if (gmailProvider.connectionStatus === 'revoked') {
      return 'revoked';
    }
    if (gmailProvider.tokenInvalid) {
      return 'error';
    }
    if (gmailProvider.tokenExpiresIn && gmailProvider.tokenExpiresIn < 0) {
      return 'expired';
    }
    return 'disconnected';
  }, [isLoading, error, gmailProvider]);

  // Format dates for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Never';
    try {
      const date = new Date(dateString);
      return `${formatDistanceToNow(date, { addSuffix: true })}`;
    } catch (_e) {
      return 'Invalid date';
    }
  };

  // Simple indicator without popover
  if (!showPopover) {
    return (
      <ConnectionStatusIndicator
        status={status}
        showText={showLabel}
        size={size}
        className={className}
        lastUpdated={gmailProvider?.lastVerified}
        error={error?.message || gmailProvider?.tokenError || gmailProvider?.lastApiError}
      />
    );
  }

  // Full indicator with popover
  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="p-1 h-auto" aria-label="Connection Status">
          <ConnectionStatusIndicator
            status={status}
            showText={showLabel}
            size={size}
            className={className}
            inline
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center">
              <ConnectionStatusIndicator status={status} showText size="sm" className="mr-2" />
              Gmail Connection
            </CardTitle>
            <CardDescription>
              {gmailProvider?.email || 'No email account connected'}
            </CardDescription>
          </CardHeader>
          <CardContent className="pb-3 space-y-2">
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-1.5 text-muted-foreground">
                <RefreshCw className="h-3 w-3" />
                <span>Status:</span>
              </div>
              <div className="text-xs capitalize">{status}</div>

              <div className="flex items-center gap-1.5 text-muted-foreground">
                <Calendar className="h-3 w-3" />
                <span>Last verified:</span>
              </div>
              <div className="text-xs">{formatDate(gmailProvider?.lastVerified)}</div>

              {gmailProvider?.tokenExpires && (
                <>
                  <div className="flex items-center gap-1.5 text-muted-foreground">
                    <Settings className="h-3 w-3" />
                    <span>Token expires:</span>
                  </div>
                  <div className="text-xs">{formatDate(gmailProvider.tokenExpires)}</div>
                </>
              )}
            </div>

            {(error || gmailProvider?.tokenError || gmailProvider?.lastApiError) && (
              <div className="mt-2 text-xs p-2 bg-red-50 dark:bg-red-950/30 text-red-700 dark:text-red-300 rounded-md flex items-start">
                <Info className="h-3 w-3 mt-0.5 mr-1 flex-shrink-0" />
                <span>
                  {error?.message || gmailProvider?.tokenError || gmailProvider?.lastApiError}
                </span>
              </div>
            )}
          </CardContent>
          <CardFooter className="pt-2 border-t justify-between">
            <Button
              size="sm"
              variant="outline"
              className="h-8 text-xs"
              onClick={() => refetch()}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              ) : (
                <RefreshCw className="h-3 w-3 mr-1" />
              )}
              Refresh
            </Button>

            <Button
              size="sm"
              variant="outline"
              className="h-8 text-xs"
              onClick={() => {
                window.location.href = '/token-management';
              }}
            >
              <Settings className="h-3 w-3 mr-1" />
              Manage
            </Button>
          </CardFooter>
        </Card>
      </PopoverContent>
    </Popover>
  );
}
