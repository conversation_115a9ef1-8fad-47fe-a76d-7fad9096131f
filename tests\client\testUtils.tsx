export const createQueryWrapper = () => {
  const QueryWrapper = ({ children }: { children: React.ReactNode }) => {
    const { QueryClient, QueryClientProvider } = require('@tanstack/react-query');
    const queryClient = new QueryClient({
      defaultOptions: { queries: { retry: false, cacheTime: 0 } },
    });
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
  return QueryWrapper;
}; 