import type {
  Achievement as AchievementSchema,
  Email as EmailSchema,
  Settings as SettingsSchema,
  Stats as StatsSchema,
  User as UserSchema,
} from '@shared/schema';

// Use the schema Email type directly to avoid type conflicts
export type Email = EmailSchema;
// Extend the User type to include tier and additional properties
export type User = UserSchema & {
  tier?: 'free' | 'pro'; // Add tier property that might be missing in some contexts
  nextSync: Date | null;
};
export type Settings = SettingsSchema;
export type Achievement = AchievementSchema;
export type Stats = StatsSchema;

export interface EmailFilters {
  status: 'all' | 'read' | 'unread' | 'archived' | 'trashed' | 'important' | 'snoozed';
  priority: 'all' | 'high' | 'medium' | 'low';
  categories: string[];
  timeRange: 'all' | 'today' | 'this_week' | 'this_month' | 'custom';
  customDateRange?: {
    from: Date | null;
    to: Date | null;
  };
}

export interface InboxProgress {
  total: number;
  processed: number;
  cleared: number; // For backward compatibility
  percentage: number;
}

export interface EmailState {
  emails: Email[];
  filteredEmails: Email[];
  selectedEmail: Email | null;
  isLoading: boolean;
  error: Error | null;
  filters: EmailFilters;
  searchQuery: string;
  inboxProgress: InboxProgress;
}

export interface PaginatedEmailsResponse {
  emails: Email[];
  totalPages: number;
  totalEmails: number;
  currentPage: number;
}

export interface ProviderStatus {
  isConnected: boolean;
  provider: string;
  email?: string;
  connectionVerified?: boolean;
  connectionStatus?: string;
  lastSynced?: string | null;
  lastVerified?: string | null;
  tokenExpires?: string | null;
  tokenExpiresIn?: number | null;
  tokenInvalid?: boolean;
  tokenError?: string | null;
  lastApiError?: string | null;
  lastRefreshedAt?: string | null;
  lastConnectionAttempt?: string | null;
  refreshAttempted?: boolean;
  refreshSucceeded?: boolean;
  refreshError?: string | null;
}
