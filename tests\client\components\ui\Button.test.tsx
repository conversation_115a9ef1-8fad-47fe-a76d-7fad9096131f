import { fireEvent, render, screen } from '@testing-library/react';
import { Button } from '@/components/ui/button';
import '@testing-library/jest-dom';

describe('UI/Button component', () => {
  it('renders different variants', () => {
    render(
      <>
        <Button data-testid="btn-default">Default</Button>
        <Button variant="secondary" data-testid="btn-secondary">Secondary</Button>
        <Button variant="destructive" data-testid="btn-destructive">Destructive</Button>
      </>
    );

    expect(screen.getByTestId('btn-default')).toHaveClass('bg-primary');
    expect(screen.getByTestId('btn-secondary')).toHaveClass('bg-secondary');
    expect(screen.getByTestId('btn-destructive')).toHaveClass('bg-destructive');
  });

  it('fires onClick handler', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click</Button>);

    fireEvent.click(screen.getByRole('button', { name: /click/i }));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('honours "disabled" prop', () => {
    const handleClick = jest.fn();
    render(
      <Button disabled onClick={handleClick} data-testid="btn-disabled">
        Disabled
      </Button>
    );
    const btn = screen.getByTestId('btn-disabled');
    expect(btn).toBeDisabled();
    fireEvent.click(btn);
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('shows content while "loading" (simulated with disabled + text)', () => {
    render(
      <Button disabled>Loading…</Button>
    );
    expect(screen.getByRole('button', { name: /loading/i })).toBeInTheDocument();
  });
}); 