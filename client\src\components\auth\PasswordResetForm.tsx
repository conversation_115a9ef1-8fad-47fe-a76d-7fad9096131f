import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Mail, Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

const passwordResetSchema = z.object({
  email: z.string().email('Please enter a valid email address.'),
});

export type PasswordResetFormValues = z.infer<typeof passwordResetSchema>;

interface PasswordResetFormProps {
  onSubmit: (values: PasswordResetFormValues) => Promise<void>;
  isSubmitting: boolean;
  resetSent: boolean;
}

export function PasswordResetForm({ onSubmit, isSubmitting, resetSent }: PasswordResetFormProps) {
  const form = useForm<PasswordResetFormValues>({
    resolver: zod<PERSON><PERSON>olver(passwordResetSchema),
    defaultValues: {
      email: '',
    },
  });

  if (resetSent) {
    return (
      <div className="text-center p-4 bg-green-50 dark:bg-green-950/50 text-green-700 dark:text-green-300 rounded-md">
        <h3 className="font-medium">Password Reset Email Sent</h3>
        <p className="text-sm mt-2">
          If an account exists for the email provided, a password reset link has been sent. Please
          check your inbox (and spam folder).
        </p>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                <Mail className="h-4 w-4 mr-2" /> Email
              </FormLabel>
              <FormControl>
                <Input type="email" placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Send Reset Link
        </Button>
      </form>
    </Form>
  );
} 