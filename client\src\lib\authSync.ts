/**
 * Authentication State Synchronization
 *
 * This manager runs in the background to periodically check for and repair
 * common authentication issues, ensuring the client's state remains
 * synchronized with the backend. It is a stateless driver that uses
 * centralized modules for diagnostics and repair.
 */
import { onAuthStateChanged, signOut } from 'firebase/auth';
import apiClient from './apiClient';
import { attemptAuthRepair, diagnoseAuthentication } from './authFixer';
import { auth } from './firebase';
import { getCurrentFirebaseUser } from './firebaseAuth';
import logger from './logger';
import { queryClient } from './queryClient';

interface AuthSyncConfig {
  syncInterval: number; // in milliseconds
}

const DEFAULT_CONFIG: AuthSyncConfig = {
  syncInterval: 5 * 60 * 1000, // 5 minutes
};

class AuthSyncManager {
  private config: AuthSyncConfig;
  private syncIntervalId: ReturnType<typeof setInterval> | null = null;
  private syncInProgress = false;
  private unsubscribeAuthState: () => void;
  private lastAuthStateChangeTime = 0;
  private authStateChangeDebounceMs = 2000; // Prevent rapid auth state changes

  constructor(config: Partial<AuthSyncConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.unsubscribeAuthState = () => {};
  }

  public start(): void {
    if (this.syncIntervalId) {
      logger.warn('[AuthSync] Manager already started.', {
        message: 'Attempted to start an already running AuthSyncManager.',
      });
      return;
    }

    logger.info(
      '[AuthSync] Starting authentication synchronization manager. Initializing periodic auth state checks.'
    );

    // Initial check
    this.syncAuthState().catch((err) => {
      logger.error('[AuthSync] Initial sync failed', err);
    });

    // Set up periodic sync
    this.syncIntervalId = setInterval(() => this.syncAuthState(), this.config.syncInterval);

    // Listen for Firebase auth state changes with debouncing
    this.unsubscribeAuthState = onAuthStateChanged(auth, (user) => {
      const now = Date.now();
      if (now - this.lastAuthStateChangeTime < this.authStateChangeDebounceMs) {
        logger.debug('[AuthSync] Auth state change debounced to prevent rapid invalidations');
        return;
      }
      this.lastAuthStateChangeTime = now;

      logger.info('[AuthSync] Firebase auth state changed.', { hasUser: !!user });
      // When auth state changes (login/logout), invalidate all queries to force a UI refresh.
      // This is the simplest way to ensure the entire app reflects the new auth state.
      queryClient.invalidateQueries().then(() => {
        logger.info(
          '[AuthSync] All queries invalidated due to auth state change. Forcing UI and data refresh.'
        );
      });
    });
  }

  public stop(): void {
    if (this.syncIntervalId) {
      clearInterval(this.syncIntervalId);
      this.syncIntervalId = null;
      logger.info(
        '[AuthSync] Stopped authentication synchronization manager. Cleared periodic sync interval.'
      );
    }
    this.unsubscribeAuthState();
  }

  public async forceSync(): Promise<void> {
    logger.info('[AuthSync] Forcing authentication synchronization. Manual sync triggered.');
    await this.syncAuthState(true);
  }

  private async syncAuthState(force = false): Promise<void> {
    if (this.syncInProgress && !force) {
      logger.debug(
        '[AuthSync] Sync already in progress. Skipping. Skipping sync to avoid race conditions.'
      );
      return;
    }

    this.syncInProgress = true;
    logger.debug(
      '[AuthSync] Starting authentication state sync. Checking for inconsistencies between client and server auth state.'
    );

    try {
      const firebaseUser = await getCurrentFirebaseUser();
      const diagnostics = await diagnoseAuthentication(firebaseUser);
      logger.info('[AuthSync] Diagnostics complete.', {
        isSessionValid: diagnostics.isSessionValid,
        isGmailConnected: diagnostics.isGmailConnected,
        canAutoFix: diagnostics.canAutoFix,
        action: diagnostics.recommendedAction,
      });

      if (diagnostics.canAutoFix) {
        logger.info(
          '[AuthSync] Attempting automatic repair. Diagnostics recommended an automatic fix.'
        );
        const result = await attemptAuthRepair(firebaseUser);
        logger.info(
          `[AuthSync] Repair attempt finished: ${result.message} Automatic repair process completed.`
        );
      } else if (!diagnostics.hasFirebaseToken) {
        // If there's no Firebase token but the session somehow still thinks it's valid,
        // force a full logout to clean up the state.
        try {
          const { isAuthenticated } = await apiClient.get<{ isAuthenticated: boolean }>('/api/auth/status');
          
          if (isAuthenticated) {
            logger.warn(
              '[AuthSync] Mismatch detected: No Firebase token but session appears valid. Forcing logout.'
            );
            await apiClient.post('/api/auth/logout');
            await signOut(auth);
            await queryClient.invalidateQueries();
          }
        } catch (error) {
          // If we can't check auth status, assume we need to clean up
          logger.warn('[AuthSync] Could not check auth status, cleaning up session to be safe');
          await signOut(auth);
          await queryClient.invalidateQueries();
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error('[AuthSync] Sync failed.', {
        error: errorMessage,
        stack: (error as Error).stack,
      });
    } finally {
      this.syncInProgress = false;
      logger.debug(
        '[AuthSync] Authentication state sync finished. Completed check for client and server auth state inconsistencies.'
      );
    }
  }
}

const authSyncManager = new AuthSyncManager();

export default authSyncManager;
export type { AuthSyncConfig };
