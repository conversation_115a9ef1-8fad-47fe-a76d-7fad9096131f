# InboxZeroAI Testing Blueprint

## Overview
This blueprint provides a structured approach to testing your full-stack email management application, organized by layer and priority.

## Testing Strategy

### Priority Levels
- **P1 (Critical)**: Core authentication, data integrity, security
- **P2 (High)**: Main user flows, API endpoints, business logic
- **P3 (Medium)**: UI components, error handling, edge cases
- **P4 (Low)**: Performance, integration scenarios

---

## 1. Server-Side Testing (`tests/server/`)

### 1.1 Authentication Module Tests (`tests/server/auth/`)

#### **P1: Core Authentication Logic** *DONE*
```
auth/google.test.ts
- OAuth flow initiation
- Token exchange with Google
- Profile data extraction
- Error handling for invalid tokens

auth/user.service.test.ts
- findOrCreateUser with existing user
- findOrCreateUser with new user
- Email-based user matching
- Profile update logic

auth/firebase.test.ts
- Firebase ID token verification
- UID-based user lookup
- Email-based account linking
- Duplicate account prevention

auth/session.test.ts
- Session creation and storage
- Redis session store (with fallback)
- Session expiration
- Session cleanup
```

#### **P2: Authentication Routes** *DONE*
```
auth/routes.test.ts
- GET /api/auth/status (authenticated vs unauthenticated)
- POST /api/auth/google (OAuth initiation)
- GET /api/auth/google/callback (successful callback)
- POST /api/auth/logout (session cleanup)
- POST /api/auth/firebase/verify (token verification)
- POST /api/auth/register (user registration)
- POST /api/auth/refresh-token (token refresh)
- GET /api/auth/token-status (token validation)
- POST /api/auth/fix-token-issues (token repair)
- GET /api/auth/csrf (CSRF token generation)
```

### 1.2 Middleware Tests (`tests/server/middleware/`)

#### **P1: Security Middleware** *DONE*
```
middleware/simpleAuth.test.ts
- Valid session authentication
- Invalid session rejection
- req.user population
- req.authMethod setting

middleware/csrf.test.ts
- CSRF token generation
- Valid token validation
- Invalid token rejection
- Firebase endpoint bypass logic
- Express route mounting edge cases

middleware/admin.test.ts
- Admin role verification (user.role === 'admin')
- Non-admin rejection
- Missing user handling
```

#### **P2: Rate Limiting & Security** *DONE*
```
middleware/security.test.ts
- Rate limiting order (strict before general)
- CSP header enforcement
- Request ID generation (uuidv4)
- Security headers validation
```

### 1.3 Services Tests (`tests/server/services/`)

#### **P1: Core Services** *DONE*
```
services/tokenService.test.ts
- Token parsing and validation
- Token refresh logic
- Token expiration detection
- Secure token storage

services/redis.test.ts
- Redis connection establishment
- Fallback to in-memory store
- Connection error handling
- Command execution (hGetAll, hIncrBy)
```

#### **P2: Business Logic Services** *DONE*
```
services/emailService.test.ts (if exists)
- Email processing logic
- Triage automation
- Summarization features

services/circuitBreaker.test.ts
- Circuit state transitions (closed/open/half-open)
- Redis-based state storage
- Failure threshold management
- Recovery logic
```

### 1.4 API Routes Tests (`tests/server/routes/`)

#### **P2: Feature Routes** *DONE*
```
routes/emails.test.ts
- GET /api/emails (list with pagination)
- POST /api/emails (create)
- PUT /api/emails/:id (update)
- DELETE /api/emails/:id (delete)
- Authentication requirements
- Input validation

routes/settings.test.ts
- GET /api/settings (user settings)
- PUT /api/settings (update settings)
- Settings validation
- User isolation (can't access other user's settings)
```

### 1.5 Database Tests (`tests/server/db/`)

#### **P1: Database Operations** *DONE*
```
db/connection.test.ts
- PostgreSQL connection pooling
- Connection error handling
- Drizzle ORM integration

db/migrations.test.ts
- Schema creation
- User role column addition
- Migration rollback scenarios
```

---

## 2. Client-Side Testing (`tests/client/`)

### 2.1 Authentication Tests (`tests/client/auth/`)

#### **P1: Auth Context & Flow** *DONE*
```
auth/AuthContext.test.tsx
- Initial authentication state
- Login state updates
- Logout state clearing
- Error state management
- Token expiration handling

auth/AuthSyncManager.test.ts
- Firebase auth state monitoring
- Backend session synchronization
- React Query cache invalidation
- Non-disruptive UI updates

auth/PrivateRoute.test.tsx
- Authenticated user access
- Unauthenticated redirect to /login
- Loading state handling
```

#### **P2: Auth Components** *DONE*
```
components/auth/LoginPage.test.tsx
- Google OAuth button functionality
- Firebase authentication integration
- Error message display
- Loading states

components/auth/TokenExpirationHandler.test.tsx
- Token expiration detection
- Modal display logic
- Refresh token flow
- Error recovery options
```

### 2.2 API Client Tests (`tests/client/api/`)

#### **P1: Core API Client** *DONE*
```
lib/apiClient.test.ts
- Firebase ID token injection
- CSRF token handling
- Authentication failure detection
- Retry logic with exponential backoff
- Error parsing and standardization
- Request/response interceptors
```

### 2.3 Component Tests (`tests/client/components/`)

#### **P2: Layout Components** *DONE*
```
layout/AppLayout.test.tsx
- Header rendering
- Sidebar integration
- React Helmet meta tag management
- Responsive behavior

layout/AppSidebar.test.tsx
- Navigation link rendering
- Active route highlighting
- User dropdown functionality
- Logout functionality
```

#### **P3: UI Components** *DONE*
```
ui/Button.test.tsx
- Different button variants
- Click event handling
- Disabled state behavior
- Loading state display

ui/Input.test.tsx
- Value input and updates
- Validation error display
- Required field handling
- Accessibility attributes

ui/Card.test.tsx
- Content rendering
- Styling variations
- Responsive behavior
```

#### **P2: Feature Components** *DONE*
```
email/EmailList.test.tsx
- Email data rendering
- Pagination functionality
- Loading states
- Empty state handling

email/EmailItem.test.tsx
- Email data display
- Action button functionality (delete, archive, etc.)
- Read/unread states
```

### 2.4 Hooks Tests (`tests/client/hooks/`)

#### **P2: Custom Hooks** *DONE*
```
hooks/useAuth.test.ts
- Authentication state access
- Login/logout functions
- Error state management
- Loading states

hooks/useProviderStatus.test.ts
- Provider connection status
- User-specific data fetching
- Cache invalidation on user change
- Error handling
```

### 2.5 Context Tests (`tests/client/contexts/`)

#### **P2: React Contexts** *DONE*
```
contexts/MemoryMonitorContext.test.tsx
- Memory usage data fetching
- State management
- Provider functionality
- Error boundaries

contexts/ThemeContext.test.tsx
- Theme switching
- Persistence
- Default theme loading
```

---

## 3. Integration Tests (`tests/integration/`)

### 3.1 Authentication Flow Tests

#### **P1: End-to-End Auth Flows**
```
integration/auth-flow.test.ts
- Complete Google OAuth flow
- Firebase token verification flow
- Session persistence across requests
- Logout and session cleanup
- Token refresh scenarios
```

### 3.2 API Integration Tests

#### **P2: Client-Server Integration**
```
integration/api-integration.test.ts
- API client with real backend
- Authentication header injection
- CSRF protection end-to-end
- Error handling across layers
- Rate limiting behavior
```

### 3.3 Database Integration Tests

#### **P2: Data Flow Tests**
```
integration/database-flow.test.ts
- User creation through auth flow
- Email CRUD operations
- Settings persistence
- Data isolation between users
```

---

## 4. Security Tests (`tests/security/`)

### 4.1 Authentication Security

#### **P1: Security Validation**
```
security/auth-security.test.ts
- Session hijacking prevention
- CSRF attack prevention
- Token tampering detection
- Unauthorized access attempts
- Admin privilege escalation attempts
```

### 4.2 Input Validation

#### **P1: Input Security**
```
security/input-validation.test.ts
- SQL injection prevention
- XSS attack prevention
- Input sanitization
- File upload security (if applicable)
```

---

## 5. Performance Tests (`tests/performance/`)

### 5.1 Load Testing

#### **P4: Performance Validation**
```
performance/api-load.test.ts
- High-concurrency authentication
- Database connection pooling under load
- Redis session store performance
- Rate limiting effectiveness

performance/client-performance.test.ts
- React Query cache performance
- Component render optimization
- Bundle size validation
```

---

## 6. Utility Tests (`tests/utilities/`)

### 6.1 Helper Function Tests

#### **P3: Utility Functions**
```
utils/encryption.test.ts
- Data encryption/decryption
- Key management
- Error handling

utils/errorHandler.test.ts
- Error normalization
- Status code mapping
- Error message sanitization

utils/circuitBreaker.test.ts
- State management
- Failure detection
- Recovery mechanisms
```

---

## 7. Configuration Tests (`tests/config/`)

### 7.1 Environment & Setup

#### **P2: Configuration Validation**
```
config/environment.test.ts
- Environment variable validation
- Multi-line JSON parsing (Firebase config)
- Base64 decoding
- Required variable detection

config/build.test.ts
- Vite configuration validation
- TypeScript compilation
- Path alias resolution
```


### Mock Strategy
- **Database**: Use test database or in-memory SQLite
- **Redis**: Use redis-mock for unit tests, real Redis for integration
- **External APIs**: Mock Google OAuth, Firebase Auth
- **File System**: Use temp directories for file operations

### Test Data Management
- **Fixtures**: Create reusable test data sets
- **Factories**: Build test objects with realistic data
- **Cleanup**: Ensure tests don't interfere with each other

---

## Implementation Priority

### Phase 1 
1. Authentication core logic
2. CSRF and security middleware
3. Database operations
4. API client functionality

### Phase 2 
1. Authentication routes
2. Feature API endpoints
3. Main React components
4. Integration tests

### Phase 3 
1. UI components
2. Error handling
3. Utility functions
4. Edge cases

### Phase 4 
1. Performance tests
2. Load testing
3. Optimization validation

---

## Success Metrics

- **Coverage**: Aim for >80% code coverage
- **Critical Path**: 100% coverage for auth and security
- **Regression Prevention**: All major bugs should have corresponding tests
- **Documentation**: Tests serve as usage examples
- **CI/CD**: All tests must pass before deployment