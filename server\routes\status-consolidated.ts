import { type Request, type Response, Router } from 'express';
import { getDbConnection, testConnection, getPoolStats, resetPoolMetrics, getAdvancedMetrics } from '../db';
import { getEnvVar } from '../lib/environmentValidator';
import { getDatabaseHealthMetrics, isDatabaseHealthy } from '../utils/databaseHealthMonitor';
import { AppError, catchAsync, DatabaseError } from '../utils/errorHandler';
import { storage } from '../storage';
import logger from '../lib/logger';

/**
 * Consolidated Status & Health Routes
 *
 * This router combines the lightweight `/api/health` endpoint from the legacy health.ts
 * with the comprehensive status endpoints from the legacy status.ts file.
 *
 * This consolidation provides a single authoritative implementation for all status
 * and health check endpoints, eliminating the need for multiple separate route files.
 */
const router = Router();

/**
 * GET /api/health or /api/status-consolidated/
 *
 * Lightweight health check endpoint (from legacy health.ts)
 * Simple endpoint that returns server status without heavy operations
 */
router.get('/', (_req: Request, res: Response) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
  });
});

/**
 * GET /api/status/health
 *
 * Enhanced health check endpoint (from legacy status.ts)
 * Returns comprehensive server status information
 */
router.get('/health', (_req: Request, res: Response) => {
  res.json({
    status: 'ok',
    message: 'InboxZeroAI server is running',
    mode: getEnvVar('NODE_ENV'),
    timestamp: new Date().toISOString(),
  });
});

/**
 * GET /api/status/database
 *
 * Check database connectivity by performing a lightweight query.
 * This is useful for verifying that the database is available and responding.
 */
router.get(
  '/database',
  catchAsync(async (_req: Request, res: Response) => {
    const client = await getDbConnection();
    const result = await client.unsafe('SELECT 1 as connected');

    if (result[0]?.connected !== 1) {
      throw new DatabaseError('Database query failed to return expected results');
    }

    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: {
        connected: true,
        latency: 0,
      },
    });
  })
);

/**
 * GET /api/status/full
 *
 * Perform a comprehensive health check of all critical systems.
 * This includes database connectivity, API dependencies, and more.
 */
router.get(
  '/full',
  catchAsync(async (_req: Request, res: Response) => {
    const startTime = Date.now();
    const checks: Record<string, any> = {};
    let overallStatus = 'ok';

    try {
      const client = await getDbConnection();
      // Check database
      try {
        const dbStartTime = Date.now();
        const _result = await client.unsafe('SELECT 1 as connected');
        const dbLatency = Date.now() - dbStartTime;

        checks.database = {
          status: 'ok',
          latency: dbLatency,
        };
      } catch (error) {
        checks.database = {
          status: 'error',
          error: error instanceof Error ? error.message : String(error),
        };
        overallStatus = 'error';
      }

      // Check memory usage
      try {
        const memoryUsage = process.memoryUsage();
        const heapUsedPercentage = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

        checks.memory = {
          status: heapUsedPercentage > 90 ? 'warning' : 'ok',
          used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          percentage: Math.round(heapUsedPercentage),
        };

        if (heapUsedPercentage > 90 && overallStatus === 'ok') {
          overallStatus = 'warning';
        }
      } catch (error) {
        checks.memory = {
          status: 'error',
          error: error instanceof Error ? error.message : String(error),
        };
      }

      // Check task queue status
      try {
        const tasksResult = await client.unsafe(
          'SELECT COUNT(*) as count, status FROM task_queue GROUP BY status'
        );

        const taskStats: Record<string, number> = {};
        tasksResult.forEach((row: any) => {
          taskStats[row.status] = Number.parseInt(row.count, 10);
        });

        const pendingTasks = taskStats.pending || 0;
        const processingTasks = taskStats.processing || 0;

        checks.taskQueue = {
          status: pendingTasks > 100 ? 'warning' : 'ok',
          pendingTasks,
          processingTasks,
          totalTasks: Object.values(taskStats).reduce((sum, count) => sum + count, 0),
        };

        if (pendingTasks > 100 && overallStatus === 'ok') {
          overallStatus = 'warning';
        }
      } catch (error) {
        checks.taskQueue = {
          status: 'error',
          error: error instanceof Error ? error.message : String(error),
        };
      }

      // Add total response time
      const totalTime = Date.now() - startTime;

      // Generate response
      res.json({
        status: overallStatus,
        timestamp: new Date().toISOString(),
        serviceId: 'inbox-zero-api',
        uptime: process.uptime(),
        responseTime: totalTime,
        checks,
      });
    } catch (error) {
      throw new AppError(error instanceof Error ? error.message : 'Failed full health check');
    }
  })
);

/**
 * GET /api/status/status
 *
 * Comprehensive server status
 */
router.get(
  '/status',
  catchAsync(async (_req: Request, res: Response) => {
    const dbHealthy = isDatabaseHealthy();
    const dbMetrics = getDatabaseHealthMetrics();

    // Quick database connectivity test
    let dbConnectable = false;
    let dbError = null;
    try {
      // Add timeout protection for the connection test
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Database connection test timeout')), 8000)
      );

      const connectionTestPromise = testConnection();

      await Promise.race([connectionTestPromise, timeoutPromise]);
      dbConnectable = true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorCode = (error as any)?.code;

      // Log different types of errors appropriately
      if (
        errorCode === 'CONNECTION_DESTROYED' ||
        errorMessage.includes('CONNECTION_DESTROYED') ||
        errorMessage.includes('Database connection unavailable') ||
        errorMessage.includes('Database pool is closing') ||
        errorMessage.includes('timeout')
      ) {
        console.warn('Database connection test failed (expected during shutdown/restart):', {
          message: errorMessage,
          code: errorCode,
        });
      } else {
        console.error('Database connection test failed:', {
          message: errorMessage,
          code: errorCode,
        });
      }

      dbConnectable = false;
      dbError = errorMessage;
    }

    const status = {
      server: {
        status: 'running',
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        nodeVersion: process.version,
        environment: getEnvVar('NODE_ENV') || 'unknown',
      },
      database: {
        healthy: dbHealthy,
        connectable: dbConnectable,
        metrics: dbMetrics,
        ...(dbError && { lastError: dbError }),
      },
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
        rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
      },
    };

    res.json(status);
  })
);

/**
 * GET /api/status/db-health
 *
 * Database-specific health check
 */
router.get(
  '/db-health',
  catchAsync(async (_req: Request, res: Response) => {
    let connectionTestResult = null;
    let connectionError = null;

    try {
      // Add timeout protection for the connection test
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Database health check timeout')), 10000)
      );

      const connectionTestPromise = testConnection();

      await Promise.race([connectionTestPromise, timeoutPromise]);
      connectionTestResult = 'passed';
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorCode = (error as any)?.code;

      // Handle specific connection errors
      if (
        errorCode === 'CONNECTION_DESTROYED' ||
        errorMessage.includes('CONNECTION_DESTROYED') ||
        errorMessage.includes('Database connection unavailable') ||
        errorMessage.includes('Database pool is closing')
      ) {
        connectionTestResult = 'failed';
        connectionError = 'Connection unavailable (expected during restart/shutdown)';
      } else {
        connectionTestResult = 'failed';
        connectionError = errorMessage;
      }
    }

    const healthMetrics = getDatabaseHealthMetrics();

    res.json({
      connectionTest: connectionTestResult,
      ...(connectionError && { connectionError }),
      healthMetrics,
      timestamp: new Date().toISOString(),
    });
  })
);

router.get('/postgres-raw', async (req, res) => {
  try {
    const start = Date.now();
    const client = await getDbConnection();
    const result = await client`select version()`;
    const duration = Date.now() - start;
    // ... existing code ...
  } catch (error) {
    // ... existing code ...
  }
});

router.get('/postgres-pool', async (req, res) => {
  try {
    const start = Date.now();
    const client = await getDbConnection();
    // Drizzle doesn't expose pool stats directly, so we'll use a simple query
    const result = await client`SELECT 1`;
    const duration = Date.now() - start;
    // ... existing code ...
  } catch (error) {
    // ... existing code ...
  }
});

/**
 * GET /api/status/pool-metrics
 * 
 * Enhanced database pool metrics from Phase 1 optimizations
 */
router.get('/pool-metrics', async (req, res) => {
  try {
    const poolStats = getPoolStats();
    
    res.json({
      timestamp: new Date().toISOString(),
      poolStats,
      description: {
        poolMetrics: {
          totalConnections: 'Total connections created since startup',
          activeConnections: 'Currently active connections',
          idleConnections: 'Currently idle connections',
          queuedRequests: 'Requests waiting for connections',
          healthCheckHits: 'Health checks served from cache',
          healthCheckMisses: 'Health checks that required actual database query',
          connectionReuses: 'Times existing connections were reused',
        },
        healthCheckCacheStats: {
          isValid: 'Whether cached health check is still valid',
          lastCheck: 'Timestamp of last health check (ms since epoch)',
          ttl: 'Time-to-live for health check cache (ms)',
          hitRate: 'Percentage of health checks served from cache (0-1)',
        },
        currentPoolConfig: 'Current pool configuration (may vary based on memory pressure)',
      },
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to retrieve pool metrics',
      message: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/status/reset-pool-metrics
 * 
 * Reset pool metrics for monitoring purposes
 */
router.post('/reset-pool-metrics', async (req, res) => {
  try {
    resetPoolMetrics();
    
    res.json({
      message: 'Pool metrics reset successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to reset pool metrics',
      message: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/status/query-metrics
 * 
 * Phase 2: Enhanced query performance metrics and cache statistics
 */
router.get('/query-metrics', async (req, res) => {
  try {
    // Get query metrics from the database storage layer
    const dbStorage = storage as any; // Type assertion for Phase 2 methods
    
    if (typeof dbStorage.getQueryMetrics === 'function') {
      const queryMetrics = dbStorage.getQueryMetrics();
      
      // Calculate cache hit rate
      const totalCacheableQueries = queryMetrics.totalQueries;
      const cacheHitRate = totalCacheableQueries > 0 
        ? (queryMetrics.cachedQueries / totalCacheableQueries * 100).toFixed(2)
        : '0.00';
      
      // Get memory usage for cache
      const memoryUsage = process.memoryUsage();
      
      res.json({
        success: true,
        data: {
          queryPerformance: {
            totalQueries: queryMetrics.totalQueries,
            cachedQueries: queryMetrics.cachedQueries,
            bulkOperations: queryMetrics.bulkOperations,
            optimizedQueries: queryMetrics.optimizedQueries,
            averageQueryTime: Math.round(queryMetrics.averageQueryTime * 100) / 100,
            cacheHitRate: `${cacheHitRate}%`,
          },
          slowQueries: {
            count: queryMetrics.slowQueries.length,
            recentSlowQueries: queryMetrics.slowQueries.slice(-10).map((q: any) => ({
              query: q.query,
              duration: `${q.duration}ms`,
              timestamp: new Date(q.timestamp).toISOString(),
            })),
          },
          cacheStatistics: {
            cacheSize: dbStorage.queryCache?.size || 0,
            maxCacheSize: 1000,
            cacheUtilization: `${((dbStorage.queryCache?.size || 0) / 1000 * 100).toFixed(1)}%`,
          },
          memoryUsage: {
            heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
            heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
            external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`,
          },
          timestamp: new Date().toISOString(),
        },
        message: 'Query metrics retrieved successfully'
      });
    } else {
      res.json({
        success: true,
        data: {
          message: 'Query metrics not available - using legacy storage implementation',
          timestamp: new Date().toISOString(),
        }
      });
    }
  } catch (error) {
    logger.error('[STATUS] Failed to get query metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve query metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/status/reset-query-metrics
 * 
 * Phase 2: Reset query performance metrics for monitoring
 */
router.post('/reset-query-metrics', async (req, res) => {
  try {
    const dbStorage = storage as any; // Type assertion for Phase 2 methods
    
    if (typeof dbStorage.resetQueryMetrics === 'function') {
      dbStorage.resetQueryMetrics();
      
      res.json({
        success: true,
        data: {
          message: 'Query metrics reset successfully',
          timestamp: new Date().toISOString(),
        }
      });
    } else {
      res.json({
        success: false,
        error: 'Query metrics reset not available - using legacy storage implementation'
      });
    }
  } catch (error) {
    logger.error('[STATUS] Failed to reset query metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset query metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/status/cache-stats
 * 
 * Phase 2: Detailed cache performance statistics
 */
router.get('/cache-stats', async (req, res) => {
  try {
    const dbStorage = storage as any; // Type assertion for Phase 2 methods
    
    if (dbStorage.queryCache) {
      const cache = dbStorage.queryCache;
      const now = Date.now();
      
      // Analyze cache entries
      const entries = Array.from(cache.entries()) as Array<[string, any]>;
      const validEntries = entries.filter(([_, entry]: [string, any]) => now - entry.timestamp < entry.ttl);
      const expiredEntries = entries.length - validEntries.length;
      
      // Group by operation type
      const operationStats = entries.reduce((acc: Record<string, { count: number; avgAge: number; totalAge: number }>, [key, entry]: [string, any]) => {
        const operation = key.split(':')[0];
        if (!acc[operation]) {
          acc[operation] = { count: 0, avgAge: 0, totalAge: 0 };
        }
        acc[operation].count++;
        const age = now - entry.timestamp;
        acc[operation].totalAge += age;
        acc[operation].avgAge = acc[operation].totalAge / acc[operation].count;
        return acc;
      }, {} as Record<string, { count: number; avgAge: number; totalAge: number }>);
      
      // Format operation stats
      const formattedStats = Object.entries(operationStats).map(([operation, stats]: [string, any]) => ({
        operation,
        cachedQueries: stats.count,
        averageAge: `${Math.round(stats.avgAge / 1000)}s`,
      }));
      
      res.json({
        success: true,
        data: {
          cacheOverview: {
            totalEntries: entries.length,
            validEntries: validEntries.length,
            expiredEntries,
            cacheEfficiency: `${((validEntries.length / entries.length) * 100).toFixed(1)}%`,
          },
          operationBreakdown: formattedStats,
          cacheHealth: {
            status: expiredEntries > entries.length * 0.3 ? 'needs_cleanup' : 'healthy',
            recommendation: expiredEntries > entries.length * 0.3 
              ? 'Consider reducing cache TTL or increasing cleanup frequency'
              : 'Cache is performing well',
          },
          timestamp: new Date().toISOString(),
        },
        message: 'Cache statistics retrieved successfully'
      });
    } else {
      res.json({
        success: false,
        error: 'Cache statistics not available - using legacy storage implementation'
      });
    }
  } catch (error) {
    logger.error('[STATUS] Failed to get cache stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve cache statistics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/status/advanced-metrics
 * 
 * Phase 3: Advanced connection quality and resource usage metrics
 */
router.get('/advanced-metrics', async (req, res) => {
  try {
    const advancedMetrics = getAdvancedMetrics();
    
    res.json({
      success: true,
      data: {
        connectionQuality: {
          averageLatency: `${Math.round(advancedMetrics.connectionQuality.averageLatency)}ms`,
          errorRate: `${(advancedMetrics.connectionQuality.errorRate * 100).toFixed(2)}%`,
          uptime: `${Math.round((Date.now() - advancedMetrics.connectionQuality.uptime) / 1000)}s`,
          consecutiveFailures: advancedMetrics.connectionQuality.consecutiveFailures,
          performanceScore: Math.round(advancedMetrics.connectionQuality.performanceScore),
          lastHealthCheck: advancedMetrics.connectionQuality.lastHealthCheck > 0 
            ? new Date(advancedMetrics.connectionQuality.lastHealthCheck).toISOString()
            : 'Never',
        },
        resourceUsage: {
          memoryUsage: `${Math.round(advancedMetrics.resourceUsage.memoryUsageMB)}MB`,
          connectionPoolSize: advancedMetrics.resourceUsage.connectionPoolSize,
          activeQueries: advancedMetrics.resourceUsage.activeQueries,
          queueDepth: advancedMetrics.resourceUsage.queueDepth,
          cacheHitRate: `${(advancedMetrics.resourceUsage.cacheHitRate * 100).toFixed(1)}%`,
          resourcePressure: advancedMetrics.resourceUsage.resourcePressure,
        },
        poolMetrics: {
          totalConnections: advancedMetrics.poolMetrics.totalConnections,
          activeConnections: advancedMetrics.poolMetrics.activeConnections,
          idleConnections: advancedMetrics.poolMetrics.idleConnections,
          queuedRequests: advancedMetrics.poolMetrics.queuedRequests,
          connectionLatency: `${Math.round(advancedMetrics.poolMetrics.connectionLatencyMs)}ms`,
          poolUtilization: `${Math.round(advancedMetrics.poolMetrics.poolUtilization)}%`,
          connectionErrors: advancedMetrics.poolMetrics.connectionErrors,
          poolResizes: advancedMetrics.poolMetrics.poolResizes,
          resourceCleanups: advancedMetrics.poolMetrics.resourceCleanups,
          memoryPressureAdjustments: advancedMetrics.poolMetrics.memoryPressureAdjustments,
          healthCheckHits: advancedMetrics.poolMetrics.healthCheckHits,
          healthCheckMisses: advancedMetrics.poolMetrics.healthCheckMisses,
          connectionReuses: advancedMetrics.poolMetrics.connectionReuses,
        },
        timestamp: new Date().toISOString(),
      },
      message: 'Advanced metrics retrieved successfully'
    });
  } catch (error) {
    logger.error('[STATUS] Failed to get advanced metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve advanced metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/status/connection-quality
 * 
 * Phase 3: Detailed connection quality analysis
 */
router.get('/connection-quality', async (req, res) => {
  try {
    const poolStats = getPoolStats();
    const advancedMetrics = getAdvancedMetrics();
    
    // Calculate connection health score
    const latencyScore = Math.max(0, 100 - (advancedMetrics.connectionQuality.averageLatency / 1000) * 50);
    const errorScore = Math.max(0, 100 - (advancedMetrics.connectionQuality.errorRate * 100));
    const uptimeScore = Math.min(100, (Date.now() - advancedMetrics.connectionQuality.uptime) / (24 * 60 * 60 * 1000) * 10); // 10 points per day
    const overallHealth = (latencyScore + errorScore + uptimeScore) / 3;
    
    // Determine connection status
    let status = 'excellent';
    if (overallHealth < 50) status = 'poor';
    else if (overallHealth < 70) status = 'fair';
    else if (overallHealth < 85) status = 'good';
    
    res.json({
      success: true,
      data: {
        connectionHealth: {
          overallScore: Math.round(overallHealth),
          status,
          components: {
            latency: {
              score: Math.round(latencyScore),
              value: `${Math.round(advancedMetrics.connectionQuality.averageLatency)}ms`,
              status: latencyScore > 80 ? 'excellent' : latencyScore > 60 ? 'good' : 'needs_improvement',
            },
            reliability: {
              score: Math.round(errorScore),
              errorRate: `${(advancedMetrics.connectionQuality.errorRate * 100).toFixed(2)}%`,
              consecutiveFailures: advancedMetrics.connectionQuality.consecutiveFailures,
              status: errorScore > 90 ? 'excellent' : errorScore > 70 ? 'good' : 'needs_improvement',
            },
            availability: {
              score: Math.round(uptimeScore),
              uptime: `${Math.round((Date.now() - advancedMetrics.connectionQuality.uptime) / 1000 / 60 / 60)}h`,
              status: uptimeScore > 80 ? 'excellent' : uptimeScore > 60 ? 'good' : 'needs_improvement',
            },
          },
        },
        poolEfficiency: {
          utilization: `${Math.round(advancedMetrics.poolMetrics.poolUtilization)}%`,
          reusageRate: poolStats.poolMetrics.totalConnections > 0 
            ? `${Math.round((poolStats.poolMetrics.connectionReuses / poolStats.poolMetrics.totalConnections) * 100)}%`
            : '0%',
          cacheHitRate: `${Math.round((poolStats.healthCheckCacheStats.hitRate) * 100)}%`,
          queueEfficiency: poolStats.poolMetrics.queuedRequests === 0 ? 'optimal' : 
                          poolStats.poolMetrics.queuedRequests < 10 ? 'good' : 'needs_attention',
        },
        recommendations: [
          ...(advancedMetrics.connectionQuality.averageLatency > 500 ? ['Consider optimizing database queries or network connection'] : []),
          ...(advancedMetrics.connectionQuality.errorRate > 0.05 ? ['Investigate connection errors and improve error handling'] : []),
          ...(advancedMetrics.poolMetrics.poolUtilization > 80 ? ['Consider increasing connection pool size'] : []),
          ...(advancedMetrics.resourceUsage.resourcePressure === 'high' || advancedMetrics.resourceUsage.resourcePressure === 'critical' ? ['Memory pressure detected - consider scaling resources'] : []),
          ...(poolStats.poolMetrics.queuedRequests > 10 ? ['Connection queue is building up - consider pool optimization'] : []),
        ],
        timestamp: new Date().toISOString(),
      },
      message: 'Connection quality analysis completed'
    });
  } catch (error) {
    logger.error('[STATUS] Failed to get connection quality:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve connection quality metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/status/resource-usage
 * 
 * Phase 3: Comprehensive resource usage analysis
 */
router.get('/resource-usage', async (req, res) => {
  try {
    const advancedMetrics = getAdvancedMetrics();
    const memoryUsage = process.memoryUsage();
    
    // Calculate resource efficiency
    const memoryEfficiency = Math.max(0, 100 - ((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100));
    const poolEfficiency = Math.max(0, 100 - advancedMetrics.poolMetrics.poolUtilization);
    const overallEfficiency = (memoryEfficiency + poolEfficiency) / 2;
    
    res.json({
      success: true,
      data: {
        resourceSummary: {
          overallEfficiency: Math.round(overallEfficiency),
          resourcePressure: advancedMetrics.resourceUsage.resourcePressure,
          status: advancedMetrics.resourceUsage.resourcePressure === 'low' ? 'optimal' :
                  advancedMetrics.resourceUsage.resourcePressure === 'medium' ? 'good' :
                  advancedMetrics.resourceUsage.resourcePressure === 'high' ? 'warning' : 'critical',
        },
        memoryAnalysis: {
          heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
          heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
          external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`,
          rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
          heapUtilization: `${Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)}%`,
          efficiency: Math.round(memoryEfficiency),
        },
        connectionPoolAnalysis: {
          activeConnections: advancedMetrics.resourceUsage.connectionPoolSize,
          poolUtilization: `${Math.round(advancedMetrics.poolMetrics.poolUtilization)}%`,
          queueDepth: advancedMetrics.resourceUsage.queueDepth,
          totalResizes: advancedMetrics.poolMetrics.poolResizes,
          memoryPressureAdjustments: advancedMetrics.poolMetrics.memoryPressureAdjustments,
          resourceCleanups: advancedMetrics.poolMetrics.resourceCleanups,
        },
        performanceOptimizations: {
          cacheHitRate: `${(advancedMetrics.resourceUsage.cacheHitRate * 100).toFixed(1)}%`,
          connectionReuses: advancedMetrics.poolMetrics.connectionReuses,
          averageLatency: `${Math.round(advancedMetrics.poolMetrics.connectionLatencyMs)}ms`,
          healthCheckCacheHits: advancedMetrics.poolMetrics.healthCheckHits,
        },
        recommendations: [
          ...(memoryUsage.heapUsed / memoryUsage.heapTotal > 0.8 ? ['High memory usage detected - consider memory optimization'] : []),
          ...(advancedMetrics.poolMetrics.poolUtilization > 80 ? ['Connection pool highly utilized - consider increasing pool size'] : []),
          ...(advancedMetrics.resourceUsage.queueDepth > 5 ? ['Connection queue building up - optimize pool configuration'] : []),
          ...(advancedMetrics.poolMetrics.memoryPressureAdjustments > 10 ? ['Frequent memory pressure adjustments - consider resource scaling'] : []),
          ...(advancedMetrics.resourceUsage.cacheHitRate < 0.5 ? ['Low cache hit rate - review caching strategy'] : []),
        ],
        timestamp: new Date().toISOString(),
      },
      message: 'Resource usage analysis completed'
    });
  } catch (error) {
    logger.error('[STATUS] Failed to get resource usage:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve resource usage metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
