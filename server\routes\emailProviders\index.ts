/**
 * This router consolidates all email provider-specific routes.
 * Currently, it only handles Gmail.
 */
import { type Request, type Response, Router } from 'express';
import logger from '../../lib/logger';
import { sendError, sendSuccess } from '../../lib/standardizedResponses';
import { requireAuth } from '../../middleware/simpleAuth';
import tokenService from '../../services/tokenService';
import { catchAsync } from '../../utils/errorHandler';
import gmailRouter from './gmail';
import { ErrorCode } from '../../lib/standardizedResponses';

const router = Router();

// Aggregated status endpoint for all email providers
router.get(
  '/status',
  requireAuth,
  catchAsync(async (req: Request, res: Response) => {
    if (!req.user) {
      return sendError(res, ErrorCode.UNAUTHORIZED, 'Authentication required.');
    }

    try {
      const gmailStatus = await tokenService.verifyConnection(req.user);
      const outlookStatus = { isConnected: false, tokenInvalid: true, reason: 'Not implemented' }; // Placeholder

      // Map internal keys to client-friendly shape
      const mapStatus = (provider: string, status: any) => ({
        provider,
        isConnected: status.connected ?? status.isConnected ?? false,
        tokenInvalid: !status.connected,
        connectionVerified: status.connected,
        ...status,
      });

      const providerStatuses = [
        mapStatus('google', gmailStatus),
        mapStatus('outlook', outlookStatus),
      ];

      return sendSuccess(res, { providers: providerStatuses });
    } catch (error) {
      logger.error('Error fetching provider status', {
        userId: req.user.id,
        error: error instanceof Error ? error.message : String(error),
      });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to fetch provider status.');
    }
  })
);

router.use('/gmail', gmailRouter);

export default router;
