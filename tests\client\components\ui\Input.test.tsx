import { fireEvent, render, screen } from '@testing-library/react';
import { Input } from '@/components/ui/input';
import '@testing-library/jest-dom';

describe('UI/Input component', () => {
  it('updates value on user input', () => {
    render(<Input aria-label="name" />);
    const input = screen.getByRole('textbox', { name: /name/i });
    fireEvent.change(input, { target: { value: 'Alice' } });
    expect(input).toHaveValue('Alice');
  });

  it('displays validation error via aria-invalid', () => {
    render(<Input aria-label="email" aria-invalid="true" />);
    const input = screen.getByRole('textbox', { name: /email/i });
    expect(input).toHaveAttribute('aria-invalid', 'true');
  });

  it('marks required field', () => {
    render(<Input aria-label="password" required />);
    const input = screen.getByRole('textbox', { name: /password/i });
    expect(input).toBeRequired();
  });

  it('supports accessibility attributes', () => {
    render(<Input aria-label="username" />);
    const input = screen.getByRole('textbox', { name: /username/i });
    expect(input).toBeInTheDocument();
  });
}); 