/**
 * Redis Service tests
 *
 * Focus:
 * 1. Successful connection establishment when REDIS_URL provided.
 * 2. Fallback/no REDIS_URL – getRedisClient throws.
 * 3. Connection error handling (connect rejects) logs error.
 * 4. Client exposes hGetAll/hIncrBy commands after initialization.
 */

// ------------------------------
// Logger mock
// ------------------------------

const mockLogger = {
  info: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

jest.mock('@server/lib/logger', () => ({
  __esModule: true,
  default: mockLogger,
}));

// ------------------------------
// Dynamic import helpers to reset module state between tests
// ------------------------------
let originalRedisUrl: string | undefined;

function freshRedisService() {
  jest.resetModules();
  jest.dontMock('@server/services/redis');
  // Re-import module fresh each time to avoid shared state.
  return require('@server/services/redis');
}

describe('Redis service', () => {
  afterEach(() => {
    jest.clearAllMocks();
    jest.dontMock('redis');
    if (originalRedisUrl !== undefined) {
      process.env.REDIS_URL = originalRedisUrl;
    } else {
      delete process.env.REDIS_URL;
    }
  });

  it('initializes Redis client when REDIS_URL is set', async () => {
    originalRedisUrl = process.env.REDIS_URL;
    process.env.REDIS_URL = 'redis://localhost:6379';

    // Mock node-redis createClient
    const mockClient = {
      on: jest.fn(),
      connect: jest.fn().mockResolvedValue(undefined),
      hGetAll: jest.fn(),
      hIncrBy: jest.fn(),
    };

    jest.doMock('redis', () => ({ createClient: jest.fn(() => mockClient) }));

    const { initializeRedis, getRedisClient } = freshRedisService();

    initializeRedis();
    const client = getRedisClient();
    expect(client).toBe(mockClient);
  });

  it('throws when getRedisClient is called without initialization/REDIS_URL', () => {
    originalRedisUrl = process.env.REDIS_URL;
    const { initializeRedis, getRedisClient } = freshRedisService();

    // No REDIS_URL
    initializeRedis();
    expect(() => getRedisClient()).toThrowError('Redis client has not been initialized');
    expect(mockLogger.warn).toHaveBeenCalledWith(
      'REDIS_URL not found. Redis-dependent services will be unavailable.'
    );
  });

  it('logs an error if Redis connect fails', async () => {
    originalRedisUrl = process.env.REDIS_URL;
    process.env.REDIS_URL = 'redis://localhost:6379';
    const mockClient = {
      on: jest.fn(),
      connect: jest.fn().mockRejectedValue(new Error('connection refused')),
    };

    jest.doMock('redis', () => ({ createClient: jest.fn(() => mockClient) }));

    const { initializeRedis } = freshRedisService();

    initializeRedis();

    // allow microtask queue to flush
    await Promise.resolve();

    expect(mockLogger.error).toHaveBeenCalledWith(
      'Failed to connect Redis client:',
      expect.any(Error)
    );
  });

  it('client exposes basic command methods', () => {
    originalRedisUrl = process.env.REDIS_URL;
    process.env.REDIS_URL = 'redis://localhost:6379';
    const mockClient = {
      on: jest.fn(),
      connect: jest.fn().mockResolvedValue(undefined),
      hGetAll: jest.fn(),
      hIncrBy: jest.fn(),
    };

    jest.doMock('redis', () => ({ createClient: jest.fn(() => mockClient) }));

    const { initializeRedis, getRedisClient } = freshRedisService();
    initializeRedis();
    const client = getRedisClient();
    expect(typeof client.hGetAll).toBe('function');
    expect(typeof client.hIncrBy).toBe('function');
  });
}); 