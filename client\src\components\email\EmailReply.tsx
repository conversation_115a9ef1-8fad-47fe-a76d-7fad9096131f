import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Edit, HelpCircle, MessageSquare, RefreshCcw, Send, Sparkles } from 'lucide-react';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import apiClient from '@/lib/apiClient';
import type { Email } from '@/types/email';
import { useAuth } from '@/context/AuthContext';

interface EmailReplyProps {
  email: Email;
  onReplyChange?: (reply: string) => void;
}

interface ReplyResponse {
  reply: string;
  fromCache: boolean;
}

const EmailReply: React.FC<EmailReplyProps> = ({ email, onReplyChange }) => {
  const [replyContent, setReplyContent] = useState(email?.aiReply || '');
  const [selectedTone, setSelectedTone] = useState<string>('Professional');
  const [explanation, setExplanation] = useState<string | null>(null);
  const [hasReply, setHasReply] = useState<boolean>(!!email?.aiReply);
  const [isGeneratingInitialReply, setIsGeneratingInitialReply] = useState<boolean>(false);
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { user } = useAuth();

  // Use a ref to track if we've already initiated generation for this email
  const generationInitiated = useRef<Set<string>>(new Set());

  // Early return if email or email.messageId is not available
  if (!email || !email.messageId) {
    return (
      <div className="bg-muted/60 border border-border rounded-lg p-4">
        <div className="flex items-center justify-center">
          <p className="text-muted-foreground text-sm">Loading email details...</p>
        </div>
      </div>
    );
  }

  // Generate on-demand reply mutation
  const { mutate: generateReply, isPending: isGeneratingReply } = useMutation<ReplyResponse, Error>(
    {
      mutationFn: () => apiClient.post(`/api/ai/generate-reply/${encodeURIComponent(email.messageId)}`),
      onSuccess: (response) => {
        setReplyContent(response.reply);
        setHasReply(true);
        setIsGeneratingInitialReply(false);

        // Update the email in the cache with the new AI reply
        queryClient.setQueryData<Email>(['emails', 'detail', email.messageId], (oldData) =>
          oldData ? { ...oldData, aiReply: response.reply } : undefined
        );

        // Invalidate and refetch the email detail to ensure server consistency
        queryClient.invalidateQueries({ queryKey: ['emails', 'detail', email.messageId] });

        // Also invalidate the email list to ensure consistency
        queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });

        // Notify parent component of the reply change
        onReplyChange?.(response.reply);

        if (!response.fromCache) {
          toast({
            title: 'AI reply generated',
            description: 'A reply was generated based on the email content.',
          });
        }
      },
      onError: (error) => {
        setIsGeneratingInitialReply(false);
        // Remove from generation tracking on error so it can be retried
        if (email?.messageId) {
          generationInitiated.current.delete(email.messageId);
        }
        toast({
          title: 'Failed to generate reply',
          description: error.message,
          variant: 'destructive',
        });
      },
    }
  );

  // Send reply mutation
  const sendReplyMutation = useMutation<void, Error, { content: string }>({
    mutationFn: ({ content }) => apiClient.post(`/api/emails/by-message-id/${encodeURIComponent(email.messageId)}/reply`, { content }),
    onSuccess: () => {
      toast({
        title: 'Reply sent',
        description: 'Your reply has been successfully sent.',
      });
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
      onReplyChange?.('');
    },
    onError: (error) => {
      toast({
        title: 'Reply failed',
        description: error.message || 'Failed to send reply. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Regenerate reply mutation
  const { mutate: regenerateReplyFn, isPending: isRegenerating } = useMutation<
    string,
    Error,
    string
  >({
    mutationFn: (tone: string) => apiClient.post(`/api/ai/regenerate-reply/by-message-id/${encodeURIComponent(email.messageId)}`, { tone }),
    onSuccess: (newReply) => {
      setReplyContent(newReply);

      // Update the email in the cache with the new AI reply
      queryClient.setQueryData<Email>(['emails', 'detail', email.messageId], (oldData) =>
        oldData ? { ...oldData, aiReply: newReply } : undefined
      );

      // Invalidate and refetch the email detail to ensure server consistency
      queryClient.invalidateQueries({ queryKey: ['emails', 'detail', email.messageId] });

      // Also invalidate the email list to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });

      // Notify parent component of the reply change
      onReplyChange?.(newReply);

      toast({
        title: 'Reply regenerated',
        description: `Generated a new ${selectedTone} reply.`,
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to regenerate reply',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Explain categories mutation
  const { mutate: explainCategoriesFn, isPending: isExplaining } = useMutation<string, Error>({
    mutationFn: () => apiClient.post(`/api/ai/explain-categories/by-message-id/${encodeURIComponent(email.messageId)}`),
    onSuccess: (explanation) => {
      setExplanation(explanation);
    },
    onError: (error) => {
      toast({
        title: 'Failed to explain categories',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Handler functions
  const handleGenerateReply = useCallback(() => {
    setIsGeneratingInitialReply(true);
    generateReply();
  }, [generateReply]);

  const handleToneChange = (tone: string) => {
    setSelectedTone(tone);
    regenerateReplyFn(tone);
  };

  const handleRegenerate = () => {
    regenerateReplyFn(selectedTone);
  };

  const handleExplain = () => {
    explainCategoriesFn();
  };

  const handleSendReply = () => {
    sendReplyMutation.mutate({ content: replyContent });
  };

  // Update state when email changes
  useEffect(() => {
    // Reset state for new email
    setReplyContent(email?.aiReply || '');
    setHasReply(!!email?.aiReply);
    setIsGeneratingInitialReply(false); // Reset generation state when email changes
    setExplanation(null); // Reset explanation
    onReplyChange?.(email?.aiReply || '');

    // Clear generation tracking for previous emails when email changes
    if (email?.messageId) {
      generationInitiated.current.clear();
    }
  }, [email?.messageId, email?.aiReply, onReplyChange]);

  // Generate on-demand reply if one doesn't exist
  useEffect(() => {
    // Only trigger if there's no reply from the server and we haven't already started generating one.
    if (
      email?.messageId &&
      (!email.aiReply || email.aiReply.trim() === '') && // Check for empty or null reply
      !isGeneratingInitialReply &&
      !isGeneratingReply && // Also check if mutation is already pending
      !generationInitiated.current.has(email.messageId)
    ) {
      generationInitiated.current.add(email.messageId);
      setIsGeneratingInitialReply(true);
      generateReply();
    }
  }, [email?.messageId, email?.aiReply, isGeneratingInitialReply, isGeneratingReply, generateReply]);

  // Derived state
  const isLoading = isRegenerating || isGeneratingReply || isGeneratingInitialReply;
  const showEmptyState = !hasReply && !replyContent && !isLoading;
  const showContentArea = !isLoading && (hasReply || !!replyContent);

  return (
    <div className="bg-muted/60 hover:bg-muted transition-colors border border-border rounded-lg overflow-hidden shadow-sm animate-in fade-in-50 duration-300 max-w-full relative z-10">
      <div className="p-3 sm:p-4 border-b border-border bg-card/30 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
        <div className="flex items-center">
          <div className="h-5 w-5 sm:h-6 sm:w-6 mr-2 flex items-center justify-center rounded-full bg-primary/20 flex-shrink-0">
            <MessageSquare className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-primary" />
          </div>
          <h3 className="font-medium text-foreground text-sm sm:text-base">AI Suggested Reply</h3>
        </div>
        <div className="flex items-center bg-muted/50 rounded-md p-1 border border-border self-start sm:self-auto">
          <span className="text-xs text-muted-foreground mr-1 sm:mr-2 pl-1 sm:pl-2">Tone:</span>
          <Select value={selectedTone} onValueChange={handleToneChange}>
            <SelectTrigger className="text-xs w-24 sm:w-28 h-7 border-0 bg-transparent focus:ring-0 focus:ring-offset-0">
              <SelectValue placeholder="Select tone" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Professional">
                <div className="flex items-center">
                  <span className="h-2 w-2 rounded-full bg-blue-500 mr-2 flex-shrink-0" />
                  <span className="text-xs sm:text-sm">Professional</span>
                </div>
              </SelectItem>
              <SelectItem value="Friendly">
                <div className="flex items-center">
                  <span className="h-2 w-2 rounded-full bg-green-500 mr-2 flex-shrink-0" />
                  <span className="text-xs sm:text-sm">Friendly</span>
                </div>
              </SelectItem>
              <SelectItem value="Direct">
                <div className="flex items-center">
                  <span className="h-2 w-2 rounded-full bg-purple-500 mr-2 flex-shrink-0" />
                  <span className="text-xs sm:text-sm">Direct</span>
                </div>
              </SelectItem>
              <SelectItem value="Witty">
                <div className="flex items-center">
                  <span className="h-2 w-2 rounded-full bg-amber-500 mr-2 flex-shrink-0" />
                  <span className="text-xs sm:text-sm">Witty</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="p-3 sm:p-4">
        {/* Loading state */}
        {isLoading && (
          <div className="w-full min-h-[100px] sm:min-h-[120px] flex items-center justify-center bg-muted/20 rounded-md border border-border mb-3">
            <div className="flex flex-col items-center text-muted-foreground">
              {isRegenerating ? (
                <>
                  <RefreshCcw className="h-4 w-4 sm:h-5 sm:w-5 animate-spin mb-2" />
                  <span className="text-xs sm:text-sm text-center px-2">
                    Regenerating {selectedTone.toLowerCase()} reply...
                  </span>
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 sm:h-5 sm:w-5 animate-pulse mb-2" />
                  <span className="text-xs sm:text-sm text-center px-2">
                    Generating initial AI reply...
                  </span>
                </>
              )}
            </div>
          </div>
        )}

        {/* Empty state */}
        {showEmptyState && (
          <div className="w-full min-h-[100px] sm:min-h-[120px] flex flex-col items-center justify-center bg-muted/20 rounded-md border border-border mb-3 p-3 sm:p-4">
            <Sparkles className="h-4 w-4 sm:h-5 sm:w-5 text-primary mb-2" />
            <p className="text-xs sm:text-sm text-center text-muted-foreground mb-2 sm:mb-3">
              No AI reply has been generated yet
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={handleGenerateReply}
              className="border-primary/20 hover:bg-primary/5 hover:text-primary transition-colors text-xs sm:text-sm py-1 h-7 sm:h-8"
            >
              <Sparkles className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
              Generate Reply
            </Button>
          </div>
        )}

        {/* Content area */}
        {showContentArea && (
          <Textarea
            className="w-full min-h-[100px] sm:min-h-[120px] p-2 sm:p-3 border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-foreground text-xs sm:text-sm bg-muted/20 hover:bg-card transition-colors resize-y"
            value={replyContent}
            onChange={(e) => setReplyContent(e.target.value)}
            placeholder="AI reply will appear here"
          />
        )}

        {/* Category explanation */}
        {explanation && (
          <div className="mt-3 p-2 sm:p-3 bg-muted/40 rounded-md text-xs sm:text-sm text-foreground border border-border animate-in slide-in-from-bottom-3 duration-300">
            <div className="flex items-center mb-1">
              <HelpCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 text-primary flex-shrink-0" />
              <p className="font-medium">Category Explanation:</p>
            </div>
            <p className="pl-4 sm:pl-5 text-muted-foreground text-xs sm:text-sm">{explanation}</p>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row sm:justify-between mt-3 sm:mt-4 gap-2 sm:gap-0">
          <div className="flex flex-wrap gap-1.5 sm:gap-2">
            <Button
              variant="outline"
              size="sm"
              className="border-primary/20 hover:bg-primary/5 hover:text-primary transition-colors h-7 sm:h-8 py-0 px-2 sm:px-3 text-xs sm:text-sm"
            >
              <Edit className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
              <span className="hidden xs:inline">Edit</span>
            </Button>

            {(hasReply || replyContent) && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRegenerate}
                  disabled={isLoading}
                  className="border-primary/20 hover:bg-primary/5 hover:text-primary transition-colors h-7 sm:h-8 py-0 px-2 sm:px-3 text-xs sm:text-sm"
                >
                  <RefreshCcw
                    className={`h-3 w-3 sm:h-4 sm:w-4 mr-1 ${isRegenerating ? 'animate-spin' : ''}`}
                  />
                  <span className="hidden xs:inline">
                    {isRegenerating ? 'Regenerating...' : 'Regenerate'}
                  </span>
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExplain}
                  disabled={isExplaining || isLoading}
                  className="border-primary/20 hover:bg-primary/5 hover:text-primary transition-colors h-7 sm:h-8 py-0 px-2 sm:px-3 text-xs sm:text-sm"
                >
                  <HelpCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                  <span className="hidden xs:inline">
                    {isExplaining ? 'Loading...' : 'Explain'}
                  </span>
                </Button>
              </>
            )}

            {showEmptyState && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleGenerateReply}
                className="border-primary/20 hover:bg-primary/5 hover:text-primary transition-colors h-7 sm:h-8 py-0 px-2 sm:px-3 text-xs sm:text-sm"
              >
                <Sparkles className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                <span className="hidden xs:inline">Generate Reply</span>
              </Button>
            )}
          </div>

          <div className="self-end">
            <Button
              variant="default"
              size="sm"
              onClick={handleSendReply}
              disabled={isLoading || !replyContent?.trim()}
              className="relative group overflow-hidden h-7 sm:h-8 py-0 text-xs sm:text-sm"
            >
              <div className="flex items-center">
                <Send
                  className={`h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 ${isLoading ? 'animate-bounce' : 'group-hover:translate-x-1 transition-transform'}`}
                />
                {isLoading ? 'Sending...' : 'Send Reply'}
              </div>
              <span className="absolute inset-0 h-full w-full scale-0 rounded-md transition-all duration-300 group-hover:scale-100 group-hover:bg-primary/10" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailReply;
