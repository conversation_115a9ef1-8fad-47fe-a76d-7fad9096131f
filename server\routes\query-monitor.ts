/**
 * Query Monitor API Routes
 *
 * Provides endpoints for accessing database query performance metrics,
 * optimization recommendations, and system health data.
 */

import { type Request, type Response, Router } from 'express';
import { getEnvVar } from '../lib/environmentValidator';
import logger from '../lib/logger';
import { sendError, sendSuccess } from '../lib/standardizedResponses';
import { queryMonitor } from '../utils/queryMonitor';

const router = Router();

/**
 * Get query performance statistics
 * GET /api/query-monitor/stats
 */
router.get('/stats', (req: Request, res: Response) => {
  try {
    const timeWindow = req.query.timeWindow
      ? Number.parseInt(req.query.timeWindow as string)
      : undefined;
    const stats = queryMonitor.getStats(timeWindow);

    res.json({
      success: true,
      data: stats,
      timeWindow: timeWindow || 'all_time',
    });
  } catch (error) {
    logger.error('Error fetching query stats', {
      error: (error as Error).message,
      userId: req.session?.userId,
    });

    res.status(500).json({
      success: false,
      message: 'Failed to fetch query statistics',
      error: getEnvVar('NODE_ENV') === 'development' ? (error as Error).message : undefined,
    });
  }
});

/**
 * Get optimization recommendations
 * GET /api/query-monitor/recommendations
 */
router.get('/recommendations', (req: Request, res: Response) => {
  try {
    const recommendations = queryMonitor.getOptimizationRecommendations();
    const stats = queryMonitor.getStats();

    res.json({
      success: true,
      data: {
        recommendations,
        performanceScore: stats.performanceScore,
        totalQueries: stats.totalQueries,
        slowQueries: stats.slowQueries,
        avgExecutionTime: stats.avgExecutionTime,
      },
    });
  } catch (error) {
    logger.error('Error fetching optimization recommendations', {
      error: (error as Error).message,
      userId: req.session?.userId,
    });

    res.status(500).json({
      success: false,
      message: 'Failed to fetch optimization recommendations',
      error: getEnvVar('NODE_ENV') === 'development' ? (error as Error).message : undefined,
    });
  }
});

/**
 * Export metrics data
 * GET /api/query-monitor/export
 */
router.get('/export', (req: Request, res: Response) => {
  try {
    const format = (req.query.format as 'json' | 'csv') || 'json';
    const exportData = queryMonitor.exportMetrics(format);

    const contentType = format === 'csv' ? 'text/csv' : 'application/json';
    const filename = `query-metrics-${Date.now()}.${format}`;

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.send(exportData);
  } catch (error) {
    logger.error('Error exporting query metrics', {
      error: (error as Error).message,
      userId: req.session?.userId,
    });

    res.status(500).json({
      success: false,
      message: 'Failed to export query metrics',
      error: getEnvVar('NODE_ENV') === 'development' ? (error as Error).message : undefined,
    });
  }
});

/**
 * Reset query monitoring statistics
 * POST /api/query-monitor/reset
 */
router.post('/reset', (req: Request, res: Response) => {
  try {
    queryMonitor.reset();

    logger.info('Query monitor statistics reset', {
      userId: req.session?.userId,
      userEmail: req.user?.email,
    });

    res.json({
      success: true,
      message: 'Query monitoring statistics have been reset',
    });
  } catch (error) {
    logger.error('Error resetting query monitor', {
      error: (error as Error).message,
      userId: req.session?.userId,
    });

    res.status(500).json({
      success: false,
      message: 'Failed to reset query monitoring statistics',
      error: getEnvVar('NODE_ENV') === 'development' ? (error as Error).message : undefined,
    });
  }
});

/**
 * Get detailed query pattern analysis
 * GET /api/query-monitor/patterns
 */
router.get('/patterns', (req: Request, res: Response) => {
  try {
    const timeWindow = req.query.timeWindow
      ? Number.parseInt(req.query.timeWindow as string)
      : undefined;
    const stats = queryMonitor.getStats(timeWindow);

    // Sort patterns by total time (impact)
    const sortedPatterns = Object.entries(stats.queryPatterns)
      .sort(([, a], [, b]) => b.totalTime - a.totalTime)
      .slice(0, 20) // Top 20 patterns
      .map(([queryId, pattern]) => ({
        queryId,
        ...pattern,
        impact: pattern.totalTime,
        efficiency: pattern.count / pattern.avgTime, // Higher is better
      }));

    res.json({
      success: true,
      data: {
        patterns: sortedPatterns,
        totalPatterns: Object.keys(stats.queryPatterns).length,
        timeWindow: timeWindow || 'all_time',
      },
    });
  } catch (error) {
    logger.error('Error fetching query patterns', {
      error: (error as Error).message,
      userId: req.session?.userId,
    });

    res.status(500).json({
      success: false,
      message: 'Failed to fetch query patterns',
      error: getEnvVar('NODE_ENV') === 'development' ? (error as Error).message : undefined,
    });
  }
});

/**
 * Get system health summary
 * GET /api/query-monitor/health
 */
router.get('/health', (req: Request, res: Response) => {
  try {
    const stats = queryMonitor.getStats();
    const recommendations = queryMonitor.getOptimizationRecommendations();

    // Determine health status
    let healthStatus: 'excellent' | 'good' | 'warning' | 'critical';
    if (stats.performanceScore >= 90) {
      healthStatus = 'excellent';
    } else if (stats.performanceScore >= 75) {
      healthStatus = 'good';
    } else if (stats.performanceScore >= 50) {
      healthStatus = 'warning';
    } else {
      healthStatus = 'critical';
    }

    // Key metrics for dashboard
    const healthData = {
      status: healthStatus,
      performanceScore: stats.performanceScore,
      metrics: {
        totalQueries: stats.totalQueries,
        avgExecutionTime: Math.round(stats.avgExecutionTime),
        slowQueries: stats.slowQueries,
        errorQueries: stats.errorQueries,
        slowQueryPercentage:
          stats.totalQueries > 0 ? Math.round((stats.slowQueries / stats.totalQueries) * 100) : 0,
      },
      alerts: recommendations.length > 0 ? recommendations.slice(0, 3) : [],
      timestamp: Date.now(),
    };

    res.json({
      success: true,
      data: healthData,
    });
  } catch (error) {
    logger.error('Error fetching query health data', {
      error: (error as Error).message,
      userId: req.session?.userId,
    });

    res.status(500).json({
      success: false,
      message: 'Failed to fetch query health data',
      error: getEnvVar('NODE_ENV') === 'development' ? (error as Error).message : undefined,
    });
  }
});

export default router;
