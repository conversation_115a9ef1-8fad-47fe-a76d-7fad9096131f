/**
 * Email Provider Status and Connection Management
 *
 * This module provides functions for managing email provider connections,
 * particularly Gmail OAuth integration and status checking.
 */

import apiClient from "./apiClient";
import { handleError } from "./errorHandler";

/**
 * Interface for email provider connection status
 */
export interface EmailProviderStatus {
  isConnected: boolean;
  provider: string;
  email?: string;
  connectionVerified?: boolean;
  connectionStatus?: string;
  lastSynced?: string | null;
  lastVerified?: string | null;
  tokenExpires?: string | null;
  tokenExpiresIn?: number | null;
  tokenInvalid?: boolean;
  tokenError?: string | null;
  lastApiError?: string | null;
  lastRefreshedAt?: string | null;
  lastConnectionAttempt?: string | null;
  refreshAttempted?: boolean;
  refreshSucceeded?: boolean;
  refreshError?: string | null;
  middlewareContext?: {
    circuitBreakerOpen?: boolean;
    attempted?: boolean;
    error?: string | null;
  };
}

/**
 * Interface for the provider status response
 */
export interface EmailProviderStatusResponse {
  success: boolean;
  providers: EmailProviderStatus[];
  timestamp: string;
  requestId: string;
}

/**
 * Get the status of connected email providers
 */
export const getEmailProviderStatus = async (): Promise<EmailProviderStatus[]> => {
  try {
    /*
     * Server responses are standardized via `sendSuccess`, e.g.
     * {
     *   success: true,
     *   data: { providers: [...] },
     *   timestamp: "…",
     *   requestId: "…"
     * }
     * The generic type therefore needs to include the `data` wrapper.
     */
    const response = await apiClient.get<{
      data: {
        providers?: EmailProviderStatus[];
      };
    }>("/api/email-providers/status");

    return response?.data?.providers ?? [];
  } catch (error) {
    handleError(error, { operation: "getEmailProviderStatus" });
    throw error;
  }
};

/**
 * Initiates the OAuth flow to connect a Gmail account.
 */
export const connectGmailAccount = async (): Promise<void> => {
  try {
    const { authUrl } = await apiClient.get<{ authUrl: string }>(
      "/api/auth/google"
    );
    window.location.href = authUrl;
  } catch (error) {
    handleError(error, { operation: "connectGmailAccount" });
    throw error;
  }
};

/**
 * Disconnect a Gmail account
 */
export const disconnectGmailAccount = async (): Promise<void> => {
  try {
    await apiClient.post("/api/email-providers/gmail/disconnect");
  } catch (error) {
    // The main error handler now understands Gmail-specific issues.
    handleError(error, { operation: "disconnect_gmail" });
    throw error;
  }
};

/**
 * Refresh Gmail tokens
 */
export const refreshGmailTokens = async (): Promise<void> => {
  try {
    await apiClient.post("/api/auth/token/refresh");
  } catch (error) {
    // The main error handler now understands Gmail-specific issues.
    handleError(error, { operation: "refresh_gmail_tokens" });
    throw error;
  }
};

/**
 * Emergency force reconnect for Gmail.
 * Use this only as a last resort when normal reconnection fails.
 */
export const forceReconnectGmail = async (): Promise<{ authUrl?: string }> => {
  try {
    const response = await apiClient.post<{ authUrl?: string }>(
      "/api/gmail-connection/force-reconnect"
    );
    return response;
  } catch (error) {
    // The main error handler now understands Gmail-specific issues.
    handleError(error, { operation: "force_reconnect_gmail", emergency: true });
    throw error;
  }
};
