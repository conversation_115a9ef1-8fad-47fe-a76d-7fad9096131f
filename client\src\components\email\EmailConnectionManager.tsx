import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2, Mail, RefreshCw } from 'lucide-react';
import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useProviderStatus } from '@/hooks/use-provider-status';
import { Alert, AlertDescription } from '../ui/alert';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import { GmailConnectionPrompt } from './GmailConnectionPrompt';
import { GmailConnectionStatus } from './GmailConnectionStatus';

/**
 * EmailConnectionManager Component
 *
 * This component manages email provider connections and provides
 * diagnostic and troubleshooting tools for email connections.
 */
const EmailConnectionManager = () => {
  const { isLoading, gmailProvider, error: providerError } = useProviderStatus();
  const { user, error: authError } = useAuth();
  const [reconnecting, setReconnecting] = useState(false);

  // Use both the user provider from AuthContext and the API status to determine connection state.
  // This helps differentiate between a user who has never connected vs. a user whose token is invalid.
  const hasGmailConfigured = user?.provider === 'google' || gmailProvider?.isConnected;
  const isGmailConnected = gmailProvider?.isConnected === true;

  // Function to reconnect Gmail
  const reconnectGmail = () => {
    setReconnecting(true);
    // The actual auth URL is now centralized on the backend.
    window.location.href = '/api/auth/google';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Mail className="h-5 w-5 mr-2" />
            Email Connection Management
          </CardTitle>
          <CardDescription>
            Manage your email provider connections and fix any connection issues.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {authError && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{authError}</AlertDescription>
            </Alert>
          )}

          {isLoading || providerError ? (
            <div className="flex justify-center items-center p-6">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Checking connection status...</span>
            </div>
          ) : hasGmailConfigured ? (
            // Gmail is configured, show status and reconnect options
            <div className="space-y-4">
              <GmailConnectionStatus />
              {/* This prompt will only show if re-authentication is needed */}
              <GmailConnectionPrompt />

              {isGmailConnected && (
                <div className="mt-6 border-t pt-6">
                  <h3 className="text-sm font-medium mb-2">Need to force a reconnect?</h3>
              <p className="text-sm text-muted-foreground mb-4">
                    If you're experiencing persistent issues, you can try manually reconnecting your
                    Gmail account.
              </p>
                  <Button variant="outline" onClick={reconnectGmail} disabled={reconnecting}>
                {reconnecting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Reconnecting...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reconnect Gmail
                  </>
                )}
              </Button>
            </div>
              )}
            </div>
          ) : (
            // No Gmail configured at all, show the primary connection prompt.
            <GmailConnectionPrompt />
          )}
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-6">
          <p className="text-xs text-muted-foreground">
            Your email data is securely accessed using OAuth. We never store your actual emails.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
};

export default EmailConnectionManager;
