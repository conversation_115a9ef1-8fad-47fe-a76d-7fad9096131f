import { ChevronLeft, ChevronRight } from 'lucide-react';
import type React from 'react';
import { Button } from '@/components/ui/button';
import { useEmailList } from '@/context/EmailListContext';

interface EmailPaginationProps {
  className?: string;
}

const EmailPagination: React.FC<EmailPaginationProps> = ({ className = '' }) => {
  const { currentPage, totalPages, goToPage, nextPage, prevPage } = useEmailList();

  // Don't render if there's only one page
  if (totalPages <= 1) return null;

  // Generate page numbers
  const pageNumbers: Array<number | { type: 'ellipsis'; id: string }> = [];
  // Always show the first page
  pageNumbers.push(1);

  // Create a sliding window around the current page
  const start = Math.max(2, currentPage - 1);
  const end = Math.min(totalPages - 1, currentPage + 1);

  // Add ellipsis if there's a gap between the first page and the window
  if (start > 2) {
    pageNumbers.push({ type: 'ellipsis', id: 'start' });
  }

  // Add the sliding window
  for (let i = start; i <= end; i++) {
    pageNumbers.push(i);
  }

  // Add ellipsis if there's a gap between the window and the last page
  if (end < totalPages - 1) {
    pageNumbers.push({ type: 'ellipsis', id: 'end' });
  }

  // Always show the last page if there's more than one page
  if (totalPages > 1) {
    pageNumbers.push(totalPages);
  }

  return (
    <div className={`flex items-center justify-center py-4 ${className}`}>
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={prevPage}
          disabled={currentPage === 1}
          className="h-8 w-8 p-0"
        >
          <span className="sr-only">Previous page</span>
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {pageNumbers.map((page) =>
          typeof page === 'number' ? (
            <Button
              key={`page-${page}`}
              variant={currentPage === page ? 'default' : 'outline'}
              size="sm"
              onClick={() => goToPage(page)}
              className="h-8 w-8 p-0"
            >
              {page}
            </Button>
          ) : (
            <span key={`ellipsis-${page.id}`} className="px-2">
              ...
            </span>
          )
        )}

        <Button
          variant="outline"
          size="sm"
          onClick={nextPage}
          disabled={currentPage === totalPages}
          className="h-8 w-8 p-0"
        >
          <span className="sr-only">Next page</span>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default EmailPagination;
