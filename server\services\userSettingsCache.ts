/**
 * User Settings Cache Service
 * 
 * Provides caching for user settings to avoid N+1 query problems
 * when processing multiple emails for the same user.
 */

import type { Settings } from '@shared/schema';
import logger from '../lib/logger';
import { storage } from '../storage';

interface CacheEntry {
  settings: Settings | undefined;
  timestamp: number;
  ttl: number;
}

class UserSettingsCache {
  private cache = new Map<number, CacheEntry>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 1000; // Maximum number of cached entries

  /**
   * Get user settings with caching
   * @param userId The user ID
   * @param ttl Time to live in milliseconds (optional)
   * @returns User settings or undefined
   */
  async getSettings(userId: number, ttl: number = this.DEFAULT_TTL): Promise<Settings | undefined> {
    const now = Date.now();
    const cacheKey = userId;

    // Check if we have a valid cached entry
    const cachedEntry = this.cache.get(cacheKey);
    if (cachedEntry && (now - cachedEntry.timestamp) < cachedEntry.ttl) {
      logger.debug(`[UserSettingsCache] Cache hit for user ${userId}`);
      return cachedEntry.settings;
    }

    // Cache miss or expired - fetch from database
    logger.debug(`[UserSettingsCache] Cache miss for user ${userId}, fetching from database`);
    
    try {
      const settings = await storage.getSettings(userId);
      
      // Store in cache
      this.setCache(userId, settings, ttl);
      
      return settings;
    } catch (error) {
      logger.error(`[UserSettingsCache] Failed to fetch settings for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Set cache entry
   * @param userId The user ID
   * @param settings The settings to cache
   * @param ttl Time to live in milliseconds
   */
  private setCache(userId: number, settings: Settings | undefined, ttl: number): void {
    // Implement LRU eviction if cache is full
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldestEntry();
    }

    this.cache.set(userId, {
      settings,
      timestamp: Date.now(),
      ttl,
    });

    logger.debug(`[UserSettingsCache] Cached settings for user ${userId} with TTL ${ttl}ms`);
  }

  /**
   * Invalidate cache entry for a specific user
   * @param userId The user ID
   */
  invalidate(userId: number): void {
    const deleted = this.cache.delete(userId);
    if (deleted) {
      logger.debug(`[UserSettingsCache] Invalidated cache for user ${userId}`);
    }
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    logger.debug(`[UserSettingsCache] Cleared cache (${size} entries)`);
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    maxSize: number;
    entries: Array<{ userId: number; age: number; ttl: number }>;
  } {
    const now = Date.now();
    const entries = Array.from(this.cache.entries()).map(([userId, entry]) => ({
      userId,
      age: now - entry.timestamp,
      ttl: entry.ttl,
    }));

    return {
      size: this.cache.size,
      maxSize: this.MAX_CACHE_SIZE,
      entries,
    };
  }

  /**
   * Evict the oldest cache entry (LRU)
   */
  private evictOldestEntry(): void {
    let oldestKey: number | undefined;
    let oldestTimestamp = Date.now();

    for (const [userId, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = userId;
      }
    }

    if (oldestKey !== undefined) {
      this.cache.delete(oldestKey);
      logger.debug(`[UserSettingsCache] Evicted oldest entry for user ${oldestKey}`);
    }
  }

  /**
   * Clean up expired entries
   */
  cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [userId, entry] of this.cache.entries()) {
      if ((now - entry.timestamp) >= entry.ttl) {
        this.cache.delete(userId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.debug(`[UserSettingsCache] Cleaned up ${cleanedCount} expired entries`);
    }
  }

  /**
   * Batch fetch settings for multiple users
   * Useful for processing multiple users at once
   * @param userIds Array of user IDs
   * @param ttl Time to live in milliseconds
   * @returns Map of userId to settings
   */
  async batchGetSettings(
    userIds: number[], 
    ttl: number = this.DEFAULT_TTL
  ): Promise<Map<number, Settings | undefined>> {
    const result = new Map<number, Settings | undefined>();
    const uncachedUserIds: number[] = [];
    const now = Date.now();

    // Check cache for each user
    for (const userId of userIds) {
      const cachedEntry = this.cache.get(userId);
      if (cachedEntry && (now - cachedEntry.timestamp) < cachedEntry.ttl) {
        result.set(userId, cachedEntry.settings);
      } else {
        uncachedUserIds.push(userId);
      }
    }

    // Fetch uncached settings
    if (uncachedUserIds.length > 0) {
      logger.debug(`[UserSettingsCache] Batch fetching settings for ${uncachedUserIds.length} users`);
      
      // Fetch settings for uncached users
      const fetchPromises = uncachedUserIds.map(async (userId) => {
        try {
          const settings = await storage.getSettings(userId);
          this.setCache(userId, settings, ttl);
          return { userId, settings };
        } catch (error) {
          logger.error(`[UserSettingsCache] Failed to fetch settings for user ${userId}:`, error);
          return { userId, settings: undefined };
        }
      });

      const fetchResults = await Promise.allSettled(fetchPromises);
      
      for (const fetchResult of fetchResults) {
        if (fetchResult.status === 'fulfilled') {
          const { userId, settings } = fetchResult.value;
          result.set(userId, settings);
        }
      }
    }

    return result;
  }
}

// Export singleton instance
export const userSettingsCache = new UserSettingsCache();

// Start periodic cleanup (every 10 minutes)
setInterval(() => {
  userSettingsCache.cleanup();
}, 10 * 60 * 1000);
