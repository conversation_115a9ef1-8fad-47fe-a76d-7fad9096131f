import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Loader2, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useProviderStatus } from '@/hooks/use-provider-status';
import { useAuth } from '@/context/AuthContext';
import apiClient from '@/lib/apiClient';

interface ProcessingStatus {
  success: boolean;
  errors: number;
  message?: string;
}

interface FixSummariesResult {
  successful: number;
  failed: number;
  remaining: number;
}

export function FixEmailSummariesButton() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { gmailProvider } = useProviderStatus();
  
  // Check if Gmail is connected
  const isGmailConnected = gmailProvider?.isConnected || false;

  // Use useQuery to fetch the number of emails with errored summaries
  const { data: processingStatus, isLoading: isLoadingStatus } = useQuery<ProcessingStatus>({
    queryKey: ['/api/emails/processing-status'],
    queryFn: () => apiClient.get('/api/emails/processing-status'),
    enabled: isGmailConnected, // Only query when Gmail is connected
    refetchInterval: 60000, // Refetch every 60 seconds
  });

  const errorCount = processingStatus?.errors || 0;

  // Use useMutation for the fix operation
  const fixMutation = useMutation<
    { message: string; fixed: number; errors: number },
    Error
  >({
    mutationFn: () => apiClient.post('/api/emails/summaries/fix'),
    onSuccess: (result) => {
      toast({
        title: 'Fix complete',
        description: `Fixed ${result.fixed} email summaries.`,
      });
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['emails', 'processing-status', user?.id] });
    },
    onError: (error) => {
      toast({
        title: 'Fix failed',
        description: error.message || 'Could not fix email summaries.',
        variant: 'destructive',
      });
    },
  });

  const handleFixSummaries = () => {
    fixMutation.mutate();
  };

  // Don't render the button if Gmail is not connected
  if (!isGmailConnected) {
    return null;
  }

  return (
    <Button
      variant={errorCount > 0 ? 'destructive' : 'outline'}
      size="sm"
      onClick={handleFixSummaries}
      disabled={fixMutation.isPending || isLoadingStatus}
      className="gap-2"
    >
      {fixMutation.isPending || isLoadingStatus ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <RefreshCw className="h-4 w-4" />
      )}
      {fixMutation.isPending ? 'Fixing...' : errorCount > 0 ? `Fix Summaries (${errorCount})` : 'Fix Summaries'}
    </Button>
  );
}

export default FixEmailSummariesButton;
