import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createContext, type ReactNode, useContext } from 'react';
import { useToast } from '@/hooks/use-toast';
import apiClient from '@/lib/apiClient';
import { sampleSettings, sampleStats } from '@/lib/sampleData';
import type { Settings, Stats } from '@/types/email';
import { useAuth } from './AuthContext';

interface InboxProgress {
  total: number;
  processed: number;
  cleared: number;
  percentage: number;
}

interface UserSettingsContextType {
  settings: Partial<Settings> | null;
  stats: Stats | null;
  inboxProgress: InboxProgress;
  isLoading: boolean;
  isUpdating: boolean;
  error: Error | null;
  refreshSettings: () => void;
  refreshStats: () => void;
  updateSettings: (settings: Partial<Settings>) => Promise<void>;
}

const UserSettingsContext = createContext<UserSettingsContextType | undefined>(undefined);

// Use named function components to avoid Fast Refresh incompatibility
export function UserSettingsProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  // Determine if we're in test mode based on the user's email (consistent across contexts)
  const isTestMode = user && (user.email === '<EMAIL>' || user.email === '<EMAIL>');

  // Fetch real settings if we have a real user
  const {
    data: settingsData,
    isLoading: settingsLoading,
    error: settingsError,
    refetch: refreshSettings,
  } = useQuery<Settings>({
    queryKey: ['/api/settings'],
    enabled: !!user,
    retry: (failureCount, error: any) => {
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 1;
    },
  });

  // Fetch real stats if we have a real user
  const {
    data: statsData,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats,
  } = useQuery<Stats>({
    queryKey: ['/api/stats'],
    enabled: !!user,
    retry: (failureCount, error: any) => {
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 1;
    },
  });

  // Update settings mutation
  const { mutateAsync: updateSettingsMutation, isPending: isUpdating } = useMutation<
    Settings,
    Error,
    Partial<Settings>
  >({
    mutationFn: (newSettings) => apiClient.patch('/api/settings', newSettings),
    onSuccess: (data) => {
      toast({
        title: 'Settings updated',
        description: 'Your settings have been successfully updated.',
      });
      queryClient.setQueryData(['/api/settings'], data);
    },
    onError: (error) => {
      toast({
        title: 'Failed to update settings',
        description: error.message || 'An error occurred while updating settings.',
        variant: 'destructive',
      });
    },
  });

  // Use real data if available, otherwise fallback to sample data
  const settings = settingsData ?? (isTestMode ? sampleSettings : null);
  const stats = statsData ?? (isTestMode ? sampleStats : null);

  // Calculate inbox progress
  const inboxProgress: InboxProgress = {
    total: stats?.totalEmails ?? 0,
    processed: stats?.emailsProcessed ?? 0,
    cleared: stats?.archivedEmails ?? 0,
    percentage: Math.round(((stats?.archivedEmails ?? 0) / (stats?.totalEmails || 1)) * 100),
  };

  // Determine loading state
  const isLoading = settingsLoading || statsLoading;

  // Combine errors
  const error = settingsError || statsError;

  return (
    <UserSettingsContext.Provider
      value={{
        settings,
        stats,
        inboxProgress,
        isLoading,
        isUpdating,
        error: error as Error | null,
        refreshSettings: refreshSettings,
        refreshStats: refetchStats,
        updateSettings: async (settings: Partial<Settings>) => {
          await updateSettingsMutation(settings);
        },
      }}
    >
      {children}
    </UserSettingsContext.Provider>
  );
}

// Use named function to avoid Fast Refresh incompatibility
export function useUserSettings() {
  const context = useContext(UserSettingsContext);
  if (context === undefined) {
    throw new Error('useUserSettings must be used within a UserSettingsProvider');
  }
  return context;
}
