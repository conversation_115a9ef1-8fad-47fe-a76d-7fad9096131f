import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/context/AuthContext';
import type { EmailProviderStatus } from '@/lib/emailProviders';
import { getEmailProviderStatus } from '@/lib/emailProviders';

/**
 * A centralized hook to fetch and manage the status of all email providers.
 *
 * This hook encapsulates the TanStack Query logic for fetching email provider status,
 * ensuring that all components in the application use the same data source,
 * caching, and retry logic. It replaces multiple, inconsistent implementations
 * throughout the codebase.
 *
 * @returns The state from TanStack Query, including:
 *  - `providers`: An array of provider statuses.
 *  - `gmailProvider`: The specific status for the 'google' provider, if it exists.
 *  - `isLoading`, `isError`, `refetch`, etc.
 */
export function useProviderStatus() {
  const { user, loading: authLoading } = useAuth();

  const {
    data: providers,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery<EmailProviderStatus[], Error>({
    queryKey: ['emailProviderStatus', user?.id],
    queryFn: getEmailProviderStatus,
    // Only enable when user is authenticated and not on auth pages
    enabled: Boolean(
      user &&
        !authLoading &&
        typeof window !== 'undefined' &&
        !window.location.pathname.includes('/login') &&
        !window.location.pathname.includes('/signup')
    ),
    refetchOnWindowFocus: true,
    // Use reasonable polling interval - not too aggressive
    refetchInterval: 60000, // Poll every 60 seconds (reduced from 30)
    staleTime: 0, // Always fetch fresh data to detect connection changes quickly
    retry: (failureCount, err) => {
      // Don't retry on auth-related errors (401, 403)
      if (err && typeof err === 'object' && 'response' in err) {
        const status = (err as { response?: { status?: number } }).response?.status;
        if (status === 401 || status === 403) {
          return false;
        }
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });

  // Ensure providers is always an array to prevent downstream errors.
  const safeProviders = Array.isArray(providers) ? providers : [];

  // Find the Gmail provider for easy access in components.
  const gmailProvider = safeProviders.find(
    (p) => p.provider === 'google' || p.provider === 'gmail'
  );

  return {
    providers: safeProviders,
    gmailProvider,
    isLoading,
    isError,
    error,
    refetch,
  };
}
