/**
 * Firebase Configuration
 *
 * This file initializes the Firebase app and provides the authentication instance.
 */

import { type FirebaseApp, initializeApp } from 'firebase/app';
import { type Auth, connectAuthEmulator, getAuth } from 'firebase/auth';
import logger from './logger';

export interface User {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  provider?: string; // Add provider field (gmail, outlook, etc.)
}

// Validate critical environment variables
const validateEnvironment = () => {
  const requiredVars = {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
    appId: import.meta.env.VITE_FIREBASE_APP_ID,
  };

  const missing = Object.entries(requiredVars)
    .filter(([_, value]) => !value || value === 'undefined')
    .map(([key]) => `VITE_FIREBASE_${key.toUpperCase()}`);

  if (missing.length > 0) {
    logger.firebase.initFailure(
      new Error(`Firebase configuration incomplete. Missing: ${missing.join(', ')}`)
    );
    throw new Error(`Firebase configuration incomplete. Missing: ${missing.join(', ')}`);
  }

  return requiredVars;
};

// Firebase configuration with validation
const getFirebaseConfig = () => {
  const env = validateEnvironment();

  return {
    apiKey: env.apiKey,
    authDomain: env.authDomain,
    projectId: env.projectId,
    storageBucket: env.storageBucket,
    messagingSenderId: env.messagingSenderId,
    appId: env.appId,
  };
};

// Initialize Firebase with proper error handling
let app: FirebaseApp;
let auth: Auth;

try {
  const firebaseConfig = getFirebaseConfig();

  logger.info('Initializing with validated config', {
    apiKey: `${firebaseConfig.apiKey.slice(0, 10)}...`,
    authDomain: firebaseConfig.authDomain,
    projectId: firebaseConfig.projectId,
    environment: import.meta.env.MODE,
  }, 'firebase');

  app = initializeApp(firebaseConfig);
  auth = getAuth(app);

  // Connect to auth emulator in development if needed
  if (
    import.meta.env.MODE === 'development' &&
    import.meta.env.VITE_USE_FIREBASE_EMULATOR === 'true'
  ) {
    logger.info('Connecting to auth emulator', undefined, 'firebase');
    connectAuthEmulator(auth, 'http://localhost:9099');
  }

  // Set auth language to match browser locale
  auth.languageCode = navigator.language || 'en';

  logger.firebase.initSuccess();
} catch (error) {
  logger.firebase.initFailure(error);

  // In production, we want to fail fast
  if (import.meta.env.MODE === 'production') {
    throw new Error('Firebase initialization failed in production. Check environment variables.');
  }

  throw error;
}

export { app, auth };

// Export typed Firebase auth instance
export default auth;
