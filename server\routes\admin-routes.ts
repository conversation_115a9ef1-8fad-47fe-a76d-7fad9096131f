/**
 * Admin Routes
 *
 * This module provides admin-only routes for system monitoring and management.
 * These routes require admin authentication.
 */

import type { NextFunction, Request, Response } from 'express';
import { Router } from 'express';
import { eq, isNull, not } from 'drizzle-orm';
import { getDb } from '../db';
import logger from '../lib/logger';
import { users } from '../../shared/schema';
import { isAdmin } from '../middleware/admin';
import { circuitBreakerRegistry } from '../utils/circuitBreaker';
import { getDetailedHealthReport, getSystemCircuitStats } from '../utils/circuitHealthMonitor';

const router = Router();

// Remove redundant auth middleware since it's applied at mount level
// Apply middleware globally to ensure all admin routes have user context
// router.use((req: Request, res: Response, next: NextFunction) => {
//   requireAuth(req, res, next);
// });

// router.use((req: Request, res: Response, next: NextFunction) => {
//   isAdmin(req, res, next);
// });

/**
 * Route to get circuit breaker health metrics
 */
router.get('/circuit-health', async (req: Request, res: Response): Promise<void> => {
  try {
    // Get circuit health report
    const healthReport = await getDetailedHealthReport();
    const stats = await getSystemCircuitStats();

    res.json({
      success: true,
      health: {
        report: healthReport,
        stats: stats,
      },
    });
  } catch (error) {
    logger.error('Error getting circuit health metrics', {
      error: (error as Error).message,
      userId: req.session?.userId,
    });

    res.status(500).json({
      success: false,
      error: 'Server error getting circuit health metrics',
    });
  }
});

/**
 * Route to reset a specific circuit breaker
 */
router.post('/reset-circuit/:circuitId', async (req: Request, res: Response): Promise<void> => {
  try {
    const { circuitId } = req.params;

    // Check if circuit exists
    const circuit = circuitBreakerRegistry.get(circuitId);

    if (!circuit) {
      res.status(404).json({
        success: false,
        error: `Circuit '${circuitId}' not found`,
      });
      return;
    }

    // Force reset the circuit
    await circuit.forceReset();

    logger.info(`Circuit '${circuitId}' manually reset by admin`, {
      userId: req.session?.userId,
      circuitId,
    });

    res.json({
      success: true,
      message: `Circuit '${circuitId}' has been reset`,
      newState: await circuit.getStateDetails(),
    });
  } catch (error) {
    logger.error('Error resetting circuit', {
      error: (error as Error).message,
      userId: req.session?.userId,
      circuitId: req.params.circuitId,
    });

    res.status(500).json({
      success: false,
      error: 'Server error resetting circuit',
    });
  }
});

/**
 * Route to get a list of users with token errors
 */
router.get('/users-with-token-errors', async (req: Request, res: Response): Promise<void> => {
  try {
    const db = await getDb();
    // Find users with token errors
    const usersWithErrors = await db
      .select({
        id: users.id,
        email: users.email,
        tokenStatus: users.tokenStatus,
        lastTokenError: users.lastTokenError,
        tokenErrorTime: users.tokenErrorTime,
        tokenErrorCount: users.tokenErrorCount,
        tokenLastRefreshed: users.tokenLastRefreshed,
      })
      .from(users)
      .where(not(isNull(users.lastTokenError)));

    res.json({
      success: true,
      users: usersWithErrors,
      count: usersWithErrors.length,
    });
  } catch (error) {
    logger.error('Error getting users with token errors', {
      error: (error as Error).message,
      userId: req.session?.userId,
    });

    res.status(500).json({
      success: false,
      error: 'Server error getting users with token errors',
    });
  }
});

/**
 * Route to reset token errors for a user
 */
router.post(
  '/reset-user-token-errors/:userId',
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const db = await getDb();

      // Reset token error flags
      await db
        .update(users)
        .set({
          lastTokenError: null,
          tokenErrorCount: 0,
          tokenErrorTime: null,
          tokenStatus: 'unknown', // Will be updated on next operation
        })
        .where(eq(users.id, Number.parseInt(userId)));

      logger.info(`Token errors reset for user ${userId} by admin`, {
        adminId: req.session?.userId,
        userId,
      });

      res.json({
        success: true,
        message: `Token errors reset for user ${userId}`,
      });
    } catch (error) {
      logger.error('Error resetting user token errors', {
        error: (error as Error).message,
        userId: req.session?.userId,
        targetUserId: req.params.userId,
      });

      res.status(500).json({
        success: false,
        error: 'Server error resetting user token errors',
      });
    }
  }
);

/**
 * Get system health overview
 */
router.get('/system-health', async (req: Request, res: Response): Promise<void> => {
  try {
    const db = await getDb();
    // Get counts of users and emails
    const userCount = await db.select({ count: users.id }).from(users);

    // Get circuit breaker health
    const stats = await getSystemCircuitStats();

    // Get memory usage
    const memoryUsage = process.memoryUsage();

    res.json({
      success: true,
      health: {
        users: userCount[0]?.count || 0,
        circuitBreakers: {
          total: stats.total,
          open: stats.open,
          halfOpen: stats.halfOpen,
          closed: stats.closed,
        },
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
          external: Math.round(memoryUsage.external / 1024 / 1024), // MB
        },
        uptime: Math.round(process.uptime()), // seconds
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    logger.error('Error getting system health', {
      error: (error as Error).message,
      userId: req.session?.userId,
    });

    res.status(500).json({
      success: false,
      error: 'Server error getting system health',
    });
  }
});



export default router;
