/**
 * Authentication Repair Utility
 *
 * This utility diagnoses and fixes authentication issues between Firebase and session auth.
 * It has been refactored to use the centralized apiClient and centralized API functions.
 */

import type { User as FirebaseUser } from 'firebase/auth';
import type { ProviderStatus } from '@/types/email';
import apiClient from './apiClient';
import { getEmailProviderStatus } from './emailProviders';
import { queryClient } from './queryClient';

export interface AuthDiagnostics {
  firebaseUser: FirebaseUser | null;
  hasFirebaseToken: boolean;
  isSessionValid: boolean;
  isBackendConnected: boolean; // Renamed for clarity
  isGmailConnected: boolean; // Renamed for clarity
  recommendedAction: string;
  canAutoFix: boolean;
}

/**
 * Comprehensive authentication diagnostics.
 */
export async function diagnoseAuthentication(
  firebaseUser: FirebaseUser | null
): Promise<AuthDiagnostics> {
  const hasFirebaseToken = !!firebaseUser;

  const { isAuthenticated: isSessionValid } = await apiClient.get<{ isAuthenticated: boolean }>(
    '/api/auth/status'
  );

  let isBackendConnected = false;
  if (isSessionValid) {
    // If the session is valid according to the backend, we can assume
    // the backend connection is fine for the current user.
    isBackendConnected = true;
  }

  let isGmailConnected = false;
  try {
    // This check requires a valid session, so we only run if authenticated.
    if (isSessionValid) {
      const providers = await apiClient.get<ProviderStatus[]>('/api/email-providers/status');
      isGmailConnected = providers?.some((p: ProviderStatus) => p.isConnected) || false;
    }
  } catch (error) {
    console.error('[AuthFixer] Gmail check failed:', error);
  }

  let recommendedAction = 'Authentication appears healthy';
  let canAutoFix = false;

  if (!firebaseUser) {
    recommendedAction = 'User needs to log in completely.';
  } else if (!isSessionValid) {
    recommendedAction =
      'Session is invalid or expired. It needs to be restored using the Firebase token.';
    canAutoFix = true;
  } else if (!isGmailConnected) {
    recommendedAction = 'User is logged in, but the Gmail connection needs to be re-established.';
    canAutoFix = true;
  }

  return {
    firebaseUser,
    hasFirebaseToken,
    isSessionValid,
    isBackendConnected,
    isGmailConnected,
    recommendedAction,
    canAutoFix,
  };
}

/**
 * Attempts to automatically fix common authentication issues.
 */
export async function attemptAuthRepair(firebaseUser: FirebaseUser | null): Promise<{
  success: boolean;
  message: string;
}> {
  const diagnostics = await diagnoseAuthentication(firebaseUser);

  if (!diagnostics.hasFirebaseToken || !diagnostics.firebaseUser) {
    return {
      success: false,
      message: 'Cannot repair authentication without a Firebase user. Please log in again.',
    };
  }

  // Scenario 1: Firebase user exists, but backend session is invalid.
  // This is the most common issue, where the session cookie has expired.
  if (!diagnostics.isSessionValid) {
    try {
      const idToken = await diagnostics.firebaseUser.getIdToken(true); // Force refresh
      await apiClient.post('/api/auth/firebase/verify', { idToken });
      // After successful verification, invalidate all queries to force a fresh data load
      // which aligns the client state with the new session.
      await queryClient.invalidateQueries();
      return {
        success: true,
        message: 'Session successfully restored.',
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[AuthFixer] Session restore failed:', errorMessage, error);
      return {
        success: false,
        message: `Failed to restore session: ${errorMessage}`,
      };
    }
  }

  // Scenario 2: Session is valid, but the email provider (e.g., Gmail) is disconnected.
  if (diagnostics.isSessionValid && !diagnostics.isGmailConnected) {
    try {
      await apiClient.post('/api/auth/refresh-token');
      // Invalidate just the provider status to get fresh connection data.
      await queryClient.invalidateQueries({ queryKey: ['emailProviderStatus'] });
      return {
        success: true,
        message: 'Email provider connection refresh attempted successfully.',
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[AuthFixer] Email provider token refresh failed:', errorMessage, error);
      return {
        success: false,
        message: `Failed to refresh email provider connection: ${errorMessage}`,
      };
    }
  }

  if (diagnostics.isSessionValid && diagnostics.isGmailConnected) {
    return {
      success: true,
      message: 'Authentication appears to be healthy. No repair needed.',
    };
  }

  return {
    success: false,
    message:
      'Could not determine an automatic fix. A full page reload or manual login may be required.',
  };
}

/**
 * Checks the status of various email provider connections.
 * This could be expanded to include other providers like Outlook.
 * @returns {Promise<ProviderStatus[]>} A promise that resolves to a list of provider statuses.
 */
export const checkProviderStatus = async (): Promise<ProviderStatus[]> => {
  try {
    const response = await apiClient.get<ProviderStatus[]>('/api/email-providers/status');
    return response;
  } catch (error) {
    console.error('[AuthFixer] Failed to check provider status:', error);
    return [];
  }
};
