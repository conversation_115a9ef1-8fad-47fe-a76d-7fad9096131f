/**
 * AI Service - Optimized wrapper for AI model loading and usage
 *
 * This service implements lazy loading of AI models, ensuring they're only
 * loaded when needed rather than at application startup. This significantly
 * improves initial load performance.
 */

// Track loading state and promises
let modelLoading = false;
let modelLoadPromise: Promise<any> | null = null;

/**
 * <PERSON><PERSON> loads the sentiment analysis model
 * Prevents multiple simultaneous load attempts
 */
export const loadSentimentModel = async () => {
  if (modelLoadPromise) {
    return modelLoadPromise; // Return existing promise if already loading
  }

  // Signal that loading is in progress
  modelLoading = true;

  // Create and store the loading promise
  modelLoadPromise = import('@xenova/transformers').then(async ({ pipeline }) => {
    try {
      // Dynamically import the model only when needed
      const classifier = await pipeline(
        'sentiment-analysis',
        'Xenova/distilbert-base-uncased-finetuned-sst-2-english'
      );

      return classifier;
    } catch (error) {
      console.error('Error loading sentiment analysis model:', error);
      // Clear the promise on error to allow retrying
      modelLoadPromise = null;
      throw error;
    } finally {
      modelLoading = false;
    }
  });

  return modelLoadPromise;
};

/**
 * Analyzes the sentiment of a text, loading the model if needed
 */
export const analyzeSentiment = async (text: string) => {
  try {
    const classifier = await loadSentimentModel();
    return await classifier(text);
  } catch (error) {
    console.error('Error analyzing sentiment:', error);
    return [{ label: 'ERROR', score: 0 }];
  }
};

/**
 * Check if the model is currently loading
 */
export const isModelLoading = () => modelLoading;

/**
 * Preload the model in the background during idle time
 * This is optional and can be called during application idle periods
 */
export const preloadModelInBackground = () => {
  if ('requestIdleCallback' in window) {
    (window as any).requestIdleCallback(
      () => {
        loadSentimentModel().catch((error) => {
          console.warn('Background model preload failed:', error);
        });
      },
      { timeout: 10000 }
    );
  } else {
    // Fallback for browsers without requestIdleCallback
    setTimeout(() => {
      loadSentimentModel().catch((error) => {
        console.warn('Background model preload failed:', error);
      });
    }, 5000);
  }
};
