/**
 * Consolidated Encryption Utilities
 *
 * This module provides encryption and decryption functions to securely
 * store sensitive data like OAuth tokens in the database.
 * This implementation consolidates functionality from both TypeScript and JavaScript versions.
 *
 * It uses AES-256-GCM for strong encryption with authentication and provides
 * backward compatibility with the older AES-256-GCM format used by the JavaScript version.
 */
import * as crypto from 'node:crypto';
import { getEnvVar } from '../lib/environmentValidator';
import logger from '../lib/logger';

// Encryption settings for new encryption (TypeScript version)
const ENCRYPTION_ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16; // For AES, this is always 16 bytes
const _AUTH_TAG_LENGTH = 16; // For GCM mode
const ENCRYPTION_KEY_ENV = 'ENCRYPTION_KEY';

// Encryption prefix to identify encrypted data
const ENCRYPTION_PREFIX = 'enc:';

// Legacy encryption settings (JavaScript version compatibility)
const LEGACY_ALGORITHM = 'aes-256-gcm';
const _LEGACY_AUTH_TAG_LENGTH = 16;
const _LEGACY_IV_LENGTH = 12;
const LEGACY_KEY_ENV = 'TOKEN_ENCRYPTION_KEY';

let ENCRYPTION_KEY_BUFFER: Buffer | undefined;

/**
 * Safe logging that falls back to console in test environments
 */
function safeLog(level: 'error' | 'debug' | 'warn', message: string, meta?: any) {
  try {
    if (logger?.[level]) {
      logger[level](message, meta);
    } else {
      // Fallback for test environments where logger might not be available
      if (level === 'error') {
        console.error(message, meta);
      } else if (level === 'debug' && process.env.NODE_ENV === 'development') {
        console.log(`[DEBUG] ${message}`, meta);
      }
    }
  } catch {
    // Silent fallback if logger is not available
  }
}

/**
 * Validates and returns the encryption key from environment variables.
 * This function ensures the key is a 64-character hex string (32 bytes).
 * It will throw an error if the key is missing or invalid, preventing insecure operations.
 *
 * @returns A 32-byte Buffer containing the encryption key.
 * @throws {Error} If the ENCRYPTION_KEY is not set or has an invalid format.
 */
function getEncryptionKey(): Buffer {
  if (ENCRYPTION_KEY_BUFFER) {
    return ENCRYPTION_KEY_BUFFER;
  }

  const envKey = process.env[ENCRYPTION_KEY_ENV];

  if (!envKey) {
    safeLog('error', 'CRITICAL: ENCRYPTION_KEY environment variable is not set.');
    throw new Error('Application is not configured with an ENCRYPTION_KEY.');
  }

  if (typeof envKey !== 'string' || envKey.length !== 64 || !/^[0-9a-fA-F]+$/.test(envKey)) {
    safeLog('error', 'CRITICAL: ENCRYPTION_KEY must be a 64-character hex string (32 bytes).');
    throw new Error('Invalid ENCRYPTION_KEY format.');
  }

  ENCRYPTION_KEY_BUFFER = Buffer.from(envKey, 'hex');
  return ENCRYPTION_KEY_BUFFER;
}

/**
 * Get legacy encryption key for backward compatibility with JavaScript version.
 * This is now hardened to prevent insecure key padding.
 *
 * @returns Legacy encryption key as Buffer.
 * @throws {Error} If the legacy key is missing or not 32 bytes.
 */
function getLegacyEncryptionKey(): Buffer {
  const legacyKey = process.env.TOKEN_ENCRYPTION_KEY || process.env.SESSION_SECRET; // Use direct access to avoid circular dependency during startup

  if (!legacyKey || typeof legacyKey !== 'string' || legacyKey.length < 32) {
    throw new Error(
      `Legacy encryption key from ${LEGACY_KEY_ENV} is missing or too short. It must be at least 32 characters.`
    );
  }

  // Use the first 32 bytes, but do NOT pad.
  return Buffer.from(String(legacyKey).slice(0, 32));
}

/**
 * Check if data is in legacy encryption format (JavaScript version)
 * Legacy format: base64(iv):base64(authTag):base64(encrypted)
 *
 * @param text Text to check
 * @returns True if the text is in legacy format
 */
export function isLegacyEncrypted(text: string): boolean {
  if (!text || typeof text !== 'string') return false;

  // Don't check texts that are already identified as new format
  if (text.startsWith(ENCRYPTION_PREFIX)) return false;

  // Check if it matches legacy encryption format
  const parts = text.split(':');

  if (parts.length !== 3) return false;

  try {
    // Try to decode each part to make sure it's valid base64
    Buffer.from(parts[0], 'base64');
    Buffer.from(parts[1], 'base64');
    Buffer.from(parts[2], 'base64');
    return true;
  } catch {
    return false;
  }
}

/**
 * Decrypt legacy format data (JavaScript version compatibility)
 *
 * @param encryptedText Encrypted text in legacy format
 * @returns Decrypted text or null if decryption fails
 */
function decryptLegacy(encryptedText: string): string | null {
  try {
    // Split the parts
    const [ivBase64, authTagBase64, encryptedBase64] = encryptedText.split(':');

    if (!ivBase64 || !authTagBase64 || !encryptedBase64) {
      return null;
    }

    // Convert from base64
    const iv = Buffer.from(ivBase64, 'base64');
    const authTag = Buffer.from(authTagBase64, 'base64');
    const encrypted = Buffer.from(encryptedBase64, 'base64');

    // Get legacy encryption key
    const key = getLegacyEncryptionKey();

    // Create a decipher
    // @ts-expect-error Known issue with @types/node and crypto Buffer types
    const decipher = crypto.createDecipheriv(LEGACY_ALGORITHM, key, iv);

    // Set the auth tag
    // @ts-expect-error Known issue with @types/node and crypto Buffer types
    decipher.setAuthTag(authTag);

    // Decrypt the data
    // Provide input as buffer, and specify output encoding for both update and final
    const decrypted = Buffer.concat([
      // @ts-expect-error Known issue with @types/node and crypto Buffer types
      decipher.update(encrypted),
      // @ts-expect-error Known issue with @types/node and crypto Buffer types
      decipher.final(),
    ]);

    return decrypted.toString('utf8');
  } catch (error) {
    // It's common for this to fail if the key is wrong, so we log at debug level.
    safeLog('debug', 'Legacy decryption failed. This may be expected if keys have rotated.', {
      message: (error as Error).message,
    });
    return null;
  }
}

/**
 * Encrypt data using AES-256-GCM
 *
 * @param text Text to encrypt
 * @returns Encrypted data with prefix
 */
export function encrypt(text: string): string {
  try {
    // Generate a random initialization vector
    const iv = crypto.randomBytes(IV_LENGTH);

    // Get the encryption key
    const key = getEncryptionKey();

    // Create cipher with key and IV
    // @ts-expect-error Known issue with @types/node and crypto Buffer types
    const cipher = crypto.createCipheriv(ENCRYPTION_ALGORITHM, key, iv);

    // Encrypt the data
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Get the authentication tag
    const authTag = cipher.getAuthTag().toString('hex');

    // Combine IV, encrypted data, and auth tag
    const encryptedData = [iv.toString('hex'), encrypted, authTag].join(':');

    // Add prefix for identification
    return ENCRYPTION_PREFIX + encryptedData;
  } catch (error) {
    safeLog('error', 'Encryption failed:', error);
    // Re-throw the original error to preserve the specific message
    throw error;
  }
}

/**
 * Decrypt data using AES-256-GCM with backward compatibility
 *
 * @param encryptedText Encrypted data with prefix or legacy format
 * @returns Decrypted text
 * @throws {Error} If decryption fails for any reason.
 */
export function decrypt(encryptedText: string): string {
  // If it has the new prefix, it MUST be decrypted this way.
  if (encryptedText.startsWith(ENCRYPTION_PREFIX)) {
    try {
      safeLog('debug', 'Attempting to decrypt new format...');
      const decrypted = decryptNewFormat(encryptedText);
      safeLog('debug', 'New format decryption successful.');
      return decrypted;
    } catch (error) {
      safeLog('error', 'Decryption failed for new format', {
        message: (error as Error).message,
        stack: (error as Error).stack,
        encryptedTextPreview: `${encryptedText.substring(0, 20)}...`,
      });
      throw new Error('Failed to decrypt data.', { cause: error as Error });
    }
  }

  // If it's in the legacy format, try to decrypt it.
  if (isLegacyEncrypted(encryptedText)) {
    safeLog('debug', 'Attempting to decrypt legacy format...');
    const legacyDecrypted = decryptLegacy(encryptedText);
    if (legacyDecrypted !== null) {
      safeLog('debug', 'Legacy format decryption successful.');
      return legacyDecrypted;
    }

    safeLog('warn', 'Decryption failed for a value that appeared to be legacy-encrypted.', {
      encryptedTextPreview: `${encryptedText.substring(0, 20)}...`,
    });
    throw new Error('Failed to decrypt legacy data.');
  }

  // If it's not in either encrypted format, return it as-is.
  return encryptedText;
}

/**
 * Decrypt new format data (TypeScript version)
 *
 * @param encryptedText Encrypted data with prefix
 * @returns Decrypted text
 */
function decryptNewFormat(encryptedText: string): string {
  // Remove the prefix
  const data = encryptedText.substring(ENCRYPTION_PREFIX.length);

  // Split the parts
  const parts = data.split(':');

  // An empty string encrypts to 'iv::authTag', which splits into 3 parts.
  // A non-empty string is 'iv:encrypted:authTag', also 3 parts.
  // So we must always have exactly 3 parts.
  if (parts.length !== 3) {
    throw new Error('Invalid new-format encrypted data: incorrect number of parts.');
  }

  const [ivHex, encryptedHex, authTagHex] = parts;

  // Convert from hex
  const iv = Buffer.from(ivHex, 'hex');
  const authTag = Buffer.from(authTagHex, 'hex');

  // Get the encryption key
  const key = getEncryptionKey();

  // Create decipher
  // @ts-expect-error Known issue with @types/node and crypto Buffer types
  const decipher = crypto.createDecipheriv(ENCRYPTION_ALGORITHM, key, iv);

  // Set the auth tag
  // @ts-expect-error Known issue with @types/node and crypto Buffer types
  decipher.setAuthTag(authTag);

  // Decrypt the data
  const encryptedBuffer = Buffer.from(encryptedHex, 'hex');
  const decrypted = Buffer.concat([
    // @ts-expect-error Known issue with @types/node and crypto Buffer types
    decipher.update(encryptedBuffer),
    // @ts-expect-error Known issue with @types/node and crypto Buffer types
    decipher.final(),
  ]);

  return decrypted.toString('utf8');
}

/**
 * Check if data is encrypted (new format)
 *
 * @param text Text to check
 * @returns True if the text is encrypted
 */
export function isEncrypted(text: string): boolean {
  return typeof text === 'string' && text.startsWith(ENCRYPTION_PREFIX);
}

/**
 * Check if data is encrypted in any format (new or legacy)
 *
 * @param text Text to check
 * @returns True if the text is encrypted in any recognized format
 */
export function isAnyFormatEncrypted(text: string): boolean {
  return isEncrypted(text) || isLegacyEncrypted(text);
}

/**
 * Generate a new encryption key
 * Useful for initial setup or key rotation
 *
 * @returns New encryption key as a Base64 string
 */
export function generateEncryptionKey(): string {
  const key = crypto.randomBytes(32);
  return key.toString('base64');
}

/**
 * Test the encryption setup
 * Useful for validating the encryption system is working
 *
 * @returns True if encryption/decryption works
 */
export function testEncryption(): boolean {
  try {
    const testData = `Test data: ${Date.now()}`;
    const encrypted = encrypt(testData);
    const decrypted = decrypt(encrypted);

    const success = decrypted === testData;

    if (success) {
      safeLog('debug', 'Encryption test passed');
    } else {
      safeLog('error', 'Encryption test failed: data mismatch');
    }

    return success;
  } catch (error) {
    safeLog('error', 'Encryption test failed:', error);
    return false;
  }
}

/**
 * Migrate data from legacy format to new format
 * Useful for upgrading existing encrypted data
 *
 * @param encryptedText Encrypted text in legacy format
 * @returns Encrypted text in new format or null if migration fails
 */
export function migrateLegacyEncryption(encryptedText: string): string | null {
  try {
    if (!isLegacyEncrypted(encryptedText)) {
      return null; // Not legacy format
    }

    // Decrypt using legacy format
    const decrypted = decryptLegacy(encryptedText);
    if (decrypted === null) {
      return null; // Failed to decrypt
    }

    // Re-encrypt using new format
    return encrypt(decrypted);
  } catch (error) {
    safeLog('error', 'Legacy encryption migration failed:', error);
    return null;
  }
}

/**
 * Run a full diagnostic on encryption configuration
 */
export function validateEncryptionConfig(): {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // Check if encryption key is available
  try {
    getEncryptionKey();
  } catch (_error) {
    if (getEnvVar('NODE_ENV') === 'production') {
      issues.push('No encryption key set in production environment');
    } else {
      recommendations.push('Set ENCRYPTION_KEY environment variable for better security');
    }
  }

  // Test encryption functionality
  if (!testEncryption()) {
    issues.push('Encryption test failed');
  }

  return {
    isValid: issues.length === 0,
    issues,
    recommendations,
  };
}

/**
 * Check if a string is encrypted with the new format
 * @param text Text to check
 * @param encryptedText Text in a legacy encrypted format.
 * @returns The same text, re-encrypted in the new format, or the original text if decryption fails.
 */
export function reEncryptLegacy(encryptedText: string): string {
  if (!isLegacyEncrypted(encryptedText)) {
    return encryptedText;
  }

  const decrypted = decryptLegacy(encryptedText);

  if (decrypted) {
    safeLog('debug', 'Successfully decrypted legacy token, re-encrypting with new key.');
    return encrypt(decrypted);
  }

  safeLog('warn', 'Could not re-encrypt legacy token because decryption failed.');
  return encryptedText;
}

// Legacy compatibility exports (for JavaScript version compatibility)
export const encryptLegacy = encrypt; // Use new encryption for all new data
// Removed conflicting: export const decryptLegacy = decrypt;
// The internal function decryptLegacy is used by the main decrypt for legacy cases.
export const isEncryptedLegacy = isAnyFormatEncrypted;
