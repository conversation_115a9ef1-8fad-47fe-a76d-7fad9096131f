/// <reference types="node" />

/**
 * Database Pool Manager
 *
 * Optimizes database connection pooling based on memory pressure and system load
 * to ensure efficient resource usage while maintaining performance.
 */

// Use ES module imports
import pkg from 'pg';

const { Pool } = pkg;
type PoolClient = any; // Simplified type for compatibility

import logger from '../lib/logger';
import { memoryManager } from './memoryManager';

// Default pool configuration
const DEFAULT_POOL_CONFIG = {
  min: 2, // Minimum number of connections
  max: 10, // Maximum number of connections
  idleTimeoutMillis: 60000, // How long a client is allowed to remain idle (ms)
  connectionTimeoutMillis: 5000, // How long to wait for a new connection (ms)
};

// Pool sizes for different memory pressure levels
const POOL_SIZES = {
  LOW: { min: 2, max: 10 },
  MEDIUM: { min: 2, max: 7 },
  HIGH: { min: 1, max: 5 },
  CRITICAL: { min: 1, max: 3 },
};

/**
 * Database Pool Manager for optimizing connection pooling
 */
export class DatabasePoolManager {
  private pool: any; // Use any type for compatibility
  private pressureLevel = 'LOW';
  private monitoringInterval: NodeJS.Timeout | null = null;
  private connectionStats = {
    total: 0,
    active: 0,
    idle: 0,
    waiting: 0,
  };

  /**
   * Create a new adaptive database pool manager
   * @param connectionString Database connection string
   * @param initialConfig Initial pool configuration
   */
  constructor(
    private connectionString: string,
    private config = DEFAULT_POOL_CONFIG
  ) {
    // Create database pool with initial configuration
    this.pool = new Pool({
      connectionString,
      min: config.min,
      max: config.max,
      idleTimeoutMillis: config.idleTimeoutMillis,
      connectionTimeoutMillis: config.connectionTimeoutMillis,
      // Add SSL configuration for Supabase
      ssl:
        connectionString.includes('supabase') || process.env.NODE_ENV === 'production' // Use direct access to avoid circular dependency during startup
          ? { rejectUnauthorized: false }
          : false,
    });

    // Monitor connection usage
    this.setupConnectionTracking();

    // Set up memory pressure monitoring
    this.monitorMemoryPressure();

    // Register cleanup callback with memory manager
    memoryManager.registerCleanupCallback('database-pool', async () => {
      await this.cleanupIdleConnections();
    });

    // Start periodic pool monitoring
    this.startMonitoring();

    logger.info(`Database pool manager initialized with ${config.min}-${config.max} connections`);
  }

  /**
   * Get a client from the pool
   */
  public async getClient(): Promise<PoolClient> {
    try {
      const client = await this.pool.connect();
      this.connectionStats.active++;
      this.connectionStats.idle--;

      // Add release hook to track when connection returns to pool
      const originalRelease = client.release.bind(client);
      client.release = (err?: Error) => {
        this.connectionStats.active--;
        this.connectionStats.idle++;
        return originalRelease(err);
      };

      return client;
    } catch (error) {
      logger.error('Error getting client from pool:', error);
      throw error;
    }
  }

  /**
   * Get the current pool statistics
   */
  public getPoolStats() {
    return {
      ...this.connectionStats,
      pressureLevel: this.pressureLevel,
      poolMin: this.config.min,
      poolMax: this.config.max,
      idleTimeoutMs: this.config.idleTimeoutMillis,
    };
  }

  /**
   * Clean up idle connections based on current memory pressure
   */
  public async cleanupIdleConnections(): Promise<void> {
    const memoryStats = memoryManager.getMemoryStats();

    // Only aggressively clean up if memory pressure is high
    if (memoryStats.pressureLevel === 'HIGH' || memoryStats.pressureLevel === 'CRITICAL') {
      logger.info(
        `Cleaning up idle database connections due to ${memoryStats.pressureLevel} memory pressure`
      );

      try {
        // Get pool internal metrics
        const idleCount = this.connectionStats.idle;

        if (idleCount > POOL_SIZES[memoryStats.pressureLevel].min) {
          // End connections one by one to avoid disruption
          const reduceBy = Math.min(
            idleCount - POOL_SIZES[memoryStats.pressureLevel].min,
            idleCount - 1 // Always keep at least one connection
          );

          if (reduceBy > 0) {
            logger.info(`Reducing idle connections by ${reduceBy}`);
            // Note: In a real implementation, we would need a way to directly
            // access and end specific idle clients in the pool. Since pg-pool
            // doesn't expose this directly, this is a conceptual implementation.
            await this.pool.query('SELECT pg_sleep(0.1)');
          }
        }
      } catch (error) {
        logger.error('Error cleaning up idle connections:', error);
      }
    }
  }

  /**
   * Reconfigure the pool based on memory pressure
   */
  private reconfigurePool(): void {
    const memoryStats = memoryManager.getMemoryStats();
    const newPressureLevel = memoryStats.pressureLevel;

    // Only update if pressure level changed
    if (this.pressureLevel !== newPressureLevel) {
      const newPoolConfig = POOL_SIZES[newPressureLevel];
      logger.info(
        `Memory pressure changed to ${newPressureLevel}, adjusting database pool to ${newPoolConfig.min}-${newPoolConfig.max} connections`
      );

      // Update our tracking
      this.pressureLevel = newPressureLevel;
      this.config.min = newPoolConfig.min;
      this.config.max = newPoolConfig.max;

      // Adjust idle timeout based on pressure
      if (newPressureLevel === 'HIGH' || newPressureLevel === 'CRITICAL') {
        this.config.idleTimeoutMillis = 30000; // 30 seconds for high pressure
      } else {
        this.config.idleTimeoutMillis = 60000; // 1 minute for normal pressure
      }

      // Note: pg-pool doesn't support dynamic reconfiguration of min/max,
      // so in a real implementation we would need to create a new pool and
      // migrate connections. This is conceptual only.
    }
  }

  /**
   * Set up tracking of connection stats
   */
  private setupConnectionTracking(): void {
    // Initialize stats
    this.connectionStats = {
      total: this.config.min,
      active: 0,
      idle: this.config.min,
      waiting: 0,
    };

    // Listen for pool events (not directly supported by pg-pool but conceptual)
    this.pool.on('connect', () => {
      this.connectionStats.total++;
      this.connectionStats.idle++;
    });

    this.pool.on('error', (err: Error) => {
      logger.error('Pool error:', err);
    });
  }

  /**
   * Monitor memory pressure and adjust pool accordingly
   */
  private monitorMemoryPressure(): void {
    // Update initial pressure level
    const memoryStats = memoryManager.getMemoryStats();
    this.pressureLevel = memoryStats.pressureLevel;

    // Listen for memory pressure changes
    memoryManager.on('pressureChange', ({ currentLevel }) => {
      logger.info(`Memory pressure changed to ${currentLevel}, adjusting database pool`);
      this.reconfigurePool();

      // If pressure is critical, immediately clean up connections
      if (currentLevel === 'CRITICAL') {
        this.cleanupIdleConnections().catch((err) => {
          logger.error('Error during emergency connection cleanup:', err);
        });
      }
    });
  }

  /**
   * Start periodic monitoring of the connection pool
   */
  private startMonitoring(intervalMs = 60000): void {
    this.monitoringInterval = setInterval(() => {
      // Get pool stats and log periodically
      const stats = this.getPoolStats();
      logger.debug(`Database pool stats: ${JSON.stringify(stats)}`);

      // Check if pool needs adjustment
      this.reconfigurePool();
    }, intervalMs);
  }

  /**
   * Stop pool monitoring
   */
  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  /**
   * End the pool and clean up resources
   */
  public async end(): Promise<void> {
    this.stopMonitoring();

    // Unregister from memory manager
    memoryManager.unregisterCleanupCallback('database-pool');

    // End the pool
    await this.pool.end();
    logger.info('Database pool shut down');
  }
}

// Create default pool manager using DATABASE_URL
const connectionString = process.env.DATABASE_URL || ''; // Use direct access to avoid circular dependency during startup
export const databasePoolManager = new DatabasePoolManager(connectionString);

export default databasePoolManager;
