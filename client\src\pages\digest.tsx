import { Bar<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON> } from 'lucide-react';
import type React from 'react';
import DailyDigest from '@/components/email/DailyDigest';
import AppLayout from '@/components/layout/AppLayout';
import { useAuth } from '@/context/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';

const DigestPage: React.FC = () => {
  const { user, loading: isLoading } = useAuth();
  const isMobile = useIsMobile();

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-full">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="p-3 sm:p-4 overflow-y-auto h-full">
        <div className="flex items-center space-x-2 mb-2">
          <Sparkles className="h-5 w-5 sm:h-6 sm:w-6 text-primary flex-shrink-0" />
          <h1 className="text-xl sm:text-2xl font-bold truncate">Summarize My Day</h1>
        </div>

        <div className="flex flex-col md:flex-row items-start gap-3 sm:gap-6 mb-4 sm:mb-6">
          <div className="flex-1 w-full">
            <p className="text-sm sm:text-base text-muted-foreground">
              Get AI-powered insights into your email activity and discover patterns to help you
              become more productive.
            </p>
          </div>

          {/* Feature badges - stack vertically on mobile */}
          <div
            className={`flex ${isMobile ? 'flex-col space-y-2' : 'flex-row gap-4'} text-xs sm:text-sm mt-2 md:mt-0`}
          >
            <div className="flex items-center gap-1 text-info-foreground whitespace-nowrap">
              <BarChart2 className="h-3.5 w-3.5 sm:h-4 sm:w-4 flex-shrink-0" />
              <span>Smart Analytics</span>
            </div>
            <div className="flex items-center gap-1 text-success-foreground whitespace-nowrap">
              <Clock className="h-3.5 w-3.5 sm:h-4 sm:w-4 flex-shrink-0" />
              <span>Time-Saving Suggestions</span>
            </div>
          </div>
        </div>

        <DailyDigest />
      </div>
    </AppLayout>
  );
};

export default DigestPage;
