import { QueryClient, type QueryFunction } from '@tanstack/react-query';
import apiClient from './apiClient';
import { getStaleTime } from './cacheManager';
import { type AppError, ErrorCategory, handleError } from './errorHandler';

/**
 * The primary QueryFunction used by default in the QueryClient.
 * It acts as a bridge between React Query and our centralized `apiClient`.
 *
 * It assumes the first part of the `queryKey` is the URL endpoint.
 * Any subsequent parts of the queryKey are passed as parameters.
 *
 * Example:
 * `useQuery({ queryKey: ['/api/emails', { page: 2 }] })`
 * will call `apiClient.get('/api/emails?page=2')`
 */
const defaultQueryFn: QueryFunction<unknown, readonly unknown[]> = async ({ queryKey }) => {
  const [url, params] = queryKey as [string, object | undefined];

  let fullUrl = url;
  if (params && Object.keys(params).length > 0) {
    const searchParams = new URLSearchParams();
    for (const [key, value] of Object.entries(params)) {
      if (value !== null && value !== undefined) {
        searchParams.append(key, String(value));
      }
    }
    const paramString = searchParams.toString();
    if (paramString) {
      fullUrl += `?${paramString}`;
    }
  }

  try {
    // We assume GET requests for queries. Mutations should use `useMutation`.
    let data = await apiClient.get(fullUrl);

    /*
     * All server routes now use `sendSuccess`, which wraps the actual payload
     * under a `data` key.  Older hooks/components assume they receive the
     * raw payload directly (e.g. `{ emails: [...] }`).  To remain backward-
     * compatible we transparently unwrap here.
     */
    if (data && typeof data === 'object' && 'success' in data && (data as any).success === true) {
      const maybeData = (data as any).data;
      if (maybeData !== undefined) {
        data = maybeData;
      }
    }

    return data;
  } catch (error) {
    // The apiClient is now responsible for creating a structured AppError.
    // We just need to ensure it's logged and re-thrown for React Query.
    handleError(
      error,
      {
        queryKey,
        operation: 'query',
      },
      true // Show toast by default for query failures
    );
    throw error;
  }
};

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: defaultQueryFn,
      refetchOnWindowFocus: false,

      // Use a function for staleTime to allow for endpoint-specific caching rules
      staleTime: ({ queryKey }) => {
        const key = queryKey[0] as string;
        return getStaleTime(key);
      },

      // Garbage collection time.
      // Keeping it reasonably short to avoid holding onto stale data for too long.
      gcTime: 10 * 60 * 1000, // 10 minutes

      // Retry logic for query failures.
      retry: (failureCount, error) => {
        const appError = error as AppError;

        // Never retry authentication/authorization errors.
        if (
          appError.category === ErrorCategory.AUTHENTICATION ||
          appError.category === ErrorCategory.AUTHORIZATION
        ) {
          return false;
        }

        // Retry server errors, network issues, or rate-limiting a few times.
        if (
          appError.category === ErrorCategory.SERVER ||
          appError.category === ErrorCategory.NETWORK ||
          appError.category === ErrorCategory.API_SERVICE
        ) {
          return failureCount < 3; // Retry up to 3 times
        }

        // Don't retry for other errors like validation or client-side issues.
        return false;
      },

      // Use exponential backoff for retries to avoid overwhelming the server.
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Max 30s
    },
    mutations: {
      // Mutations are handled via `useMutation` and our `apiClient`'s request method,
      // which has its own retry logic. We disable React Query's mutation retry
      // to avoid conflicting retry attempts.
      retry: false,
    },
  },
});
