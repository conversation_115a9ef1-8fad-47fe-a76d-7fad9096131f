import { LogOut, RefreshCw } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import authSync from '@/lib/authSync';

const TokenExpirationHandler: React.FC = () => {
  const { authError, clearAuthError, logout } = useAuth();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (
      authError &&
      (authError.code === 'auth/id-token-expired' || authError.code === 'auth/invalid-user-token')
    ) {
      setIsModalOpen(true);
    }
  }, [authError]);

  const handleDialogChange = (open: boolean) => {
    if (!open) {
      clearAuthError();
      setIsModalOpen(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // We use the new authSync manager to handle token logic
      await authSync.forceSync();

      toast({
        title: 'Session Refreshed',
        description: 'Your session has been successfully refreshed.',
      });
      setIsModalOpen(false);
      clearAuthError();
    } catch (error) {
      toast({
        title: 'Refresh Failed',
        description:
          (error instanceof Error ? error.message : 'An unknown error occurred.') +
          ' Please log in again.',
        variant: 'destructive',
      });
      logout(); // Redirect to login on failure
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleLogin = () => {
    logout();
  };

  return (
    <Dialog open={isModalOpen} onOpenChange={handleDialogChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Session Expired</DialogTitle>
          <DialogDescription>
            Your session has expired. Please refresh your session to continue or log in again.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={handleLogin}>
            <LogOut className="mr-2 h-4 w-4" />
            Login
          </Button>
          <Button onClick={handleRefresh} disabled={isRefreshing}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TokenExpirationHandler;
