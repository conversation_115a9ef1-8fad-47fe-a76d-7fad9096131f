/**
 * Firebase Configuration Validator
 *
 * This module validates Firebase configuration and helps diagnose common issues.
 * It consolidates functionality from:
 * - check-private-key-format.js
 * - firebase-config-check.js
 */

// Color codes for console output
const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GRE<PERSON>: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
};

// Logging functions
function log(message: string, color = COLORS.RESET) {
  // eslint-disable-next-line no-console
  console.log(color, message, COLORS.RESET);
}

function logSuccess(message: string) {
  log(`✓ ${message}`, COLORS.GREEN);
}

function logError(message: string) {
  log(`✗ ${message}`, COLORS.RED);
}

function logWarning(message: string) {
  log(`⚠ ${message}`, COLORS.YELLOW);
}

function logInfo(message: string) {
  log(`ℹ ${message}`, COLORS.BLUE);
}

function logHeader(message: string) {
  log(`\n=== ${message} ===`, COLORS.CYAN);
}

/**
 * Check if Firebase configuration is properly set up
 */
export function checkFirebaseConfig(): boolean {
  logHeader('Firebase Configuration Check');

  let configValid = true;

  // Store variables in a local object, do not mutate process.env
  const config = {
    apiKey: process.env.FIREBASE_API_KEY || process.env.VITE_FIREBASE_API_KEY,
    authDomain: process.env.FIREBASE_AUTH_DOMAIN || process.env.VITE_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.FIREBASE_PROJECT_ID || process.env.VITE_FIREBASE_PROJECT_ID,
    serviceAccount:
      process.env.FIREBASE_SERVICE_ACCOUNT || process.env.VITE_FIREBASE_SERVICE_ACCOUNT,
  };

  const sources = {
    apiKey: process.env.FIREBASE_API_KEY ? 'FIREBASE_API_KEY' : 'VITE_FIREBASE_API_KEY',
    authDomain: process.env.FIREBASE_AUTH_DOMAIN
      ? 'FIREBASE_AUTH_DOMAIN'
      : 'VITE_FIREBASE_AUTH_DOMAIN',
    projectId: process.env.FIREBASE_PROJECT_ID ? 'FIREBASE_PROJECT_ID' : 'VITE_FIREBASE_PROJECT_ID',
    serviceAccount: process.env.FIREBASE_SERVICE_ACCOUNT
      ? 'FIREBASE_SERVICE_ACCOUNT'
      : 'VITE_FIREBASE_SERVICE_ACCOUNT',
  };

  // Check required environment variables
  if (!config.apiKey) {
    logError(
      'Firebase API Key is not set in environment (FIREBASE_API_KEY or VITE_FIREBASE_API_KEY)'
    );
    configValid = false;
  } else {
    logSuccess(`Firebase API Key is set (${sources.apiKey})`);
  }

  if (!config.authDomain) {
    logError(
      'Firebase Auth Domain is not set in environment (FIREBASE_AUTH_DOMAIN or VITE_FIREBASE_AUTH_DOMAIN)'
    );
    configValid = false;
  } else {
    logSuccess(`Firebase Auth Domain is set (${sources.authDomain})`);
  }

  if (!config.projectId) {
    logError(
      'Firebase Project ID is not set in environment (FIREBASE_PROJECT_ID or VITE_FIREBASE_PROJECT_ID)'
    );
    configValid = false;
  } else {
    logSuccess(`Firebase Project ID is set (${sources.projectId})`);
  }

  // Check if we have Firebase service account for Admin SDK
  if (!config.serviceAccount) {
    logError(
      'Firebase Service Account is not set in environment (FIREBASE_SERVICE_ACCOUNT or VITE_FIREBASE_SERVICE_ACCOUNT)'
    );
    configValid = false;
  } else {
    logSuccess(`Firebase Service Account is set (${sources.serviceAccount})`);

    // Validate service account format
    try {
      const serviceAccountStr = config.serviceAccount;
      if (!serviceAccountStr) throw new Error('Service Account is not defined');
      const serviceAccount = JSON.parse(serviceAccountStr);

      // Check required fields
      const requiredFields = [
        'type',
        'project_id',
        'private_key_id',
        'private_key',
        'client_email',
        'client_id',
        'auth_uri',
        'token_uri',
        'client_x509_cert_url',
      ];

      const missingFields = requiredFields.filter((field) => !serviceAccount[field]);

      if (missingFields.length > 0) {
        logError(`Service account is missing required fields: ${missingFields.join(', ')}`);
        configValid = false;
      } else {
        logSuccess('Service account contains all required fields');
      }

      // Check private key format
      if (serviceAccount.private_key) {
        const privateKey = serviceAccount.private_key;

        // Private key should start with this prefix
        const expectedPrefix = '-----BEGIN PRIVATE KEY-----';

        if (!privateKey.includes(expectedPrefix)) {
          logError('Private key format is invalid - missing proper BEGIN PRIVATE KEY prefix');
          logWarning('Private key might need newline characters properly escaped');
          configValid = false;
        } else {
          logSuccess('Private key appears to be in the correct format');
        }

        // Check for common newline issues in private key
        if (privateKey.includes('\\n') && !privateKey.includes('\n')) {
          logWarning('Private key has escaped newlines (\\n) but may need actual newlines');
          logInfo('Try replacing \\n with actual newlines in your private key');
        }
      }
    } catch (error) {
      logError(`Failed to parse Service Account as JSON: ${error}`);
      configValid = false;
    }
  }

  return configValid;
}

/**
 * Fix common issues with Firebase service account format
 */
export function fixFirebaseServiceAccount(): string | null {
  const serviceAccountVar =
    process.env.FIREBASE_SERVICE_ACCOUNT || process.env.VITE_FIREBASE_SERVICE_ACCOUNT;

  if (!serviceAccountVar) {
    logError(
      'Firebase Service Account is not set in environment (FIREBASE_SERVICE_ACCOUNT or VITE_FIREBASE_SERVICE_ACCOUNT)'
    );
    return null;
  }

  try {
    const serviceAccount = serviceAccountVar;

    // Try to parse as JSON
    const parsed = JSON.parse(serviceAccount);

    // Check if private key has escaped newlines
    if (parsed.private_key?.includes('\\n')) {
      // Replace escaped newlines with actual newlines
      parsed.private_key = parsed.private_key.replace(/\\n/g, '\n');
      logSuccess('Fixed escaped newlines in private key');
    }

    // Return the fixed service account as a JSON string
    return JSON.stringify(parsed);
  } catch (error) {
    logError(`Failed to parse FIREBASE_SERVICE_ACCOUNT as JSON: ${error}`);
    return null;
  }
}

/**
 * Run a full diagnostic on Firebase configuration
 */
export function runFirebaseDiagnostic(): void {
  logHeader('Firebase Diagnostic Results');

  // Check Firebase config
  const configValid = checkFirebaseConfig();

  // Recommendations
  logHeader('Recommendations');

  if (!configValid) {
    log('• Make sure all required Firebase environment variables are set', COLORS.YELLOW);
    log('• Check the format of your Firebase service account', COLORS.YELLOW);
    log(
      '• If using private key with escaped newlines, try replacing \\n with actual newlines',
      COLORS.YELLOW
    );
    log(
      '• You can use the fixFirebaseServiceAccount() function to fix common issues',
      COLORS.YELLOW
    );

    // Try to fix service account
    const fixed = fixFirebaseServiceAccount();
    if (fixed) {
      log('\nFixed Firebase service account:', COLORS.GREEN);
      log('Set your FIREBASE_SERVICE_ACCOUNT environment variable to:', COLORS.RESET);
      log(`\n${fixed}`, COLORS.YELLOW);
    }
  } else {
    logSuccess('Firebase configuration appears to be valid');
  }
}
