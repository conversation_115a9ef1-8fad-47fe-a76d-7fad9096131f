/**
 * Data Egress Optimization Integration Tests
 *
 * Tests the complete data egress optimization flow including
 * API endpoints, field selection, and data transfer reduction.
 */

// All mocks are defined before imports to prevent initialization issues

// Mock core services that might cause initialization issues
jest.mock('@server/lib/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  loggerContextMiddleware: (req: any, res: any, next: any) => next(),
  updateLoggerContext: jest.fn(),
}));

jest.mock('@server/storage', () => ({
  __esModule: true,
  storage: {
    getUser: jest.fn(),
    getUserByFirebaseUid: jest.fn(),
  },
}));

jest.mock('@server/auth/firebase', () => ({
  __esModule: true,
  verifyFirebaseToken: jest.fn(),
}));

// Mock all the services that email routes depend on
jest.mock('@server/services/tokenService', () => ({
  __esModule: true,
  default: {
    getValidTokens: jest.fn(),
  },
}));

jest.mock('@server/services/dataRetention', () => ({
  __esModule: true,
  dataRetentionService: {
    cleanupExpiredContent: jest.fn(),
  },
}));

jest.mock('@server/utils/errorHandler', () => ({
  __esModule: true,
  catchAsync: (fn: any) => fn,
}));

jest.mock('@server/utils/inputValidator', () => ({
  __esModule: true,
  EmailSchemas: {},
  validateRequest: jest.fn(),
}));

jest.mock('@server/lib/standardizedResponses', () => ({
  __esModule: true,
  ErrorCode: {},
  sendError: jest.fn(),
  sendSuccess: jest.fn(),
}));

// Mock authentication middleware
jest.mock('@server/middleware/simpleAuth', () => ({
  __esModule: true,
  requireAuth: (req: any, res: any, next: any) => {
    req.user = { id: 1, email: '<EMAIL>' };
    next();
  },
  optionalAuth: (req: any, res: any, next: any) => {
    req.user = { id: 1, email: '<EMAIL>' };
    next();
  },
}));

jest.mock('@server/services/emailDataService', () => {
  // Define mock data inside the mock to avoid initialization issues
  const mockEmailsInMock = [
    {
      id: 1,
      messageId: 'msg-1',
      subject: 'Test Email 1',
      snippet: 'This is a test email snippet',
      sender: 'Test Sender 1',
      senderEmail: '<EMAIL>',
      receivedAt: new Date('2024-01-01'),
      isRead: false,
      isArchived: false,
      isTrashed: false,
      isImportant: false,
      isReplied: false,
      categories: ['work'],
      priority: 'normal',
      snoozedUntil: null,
      threadId: 'thread-1',
      provider: 'gmail',
      summary: 'This is a summary of the email',
      aiReply: 'This is an AI generated reply',
      labelIds: ['INBOX'],
      originalContent: 'This is the original email content'.repeat(100),
      htmlContent: '<p>This is HTML content</p>'.repeat(50),
      userId: 1,
      contentExpiresAt: null,
      lastAccessed: new Date(),
      isContentEncrypted: false,
      retentionDays: 30,
      replyDate: null,
      replyId: null,
    },
    {
      id: 2,
      messageId: 'msg-2',
      subject: 'Test Email 2',
      snippet: 'Another test email snippet',
      sender: 'Test Sender 2',
      senderEmail: '<EMAIL>',
      receivedAt: new Date('2024-01-02'),
      isRead: true,
      isArchived: false,
      isTrashed: false,
      isImportant: true,
      isReplied: false,
      categories: ['personal'],
      priority: 'high',
      snoozedUntil: null,
      threadId: 'thread-2',
      provider: 'gmail',
      summary: 'Summary of the second email',
      aiReply: 'AI reply for second email',
      labelIds: ['INBOX', 'IMPORTANT'],
      originalContent: 'Original content of second email'.repeat(100),
      htmlContent: '<p>HTML content of second email</p>'.repeat(50),
      userId: 1,
      contentExpiresAt: null,
      lastAccessed: new Date(),
      isContentEncrypted: false,
      retentionDays: 30,
      replyDate: null,
      replyId: null,
    },
  ];

  return {
    __esModule: true,
    emailDataService: {
      getEmailsForList: jest.fn().mockResolvedValue(
        mockEmailsInMock.map(email => ({
          id: email.id,
          messageId: email.messageId,
          subject: email.subject,
          snippet: email.snippet,
          sender: email.sender,
          senderEmail: email.senderEmail,
          receivedAt: email.receivedAt,
          isRead: email.isRead,
          isArchived: email.isArchived,
          isTrashed: email.isTrashed,
          isImportant: email.isImportant,
          isReplied: email.isReplied,
          categories: email.categories,
          priority: email.priority,
          snoozedUntil: email.snoozedUntil,
          threadId: email.threadId,
          provider: email.provider,
          // Excludes: originalContent, htmlContent, summary, aiReply
        }))
      ),
      getEmailsForSummary: jest.fn().mockResolvedValue(
        mockEmailsInMock.map(email => ({
          id: email.id,
          messageId: email.messageId,
          subject: email.subject,
          snippet: email.snippet,
          sender: email.sender,
          senderEmail: email.senderEmail,
          receivedAt: email.receivedAt,
          isRead: email.isRead,
          isArchived: email.isArchived,
          isTrashed: email.isTrashed,
          isImportant: email.isImportant,
          isReplied: email.isReplied,
          categories: email.categories,
          priority: email.priority,
          snoozedUntil: email.snoozedUntil,
          threadId: email.threadId,
          provider: email.provider,
          summary: email.summary,
          aiReply: email.aiReply,
          labelIds: email.labelIds,
          // Excludes: originalContent, htmlContent
        }))
      ),
      getEmailForDetail: jest.fn().mockResolvedValue(mockEmailsInMock[0]),
    },
  };
});

// Mock legacy email service for total count
jest.mock('@server/services/email-v2', () => ({
  __esModule: true,
  emailService: {
    getEmails: jest.fn().mockResolvedValue({
      emails: [],
      totalEmails: 2,
    }),
  },
}));

// Import after all mocks are set up
import request from 'supertest';
import { app } from '@server/app';
import { EmailViewType } from '@server/types/emailViews';

describe('Data Egress Optimization Integration Tests', () => {
  describe('GET /api/emails - Optimized Email List', () => {
    it('should return optimized email list with view=list parameter', async () => {
      console.log('Running test: should return optimized email list with view=list parameter');
      const response = await request(app)
        .get('/api/emails?view=list&limit=20&offset=0')
        .expect(200);

      expect(response.body).toHaveProperty('emails');
      expect(response.body).toHaveProperty('optimized', true);
      expect(response.body).toHaveProperty('viewType', 'list');
      expect(response.body).toHaveProperty('estimatedDataReduction', '85%');

      const emails = response.body.emails;
      expect(emails).toHaveLength(2);

      // Verify list view fields are present
      expect(emails[0]).toHaveProperty('id');
      expect(emails[0]).toHaveProperty('messageId');
      expect(emails[0]).toHaveProperty('subject');
      expect(emails[0]).toHaveProperty('snippet');
      expect(emails[0]).toHaveProperty('sender');
      expect(emails[0]).toHaveProperty('isRead');
      expect(emails[0]).toHaveProperty('categories');

      // Verify heavy content fields are excluded
      expect(emails[0]).not.toHaveProperty('originalContent');
      expect(emails[0]).not.toHaveProperty('htmlContent');
      expect(emails[0]).not.toHaveProperty('summary');
      expect(emails[0]).not.toHaveProperty('aiReply');
    });

    it('should return optimized email summary with view=summary parameter', async () => {
      const response = await request(app)
        .get('/api/emails?view=summary&limit=20&offset=0')
        .expect(200);

      expect(response.body).toHaveProperty('optimized', true);
      expect(response.body).toHaveProperty('viewType', 'summary');
      expect(response.body).toHaveProperty('estimatedDataReduction', '70%');

      const emails = response.body.emails;
      expect(emails).toHaveLength(2);

      // Verify summary fields are present
      expect(emails[0]).toHaveProperty('summary');
      expect(emails[0]).toHaveProperty('aiReply');
      expect(emails[0]).toHaveProperty('labelIds');

      // Verify heavy content fields are still excluded
      expect(emails[0]).not.toHaveProperty('originalContent');
      expect(emails[0]).not.toHaveProperty('htmlContent');
    });

    it('should default to list view when no view parameter specified', async () => {
      const response = await request(app)
        .get('/api/emails?limit=20&offset=0')
        .expect(200);

      expect(response.body).toHaveProperty('optimized', true);
      expect(response.body).toHaveProperty('viewType', 'list');
    });

    it('should handle pagination correctly with optimization', async () => {
      const response = await request(app)
        .get('/api/emails?view=list&limit=10&offset=5')
        .expect(200);

      expect(response.body).toHaveProperty('limit', 10);
      expect(response.body).toHaveProperty('hasMore');
      expect(response.body).toHaveProperty('totalEmails', 2);
    });

    it('should handle filters with optimization', async () => {
      const response = await request(app)
        .get('/api/emails?view=list&important=true&archived=false')
        .expect(200);

      expect(response.body).toHaveProperty('optimized', true);
      expect(response.body.emails).toBeDefined();
    });
  });

  describe('GET /api/emails/by-message-id/:messageId - Optimized Email Detail', () => {
    it('should return full email detail with optimization tracking', async () => {
      const response = await request(app)
        .get('/api/emails/by-message-id/msg-1')
        .expect(200);

      const email = response.body;

      // Should include all fields for detail view
      expect(email).toHaveProperty('id');
      expect(email).toHaveProperty('messageId');
      expect(email).toHaveProperty('subject');
      expect(email).toHaveProperty('originalContent');
      expect(email).toHaveProperty('htmlContent');
      expect(email).toHaveProperty('summary');
      expect(email).toHaveProperty('aiReply');
    });

    it('should return 404 for non-existent email', async () => {
      const { emailDataService } = require('@server/services/emailDataService');
      emailDataService.getEmailForDetail.mockResolvedValueOnce(null);

      await request(app)
        .get('/api/emails/by-message-id/nonexistent')
        .expect(404);
    });
  });

  describe('Data Size Comparison', () => {
    it('should demonstrate significant data reduction between view types', async () => {
      // Get list view response
      const listResponse = await request(app)
        .get('/api/emails?view=list&limit=1')
        .expect(200);

      // Get summary view response
      const summaryResponse = await request(app)
        .get('/api/emails?view=summary&limit=1')
        .expect(200);

      // Get detail view response
      const detailResponse = await request(app)
        .get('/api/emails/by-message-id/msg-1')
        .expect(200);

      // Calculate response sizes
      const listSize = JSON.stringify(listResponse.body.emails[0]).length;
      const summarySize = JSON.stringify(summaryResponse.body.emails[0]).length;
      const detailSize = JSON.stringify(detailResponse.body).length;

      // Verify size relationships
      expect(listSize).toBeLessThan(summarySize);
      expect(summarySize).toBeLessThan(detailSize);

      // Calculate reduction percentages
      const listReduction = ((detailSize - listSize) / detailSize) * 100;
      const summaryReduction = ((detailSize - summarySize) / detailSize) * 100;

      // Should achieve expected reductions
      expect(listReduction).toBeGreaterThan(50); // At least 50% reduction
      expect(summaryReduction).toBeGreaterThan(30); // At least 30% reduction
    });
  });

  describe('Performance Optimization Metadata', () => {
    it('should include optimization metadata in responses', async () => {
      const response = await request(app)
        .get('/api/emails?view=list')
        .expect(200);

      expect(response.body).toHaveProperty('optimized', true);
      expect(response.body).toHaveProperty('viewType', 'list');
      expect(response.body).toHaveProperty('estimatedDataReduction', '85%');
    });

    it('should track different view types correctly', async () => {
      const listResponse = await request(app)
        .get('/api/emails?view=list')
        .expect(200);

      const summaryResponse = await request(app)
        .get('/api/emails?view=summary')
        .expect(200);

      expect(listResponse.body.viewType).toBe('list');
      expect(listResponse.body.estimatedDataReduction).toBe('85%');

      expect(summaryResponse.body.viewType).toBe('summary');
      expect(summaryResponse.body.estimatedDataReduction).toBe('70%');
    });
  });

  describe('Error Handling with Optimization', () => {
    it('should handle service errors gracefully', async () => {
      const { emailDataService } = require('@server/services/emailDataService');
      emailDataService.getEmailsForList.mockRejectedValueOnce(new Error('Database error'));

      const response = await request(app)
        .get('/api/emails?view=list')
        .expect(500);

      expect(response.body).toHaveProperty('error');
    });

    it('should handle invalid view types gracefully', async () => {
      const response = await request(app)
        .get('/api/emails?view=invalid')
        .expect(200);

      // Should default to list view
      expect(response.body).toHaveProperty('viewType', 'list');
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain compatibility with existing clients', async () => {
      const response = await request(app)
        .get('/api/emails') // No view parameter
        .expect(200);

      // Should still return expected structure
      expect(response.body).toHaveProperty('emails');
      expect(response.body).toHaveProperty('totalEmails');
      expect(response.body.emails).toBeInstanceOf(Array);
    });

    it('should handle legacy query parameters', async () => {
      const response = await request(app)
        .get('/api/emails?limit=10&offset=0&archived=false')
        .expect(200);

      expect(response.body).toHaveProperty('emails');
      expect(response.body).toHaveProperty('optimized', true);
    });
  });
});
