import React from 'react';
import { act, renderHook } from '@testing-library/react';

// Mocks
const mockApiClient = { get: jest.fn(), post: jest.fn() };
jest.mock('@/lib/apiClient', () => ({ __esModule: true, default: mockApiClient }));

jest.mock('wouter', () => ({ useLocation: () => ['/', jest.fn()] }));

import { AuthProvider, useAuth } from '@/context/AuthContext';

const Wrapper = ({ children }: { children: React.ReactNode }) => <AuthProvider>{children}</AuthProvider>;

describe('Hooks/useAuth', () => {
  beforeEach(() => jest.clearAllMocks());

  it('returns initial loading state', () => {
    const { result } = renderHook(() => useAuth(), { wrapper: Wrapper });
    expect(result.current.loading).toBe(false);
  });

  it('refetchUser sets user', async () => {
    const user = { id: 1, email: '<EMAIL>' } as any;
    mockApiClient.get.mockResolvedValueOnce({ isAuthenticated: true, user });
    const { result } = renderHook(() => useAuth(), { wrapper: Wrapper });

    await act(async () => {
      await result.current.refetchUser();
    });
    expect(result.current.user).toEqual(user);
  });
}); 