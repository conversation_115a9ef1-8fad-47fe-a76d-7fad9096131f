/**
 * Atomic, Redis-backed rate limiter for AI operations.
 * Uses a token bucket algorithm implemented with a Lua script for atomicity.
 * This implementation is safe for distributed environments.
 */

import type { RedisClientType } from 'redis';
import logger from '../lib/logger';
import { getRedisClient } from './redis';

// Default settings
const DEFAULT_BUCKET_CAPACITY = 50; // Max tokens
const DEFAULT_REFILL_PERIOD_MS = 10 * 60 * 1000; // 10 minutes in milliseconds

/**
 * Defines a Lua script for an atomic token bucket rate limiting algorithm.
 * This script handles getting the current token count, refilling the bucket based on elapsed time,
 * and decrementing the count if a token is available.
 *
 * KEYS[1] - The unique key for the rate limit bucket (e.g., `rate-limit:gemini:user123`)
 * ARGV[1] - Bucket capacity (max tokens)
 * ARGV[2] - Refill rate (tokens per millisecond)
 * ARGV[3] - Current timestamp (in milliseconds)
 *
 * @returns {number} - 0 if the request is denied, 1 if it is allowed.
 * @returns {number} - The number of remaining tokens in the bucket after the operation.
 */
const LUA_SCRIPT_TOKEN_BUCKET = `
  local key = KEYS[1]
  local capacity = tonumber(ARGV[1])
  local refill_rate = tonumber(ARGV[2])
  local now = tonumber(ARGV[3])

  local bucket = redis.call('HGETALL', key)
  
  local tokens
  local last_refill

  if #bucket == 0 then
    tokens = capacity
    last_refill = now
  else
    tokens = tonumber(bucket[2])
    last_refill = tonumber(bucket[4])
  end
  
  local elapsed = now - last_refill
  local tokens_to_add = elapsed * refill_rate
  tokens = math.min(capacity, tokens + tokens_to_add)
  last_refill = now

  local allowed = 0
  if tokens >= 1 then
    tokens = tokens - 1
    allowed = 1
  end

  redis.call('HSET', key, 'tokens', tokens, 'lastRefill', last_refill)
  -- Set an expiry on the key to prevent orphaned keys in Redis
  redis.call('PEXPIRE', key, ARGV[4])

  return { allowed, math.floor(tokens) }
`;

// Store the Lua script hash
let luaScriptHash: string | null = null;

// Helper to load the Lua script in Redis once
async function ensureLuaScript(redis: RedisClientType): Promise<string> {
  if (luaScriptHash) {
    return luaScriptHash;
  }

  luaScriptHash = await redis.scriptLoad(LUA_SCRIPT_TOKEN_BUCKET);
  return luaScriptHash;
}

/**
 * Check if an operation exceeds rate limits.
 * @returns A promise resolving to an object indicating if the request is allowed and the remaining tokens.
 */
export async function checkRateLimit(
  operationType: string,
  identifier: string | number,
  capacity: number = DEFAULT_BUCKET_CAPACITY
): Promise<{ allowed: boolean; remaining: number }> {
  const redis = getRedisClient();

  const key = `rate-limit:${operationType}:${String(identifier)}`;
  const refillRate = capacity / DEFAULT_REFILL_PERIOD_MS;
  const now = Date.now();
  // TTL should be longer than the refill period to avoid deleting active buckets
  const ttl = DEFAULT_REFILL_PERIOD_MS * 2;

  try {
    const scriptHash = await ensureLuaScript(redis);
    const result = (await redis.evalSha(scriptHash, {
      keys: [key],
      arguments: [String(capacity), String(refillRate), String(now), String(ttl)],
    })) as [number, number];

    const [allowed, remaining] = result;
    return { allowed: allowed === 1, remaining };
  } catch (error) {
    logger.error('Rate limiter Redis error. Denying request as a fallback.', {
      err: error,
      service: 'rateLimiter',
      operationType,
      identifier,
    });
    // Fail closed: If the rate limiter fails, we don't allow the operation.
    return { allowed: false, remaining: 0 };
  }
}

/**
 * Clear all rate limit data for a given pattern.
 * NOTE: This uses SCAN and DEL. It is not an atomic operation and should be used for administrative purposes only.
 */
export async function clearRateLimits(pattern = 'rate-limit:*'): Promise<void> {
  const redis = getRedisClient();
  const keys: string[] = [];

  // Use SCAN to find keys matching the pattern
  for await (const key of redis.scanIterator({ MATCH: pattern })) {
    if (typeof key === 'string') {
      keys.push(key);
    }
  }

  if (keys.length > 0) {
    await redis.del(keys);
    logger.info(`Cleared ${keys.length} rate limit keys matching pattern: ${pattern}`);
  }
}

/**
 * Get rate limit status for a specific operation and identifier
 */
export async function getRateLimitStatus(
  operationType: string,
  identifier: string | number
): Promise<{ remaining: number; resetTime: number }> {
  const redis = getRedisClient();
  const key = `rate-limit:${operationType}:${String(identifier)}`;

  try {
    const bucket = await redis.hGetAll(key);
    
    if (!bucket || Object.keys(bucket).length === 0) {
      return { remaining: DEFAULT_BUCKET_CAPACITY, resetTime: Date.now() };
    }

    const tokens = Number.parseFloat(bucket.tokens || '0');
    const lastRefill = Number.parseInt(bucket.lastRefill || '0', 10);
    
    return { 
      remaining: Math.floor(tokens), 
      resetTime: lastRefill + DEFAULT_REFILL_PERIOD_MS 
    };
  } catch (error) {
    logger.error('Error getting rate limit status', {
      err: error,
      service: 'rateLimiter',
      operationType,
      identifier,
    });
    return { remaining: 0, resetTime: Date.now() + DEFAULT_REFILL_PERIOD_MS };
  }
}
