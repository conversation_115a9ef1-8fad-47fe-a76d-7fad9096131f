import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Get a CSS variable value with fallback
 * @param variableName The name of the CSS variable (including --) e.g. --primary
 * @param fallback A fallback value if the variable isn't found
 * @returns The value of the CSS variable or the fallback
 */
export function getCssVariable(variableName: string, fallback = ''): string {
  if (typeof window === 'undefined' || !window.document || !window.document.documentElement) {
    return fallback;
  }

  try {
    // Get computed style
    const style = getComputedStyle(document.documentElement);
    const value = style.getPropertyValue(variableName).trim();

    // Return the value if it exists, otherwise use fallback
    return value || fallback;
  } catch (error) {
    console.error(`Error getting CSS variable ${variableName}:`, error);
    return fallback;
  }
}

/**
 * Check if the current theme is dark mode
 * This function closely aligns with the ThemeContext implementation
 * @returns Boolean indicating if dark mode is active
 */
export function isDarkMode(): boolean {
  if (typeof window === 'undefined') return false;

  // First check if dark class is directly applied to html element (explicit user selection)
  if (document.documentElement.classList.contains('dark')) {
    return true;
  }

  // Next check if theme is set to system (either explicitly or implicitly)
  const isSystem =
    document.documentElement.classList.contains('system') ||
    (!document.documentElement.classList.contains('light') &&
      !document.documentElement.classList.contains('dark'));

  if (isSystem) {
    // Check system preference
    return window.matchMedia?.('(prefers-color-scheme: dark)').matches;
  }

  return false;
}

// Convert hex color to RGB with safety checks
export function hexToRGB(hex: string, alpha?: number): string {
  // Default fallback color if hex is invalid
  if (!hex || typeof hex !== 'string' || !hex.startsWith('#') || hex.length !== 7) {
    // Return a medium gray as fallback
    return alpha !== undefined ? `rgba(128, 128, 128, ${alpha})` : 'rgb(128, 128, 128)';
  }

  try {
    const r = Number.parseInt(hex.slice(1, 3), 16);
    const g = Number.parseInt(hex.slice(3, 5), 16);
    const b = Number.parseInt(hex.slice(5, 7), 16);

    // Check if any of the parsed values are NaN
    if (Number.isNaN(r) || Number.isNaN(g) || Number.isNaN(b)) {
      return alpha !== undefined ? `rgba(128, 128, 128, ${alpha})` : 'rgb(128, 128, 128)';
    }

    if (alpha !== undefined) {
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    return `rgb(${r}, ${g}, ${b})`;
  } catch (_error) {
    return alpha !== undefined ? `rgba(128, 128, 128, ${alpha})` : 'rgb(128, 128, 128)';
  }
}

// Blend two colors with a percentage, with error handling
export function blendColors(color1: string, color2: string, percentage: number): string {
  // Validate inputs
  if (!color1 || !color2 || typeof color1 !== 'string' || typeof color2 !== 'string') {
    return '#888888'; // Default gray
  }

  // Validate hex format
  if (
    !color1.startsWith('#') ||
    color1.length !== 7 ||
    !color2.startsWith('#') ||
    color2.length !== 7
  ) {
    return '#888888';
  }

  try {
    // Convert hex colors to RGB components
    const r1 = Number.parseInt(color1.slice(1, 3), 16);
    const g1 = Number.parseInt(color1.slice(3, 5), 16);
    const b1 = Number.parseInt(color1.slice(5, 7), 16);

    const r2 = Number.parseInt(color2.slice(1, 3), 16);
    const g2 = Number.parseInt(color2.slice(3, 5), 16);
    const b2 = Number.parseInt(color2.slice(5, 7), 16);

    // Validate RGB values
    if (
      Number.isNaN(r1) ||
      Number.isNaN(g1) ||
      Number.isNaN(b1) ||
      Number.isNaN(r2) ||
      Number.isNaN(g2) ||
      Number.isNaN(b2)
    ) {
      return '#888888';
    }

    // Clamp percentage to 0-100 range
    const clampedPercentage = Math.max(0, Math.min(100, percentage));

    // Calculate the blended color components
    const factor = clampedPercentage / 100;
    const r = Math.round(r1 + (r2 - r1) * factor);
    const g = Math.round(g1 + (g2 - g1) * factor);
    const b = Math.round(b1 + (b2 - b1) * factor);

    // Convert back to hex
    const rHex = r.toString(16).padStart(2, '0');
    const gHex = g.toString(16).padStart(2, '0');
    const bHex = b.toString(16).padStart(2, '0');

    return `#${rHex}${gHex}${bHex}`;
  } catch (_error) {
    return '#888888'; // Fallback color on error
  }
}

/**
 * Adjusts a color for dark mode to ensure readability
 * Uses different strategies for light vs dark mode
 *
 * @param color The base color (hex) to adjust
 * @param isDark Whether dark mode is active
 * @returns Adjusted color optimized for the current theme
 */
export function getThemeAdjustedColor(color: string, isDark = false): string {
  if (!color || typeof color !== 'string' || !color.startsWith('#')) {
    return isDark ? '#c0c0c0' : '#505050'; // Default fallback based on mode
  }

  try {
    // Parse the RGB components
    let r = Number.parseInt(color.slice(1, 3), 16);
    let g = Number.parseInt(color.slice(3, 5), 16);
    let b = Number.parseInt(color.slice(5, 7), 16);

    if (Number.isNaN(r) || Number.isNaN(g) || Number.isNaN(b)) {
      return isDark ? '#c0c0c0' : '#505050';
    }

    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    if (isDark) {
      // For dark mode: if color is too dark, lighten it.
      if (brightness < 130) {
        const factor = 1.4;
        r = Math.round(Math.min(255, r * factor));
        g = Math.round(Math.min(255, g * factor));
        b = Math.round(Math.min(255, b * factor));
      }
    } else {
      // For light mode: if color is too light, darken it.
      if (brightness > 200) {
        const factor = 0.7;
        r = Math.round(r * factor);
        g = Math.round(g * factor);
        b = Math.round(b * factor);
      }
    }

    // Convert back to hex
    const rHex = r.toString(16).padStart(2, '0');
    const gHex = g.toString(16).padStart(2, '0');
    const bHex = b.toString(16).padStart(2, '0');

    return `#${rHex}${gHex}${bHex}`;
  } catch (_error) {
    return isDark ? '#c0c0c0' : '#505050'; // Fallback on error
  }
}

/**
 * Generates initials from a user's display name or email.
 * @param user An object with optional displayName and email properties.
 * @returns A string of 1 or 2 characters representing the user's initials.
 */
export function getUserInitials(name: string): string {
  if (!name) return '';
  return name
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase();
}
