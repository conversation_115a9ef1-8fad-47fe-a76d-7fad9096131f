/**
 * Middleware: Security (applySecurityMiddleware)
 *
 * Covered scenarios:
 * 1. Rate limiting precedence – sensitive endpoints block after 10 requests while standard endpoints still allow.
 * 2. Security headers – CSP (without 'unsafe-inline') and other key headers are set.
 * 3. Request ID – uuidv4 is generated when absent; existing ID is preserved.
 */

import express from 'express';
import request from 'supertest';
import { applySecurityMiddleware } from '@server/middleware/security';
import { validate as uuidValidate, version as uuidVersion } from 'uuid';

// Stub logger to silence output & avoid real transports
jest.mock('@server/lib/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

describe('security middleware', () => {
  const buildApp = () => {
    const app = express();

    // The middleware chain under test
    applySecurityMiddleware(app);

    // example endpoints
    app.post('/api/auth/reset-password', (_req: any, res: any) => res.status(200).send('reset'));
    app.get('/api/emails', (_req: any, res: any) => res.status(200).send('emails'));

    return app;
  };

  it('applies security headers & CSP without unsafe-inline', async () => {
    const app = buildApp();
    const res = await request(app).get('/api/emails');

    expect(res.headers['x-content-type-options']).toBe('nosniff');
    expect(res.headers['x-frame-options']).toBe('SAMEORIGIN');
    expect(res.headers['strict-transport-security']).toContain('max-age=31536000');
    expect(res.headers['content-security-policy']).toBeDefined();
    expect(res.headers['content-security-policy']).not.toMatch(/unsafe-inline/);
  });

  it('generates a uuid v4 request id when none provided', async () => {
    const app = buildApp();
    const res = await request(app).get('/api/emails');

    const reqId = res.headers['x-request-id'];
    expect(reqId).toBeDefined();
    expect(uuidValidate(reqId)).toBe(true);
    expect(uuidVersion(reqId)).toBe(4);
  });

  it('preserves an existing request id header', async () => {
    const existingId = 'custom-request-id-123';
    const app = buildApp();

    const res = await request(app).get('/api/emails').set('x-request-id', existingId);
    expect(res.headers['x-request-id']).toBe(existingId);
  });

  it('enforces stricter rate limit on sensitive endpoints compared to standard endpoints', async () => {
    const app = buildApp();
    const agent = request.agent(app);

    // Hit sensitive endpoint 11 times (limit is 10 per security.ts)
    let sensitiveStatus = 200;
    for (let i = 0; i < 11; i += 1) {
      const res = await agent.post('/api/auth/reset-password');
      sensitiveStatus = res.status;
    }
    expect(sensitiveStatus).toBe(429);

    // Hit standard endpoint same number of times – should still succeed (limit 200)
    let standardStatus = 200;
    for (let i = 0; i < 11; i += 1) {
      const res = await agent.get('/api/emails');
      standardStatus = res.status;
    }
    expect(standardStatus).toBe(200);
  });
}); 