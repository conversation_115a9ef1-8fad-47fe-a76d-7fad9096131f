*Variables are filled out in actual .env file*

# Base URL for OAuth callbacks
BASE_URL=
CLIENT_ORIGIN=

# Firebase Configuration
VITE_FIREBASE_API_KEY=
VITE_FIREBASE_AUTH_DOMAIN=
VITE_FIREBASE_PROJECT_ID=
VITE_FIREBASE_STORAGE_BUCKET=
VITE_FIREBASE_MESSAGING_SENDER_ID=
VITE_FIREBASE_APP_ID=

# Firebase Admin Configuration
FIREBASE_SERVICE_ACCOUNT=

# Google OAuth Configuration
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI=
# Supabase Configuration
DATABASE_URL=
SUPABASE_ANON_KEY=
# Gemini API Configuration
GEMINI_API_KEY=


VITE_PUBLIC_SUPABASE_URL=
VITE_PUBLIC_SUPABASE_ANON_KEY=

SESSION_SECRET=
ENCRYPTION_KEY=
LOCALHOST_TESTING=true
NODE_ENV=development
PORT=5000
BASE_URL=

# Development flags
BYPASS_AUTHENTICATION_FOR_STATS=