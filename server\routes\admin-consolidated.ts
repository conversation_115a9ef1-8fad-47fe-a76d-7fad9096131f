import { Router } from 'express';

import adminRouter from './admin-routes';
import errorCacheRouter from './error-cache';
import memoryMonitorRouter from './memory-monitor';

/**
 * Consolidated Admin Routes
 *
 * Combines higher-level admin utilities:
 *   • /admin/tools/* … (adminRouter)
 *   • /admin/error-cache/* … diagnostics & cache operations
 *
 * The router itself contains no auth checks; registerRoutes enforces
 * `requireAuth` + `isAdmin` when mounting.
 */
const router = Router();

router.use('/', adminRouter);
router.use('/error-cache', errorCacheRouter);
router.use('/memory', memoryMonitorRouter);

export default router;
