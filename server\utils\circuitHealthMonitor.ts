/**
 * Circuit Health Monitor
 *
 * This module provides utilities for monitoring the health of the system's
 * circuit breakers.
 */

import logger from '../lib/logger';
import { circuitBreakerRegistry } from './circuitBreaker';

// Define state types
enum CircuitState {
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN',
  CLOSED = 'CLOSED',
}

// Interface for circuit statistics
interface CircuitStats {
  total: number;
  open: number;
  halfOpen: number;
  closed: number;
}

/**
 * Get statistics about the real-time state of all registered circuit breakers.
 */
export async function getSystemCircuitStats(): Promise<CircuitStats> {
  try {
    const states = await circuitBreakerRegistry.getStates();
    const stats: CircuitStats = {
      total: 0,
      open: 0,
      halfOpen: 0,
      closed: 0,
    };

    for (const id in states) {
      stats.total++;
      const breakerState = states[id].state;
      if (breakerState === CircuitState.OPEN) {
        stats.open++;
      } else if (breakerState === CircuitState.HALF_OPEN) {
        stats.halfOpen++;
      } else {
        stats.closed++;
      }
    }
    return stats;
  } catch (error) {
    logger.error('Error getting circuit breaker stats:', error);
    return { total: 0, open: 0, halfOpen: 0, closed: 0 };
  }
}

/**
 * Check if any critical circuits are open.
 */
export async function hasCriticalOpenCircuits(): Promise<boolean> {
  try {
    const stats = await getSystemCircuitStats();
    return stats.open > 0;
  } catch (error) {
    logger.error('Error checking critical circuits:', error);
    return false; // Assume healthy on error
  }
}

/**
 * Get a detailed health report of the system's circuits and memory.
 */
export async function getDetailedHealthReport() {
  try {
    const stats = await getSystemCircuitStats();
    const breakerStates = await circuitBreakerRegistry.getStates();

    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();
    const memory = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
    };

    const overallHealth = stats.open > 0 ? 'unhealthy' : 'healthy';

    return {
      timestamp: new Date().toISOString(),
      circuitStats: stats,
      circuitBreakers: Object.values(breakerStates).map((b: any) => ({
        id: b.name,
        state: b.state,
        failures: b.failures,
        successes: b.successes,
        nextAttempt: b.nextAttempt,
      })),
      system: {
        memory,
        uptime: Math.floor(uptime),
        nodeVersion: process.version,
      },
      health: {
        overall: overallHealth,
        hasCriticalFailures: stats.open > 0,
        note: `Monitoring ${stats.total} live circuit breakers.`,
      },
    };
  } catch (error) {
    logger.error('Error generating health report:', error);
    return {
      timestamp: new Date().toISOString(),
      error: 'Failed to generate health report',
      errorMessage: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Legacy function for backward compatibility
 * Circuit breaker registration is now handled by the consolidated system
 */
export function registerCircuitHealthMonitor(): () => void {
  logger.info('Circuit health monitoring initialized (consolidated system)');

  // Return a no-op cleanup function since monitoring is now integrated
  return () => {
    logger.info('Circuit health monitoring cleanup (consolidated system)');
  };
}
