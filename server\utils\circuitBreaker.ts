/**
 * An atomic, Redis-backed Circuit Breaker implementation.
 *
 * This class provides a robust circuit breaker that is safe for distributed environments.
 * It stores its state in a Redis Hash and uses Redis transactions (MULTI/EXEC) to ensure
 * that all state changes are atomic, preventing race conditions under high load.
 *
 * Key Features:
 * - Atomic state transitions using Redis transactions.
 * - No unsafe in-memory fallback; relies on a single source of truth (Redis).
 * - Fails "closed" (allows requests) if Redis is unavailable, a safe default for many systems.
 * - Emits state change events for monitoring.
 */

import type { RedisClientType } from 'redis';
import logger from '../lib/logger';
import { getRedisClient } from '../services/redis';

export enum CircuitStateEnum {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN',
}

export interface CircuitBreakerOptions {
  name: string;
  failureThreshold?: number;
  successThreshold?: number;
  resetTimeout?: number; // ms, how long to stay OPEN before moving to HALF_OPEN
  timeout?: number; // ms, for the async function call itself (not implemented in this version)
  onStateChange?: (newState: CircuitStateEnum, oldState: CircuitStateEnum, name: string) => void;
}

// Internal representation of the circuit's state stored in a Redis Hash
interface CircuitState {
  state: CircuitStateEnum;
  failures: string; // Stored as strings in Redis
  successes: string;
  nextAttempt: string;
}

export class CircuitBreaker {
  private readonly redis: RedisClientType | null;
  private readonly key: string;
  private readonly failureThreshold: number;
  private readonly successThreshold: number;
  private readonly resetTimeout: number;
  
  // In-memory fallback state when Redis is not available
  private memoryState: CircuitState = {
    state: CircuitStateEnum.CLOSED,
    failures: '0',
    successes: '0',
    nextAttempt: String(Date.now()),
  };
  private readonly useMemoryFallback: boolean;

  constructor(public readonly options: CircuitBreakerOptions) {
    try {
      this.redis = getRedisClient();
      this.useMemoryFallback = false;
    } catch (error) {
      logger.warn(`Circuit breaker [${options.name}] falling back to in-memory state due to Redis unavailability`, {
        error: error instanceof Error ? error.message : String(error),
        service: 'circuitBreaker'
      });
      this.redis = null;
      this.useMemoryFallback = true;
    }
    
    this.key = `circuit-breaker:${this.options.name}`;

    // Set default values for optional properties
    this.failureThreshold = options.failureThreshold ?? 5;
    this.successThreshold = options.successThreshold ?? 2;
    this.resetTimeout = options.resetTimeout ?? 60000; // 60 seconds
  }

  private async getState(): Promise<CircuitState> {
    const defaultState: CircuitState = {
      state: CircuitStateEnum.CLOSED,
      failures: '0',
      successes: '0',
      nextAttempt: String(Date.now()),
    };

    // Use in-memory fallback if Redis is not available
    if (this.useMemoryFallback || !this.redis) {
      return { ...this.memoryState };
    }

    try {
      const stateData = await this.redis.hGetAll(this.key);

      if (!stateData || Object.keys(stateData).length === 0) {
        // If the key doesn't exist or is empty, initialize it with default values
        await this.redis.hSet(this.key, {
          state: defaultState.state,
          failures: defaultState.failures,
          successes: defaultState.successes,
          nextAttempt: defaultState.nextAttempt,
        });
        return defaultState;
      }

      // Manually construct the state to ensure type safety
      return {
        state: (stateData.state as CircuitStateEnum) || CircuitStateEnum.CLOSED,
        failures: stateData.failures || '0',
        successes: stateData.successes || '0',
        nextAttempt: stateData.nextAttempt || String(Date.now()),
      };
    } catch (error) {
      logger.error(
        'Failed to get circuit breaker state from Redis. Allowing request as fallback (fail-closed).',
        {
          err: error,
          service: 'circuitBreaker',
          name: this.options.name,
        }
      );
      // "Fail-closed" behavior: If Redis is down, we allow the operation to proceed.
      return defaultState;
    }
  }

  private async setState(newState: CircuitStateEnum): Promise<void> {
    const currentState = await this.getState();
    if (currentState.state === newState) return;

    const update: Partial<CircuitState> = { state: newState };

    switch (newState) {
      case CircuitStateEnum.OPEN:
        update.nextAttempt = String(Date.now() + this.resetTimeout);
        update.failures = '0';
        update.successes = '0';
        break;
      case CircuitStateEnum.HALF_OPEN:
        update.successes = '0';
        break;
      case CircuitStateEnum.CLOSED:
        update.failures = '0';
        update.successes = '0';
        break;
    }

    // Use in-memory fallback if Redis is not available
    if (this.useMemoryFallback || !this.redis) {
      this.memoryState = { ...this.memoryState, ...update };
      this.options.onStateChange?.(newState, currentState.state, this.options.name);
      return;
    }

    try {
      await this.redis.hSet(this.key, update);
      this.options.onStateChange?.(newState, currentState.state, this.options.name);
    } catch (error) {
      logger.error('Failed to set circuit breaker state in Redis.', {
        err: error,
        service: 'circuitBreaker',
        name: this.options.name,
      });
    }
  }

  public async recordSuccess(): Promise<void> {
    const state = await this.getState();
    if (state.state !== CircuitStateEnum.HALF_OPEN) {
      return;
    }

    let successes: number;

    // Use in-memory fallback if Redis is not available
    if (this.useMemoryFallback || !this.redis) {
      const currentSuccesses = Number.parseInt(this.memoryState.successes, 10);
      successes = currentSuccesses + 1;
      this.memoryState.successes = String(successes);
    } else {
      try {
        successes = await this.redis.hIncrBy(this.key, 'successes', 1);
      } catch (error) {
        logger.error('Failed to record success in Redis', {
          error: error instanceof Error ? error.message : String(error),
          service: 'circuitBreaker',
          name: this.options.name
        });
        return;
      }
    }

    if (successes >= this.successThreshold) {
      await this.setState(CircuitStateEnum.CLOSED);
    }
  }

  public async recordFailure(): Promise<void> {
    const state = await this.getState();
    if (state.state === CircuitStateEnum.OPEN) {
      return;
    }

    let failures: number;

    // Use in-memory fallback if Redis is not available
    if (this.useMemoryFallback || !this.redis) {
      const currentFailures = Number.parseInt(this.memoryState.failures, 10);
      failures = currentFailures + 1;
      this.memoryState.failures = String(failures);
    } else {
      try {
        failures = await this.redis.hIncrBy(this.key, 'failures', 1);
      } catch (error) {
        logger.error('Failed to record failure in Redis', {
          error: error instanceof Error ? error.message : String(error),
          service: 'circuitBreaker',
          name: this.options.name
        });
        return;
      }
    }

    if (state.state === CircuitStateEnum.HALF_OPEN || failures >= this.failureThreshold) {
      await this.setState(CircuitStateEnum.OPEN);
    }
  }

  public async execute<T>(asyncFn: () => Promise<T>, fallback?: () => Promise<T>): Promise<T> {
    const state = await this.getState();

    if (state.state === CircuitStateEnum.OPEN) {
      if (Date.now() >= Number(state.nextAttempt)) {
        await this.setState(CircuitStateEnum.HALF_OPEN);
        // Fall through to execute the function on the next line
      } else {
        return this.handleFallback(fallback);
      }
    }

    try {
      const result = await asyncFn();
      await this.recordSuccess();
      return result;
    } catch (error) {
      await this.recordFailure();
      return this.handleFallback(fallback, error as Error);
    }
  }

  private async handleFallback<T>(
    fallback: (() => Promise<T>) | undefined,
    originalError?: Error
  ): Promise<T> {
    if (fallback) {
      return fallback();
    }
    if (originalError) {
      throw originalError;
    }
    throw new Error(`CircuitBreaker[${this.options.name}] is OPEN and no fallback was provided.`);
  }

  public async forceReset(): Promise<void> {
    logger.warn(`Circuit Breaker [${this.options.name}] is being forcefully reset to CLOSED.`);
    await this.setState(CircuitStateEnum.CLOSED);
  }

  public async getStateDetails() {
    return { name: this.options.name, ...(await this.getState()) };
  }
}

class CircuitBreakerRegistry {
  private breakers = new Map<string, CircuitBreaker>();

  public getOrCreate(options: CircuitBreakerOptions): CircuitBreaker {
    if (!this.breakers.has(options.name)) {
      this.breakers.set(options.name, new CircuitBreaker(options));
    }
    return this.breakers.get(options.name)!;
  }

  public get(name: string): CircuitBreaker | undefined {
    return this.breakers.get(name);
  }

  public getAll(): Map<string, CircuitBreaker> {
    return this.breakers;
  }

  public async getStates(): Promise<Record<string, any>> {
    const states: Record<string, any> = {};
    for (const [name, breaker] of this.breakers.entries()) {
      states[name] = await breaker.getStateDetails();
    }
    return states;
  }
}

export const circuitBreakerRegistry = new CircuitBreakerRegistry();
