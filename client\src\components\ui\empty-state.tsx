import { InboxIcon } from 'lucide-react';
import type React from 'react';
import { cn } from '@/lib/utils';
import { Button } from './button';

interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
  compact?: boolean;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon = <InboxIcon className="h-12 w-12" />,
  title,
  description,
  action,
  secondaryAction,
  className,
  compact = false,
}) => {
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center text-center px-4 py-8 sm:py-12',
        compact ? 'space-y-3' : 'space-y-4',
        className
      )}
    >
      <div className="text-muted-foreground">{icon}</div>
      <div className={cn(compact ? 'space-y-1' : 'space-y-2')}>
        <h3 className={cn(compact ? 'text-lg font-medium' : 'text-xl font-semibold')}>{title}</h3>
        {description && <p className="text-sm text-muted-foreground max-w-sm">{description}</p>}
      </div>

      {(action || secondaryAction) && (
        <div className="flex flex-wrap gap-3 justify-center mt-2">
          {action && (
            <Button onClick={action.onClick} size={compact ? 'sm' : 'default'}>
              {action.label}
            </Button>
          )}
          {secondaryAction && (
            <Button
              variant="outline"
              onClick={secondaryAction.onClick}
              size={compact ? 'sm' : 'default'}
            >
              {secondaryAction.label}
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export { EmptyState };
