/**
 * Error Cache API Routes
 *
 * This module provides API endpoints for managing the error cache
 */

import express, { type Request, type Response } from 'express';
import { isAdmin } from '../middleware/admin';
import { circuitBreakerRegistry } from '../utils/circuitBreaker';
import { clearErrorCache } from '../utils/errorHandler';

// Initialize router
const router = express.Router();

// Apply admin middleware to all routes
router.use(isAdmin);

/**
 * Clear all error caches in the system
 * This includes error tracking caches and resets circuit breakers.
 */
router.post('/clear-error-cache', async (_req: Request, res: Response): Promise<void> => {
  try {
    // Clear custom error tracking caches
    clearErrorCache();
    // Reset all circuit breakers to the CLOSED state
    const breakers = circuitBreakerRegistry.getAll();
    for (const breaker of breakers.values()) {
      await breaker.forceReset();
    }

    res.json({
      success: true,
      message: 'Error cache and circuit breakers cleared successfully',
    });
  } catch (error) {
    console.error('Error clearing error cache:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear error cache',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

export default router;
