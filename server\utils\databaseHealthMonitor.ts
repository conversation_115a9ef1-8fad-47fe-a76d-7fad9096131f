/// <reference types="node" />

import { getDbConnection, getPoolStats, getAdvancedMetrics } from '../db';
import logger from '../lib/logger';
import { memoryManager } from './memoryManager';

interface DatabaseHealthMetrics {
  isHealthy: boolean;
  lastCheck: Date;
  consecutiveFailures: number;
  lastError?: string;
  poolStats?: any; // Enhanced pool statistics from Phase 1 optimizations
  // Phase 3: Advanced metrics integration
  advancedMetrics?: {
    connectionQuality: any;
    resourceUsage: any;
    poolMetrics: any;
  };
  performanceScore?: number;
  resourcePressure?: string;
}

class DatabaseHealthMonitor {
  private metrics: DatabaseHealthMetrics;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL_MS = 30000; // 30 seconds
  private readonly MAX_CONSECUTIVE_FAILURES = 3;

  constructor() {
    this.metrics = {
      isHealthy: true,
      lastCheck: new Date(),
      consecutiveFailures: 0,
    };
  }

  public startMonitoring(): void {
    if (this.healthCheckInterval) {
      logger.warn('[DB-HEALTH] Monitoring is already active.');
      return;
    }

    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, this.CHECK_INTERVAL_MS);

    // Perform initial health check
    this.performHealthCheck();
    logger.info('[DB-HEALTH] Database health monitoring started.');
  }

  public stopMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      logger.info('[DB-HEALTH] Database health monitoring stopped.');
    }
  }

  private async performHealthCheck(): Promise<void> {
    try {
      const queryClient = await getDbConnection();

      // Simple health check query
      await queryClient`SELECT 1`;

      // Reset failure count on success
      if (this.metrics.consecutiveFailures > 0) {
        logger.info('[DB-HEALTH] Database connection restored.');
      }

      this.metrics.isHealthy = true;
      this.metrics.consecutiveFailures = 0;
      this.metrics.lastCheck = new Date();
      this.metrics.lastError = undefined;
    } catch (error) {
      this.handleHealthCheckFailure(error);
    }
  }

  private handleHealthCheckFailure(error: any): void {
    this.metrics.consecutiveFailures++;
    this.metrics.lastCheck = new Date();
    this.metrics.lastError = error instanceof Error ? error.message : String(error);

    logger.error(
      `[DB-HEALTH] Health check failed (${this.metrics.consecutiveFailures}/${this.MAX_CONSECUTIVE_FAILURES})`,
      { error: this.metrics.lastError }
    );

    if (this.metrics.consecutiveFailures >= this.MAX_CONSECUTIVE_FAILURES) {
      if (this.metrics.isHealthy) {
        this.metrics.isHealthy = false;
        logger.error('[DB-HEALTH] Database marked as unhealthy due to consecutive failures.');
        // Optional: Trigger a notification or a more drastic recovery action here
      }
    }
  }

  public getHealthMetrics(): DatabaseHealthMetrics {
    // Include enhanced pool statistics from Phase 1 and Phase 3 optimizations
    try {
      const poolStats = getPoolStats();
      const advancedMetrics = getAdvancedMetrics();
      
      // Calculate overall performance score
      const performanceScore = advancedMetrics.connectionQuality.performanceScore;
      const resourcePressure = advancedMetrics.resourceUsage.resourcePressure;
      
      return { 
        ...this.metrics,
        poolStats,
        advancedMetrics,
        performanceScore,
        resourcePressure,
      };
    } catch (error) {
      logger.debug('[DB-HEALTH] Could not retrieve pool stats:', error);
      return { ...this.metrics };
    }
  }

  public isHealthy(): boolean {
    // Phase 3: Enhanced health check considering performance metrics
    if (!this.metrics.isHealthy) {
      return false;
    }
    
    try {
      const advancedMetrics = getAdvancedMetrics();
      
      // Consider performance score in health determination
      const performanceScore = advancedMetrics.connectionQuality.performanceScore;
      const resourcePressure = advancedMetrics.resourceUsage.resourcePressure;
      
      // If performance is critically low or resource pressure is critical, consider unhealthy
      if (performanceScore < 30 || resourcePressure === 'critical') {
        logger.warn(`[DB-HEALTH] Database performance degraded: score=${performanceScore}, pressure=${resourcePressure}`);
        return false;
      }
      
      return this.metrics.isHealthy;
    } catch (error) {
      logger.debug('[DB-HEALTH] Could not retrieve advanced metrics for health check:', error);
      return this.metrics.isHealthy;
    }
  }
}

// Singleton instance
const healthMonitor = new DatabaseHealthMonitor();

export { healthMonitor, type DatabaseHealthMetrics };

// Export functions for easy use
export const startDatabaseHealthMonitoring = () => healthMonitor.startMonitoring();
export const stopDatabaseHealthMonitoring = () => healthMonitor.stopMonitoring();
export const getDatabaseHealthMetrics = () => healthMonitor.getHealthMetrics();
export const isDatabaseHealthy = () => healthMonitor.isHealthy();
