import { useMutation, useQueryClient } from '@tanstack/react-query';
import { RotateCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import apiClient from '@/lib/apiClient';

export default function EmailSync() {
  // Access the email list context to refresh emails
  // const { refreshEmails } = useEmailList();

  // Get toast function for showing feedback
  const { toast } = useToast();
  const { user } = useAuth();

  // Check if we're on mobile
  const _isMobile = useIsMobile();

  // Get query client for cache operations
  const queryClient = useQueryClient();

  const syncMutation = useMutation<void, Error>({
    mutationFn: () => apiClient.post('/api/emails/sync'),
    onSuccess: () => {
      toast({
        title: 'Sync requested',
        description: 'Your emails will be synced shortly.',
      });
      // Invalidate queries to refetch data after sync is requested
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['stats'] });
    },
    onError: (error) => {
      toast({
        title: 'Sync failed',
        description: error.message || 'Could not request an email sync.',
        variant: 'destructive',
      });
    },
  });

  const handleSync = () => {
    syncMutation.mutate();
  };

  return (
    <Button onClick={handleSync} disabled={syncMutation.isPending} variant="outline" size="sm">
      <RotateCw className={`mr-2 h-4 w-4 ${syncMutation.isPending ? 'animate-spin' : ''}`} />
      {syncMutation.isPending ? 'Syncing...' : 'Sync Emails'}
    </Button>
  );
}
