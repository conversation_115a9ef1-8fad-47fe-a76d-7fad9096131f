/**
 * Token Refresh Hook with Resilience
 *
 * This hook provides access to token refresh functionality with
 * proper error handling and circuit breaker integration.
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import apiClient from '@/lib/apiClient';

export interface TokenStatus {
  connected: boolean;
  provider: string | null;
  status: string;
  lastError: string | null;
  lastErrorTime: string | null;
  errorCount: number;
  lastRefreshed: string | null;
}

export interface TokenRefreshResult {
  success: boolean;
  message?: string;
  error?: string;
  circuitOpen?: boolean;
  retryAfter?: number;
}

/**
 * Hook for token refresh operations with proper error handling
 */
export function useTokenRefresh() {
  const { toast } = useToast();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const queryClient = useQueryClient();

  // Query to get current token status
  const tokenStatusQuery = useQuery({
    queryKey: ['/api/auth/token-status'],
    queryFn: async () => {
      try {
        const response = await apiClient.get<{
          success: boolean;
          tokenStatus: TokenStatus;
        }>('/api/auth/token-status');
        return response.tokenStatus;
      } catch (error) {
        console.error('Error fetching token status:', error);
        return {
          connected: false,
          provider: null,
          status: 'error',
          lastError: 'Failed to fetch token status',
          lastErrorTime: null,
          errorCount: 0,
          lastRefreshed: null,
        };
      }
    },
    // Reasonable stale time to avoid too many refreshes
    staleTime: 60000, // 1 minute
    retry: 1,
  });

  // Mutation to refresh token
  const refreshMutation = useMutation({
    mutationFn: async (): Promise<TokenRefreshResult> => {
      setIsRefreshing(true);
      try {
        const response = await apiClient.post<TokenRefreshResult>('/api/auth/refresh-token');
        return response;
      } catch (error: any) {
        // Handle API errors and extract circuit breaker details if available
        if (error.response?.data) {
          return {
            success: false,
            error: error.response.data.error,
            circuitOpen: error.response.data.circuitOpen,
            retryAfter: error.response.data.retryAfter,
          };
        }
        return {
          success: false,
          error: error.message || 'Failed to refresh token',
        };
      } finally {
        setIsRefreshing(false);
      }
    },
    onSuccess: (result) => {
      if (result.success) {
        // Invalidate related queries to update token-dependent data
        queryClient.invalidateQueries({ queryKey: ['/api/auth/token-status'] });
        queryClient.invalidateQueries({
          queryKey: ['/api/email-providers/status'],
        });

        toast({
          title: 'Token refreshed',
          description: 'Your authentication has been refreshed successfully.',
        });
      } else {
        // Handle non-success results
        let errorMessage = result.error || 'Unknown error';

        // Special handling for circuit breaker
        if (result.circuitOpen) {
          errorMessage = `Token service is temporarily unavailable. Please try again ${
            result.retryAfter ? `in ${result.retryAfter} seconds` : 'later'
          }.`;
        }

        toast({
          title: 'Token refresh failed',
          description: errorMessage,
          variant: 'destructive',
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: 'Token refresh error',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      setIsRefreshing(false);
    },
  });

  // Mutation to fix token issues
  const fixTokenMutation = useMutation({
    mutationFn: async () => {
      setIsRefreshing(true);
      try {
        return await apiClient.post<{
          success: boolean;
          message: string;
          tokenRefreshed: boolean;
        }>('/api/auth/fix-token-issues');
      } finally {
        setIsRefreshing(false);
      }
    },
    onSuccess: (result) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['/api/auth/token-status'] });
      queryClient.invalidateQueries({
        queryKey: ['/api/email-providers/status'],
      });

      toast({
        title: result.tokenRefreshed ? 'Token fixed' : 'Token reset',
        description: result.message,
        variant: result.tokenRefreshed ? 'default' : 'destructive',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error fixing token',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
    },
  });

  // Function to refresh token with appropriate feedback
  const refreshToken = async () => {
    if (isRefreshing) return;

    // Check if token refresh is on cooldown (circuit breaker open)
    if (tokenStatusQuery.data?.status === 'error' && tokenStatusQuery.data?.lastErrorTime) {
      const lastErrorTime = new Date(tokenStatusQuery.data.lastErrorTime).getTime();
      const now = Date.now();
      const diffSeconds = Math.floor((now - lastErrorTime) / 1000);

      // If last error was less than 30 seconds ago and we've had multiple errors,
      // enforce a cooldown to prevent hammering the service
      if (diffSeconds < 30 && tokenStatusQuery.data.errorCount > 2) {
        toast({
          title: 'Please wait',
          description: `Too many recent attempts. Please try again in ${30 - diffSeconds} seconds.`,
          variant: 'destructive',
        });
        return;
      }
    }

    await refreshMutation.mutateAsync();
  };

  // Function to fix token issues with appropriate feedback
  const fixTokenIssues = async () => {
    if (isRefreshing) return;
    await fixTokenMutation.mutateAsync();
  };

  return {
    tokenStatus: tokenStatusQuery.data,
    isLoading: tokenStatusQuery.isLoading,
    isRefreshing,
    refreshToken,
    fixTokenIssues,
    hasTokenIssues:
      tokenStatusQuery.data?.status === 'error' && tokenStatusQuery.data?.errorCount > 0,
  };
}

export default useTokenRefresh;
