/**
 * Task Queue Routes
 *
 * This file contains all the API routes for managing the task queue
 * and email processing.
 */

import express, { type NextFunction, type Request, type Response } from 'express';
import { z } from 'zod';
import logger from '../lib/logger';
import {
  enqueueBatchEmailProcessing,
  enqueueEmailProcessing,
  getEmailProcessingStatus,
  reprocessEmailWithErrors,
} from '../services/emailProcessor';
import { getTaskQueueStats, TaskPriority } from '../services/taskQueue';
import { storage } from '../storage';
import {
  AuthenticationError,
  AuthorizationError,
  catchAsync,
  handleError,
  NotFoundError,
  ValidationError,
} from '../utils/errorHandler';
import { EmailSchemas, RequestSchemas, validateRequest } from '../utils/inputValidator';

const router = express.Router();

// Middleware to ensure the user owns the email
async function ensureEmailOwnership(req: Request, _res: Response, next: NextFunction) {
  const userId = req.session?.userId;
  const emailId = Number.parseInt(req.params.emailId, 10);

  if (!userId) {
    return next(new AuthenticationError());
  }

  if (Number.isNaN(emailId)) {
    return next(new ValidationError('Invalid email ID'));
  }

  try {
    const email = await storage.getEmail(emailId);

    if (!email) {
      return next(new NotFoundError('Email not found'));
    }

    if (email.userId !== userId) {
      return next(new AuthorizationError('You do not have permission to access this email'));
    }

    // Add the email to the request object for use in route handlers
    (req as any).email = email;
    next();
  } catch (error) {
    next(handleError(error, 'ensureEmailOwnership'));
  }
}

// Process an email (regenerate summary, categories, and reply)
router.post(
  '/process-email/:emailId',
  validateRequest(RequestSchemas.emailIdParam, 'params'),
  validateRequest(EmailSchemas.process),
  ensureEmailOwnership,
  catchAsync(async (req: Request, res: Response) => {
    const emailId = (req.params as any).emailId as number;
    const userId = req.session?.userId as number;
    const { priorityLevel = 'normal', processingTypes = ['summary', 'categorization', 'reply'] } =
      req.body as z.infer<typeof EmailSchemas.process>;

    // Map priority level string to enum
    let priority = TaskPriority.NORMAL;
    if (priorityLevel === 'high') {
      priority = TaskPriority.HIGH;
    } else if (priorityLevel === 'urgent') {
      priority = TaskPriority.URGENT;
    }

    // Enqueue the email for processing
    const tasks = await enqueueEmailProcessing(emailId, userId, priority, processingTypes);

    res.json({
      success: true,
      message: 'Email processing tasks enqueued',
      tasksCount: tasks.length,
      taskIds: tasks.map((t) => t.id),
    });
  })
);

// Process a batch of emails
router.post(
  '/process-emails/batch',
  validateRequest(EmailSchemas.batchProcess),
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.session?.userId as number;
    const {
      emailIds,
      priorityLevel = 'normal',
      processingTypes = ['summary', 'categorization', 'reply'],
    } = req.body as z.infer<typeof EmailSchemas.batchProcess>;

    if (!Array.isArray(emailIds) || emailIds.length === 0) {
      throw new ValidationError('Email IDs array is required');
    }

    // Ensure all emails belong to the user
    const emails = await storage.getEmailsByIds(emailIds, userId);
    if (emails.length !== emailIds.length) {
      throw new AuthorizationError('One or more email IDs are invalid or do not belong to you');
    }

    // Map priority level string to enum
    let priority = TaskPriority.NORMAL;
    if (priorityLevel === 'high') {
      priority = TaskPriority.HIGH;
    } else if (priorityLevel === 'urgent') {
      priority = TaskPriority.URGENT;
    }

    const task = await enqueueBatchEmailProcessing(emailIds, userId, priority, processingTypes);

    res.json({
      success: true,
      message: `Batch processing enqueued for ${emailIds.length} emails`,
      taskId: task.id,
      batchSize: emailIds.length,
    });
  })
);

// Reprocess emails with errors
router.post(
  '/reprocess-errors',
  validateRequest(z.object({ emailIds: z.array(z.number().int().positive()).nonempty() })),
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.session?.userId as number;
    const { emailIds } = req.body as { emailIds: number[] };

    // Process each email with errors
    const results = {
      total: emailIds.length,
      processed: 0,
      errors: 0,
      details: {} as Record<number, { success: boolean; errors?: string; taskCount?: number }>,
    };

    for (const emailId of emailIds) {
      try {
        // Reprocess email with errors
        const tasks = await reprocessEmailWithErrors(emailId, userId);

        results.processed++;
        results.details[emailId] = {
          success: true,
          taskCount: tasks.length,
        };
      } catch (error) {
        results.errors++;
        results.details[emailId] = {
          success: false,
          errors: error instanceof Error ? error.message : String(error),
        };
      }
    }

    res.json({
      success: true,
      message: `Reprocessed ${results.processed} of ${results.total} emails with ${results.errors} errors`,
      results,
    });
  })
);

// Get email processing status
router.get(
  '/email-processing-status/:emailId',
  validateRequest(RequestSchemas.emailIdParam, 'params'),
  ensureEmailOwnership,
  catchAsync(async (req: Request, res: Response) => {
    const emailId = (req.params as any).emailId as number;

    const status = await getEmailProcessingStatus(emailId);

    res.json({
      success: true,
      emailId,
      status,
    });
  })
);

// Get task queue stats
router.get(
  '/task-queue-stats',
  catchAsync(async (_req: Request, res: Response) => {
    const stats = await getTaskQueueStats();

    res.json({
      success: true,
      stats,
    });
  })
);

export default router;
