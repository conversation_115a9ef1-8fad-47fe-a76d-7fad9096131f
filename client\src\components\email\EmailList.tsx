import { Filter, RefreshCcw, Search, X } from 'lucide-react';
import type React from 'react';
import { useCallback, useMemo, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import type { Email, EmailFilters } from '@/types/email';
import EmailFilter, { defaultFilters } from '@/components/email/EmailFilter';
import { Skeleton } from '@/components/ui/skeleton';
import { useEmailDetail } from '@/context/EmailDetailContext';
import { useEmailList } from '@/context/EmailListContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { useStats } from '@/hooks/use-stats';
import EmailItem from './EmailItem';
import EmailPagination from './EmailPagination';
import EmailSearch from './EmailSearch';
import VirtualizedEmailList from './VirtualizedEmailList';

interface EmailListProps {
  className?: string;
}

const EmailList: React.FC<EmailListProps> = ({ className = '' }) => {
  const {
    filteredEmails,
    isLoading,
    refreshEmails,
    filters,
    setFilters,
    searchQuery,
    setSearchQuery,
  } = useEmailList();

  const { selectedEmail, selectEmail } = useEmailDetail();

  const { stats } = useStats();
  const isMobile = useIsMobile();

  // Search visibility on mobile
  const [showMobileSearch, setShowMobileSearch] = useState(false);

  // Use memoized callbacks to prevent unnecessary rerenders
  const handleRefresh = useCallback(() => {
    refreshEmails();
  }, [refreshEmails]);

  const handleFilterChange = useCallback(
    (newFilters: EmailFilters) => {
      setFilters(newFilters);
    },
    [setFilters]
  );

  const handleSearch = useCallback(
    (query: string) => {
      setSearchQuery(query);
      if (isMobile && query === '') {
        setShowMobileSearch(false);
      }
    },
    [isMobile, setSearchQuery]
  );

  // Removed toggleVirtualList function as we simplified the UI

  const toggleMobileSearch = useCallback(() => {
    setShowMobileSearch((prevState) => {
      // If we're about to show the search and there's a query, clear it
      if (!prevState && searchQuery) {
        setSearchQuery('');
      }
      return !prevState;
    });
  }, [searchQuery, setSearchQuery]);

  const handleEmailSelect = useCallback(
    (messageId: string | null) => {
      selectEmail(messageId);
    },
    [selectEmail]
  );

  const clearAllFilters = useCallback(() => {
    setSearchQuery('');
    setFilters(defaultFilters);
    setShowMobileSearch(false);
  }, [setSearchQuery, setFilters]);

  // Memoize this calculation to avoid recalculating on every render
  const hasActiveFilters = useMemo(
    () =>
      searchQuery ||
      Object.values(filters).some((v) => (Array.isArray(v) ? v.length > 0 : v !== 'all')),
    [searchQuery, filters]
  );

  return (
    <div
      className={`w-full md:w-1/2 lg:w-2/5 border-r border-border bg-card flex flex-col h-full email-list-container max-w-full min-w-0 ${className}`}
    >
      {/* Email List Toolbar */}
      <div className="flex-shrink-0 p-1 sm:p-2 border-b border-border flex justify-between items-center bg-card sticky top-0 z-10 email-list-toolbar min-h-[42px]">
        {showMobileSearch ? (
          <div className="flex items-center flex-1 overflow-hidden">
            <EmailSearch
              onSearch={handleSearch}
              searchQuery={searchQuery}
              autoFocus={true}
              className="flex-1"
            />
            <Button
              variant="ghost"
              size="sm"
              className="p-0.5 ml-1 flex-shrink-0 h-7 w-7 min-h-0 min-w-0"
              onClick={toggleMobileSearch}
            >
              <X className="h-3.5 w-3.5" />
            </Button>
          </div>
        ) : (
          <>
            <div className="flex items-center max-w-[50%] overflow-hidden">
              <span className="text-xs sm:text-sm font-medium text-foreground whitespace-nowrap truncate">
                Inbox{' '}
                {stats && (
                  <Badge variant="outline" className="ml-1 text-[10px] sm:text-xs">
                    {stats.totalEmails ? stats.totalEmails - (stats.readEmails || 0) : 0}
                  </Badge>
                )}
              </span>
            </div>
            <div className="flex items-center gap-1 flex-shrink-0">
              {isMobile ? (
                // Mobile toolbar actions
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-0.5 h-7 w-7 min-h-0 min-w-0"
                    onClick={toggleMobileSearch}
                  >
                    <Search className="h-3.5 w-3.5" />
                  </Button>

                  <Sheet>
                    <SheetTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0.5 h-7 w-7 min-h-0 min-w-0 relative"
                      >
                        <Filter className="h-3.5 w-3.5" />
                        {hasActiveFilters && (
                          <span className="absolute -top-0.5 -right-0.5 h-1.5 w-1.5 bg-primary rounded-full" />
                        )}
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="bottom" className="h-[80vh]">
                      <SheetHeader className="mb-4">
                        <SheetTitle>Filter Emails</SheetTitle>
                        <SheetDescription>
                          Select filters to narrow down your inbox
                        </SheetDescription>
                      </SheetHeader>
                      <div className="px-1">
                        <EmailFilter
                          onFilterChange={handleFilterChange}
                          activeFilters={filters}
                          isMobileView={true}
                        />

                        {hasActiveFilters && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={clearAllFilters}
                            className="w-full mt-4"
                          >
                            Clear All Filters
                          </Button>
                        )}
                      </div>
                    </SheetContent>
                  </Sheet>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-0.5 h-7 w-7 min-h-0 min-w-0"
                    onClick={handleRefresh}
                  >
                    <RefreshCcw className="h-3.5 w-3.5" />
                  </Button>
                </>
              ) : (
                // Desktop toolbar actions - simplified
                <>
                  <div>
                    <EmailSearch onSearch={handleSearch} searchQuery={searchQuery} />
                  </div>
                  <div>
                    <EmailFilter onFilterChange={handleFilterChange} activeFilters={filters} />
                  </div>
                  <Button variant="ghost" size="sm" className="p-1.5" onClick={handleRefresh}>
                    <RefreshCcw className="h-5 w-5" />
                  </Button>
                </>
              )}
            </div>
          </>
        )}
      </div>

      {/* Active filters indicator - Mobile only */}
      {isMobile && hasActiveFilters && !showMobileSearch && (
        <div className="px-2 py-1 bg-muted flex items-center justify-between border-b border-border text-xs flex-shrink-0">
          <div className="flex items-center text-muted-foreground">
            <Filter className="h-3 w-3 mr-1" />
            <span>Filters applied</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 py-0 px-2 text-xs"
            onClick={clearAllFilters}
          >
            Clear
          </Button>
        </div>
      )}

      {/* Email List Content */}
      <div className="flex-grow min-h-0 flex flex-col email-list-parent">
        <VirtualizedEmailList
          emails={filteredEmails}
          selectedEmail={selectedEmail}
          selectEmail={handleEmailSelect}
          isLoading={isLoading}
        />
      </div>

      {/* Pagination - shown only if not loading and emails exist */}
      {!isLoading && filteredEmails.length > 0 && <EmailPagination />}
    </div>
  );
};

export default EmailList;
