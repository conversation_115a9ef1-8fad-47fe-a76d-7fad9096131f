/**
 * Email Content Parser
 *
 * A utility for parsing, sanitizing, and standardizing HTML email content
 * for consistent display in the Inbox Zero application.
 */

import DOMPurify from 'dompurify';

// Configure DOMPurify to allow necessary tags but remove unwanted attributes
const ALLOWED_TAGS = [
  'a',
  'b',
  'br',
  'div',
  'h1',
  'h2',
  'h3',
  'h4',
  'h5',
  'h6',
  'i',
  'img',
  'li',
  'ol',
  'p',
  'span',
  'strong',
  'u',
  'ul',
  'blockquote',
  'table',
  'tbody',
  'td',
  'th',
  'thead',
  'tr',
];

const ALLOWED_ATTR = [
  'href',
  'src',
  'alt',
  'width',
  'height',
  'title',
  'data-original-src',
  'data-original-width',
  'data-original-height',
];

// Interface for options to customize parsing
interface ParseOptions {
  preserveLinks?: boolean;
  preserveImages?: boolean;
  maxImageWidth?: number;
  extractPlainText?: boolean;
  removeQuoted?: boolean;
  removeSignatures?: boolean;
  maxPlainTextLength?: number;
}

// Default options
const DEFAULT_OPTIONS: ParseOptions = {
  preserveLinks: true,
  preserveImages: true,
  maxImageWidth: 600,
  extractPlainText: false,
  removeQuoted: true,
  removeSignatures: true,
  maxPlainTextLength: 10000,
};

/**
 * Cache for parsed content to avoid re-parsing the same content
 * Uses weak references to allow garbage collection when needed
 */
const parsedContentCache = new Map<string, WeakRef<ParsedContent>>();
const recentContentKeys: string[] = [];
const MAX_CACHE_SIZE = 200;

// Structure for parsed content results
export interface ParsedContent {
  html: string;
  text: string;
  images: EmailImage[];
  hasExternalContent: boolean;
}

// Image data extracted from emails
export interface EmailImage {
  src: string;
  alt?: string;
  width?: number;
  height?: number;
  isInline: boolean;
  originalSrc?: string;
}

/**
 * Generate a cache key for content with options
 */
function getCacheKey(content: string, options: ParseOptions): string {
  if (!content) return '';
  // Only use the first 100 chars of content + JSON stringified options for the key
  return `${content.substring(0, 100)}|${JSON.stringify(options)}`;
}

/**
 * Main function to parse HTML email content
 *
 * @param content HTML content to parse
 * @param options Options for parsing
 * @returns Parsed and sanitized content
 */
export function parseEmailContent(
  content: string | null | undefined,
  options: ParseOptions = {}
): ParsedContent {
  // Handle empty content
  if (!content) {
    return {
      html: '',
      text: '',
      images: [],
      hasExternalContent: false,
    };
  }

  // Merge with default options
  const mergedOptions: ParseOptions = { ...DEFAULT_OPTIONS, ...options };

  // Check cache
  const cacheKey = getCacheKey(content, mergedOptions);
  const cachedResult = parsedContentCache.get(cacheKey)?.deref();
  if (cachedResult) {
    return cachedResult;
  }

  // Prepare sanitization configuration
  const purifyConfig = {
    ALLOWED_TAGS:
      mergedOptions.preserveLinks || mergedOptions.preserveImages
        ? ALLOWED_TAGS
        : ALLOWED_TAGS.filter((tag) => tag !== 'a' && tag !== 'img'),
    ALLOWED_ATTR: ALLOWED_ATTR,
    FORBID_TAGS: ['style', 'script', 'iframe'],
    FORBID_ATTR: [
      'style',
      'class',
      'id',
      'onclick',
      'onerror',
      'onload',
      'onmouseover',
      'onmouseout',
      'onmousemove',
    ],
    ADD_ATTR: ['target', 'rel'],
    ADD_TAGS: [],
    WHOLE_DOCUMENT: false,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    SANITIZE_DOM: true,
  };

  // Extract images before sanitization if needed
  const images: EmailImage[] = [];
  let hasExternalContent = false;

  try {
    // Create a document fragment to parse the HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;

    // Handle quoted content (emails with '>' at the beginning of lines)
    if (mergedOptions.removeQuoted) {
      removeQuotedContent(tempDiv);
    }

    // Handle signatures
    if (mergedOptions.removeSignatures) {
      removeSignatures(tempDiv);
    }

    // Extract and process images
    if (mergedOptions.preserveImages) {
      processImages(tempDiv, images, mergedOptions.maxImageWidth || 600);
      hasExternalContent = images.some((img) => !img.isInline);
    } else {
      // Remove all images if not preserving them
      const imgElements = tempDiv.querySelectorAll('img');
      imgElements.forEach((img) => img.remove());
    }

    // Process links
    if (mergedOptions.preserveLinks) {
      processLinks(tempDiv);
    } else {
      // Convert links to plain text if not preserving them
      const linkElements = tempDiv.querySelectorAll('a');
      linkElements.forEach((link) => {
        const textNode = document.createTextNode(link.textContent || '');
        link.parentNode?.replaceChild(textNode, link);
      });
    }

    // Simplify formatting (remove unnecessary divs, spans, etc.)
    simplifyFormatting(tempDiv);

    // Extract plain text
    const plainText = extractTextContent(tempDiv);

    // Sanitize the HTML
    let sanitizedHtml = DOMPurify.sanitize(tempDiv.innerHTML, purifyConfig);

    // Apply consistent styling (wrap in a container with standard styling)
    sanitizedHtml = wrapWithStandardStyling(sanitizedHtml);

    // Create the result
    const result: ParsedContent = {
      html: sanitizedHtml,
      text: plainText.substring(0, mergedOptions.maxPlainTextLength || 10000),
      images,
      hasExternalContent,
    };

    // Store in cache
    parsedContentCache.set(cacheKey, new WeakRef(result));
    recentContentKeys.push(cacheKey);

    // Prune cache if it gets too large
    if (recentContentKeys.length > MAX_CACHE_SIZE) {
      const oldestKey = recentContentKeys.shift();
      if (oldestKey) {
        parsedContentCache.delete(oldestKey);
      }
    }

    return result;
  } catch (error) {
    console.error('Error parsing email content:', error);

    // Fallback to simpler sanitization in case of error
    const sanitizedHtml = DOMPurify.sanitize(content, {
      ALLOWED_TAGS: ['p', 'br', 'div'],
      ALLOWED_ATTR: [],
    });

    return {
      html: `<div class="email-content-error">${sanitizedHtml}</div>`,
      text: content.replace(/<[^>]*>/g, '').substring(0, 10000),
      images: [],
      hasExternalContent: false,
    };
  }
}

/**
 * Extract plain text from HTML content
 */
export function extractTextContent(element: HTMLElement): string {
  // Clone the element to avoid modifying the original
  const tempElement = element.cloneNode(true) as HTMLElement;

  // Replace <br> with newlines
  const brs = tempElement.querySelectorAll('br');
  for (const br of Array.from(brs)) {
    br.replaceWith('\n');
  }

  // Replace block elements with newlines
  const blockElements = tempElement.querySelectorAll(
    'div, p, h1, h2, h3, h4, h5, h6, ul, ol, li, blockquote, table, tr'
  );
  for (const el of Array.from(blockElements)) {
    // Add a newline before each block element
    el.insertBefore(document.createTextNode('\n'), el.firstChild);

    // Add a newline after each block element
    el.appendChild(document.createTextNode('\n'));
  }

  // Get the text content and normalize whitespace
  return tempElement.textContent || '';
}

/**
 * Process and standardize images in the content
 */
function processImages(container: HTMLElement, images: EmailImage[], maxWidth: number): void {
  const imgElements = container.querySelectorAll('img');

  imgElements.forEach((img, index) => {
    // Extract image info
    const src = img.getAttribute('src') || '';
    const alt = img.getAttribute('alt') || '';
    const width = Number.parseInt(img.getAttribute('width') || '0', 10) || undefined;
    const height = Number.parseInt(img.getAttribute('height') || '0', 10) || undefined;
    const isInline = src.startsWith('data:') || src.startsWith('cid:');

    // Store original values as data attributes
    img.setAttribute('data-original-src', src);
    if (width) img.setAttribute('data-original-width', width.toString());
    if (height) img.setAttribute('data-original-height', height.toString());

    // Add to the images array
    images.push({
      src,
      alt,
      width,
      height,
      isInline,
      originalSrc: src,
    });

    // Limit image dimensions
    if (width && width > maxWidth) {
      const aspectRatio = width / (height || 1);
      img.setAttribute('width', maxWidth.toString());
      img.setAttribute('height', Math.round(maxWidth / aspectRatio).toString());
    }

    // Add loading="lazy" attribute for performance
    img.setAttribute('loading', 'lazy');

    // Add standard class for styling
    img.classList.add('email-content-image');

    // Add unique ID for reference
    img.id = `email-image-${index}`;
  });
}

/**
 * Process and standardize links in the content
 */
function processLinks(container: HTMLElement): void {
  const linkElements = container.querySelectorAll('a');

  linkElements.forEach((link) => {
    // Get href
    const href = link.getAttribute('href') || '';

    // Set target="_blank" to open links in new tab
    link.setAttribute('target', '_blank');

    // Add rel="noopener noreferrer" for security
    link.setAttribute('rel', 'noopener noreferrer');

    // Add standard class for styling
    link.classList.add('email-content-link');

    // Add title attribute with the URL for clarity
    if (!link.hasAttribute('title')) {
      link.setAttribute('title', href);
    }
  });
}

/**
 * Remove quoted content from email (text after > symbols, common in replies)
 */
function removeQuotedContent(container: HTMLElement): void {
  // Look for blockquote elements
  const blockquotes = container.querySelectorAll('blockquote');
  blockquotes.forEach((blockquote) => {
    // Keep nested blockquotes that might be part of the original message
    if (blockquote.parentElement?.closest('blockquote')) {
      return;
    }

    // Check if this blockquote contains sender information (common in email clients)
    const text = blockquote.textContent || '';
    if (
      text.includes('On ') &&
      (text.includes(' wrote:') || text.includes(' said:') || text.includes(' schrieb:'))
    ) {
      blockquote.remove();
    }
  });

  // Look for quoted text (lines starting with >)
  const allNodes = Array.from(container.querySelectorAll('*'));
  for (const node of allNodes) {
    if (node.nodeType === Node.TEXT_NODE && node.textContent) {
      // Check for patterns like "> text" at the beginning of lines
      if (/^\s*>/.test(node.textContent)) {
        node.textContent = '';
      }
    }

    // Check div or p elements for quoted content
    if ((node.nodeName === 'DIV' || node.nodeName === 'P') && node.textContent) {
      const text = node.textContent.trim();
      if (text.startsWith('>') || /^On\s.+wrote:/.test(text) || /^On\s.+said:/.test(text)) {
        node.remove();
      }
    }
  }
}

/**
 * Remove email signatures
 */
function removeSignatures(container: HTMLElement): void {
  // Common signature indicators
  const signaturePatterns = [
    /^-- ?$/m, // Standard signature separator
    /^--$/m, // Simple signature separator
    /^Regards,?$/i, // Common closing
    /^Best regards,?$/i, // Common closing
    /^Cheers,?$/i, // Common closing
    /^Sincerely,?$/i, // Common closing
    /^Thanks(?:,|!|.)$/i, // Common closing
    /^Thank you(?:,|!|.)$/i, // Common closing
  ];

  // Look for signature divs
  const allElements = container.querySelectorAll('div, p, span');
  for (const el of Array.from(allElements)) {
    // Check for signature class names
    if (
      el.className.includes('signature') ||
      el.id.includes('signature') ||
      el.getAttribute('data-smartmail') === 'gmail_signature'
    ) {
      el.remove();
      continue;
    }

    // Check content for signature patterns
    const text = el.textContent || '';
    for (const pattern of signaturePatterns) {
      if (pattern.test(text)) {
        // Check if this element is followed by contact info
        const nextElement = el.nextElementSibling;
        if (nextElement) {
          const nextText = nextElement.textContent || '';
          if (
            nextText.includes('@') ||
            /\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/.test(nextText) || // Phone number pattern
            /https?:\/\//.test(nextText) // URL pattern
          ) {
            // This is likely a signature
            el.remove();
            break;
          }
        }
      }
    }
  }
}

/**
 * Simplify formatting by removing unnecessary elements
 */
function simplifyFormatting(container: HTMLElement): void {
  // Remove empty elements or elements with only whitespace
  const allNodes = Array.from(container.querySelectorAll('*'));
  for (const node of allNodes) {
    if (node.childNodes.length === 0 || node.textContent?.trim() === '') {
      node.remove();
    }
  }

  // Normalize spaces, break lines, etc.
  container.innerHTML = container.innerHTML
    .replace(/(\s)+/g, ' ')
    .replace(/<br><br><br>/g, '<br><br>');
}

/**
 * Wrap the content with a standard styling container
 */
function wrapWithStandardStyling(html: string): string {
  return `
    <div class="standardized-email-content">
      ${html}
    </div>
  `;
}

/**
 * Extract images from HTML content
 *
 * @param content HTML content
 * @returns Array of image info objects
 */
export function extractImagesFromContent(content: string): EmailImage[] {
  if (!content) return [];

  const images: EmailImage[] = [];

  try {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;

    const imgElements = tempDiv.querySelectorAll('img');
    imgElements.forEach((img) => {
      const src = img.getAttribute('src') || '';
      const alt = img.getAttribute('alt') || '';
      const width = Number.parseInt(img.getAttribute('width') || '0', 10) || undefined;
      const height = Number.parseInt(img.getAttribute('height') || '0', 10) || undefined;
      const isInline = src.startsWith('data:') || src.startsWith('cid:');

      images.push({
        src,
        alt,
        width,
        height,
        isInline,
        originalSrc: src,
      });
    });
  } catch (error) {
    console.error('Error extracting images from content:', error);
  }

  return images;
}

/**
 * Simple sanitize and normalize HTML for non-emails
 *
 * @param html HTML content to sanitize
 * @returns Sanitized HTML
 */
export function sanitizeHtml(html: string): string {
  if (!html) return '';

  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ALLOWED_TAGS,
    ALLOWED_ATTR: ALLOWED_ATTR,
    FORBID_TAGS: ['style', 'script', 'iframe'],
    FORBID_ATTR: ['style', 'class', 'id'],
  });
}

/**
 * Process HTML email content to improve display
 * - Fixes common issues with email HTML
 * - Ensures proper styling
 * - Makes it safe for rendering
 *
 * @param htmlContent The raw HTML content from the email
 * @returns Processed HTML ready for rendering
 */
export function processEmailHtml(htmlContent: string): string {
  if (!htmlContent) return '';

  // First sanitize the HTML using our comprehensive sanitizer
  let processedHtml = sanitizeHtml(htmlContent);

  // Fix common email rendering issues

  // 1. Remove external CSS that could override our styles
  processedHtml = processedHtml.replace(/<link[^>]*>/gi, '');

  // 2. Remove potentially problematic attributes
  processedHtml = processedHtml.replace(/position:\s*absolute/gi, 'position: relative');
  processedHtml = processedHtml.replace(/position:\s*fixed/gi, 'position: relative');

  // 3. Fix image URLs - some emails have relative paths or missing protocols
  processedHtml = processedHtml.replace(/src="\/\//g, 'src="https://');

  // 4. Make sure all URLs open in a new tab
  processedHtml = processedHtml.replace(
    /<a\s+(?:[^>]*?\s+)?href=/gi,
    '<a target="_blank" rel="noopener noreferrer" href='
  );

  return processedHtml;
}

/**
 * Convert HTML content to plain text
 * Useful for summaries, previews, etc.
 *
 * @param htmlContent The HTML content to convert
 * @returns Plain text version of the HTML
 */
export function htmlToPlainText(htmlContent: string): string {
  if (!htmlContent) return '';

  // Use the DOM to parse the HTML and extract text
  // This works in browser environments
  if (typeof document !== 'undefined') {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    return tempDiv.textContent || tempDiv.innerText || '';
  }

  // Basic fallback for non-browser environments
  return htmlContent
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

/**
 * Extract first few sentences from an email for preview
 *
 * @param content Email content (HTML or plain text)
 * @param isHtml Whether the content is HTML
 * @param sentenceCount Number of sentences to extract
 * @returns Short preview text
 */
export function getEmailPreview(content: string, isHtml = false, sentenceCount = 2): string {
  if (!content) return '';

  // If it's HTML, convert to plain text first
  const plainText = isHtml ? htmlToPlainText(content) : content;

  // Split into sentences and take the first few
  // This is a simple implementation - not perfect for all languages
  const sentences = plainText
    .replace(/([.!?])\s*(?=[A-Z])/g, '$1|')
    .split('|')
    .filter((s) => s.trim().length > 0)
    .slice(0, sentenceCount);

  // Join and truncate if too long
  let preview = sentences.join(' ').trim();
  if (preview.length > 200) {
    preview = `${preview.substring(0, 197)}...`;
  }

  return preview;
}
