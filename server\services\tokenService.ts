/**
 * Unified Token Service
 *
 * This service consolidates all token management operations including:
 * - Token parsing and validation
 * - Token refresh with circuit breaker protection
 * - Token encryption/decryption
 * - Legacy token migration
 * - Token expiry checking
 */

import { eq, sql } from 'drizzle-orm';
import { google } from 'googleapis';
import type { User } from '../../shared/schema';
import { users } from '../../shared/schema';
import { getDb } from '../db';
import logger from '../lib/logger';
import { storage } from '../storage';
import { protectApiCallWithDetails, resetCircuitForOperation } from '../utils/apiProtector';
import { decrypt, encrypt, isEncrypted } from '../utils/encryption';
import { getGmailAPI } from './googleApiService';

// Unified token interface
export interface TokenObject {
  access_token: string;
  refresh_token?: string | null;
  expiry_date?: number | null;
  token_type?: string;
  scope?: string;
  id_token?: string;
  provider?: string;
  [key: string]: any;
  expiresIn?: number;
  error?: string;
}

// Token refresh result interface
export interface TokenRefreshResult {
  success: boolean;
  tokens?: TokenObject;
  error?: string;
  circuitOpen?: boolean;
  retryAfterMs?: number;
  needsReauth?: boolean;
}

// Token validation result
export interface TokenValidationResult {
  isValid: boolean;
  isExpired: boolean;
  needsRefresh: boolean;
  expiresIn?: number;
  error?: string;
}

export interface ConnectionStatus {
  connected: boolean;
  reason?: string;
  details?: Record<string, any>;
}

// OAuth2 client configuration interface
interface OAuth2Config {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

export class TokenService {
  private oauth2Client: InstanceType<typeof google.auth.OAuth2> | null = null;
  private config: OAuth2Config | null = null;

  /**
   * Initialize the token service with OAuth2 configuration
   * This should be called during server startup after environment validation
   */
  public initialize(config: OAuth2Config): void {
    this.config = config;
    this.oauth2Client = new google.auth.OAuth2(
      config.clientId,
      config.clientSecret,
      config.redirectUri
    );
  }

  /**
   * Returns the configured OAuth2 client instance.
   */
  public getOAuth2Client(): InstanceType<typeof google.auth.OAuth2> {
    if (!this.oauth2Client || !this.config) {
      throw new Error('TokenService not initialized. Call initialize() first during server startup.');
    }
    return this.oauth2Client;
  }

  /**
   * Parse tokens from user object, handling all legacy formats
   */
  public parseTokens(user: User | null): TokenObject | null {
    if (!user) return null;

    try {
      // Check for modern gmailTokens field first
      if (user.gmailTokens) {
        if (typeof user.gmailTokens === 'string') {
          // Handle encrypted or JSON string tokens
          if (isEncrypted(user.gmailTokens)) {
            const decrypted = decrypt(user.gmailTokens);
            return JSON.parse(decrypted);
          }
          return JSON.parse(user.gmailTokens);
        }
        if (typeof user.gmailTokens === 'object') {
          // Direct object
          return user.gmailTokens as TokenObject;
        }
      }

      // Fallback to legacy token fields
      if (user.accessToken) {
        return {
          access_token: user.accessToken,
          refresh_token: user.refreshToken || null,
          expiry_date: user.expiresAt ? user.expiresAt.getTime() : null,
          token_type: 'Bearer',
          provider: user.provider || 'google',
        };
      }

      return null;
    } catch (error) {
      logger.error('Error parsing tokens for user', {
        userId: user.id,
        error: (error as Error).message,
      });
      return null;
    }
  }

  /**
   * Validate tokens and check expiry status
   */
  public validateTokens(user: User | null, bufferSeconds = 300): TokenValidationResult {
    const tokens = this.parseTokens(user);

    if (!tokens || !tokens.access_token) {
      return {
        isValid: false,
        isExpired: true,
        needsRefresh: true,
        error: 'No valid tokens found',
      };
    }

    // Check expiry
    const now = Date.now();
    const expiryTime = tokens.expiry_date || 0;
    const bufferTime = bufferSeconds * 1000;

    const isExpired = expiryTime <= now;
    const needsRefresh = expiryTime <= now + bufferTime;
    const expiresIn = Math.max(0, Math.floor((expiryTime - now) / 1000));

    return {
      isValid: !isExpired,
      isExpired,
      needsRefresh,
      expiresIn: isExpired ? 0 : expiresIn,
    };
  }

  /**
   * Refresh tokens with circuit breaker protection
   */
  public async refreshTokens(user: User): Promise<TokenRefreshResult> {
    if (!this.oauth2Client) {
      throw new Error('TokenService not initialized. Call initialize() first during server startup.');
    }

    const tokens = this.parseTokens(user);

    if (!tokens || !tokens.refresh_token) {
      logger.warn('No refresh token available', {
        userId: user.id,
        email: user.email,
      });
      return {
        success: false,
        error: 'No refresh token available',
        needsReauth: true,
      };
    }

    try {
      // Use circuit breaker protection
      const { result, usedFallback, circuitOpen, durationMs } = await protectApiCallWithDetails(
        async () => {
          this.oauth2Client!.setCredentials({
            refresh_token: tokens.refresh_token,
          });

          const response = await this.oauth2Client!.refreshAccessToken();
          return response.credentials as TokenObject;
        },
        {
          operationId: `token-refresh-${user.id}`,
          retryCount: 2,
          retryDelay: 1000,
          useExponentialBackoff: true,
          timeoutMs: 10000,
          context: { userId: user.id, email: user.email },
          failureThreshold: 3,
          resetTimeout: 60000,
        }
      );

      // Handle circuit breaker fallback
      if (usedFallback) {
        logger.warn('Token refresh protected by circuit breaker', {
          userId: user.id,
          circuitOpen,
          email: user.email,
        });

        return {
          success: false,
          error: 'Token refresh service temporarily unavailable',
          circuitOpen: true,
          retryAfterMs: 60000,
        };
      }

      // Update user tokens in database
      if (result && result.access_token) {
        await this.updateUserTokens(user.id, result);
        await this.recordTokenSuccess(user.id);

        logger.info('Successfully refreshed tokens', {
          userId: user.id,
          email: user.email,
          durationMs,
        });

        return {
          success: true,
          tokens: result,
        };
      }

      logger.error('Token refresh returned invalid response', {
        userId: user.id,
        email: user.email,
        hasAccessToken: !!result?.access_token,
      });

      return {
        success: false,
        error: 'Invalid token refresh response',
      };
    } catch (error) {
      const errorMessage = (error as Error).message;
      logger.error('Token refresh failed', {
        userId: user.id,
        email: user.email,
        error: errorMessage,
      });

      await this.recordTokenError(user.id, errorMessage);

      // Check if it's an authentication error that requires re-auth
      const needsReauth = errorMessage.includes('invalid_grant') || 
                         errorMessage.includes('invalid_request') ||
                         errorMessage.includes('unauthorized');

      return {
        success: false,
        error: errorMessage,
        needsReauth,
      };
    }
  }

  /**
   * Get valid access token, refreshing if necessary
   */
  public async getValidAccessToken(user: User, forceRefresh = false): Promise<string | null> {
    const validation = this.validateTokens(user);

    if (validation.isValid && !forceRefresh) {
      const tokens = this.parseTokens(user);
      return tokens?.access_token || null;
    }

    // If it needs refresh or is forced, refresh it
    const refreshResult = await this.refreshTokens(user);
    return refreshResult.tokens?.access_token || null;
  }

  /**
   * Verify connection to Google APIs
   */
  public async verifyConnection(user: User): Promise<ConnectionStatus> {
    try {
      const accessToken = await this.getValidAccessToken(user);
      if (!accessToken) {
        return { connected: false, reason: 'No valid access token' };
      }

      // Test the connection by making a simple API call
      if (!this.oauth2Client) {
        throw new Error('TokenService not initialized');
      }

      this.oauth2Client.setCredentials({ access_token: accessToken });
      
      const response = await this.oauth2Client.request({
        url: 'https://www.googleapis.com/oauth2/v1/userinfo',
      });

      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid profile response from Google.');
      }

      return { 
        connected: true, 
        details: { 
          email: (response.data as any).email,
          verified: (response.data as any).verified_email 
        }
      };
    } catch (error) {
      logger.error('Connection verification failed', {
        userId: user.id,
        error: (error as Error).message,
      });
      return { 
        connected: false, 
        reason: (error as Error).message 
      };
    }
  }

  /**
   * Reset token state for debugging purposes
   */
  public async resetTokenState(userId: number): Promise<void> {
    try {
      await resetCircuitForOperation(`token-refresh-${userId}`);
      logger.info('Reset token state', { userId });
    } catch (error) {
      logger.error('Failed to reset token state', {
        userId,
        error: (error as Error).message,
      });
    }
  }

  /**
   * Emergency token refresh for admin purposes
   */
  public async emergencyRefreshToken(
    userId: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const user = await storage.getUser(userId);
      if (!user) {
        return { success: false, error: 'User not found' };
      }

      const result = await this.refreshTokens(user);
      return { success: result.success, error: result.error };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * Admin function to fix token mismatches
   */
  public async adminFixTokenMismatch(
    userId: string
  ): Promise<{ success: boolean; error?: string; details?: string }> {
    try {
      const userIdNum = Number.parseInt(userId, 10);
      const user = await storage.getUser(userIdNum);
      
      if (!user) {
        return { success: false, error: 'User not found' };
      }

      // Force refresh tokens
      const refreshResult = await this.refreshTokens(user);
      
      return { 
        success: refreshResult.success, 
        error: refreshResult.error,
        details: `Token refresh ${refreshResult.success ? 'succeeded' : 'failed'} for user ${userId}`
      };
    } catch (error) {
      return { 
        success: false, 
        error: (error as Error).message,
        details: `Exception during token fix for user ${userId}`
      };
    }
  }

  private async updateUserTokens(userId: number, tokens: TokenObject): Promise<void> {
    try {
      const db = await getDb();
      const encryptedTokens = encrypt(JSON.stringify(tokens));

      await db
        .update(users)
        .set({
          gmailTokens: encryptedTokens,
        })
        .where(eq(users.id, userId));

      logger.debug('Updated user tokens in database', { userId });
    } catch (error) {
      logger.error('Failed to update user tokens', {
        userId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  private async recordTokenSuccess(userId: number): Promise<void> {
    try {
      const db = await getDb();
      await db
        .update(users)
        .set({
          lastTokenRefresh: new Date(),
          tokenErrorCount: 0,
        })
        .where(eq(users.id, userId));
    } catch (error) {
      logger.warn('Failed to record token success', {
        userId,
        error: (error as Error).message,
      });
    }
  }

  private async recordTokenError(userId: number, errorMessage: string): Promise<void> {
    try {
      const db = await getDb();
      await db
        .update(users)
        .set({
          lastTokenError: errorMessage.substring(0, 500), // Store error message instead of date
          tokenErrorCount: sql`COALESCE(${users.tokenErrorCount}, 0) + 1`,
          tokenErrorTime: new Date(), // Use tokenErrorTime field for timestamp
        })
        .where(eq(users.id, userId));
    } catch (error) {
      logger.warn('Failed to record token error', {
        userId,
        error: (error as Error).message,
      });
    }
  }

  /**
   * Store new tokens for a user
   */
  public async storeTokens(userId: number, tokens: TokenObject): Promise<void> {
    await this.updateUserTokens(userId, tokens);
    await this.recordTokenSuccess(userId);
  }

  /**
   * Check if user has valid tokens
   */
  public hasValidTokens(user: User | null): boolean {
    const validation = this.validateTokens(user);
    return validation.isValid && !validation.needsRefresh;
  }

  /**
   * Migrate legacy token format to new format
   */
  public async migrateLegacyTokens(user: User): Promise<boolean> {
    try {
      // Check if user has legacy tokens but no modern tokens
      if (user.accessToken && !user.gmailTokens) {
        const legacyTokens: TokenObject = {
          access_token: user.accessToken,
          refresh_token: user.refreshToken || null,
          expiry_date: user.expiresAt ? user.expiresAt.getTime() : null,
          token_type: 'Bearer',
          provider: user.provider || 'google',
        };

        await this.storeTokens(user.id, legacyTokens);

        // Clear legacy fields
        const db = await getDb();
        await db
          .update(users)
          .set({
            accessToken: null,
            refreshToken: null,
            expiresAt: null,
          })
          .where(eq(users.id, user.id));

        logger.info('Migrated legacy tokens to new format', { userId: user.id });
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Failed to migrate legacy tokens', {
        userId: user.id,
        error: (error as Error).message,
      });
      return false;
    }
  }

  /**
   * Clear all tokens for a user
   */
  public async clearTokens(userId: number): Promise<void> {
    try {
      const db = await getDb();
      await db
        .update(users)
        .set({
          gmailTokens: null,
          accessToken: null,
          refreshToken: null,
          expiresAt: null,
        })
        .where(eq(users.id, userId));

      logger.info('Cleared all tokens for user', { userId });
    } catch (error) {
      logger.error('Failed to clear tokens', {
        userId,
        error: (error as Error).message,
      });
      throw error;
    }
  }
}

const tokenService = new TokenService();

export default tokenService;
