import { render, screen } from '@testing-library/react';
import type React from 'react';

// ---------- mocks ----------
const mockUseLocation = jest.fn().mockReturnValue(['/dashboard', jest.fn()]);
jest.mock('wouter', () => ({
  Link: ({ children, href }: any) => <a href={href}>{children}</a>,
  useLocation: () => mockUseLocation(),
}));

jest.mock('@/context/AuthContext', () => ({
  __esModule: true,
  useAuth: () => ({ user: { id: 1, name: 'Tester', email: '<EMAIL>' } }),
}));

jest.mock('@/hooks/use-admin', () => ({
  __esModule: true,
  useAdmin: () => ({ isAdmin: false }),
}));

jest.mock('@/hooks/use-stats', () => ({
  __esModule: true,
  useStats: () => ({ stats: { emailsProcessed: 100 } }),
}));

jest.mock('@/hooks/use-mobile', () => ({
  __esModule: true,
  useIsMobile: () => false,
}));

// Silence Lucide icons
jest.mock('lucide-react', () => ({ __esModule: true, Plus: () => <svg />, PanelLeft: () => <svg /> }));

import AppSidebar from '@/components/layout/AppSidebar';


describe('AppSidebar', () => {
  it('renders primary navigation links', () => {
    render(<AppSidebar />);
    expect(screen.getByText('Inbox')).toBeInTheDocument();
    expect(screen.getByText('Priority Map')).toBeInTheDocument();
  });

  it('highlights active route', () => {
    render(<AppSidebar />);
    const inbox = screen.getByText('Inbox').closest('button,a');
    expect(inbox).toHaveClass('variant-secondary', { exact: false });
  });

  it('renders compose button and footer user name', () => {
    render(<AppSidebar />);
    expect(screen.getByText('Compose')).toBeInTheDocument();
    expect(screen.getByText('Tester')).toBeInTheDocument();
  });
}); 