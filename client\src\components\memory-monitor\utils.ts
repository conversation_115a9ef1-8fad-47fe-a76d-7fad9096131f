export const formatBytes = (bytes: number): string => {
  // <PERSON>le invalid input defensively
  if (Number.isNaN(bytes) || bytes < 0) return '0 B';

  const KILOBYTE = 1024;
  if (bytes < KILOBYTE) return `${bytes} B`;

  const units = ['KB', 'MB', 'GB', 'TB'];
  // Determine unit index (e.g. 1 → KB, 2 → MB…)
  const exponent = Math.floor(Math.log(bytes) / Math.log(KILOBYTE));
  const value = bytes / KILOBYTE ** exponent;

  // Guard against overflow if bytes is huge
  const unit = units[exponent - 1] ?? 'PB';

  return `${Number.parseFloat(value.toFixed(2))} ${unit}`;
};

export const getPressureLevelColor = (level: string) => {
  switch (level) {
    case 'LOW':
      return 'bg-green-500';
    case 'MEDIUM':
      return 'bg-yellow-500';
    case 'HIGH':
      return 'bg-orange-500';
    case 'CRITICAL':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
};

export const getPressureLevelTextColor = (level: string) => {
  switch (level) {
    case 'LOW':
      return 'text-green-500';
    case 'MEDIUM':
      return 'text-yellow-500';
    case 'HIGH':
      return 'text-orange-500';
    case 'CRITICAL':
      return 'text-red-500';
    default:
      return 'text-gray-500';
  }
};
