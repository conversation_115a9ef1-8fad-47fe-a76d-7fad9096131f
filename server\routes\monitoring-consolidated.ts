import { Router, Request, Response } from 'express';
import queryMonitorRouter from './query-monitor';
import taskQueueRouter from './task-queue';
import { catchAsync } from '../utils/errorHandler';
import { sendSuccess, sendError, ErrorCode } from '../lib/standardizedResponses';
import logger from '../lib/logger';

/**
 * Consolidated Monitoring Routes
 *
 * Exposes operational monitoring & maintenance endpoints:
 *   • /task-queue/*           – queue stats & control
 *   • /query-monitor/*        – DB query performance metrics
 *   • /check-processing-emails/* – batch email-processing health checks
 *
 * Note: Memory monitoring is available via admin routes at /api/admin/memory/*
 */
const router = Router();

router.use('/task-queue', taskQueueRouter);
router.use('/query-monitor', queryMonitorRouter);

/**
 * GET /api/monitoring/auto-sync
 * Get automatic email sync service status and statistics
 */
router.get(
  '/auto-sync',
  catchAsync(async (_req: Request, res: Response) => {
    try {
      const { getAutoSyncStats } = await import('../services/automaticEmailSync');
      const stats = getAutoSyncStats();
      
      return sendSuccess(res, {
        autoSync: stats,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Failed to get auto-sync stats', { error });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to get auto-sync statistics');
    }
  })
);

/**
 * GET /api/monitoring/email-processing
 * Get comprehensive email processing status across all users
 */
router.get(
  '/email-processing',
  catchAsync(async (_req: Request, res: Response) => {
    try {
      const { getTaskQueueStats } = await import('../services/taskQueue');
      const { storage } = await import('../storage');
      
      // Get task queue statistics
      const taskStats = await getTaskQueueStats();
      
      // Get sample of recent emails across all users to check processing status
      const recentEmails = await storage.getEmails(0, 100, 0, {}); // Get across all users
      
      // Analyze email processing status
      const emailStats = {
        total: recentEmails.length,
        processing: 0,
        errors: 0,
        completed: 0,
        needsProcessing: 0,
      };

      for (const email of recentEmails) {
        if (!email.summary || email.summary.trim() === '') {
          emailStats.needsProcessing++;
        } else if (email.summary === 'Processing...') {
          emailStats.processing++;
        } else if (
          email.summary === 'Error generating summary' ||
          email.summary === 'Summary unavailable' ||
          email.summary.toLowerCase().includes('error') ||
          email.summary.toLowerCase().includes('failed') ||
          email.summary.toLowerCase().includes('unavailable')
        ) {
          emailStats.errors++;
        } else {
          emailStats.completed++;
        }
      }
      
      return sendSuccess(res, {
        taskQueue: taskStats,
        emailProcessing: emailStats,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Failed to get email processing stats', { error });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to get email processing statistics');
    }
  })
);

export default router;
