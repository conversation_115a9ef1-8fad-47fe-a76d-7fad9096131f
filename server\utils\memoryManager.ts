/// <reference types="node" />

/**
 * Memory Manager Utility
 *
 * This service provides memory usage monitoring, optimization, and cleanup
 * features to help prevent memory leaks and optimize resource usage in the application.
 * It implements:
 *
 * 1. Periodic memory usage monitoring
 * 2. Scheduled garbage collection hints
 * 3. Cache size limitations based on memory pressure
 * 4. Resource pooling for expensive operations
 * 5. Memory leak detection through usage pattern analysis
 */

import { EventEmitter } from 'node:events';
import logger from '../lib/logger';

// Thresholds for memory pressure (in MB)
const MEMORY_THRESHOLDS = {
  LOW: 200, // < 200MB: Low memory pressure
  MEDIUM: 500, // 200-500MB: Medium memory pressure
  HIGH: 800, // 500-800MB: High memory pressure
  CRITICAL: 1024, // > 1GB: Critical memory pressure
};

// Cleanup intervals (in ms)
const CLEANUP_INTERVALS = {
  LOW: 10 * 60 * 1000, // 10 minutes for low pressure
  MEDIUM: 5 * 60 * 1000, // 5 minutes for medium pressure
  HIGH: 2 * 60 * 1000, // 2 minutes for high pressure
  CRITICAL: 30 * 1000, // 30 seconds for critical pressure
};

// Cache size limits based on memory pressure
const CACHE_SIZE_LIMITS = {
  LOW: 10000, // Up to 10,000 items in cache when memory pressure is low
  MEDIUM: 5000, // Up to 5,000 items when memory pressure is medium
  HIGH: 1000, // Up to 1,000 items when memory pressure is high
  CRITICAL: 100, // Only keep 100 most critical items when memory pressure is critical
};

export type MemoryPressureLevel = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

export interface MemoryStats {
  rss: number; // Resident Set Size - total memory allocated in MB
  heapTotal: number; // Total size of allocated heap in MB
  heapUsed: number; // Actually used heap in MB
  external: number; // Memory used by C++ objects bound to JavaScript objects in MB
  arrayBuffers: number; // Memory used by ArrayBuffers and SharedArrayBuffers in MB
  pressureLevel: MemoryPressureLevel;
  timestamp: string;
}

export interface CacheSizeRecommendation {
  maxItems: number;
  maxSizeMB: number;
}

export interface ResourcePoolRecommendation {
  maxPoolSize: number;
  idleTimeoutMs: number;
}

export interface CleanupRecommendation {
  intervalMs: number;
  aggressiveness: 'low' | 'medium' | 'high';
}

/**
 * Memory Manager class for monitoring and optimizing memory usage
 */
class MemoryManager extends EventEmitter {
  private currentStats: MemoryStats | null = null;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private memoryHistory: MemoryStats[] = [];
  private maxHistorySize = 100;
  private registeredCaches: Map<string, WeakRef<any>> = new Map();
  private resourcePools: Map<string, any[]> = new Map();
  private isMonitoring = false;
  private cleanupCallbacks: Map<string, () => Promise<void>> = new Map();
  private lastCleanupTime: number = Date.now();
  private cleanupInProgress = false;

  constructor() {
    super();
    // Set up the memory monitoring
    this.updateMemoryStats();
  }

  /**
   * Start periodic memory monitoring
   * @param intervalMs Time between memory checks in milliseconds
   */
  public startMonitoring(intervalMs = 60000): void {
    if (this.isMonitoring) {
      logger.debug('Memory monitoring is already running');
      return;
    }

    logger.info(`Starting memory monitoring with interval: ${intervalMs}ms`);
    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.monitorMemoryUsage();
    }, intervalMs);
  }

  /**
   * Stop memory monitoring
   */
  public stopMonitoring(): void {
    if (!this.isMonitoring || !this.monitoringInterval) {
      logger.debug('Memory monitoring is not running');
      return;
    }

    logger.info('Stopping memory monitoring');
    clearInterval(this.monitoringInterval);
    this.isMonitoring = false;
    this.monitoringInterval = null;
  }

  /**
   * Get current memory statistics
   */
  public getMemoryStats(): MemoryStats {
    this.updateMemoryStats();
    return this.currentStats as MemoryStats;
  }

  /**
   * Register a cache for memory pressure-based size management
   * @param cacheId Unique identifier for the cache
   * @param cache The cache object to register
   */
  public registerCache(cacheId: string, cache: any): void {
    this.registeredCaches.set(cacheId, new WeakRef(cache));
    logger.debug(`Cache registered for memory management: ${cacheId}`);
  }

  /**
   * Get recommended cache size based on current memory pressure
   */
  public getCacheSizeRecommendation(): CacheSizeRecommendation {
    const stats = this.getMemoryStats();

    switch (stats.pressureLevel) {
      case 'CRITICAL':
        return { maxItems: CACHE_SIZE_LIMITS.CRITICAL, maxSizeMB: 5 };
      case 'HIGH':
        return { maxItems: CACHE_SIZE_LIMITS.HIGH, maxSizeMB: 20 };
      case 'MEDIUM':
        return { maxItems: CACHE_SIZE_LIMITS.MEDIUM, maxSizeMB: 50 };
      default:
        return { maxItems: CACHE_SIZE_LIMITS.LOW, maxSizeMB: 100 };
    }
  }

  /**
   * Create a resource pool with memory-aware sizing
   * @param poolId Unique identifier for the pool
   * @param factory Function to create new resources
   * @param initialSize Initial size of the pool
   */
  public createResourcePool<T>(
    poolId: string,
    factory: () => T,
    initialSize = 5
  ): { acquire: () => T; release: (resource: T) => void } {
    // Initialize the pool
    const pool: T[] = [];

    // Pre-populate the pool
    for (let i = 0; i < initialSize; i++) {
      pool.push(factory());
    }

    // Store the pool
    this.resourcePools.set(poolId, pool);

    return {
      acquire: () => {
        if (pool.length > 0) {
          return pool.pop() as T;
        }
        // Create a new resource if the pool is empty
        return factory();
      },
      release: (resource: T) => {
        // Get recommendation for pool size
        const recommendation = this.getResourcePoolRecommendation();

        // Only return to pool if under the recommended size
        if (pool.length < recommendation.maxPoolSize) {
          pool.push(resource);
        }
        // Otherwise, let the resource be garbage collected
      },
    };
  }

  /**
   * Get recommended resource pool configuration based on memory pressure
   */
  public getResourcePoolRecommendation(): ResourcePoolRecommendation {
    const stats = this.getMemoryStats();

    switch (stats.pressureLevel) {
      case 'CRITICAL':
        return { maxPoolSize: 2, idleTimeoutMs: 5000 };
      case 'HIGH':
        return { maxPoolSize: 5, idleTimeoutMs: 30000 };
      case 'MEDIUM':
        return { maxPoolSize: 10, idleTimeoutMs: 60000 };
      default:
        return { maxPoolSize: 20, idleTimeoutMs: 300000 };
    }
  }

  /**
   * Register a cleanup callback that will be called during cleanup operations
   * @param id Unique identifier for the callback
   * @param callback Function to call during cleanup
   */
  public registerCleanupCallback(id: string, callback: () => Promise<void>): void {
    this.cleanupCallbacks.set(id, callback);
    logger.debug(`Cleanup callback registered: ${id}`);
  }

  /**
   * Unregister a cleanup callback
   * @param id Identifier of the callback to remove
   */
  public unregisterCleanupCallback(id: string): void {
    if (this.cleanupCallbacks.has(id)) {
      this.cleanupCallbacks.delete(id);
      logger.debug(`Cleanup callback unregistered: ${id}`);
    }
  }

  /**
   * Run a memory cleanup operation based on current memory pressure
   */
  public async runCleanup(force = false): Promise<void> {
    if (this.cleanupInProgress && !force) {
      logger.debug('Cleanup already in progress, skipping');
      return;
    }

    try {
      this.cleanupInProgress = true;
      const stats = this.getMemoryStats();
      logger.info(
        `Running memory cleanup. Current pressure: ${stats.pressureLevel}, Heap used: ${stats.heapUsed}MB`
      );

      // Run all registered cleanup callbacks
      const promises = [];
      for (const [id, callback] of this.cleanupCallbacks.entries()) {
        logger.debug(`Running cleanup callback: ${id}`);
        promises.push(callback());
      }

      await Promise.all(promises);

      // Clean up weak references
      for (const [id, cacheRef] of this.registeredCaches.entries()) {
        const cache = cacheRef.deref();
        if (!cache) {
          // The cache has been garbage collected, remove the reference
          this.registeredCaches.delete(id);
          logger.debug(`Cache reference removed: ${id}`);
        }
      }

      // Hint to garbage collector that now would be a good time to run
      if (global.gc && typeof global.gc === 'function') {
        logger.debug('Explicitly running garbage collection');
        global.gc();
      }

      this.lastCleanupTime = Date.now();
      logger.info('Memory cleanup completed');
    } catch (error) {
      logger.error('Error during memory cleanup:', error);
    } finally {
      this.cleanupInProgress = false;
    }
  }

  /**
   * Get the recommendation for when to run the next cleanup
   */
  public getCleanupRecommendation(): CleanupRecommendation {
    const stats = this.getMemoryStats();

    switch (stats.pressureLevel) {
      case 'CRITICAL':
        return {
          intervalMs: CLEANUP_INTERVALS.CRITICAL,
          aggressiveness: 'high',
        };
      case 'HIGH':
        return {
          intervalMs: CLEANUP_INTERVALS.HIGH,
          aggressiveness: 'high',
        };
      case 'MEDIUM':
        return {
          intervalMs: CLEANUP_INTERVALS.MEDIUM,
          aggressiveness: 'medium',
        };
      default:
        return {
          intervalMs: CLEANUP_INTERVALS.LOW,
          aggressiveness: 'low',
        };
    }
  }

  /**
   * Check if cleanup should be run based on current memory pressure and time since last cleanup
   */
  public shouldRunCleanup(): boolean {
    const _stats = this.getMemoryStats();
    const recommendation = this.getCleanupRecommendation();
    const timeSinceLastCleanup = Date.now() - this.lastCleanupTime;

    // Run cleanup if enough time has passed based on the pressure level
    return timeSinceLastCleanup >= recommendation.intervalMs;
  }

  /**
   * Monitor memory usage, store stats, and trigger events
   */
  private monitorMemoryUsage(): void {
    const previousStats = this.currentStats;
    this.updateMemoryStats();

    // If pressure level changed, emit an event
    if (previousStats && previousStats.pressureLevel !== this.currentStats?.pressureLevel) {
      this.emit('pressureChange', {
        previousLevel: previousStats.pressureLevel,
        currentLevel: this.currentStats?.pressureLevel,
        stats: this.currentStats,
      });

      logger.info(
        `Memory pressure changed from ${previousStats.pressureLevel} to ${this.currentStats?.pressureLevel}`
      );
    }

    // Add stats to history
    if (this.currentStats) {
      this.memoryHistory.push(this.currentStats);

      // Trim history if needed
      if (this.memoryHistory.length > this.maxHistorySize) {
        this.memoryHistory.shift();
      }
    }

    // Check if cleanup should be run
    if (this.shouldRunCleanup()) {
      this.runCleanup().catch((err) => {
        logger.error('Error running automatic cleanup:', err);
      });
    }
  }

  /**
   * Update the current memory statistics
   */
  private updateMemoryStats(): void {
    const memoryUsage = process.memoryUsage();

    // Convert to MB for easier reading
    const rss = Math.round(memoryUsage.rss / 1024 / 1024);
    const heapTotal = Math.round(memoryUsage.heapTotal / 1024 / 1024);
    const heapUsed = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    const external = Math.round(memoryUsage.external / 1024 / 1024);
    const arrayBuffers = Math.round(memoryUsage.arrayBuffers / 1024 / 1024);

    // Determine pressure level based on heap usage
    let pressureLevel: MemoryPressureLevel = 'LOW';

    if (heapUsed > MEMORY_THRESHOLDS.CRITICAL) {
      pressureLevel = 'CRITICAL';
    } else if (heapUsed > MEMORY_THRESHOLDS.HIGH) {
      pressureLevel = 'HIGH';
    } else if (heapUsed > MEMORY_THRESHOLDS.MEDIUM) {
      pressureLevel = 'MEDIUM';
    }

    this.currentStats = {
      rss,
      heapTotal,
      heapUsed,
      external,
      arrayBuffers,
      pressureLevel,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get memory usage history
   * @param limit Maximum number of records to return
   */
  public getMemoryHistory(limit = 50): MemoryStats[] {
    const historyLength = this.memoryHistory.length;
    const startIndex = Math.max(0, historyLength - limit);
    return this.memoryHistory.slice(startIndex);
  }

  /**
   * Detect potential memory leaks by analyzing memory usage patterns
   */
  public detectMemoryLeaks(): {
    detected: boolean;
    confidence: number;
    details: string;
  } {
    if (this.memoryHistory.length < 10) {
      return {
        detected: false,
        confidence: 0,
        details: 'Not enough memory history to analyze',
      };
    }

    // Simple algorithm: check if memory usage consistently increases over time
    let increasingCount = 0;
    const samples = Math.min(10, this.memoryHistory.length - 1);

    for (let i = this.memoryHistory.length - samples; i < this.memoryHistory.length; i++) {
      const current = this.memoryHistory[i];
      const previous = this.memoryHistory[i - 1];

      if (current.heapUsed > previous.heapUsed) {
        increasingCount++;
      }
    }

    // Calculate confidence based on consistent increase
    const confidence = (increasingCount / samples) * 100;
    const detected = confidence >= 80;

    return {
      detected,
      confidence,
      details: detected
        ? `Memory usage consistently increasing (${confidence.toFixed(2)}% confidence)`
        : 'No unusual memory patterns detected',
    };
  }
}

// Create singleton instance
export const memoryManager = new MemoryManager();

// Define a type for the global gc function that may be available
declare global {
  namespace NodeJS {
    interface Global {
      gc?: () => void;
    }
  }
}

export default memoryManager;
