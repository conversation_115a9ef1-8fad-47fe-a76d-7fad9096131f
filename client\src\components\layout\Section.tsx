import type React from 'react';
import { cn } from '@/lib/utils';

interface SectionProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  contentClassName?: string;
  headerClassName?: string;
  titleClassName?: string;
  descriptionClassName?: string;
  headerRight?: React.ReactNode;
}

const Section: React.FC<SectionProps> = ({
  title,
  description,
  children,
  className,
  contentClassName,
  headerClassName,
  titleClassName,
  descriptionClassName,
  headerRight,
}) => {
  return (
    <section className={cn('space-section', className)}>
      {(title || description || headerRight) && (
        <div
          className={cn(
            'flex flex-col sm:flex-row sm:items-end justify-between mb-3 sm:mb-4',
            headerClassName
          )}
        >
          <div>
            {title && (
              <h2 className={cn('text-xl font-semibold tracking-tight mb-1', titleClassName)}>
                {title}
              </h2>
            )}
            {description && (
              <p className={cn('text-sm text-muted-foreground', descriptionClassName)}>
                {description}
              </p>
            )}
          </div>
          {headerRight && <div className="mt-2 sm:mt-0">{headerRight}</div>}
        </div>
      )}
      <div className={cn('', contentClassName)}>{children}</div>
    </section>
  );
};

export default Section;
