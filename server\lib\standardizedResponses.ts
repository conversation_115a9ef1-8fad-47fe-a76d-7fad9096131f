/**
 * Standardized Response Service
 *
 * This service provides consistent response formatting across all API endpoints.
 * It ensures uniform error handling, success responses, and eliminates the
 * inconsistent response formats identified in the code audit.
 *
 * Features:
 * - Standardized error response format
 * - Consistent success response structure
 * - Type-safe response interfaces
 * - Automatic error code mapping
 * - Request/response logging integration
 * - Development vs production error handling
 */

import type { Response } from 'express';
import logger from './logger';

/**
 * Standard success response interface
 */
export interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
  timestamp: string;
  requestId?: string;
}

/**
 * Standard error response interface
 */
export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    stack?: string; // Only in development
  };
  timestamp: string;
  requestId?: string;
}

/**
 * Pagination metadata interface
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Paginated response interface
 */
export interface PaginatedResponse<T = any> extends SuccessResponse<T[]> {
  pagination: PaginationMeta;
}

/**
 * Error codes enumeration
 */
export enum ErrorCode {
  // General Errors (4xx)
  BAD_REQUEST = 'BAD_REQUEST',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  METHOD_NOT_ALLOWED = 'METHOD_NOT_ALLOWED',
  CONFLICT = 'CONFLICT',
  VALIDATION_ERROR = 'VALIDATION_ERROR',

  // Server Errors (5xx)
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  NOT_IMPLEMENTED = 'NOT_IMPLEMENTED',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  OPERATION_FAILED = 'OPERATION_FAILED',

  // Custom Application Errors
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  // Authentication & Authorization
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',

  // Validation
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',

  // Resource Management
  DUPLICATE_RESOURCE = 'DUPLICATE_RESOURCE',

  // External Services
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  RATE_LIMITED = 'RATE_LIMITED',

  // System Errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',

  // Business Logic
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
}

/**
 * HTTP status code mapping for error codes
 */
const ERROR_STATUS_MAP: Record<ErrorCode, number> = {
  [ErrorCode.UNAUTHORIZED]: 401,
  [ErrorCode.FORBIDDEN]: 403,
  [ErrorCode.TOKEN_EXPIRED]: 401,
  [ErrorCode.INVALID_CREDENTIALS]: 401,

  [ErrorCode.VALIDATION_ERROR]: 400,
  [ErrorCode.INVALID_INPUT]: 400,
  [ErrorCode.MISSING_REQUIRED_FIELD]: 400,

  [ErrorCode.NOT_FOUND]: 404,
  [ErrorCode.METHOD_NOT_ALLOWED]: 405,
  [ErrorCode.CONFLICT]: 409,
  [ErrorCode.DUPLICATE_RESOURCE]: 409,

  [ErrorCode.EXTERNAL_SERVICE_ERROR]: 502,
  [ErrorCode.RATE_LIMITED]: 429,
  [ErrorCode.SERVICE_UNAVAILABLE]: 503,

  [ErrorCode.INTERNAL_ERROR]: 500,
  [ErrorCode.DATABASE_ERROR]: 500,
  [ErrorCode.NETWORK_ERROR]: 500,

  [ErrorCode.INSUFFICIENT_PERMISSIONS]: 403,
  [ErrorCode.QUOTA_EXCEEDED]: 429,
  [ErrorCode.OPERATION_FAILED]: 400,
  [ErrorCode.BAD_REQUEST]: 400,
  [ErrorCode.NOT_IMPLEMENTED]: 501,
};

/**
 * Standardized Response Service
 */
export class StandardizedResponseService {
  private static instance: StandardizedResponseService;

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): StandardizedResponseService {
    if (!StandardizedResponseService.instance) {
      StandardizedResponseService.instance = new StandardizedResponseService();
    }
    return StandardizedResponseService.instance;
  }

  /**
   * Send a standardized success response
   */
  success<T>(res: Response, data: T, message?: string, statusCode = 200): Response {
    const response: SuccessResponse<T> = {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
      requestId: this.getRequestId(res),
    };

    // Log successful response
    logger.info('[Response] Success response sent', {
      statusCode,
      hasData: !!data,
      message,
      requestId: response.requestId,
    });

    return res.status(statusCode).json(response);
  }

  /**
   * Send a standardized error response
   */
  error(
    res: Response,
    errorCode: ErrorCode,
    message: string,
    details?: any,
    statusCode?: number
  ): Response {
    // Determine appropriate HTTP status code
    const httpStatus = statusCode || this.getStatusCodeFromErrorCode(errorCode);

    // Sanitize error details for production
    const sanitizedDetails = this.sanitizeErrorDetails(details);

    const response: ErrorResponse = {
      success: false,
      error: {
        code: errorCode,
        message,
        details: sanitizedDetails,
      },
      timestamp: new Date().toISOString(),
      requestId: this.getRequestId(res),
    };

    // Include stack trace in non-production environments to aid debugging
    if (process.env.NODE_ENV !== 'production' && details instanceof Error) {
      response.error.stack = details.stack;
    }

    // Log error response
    logger.error('[Response] Error response sent', {
      errorCode,
      message,
      statusCode: httpStatus,
      hasDetails: !!details,
      requestId: response.requestId,
    });

    return res.status(httpStatus).json(response);
  }

  /**
   * Send a paginated success response
   */
  paginated<T>(res: Response, data: T[], pagination: PaginationMeta, message?: string): Response {
    const response: PaginatedResponse<T> = {
      success: true,
      data,
      message,
      pagination,
      timestamp: new Date().toISOString(),
      requestId: this.getRequestId(res),
    };

    logger.info('[Response] Paginated response sent', {
      dataCount: data.length,
      pagination,
      message,
      requestId: response.requestId,
    });

    return res.status(200).json(response);
  }

  /**
   * Handle and format common Express errors
   */
  handleExpressError(res: Response, error: any): Response {
    // Handle validation errors
    if (error.name === 'ValidationError') {
      return this.error(
        res,
        ErrorCode.VALIDATION_ERROR,
        'Validation failed',
        error.details || error.message
      );
    }

    // Handle database errors
    if (error.name === 'DatabaseError' || error.code === 'ECONNREFUSED') {
      return this.error(
        res,
        ErrorCode.DATABASE_ERROR,
        'Database operation failed',
        process.env.NODE_ENV !== 'production' ? error.message : undefined
      );
    }

    // Handle authentication errors
    if (error.name === 'UnauthorizedError' || error.status === 401) {
      return this.error(res, ErrorCode.UNAUTHORIZED, 'Authentication required', error.message);
    }

    // Handle rate limiting
    if (error.status === 429) {
      return this.error(res, ErrorCode.RATE_LIMITED, 'Rate limit exceeded', {
        retryAfter: error.retryAfter,
      });
    }

    // Handle not found
    if (error.status === 404) {
      return this.error(res, ErrorCode.NOT_FOUND, 'Resource not found', error.message);
    }

    // Default to internal server error
    return this.error(
      res,
      ErrorCode.INTERNAL_ERROR,
      'An unexpected error occurred',
      process.env.NODE_ENV !== 'production' ? error : undefined
    );
  }

  /**
   * Create pagination metadata
   */
  createPaginationMeta(page: number, limit: number, total: number): PaginationMeta {
    const totalPages = Math.ceil(total / limit);

    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * Get request ID from response (if available)
   */
  private getRequestId(res: Response): string | undefined {
    return res.locals?.requestId || res.get('X-Request-ID');
  }

  /**
   * Sanitize error details for production
   */
  private sanitizeErrorDetails(details: any): any {
    if (process.env.NODE_ENV === 'production') {
      // In production, exclude sensitive information
      if (details && typeof details === 'object') {
        const sanitized = { ...details };

        // Remove sensitive fields
        delete sanitized.password;
        delete sanitized.token;
        delete sanitized.secret;
        delete sanitized.key;
        delete sanitized.credentials;

        return sanitized;
      }
    }

    return details;
  }

  /**
   * Get status code from error code
   */
  private getStatusCodeFromErrorCode(errorCode: ErrorCode): number {
    return ERROR_STATUS_MAP[errorCode] || 500;
  }
}

// Export singleton instance
export const responseService = StandardizedResponseService.getInstance();

// Export convenience functions for easy use
export const sendSuccess = <T>(res: Response, data: T, message?: string, statusCode?: number) =>
  responseService.success(res, data, message, statusCode);

export const sendError = (
  res: Response,
  errorCode: ErrorCode,
  message: string,
  details?: any,
  statusCode?: number
) => responseService.error(res, errorCode, message, details, statusCode);

export const sendPaginated = <T>(
  res: Response,
  data: T[],
  pagination: PaginationMeta,
  message?: string
) => responseService.paginated(res, data, pagination, message);

export const handleError = (res: Response, error: any) =>
  responseService.handleExpressError(res, error);

export const createPagination = (page: number, limit: number, total: number) =>
  responseService.createPaginationMeta(page, limit, total);

/**
 * Express middleware for consistent error handling
 */
export const standardErrorHandler = (err: any, _req: any, res: Response, next: any) => {
  if (res.headersSent) {
    return next(err);
  }

  return handleError(res, err);
};

/**
 * Express middleware to add request ID to responses
 */
export const addRequestId = (req: any, res: Response, next: any) => {
  const requestId =
    req.headers['x-request-id'] || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  res.locals.requestId = requestId;
  res.set('X-Request-ID', requestId);

  next();
};

export const sendBadRequest = (res: Response, message: string, details?: Record<string, any>) => {
  return res.status(400).json({
    success: false,
    error: {
      code: ErrorCode.BAD_REQUEST,
      message,
      details,
    },
  });
};
