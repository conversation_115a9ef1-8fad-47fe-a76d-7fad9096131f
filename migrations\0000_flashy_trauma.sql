CREATE TABLE IF NOT EXISTS "achievements" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"name" text NOT NULL,
	"description" text NOT NULL,
	"type" text NOT NULL,
	"level" integer DEFAULT 1 NOT NULL,
	"icon" text NOT NULL,
	"unlocked_at" timestamp DEFAULT now() NOT NULL,
	"progress" integer DEFAULT 0 NOT NULL,
	"max_progress" integer NOT NULL,
	"is_complete" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "emails" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"message_id" text NOT NULL,
	"thread_id" text,
	"subject" text,
	"snippet" text,
	"sender" text,
	"sender_email" text,
	"received_at" timestamp,
	"is_read" boolean DEFAULT false,
	"is_archived" boolean DEFAULT false,
	"is_replied" boolean DEFAULT false,
	"is_trashed" boolean DEFAULT false,
	"is_important" boolean DEFAULT false,
	"snoozed_until" timestamp,
	"reply_date" timestamp,
	"reply_id" text,
	"summary" text,
	"categories" text[] DEFAULT ,
	"priority" text,
	"ai_reply" text,
	"original_content" text,
	"html_content" text,
	"provider" text,
	"label_ids" text[] DEFAULT ,
	"content_expires_at" timestamp,
	"last_accessed" timestamp DEFAULT now(),
	"is_content_encrypted" boolean DEFAULT false,
	"retention_days" integer DEFAULT 30
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "settings" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"reply_tone" text DEFAULT 'professional',
	"display_name" text,
	"custom_tone" text,
	"privacy_mode" boolean DEFAULT false,
	"notification_digest" boolean DEFAULT true,
	"categories" jsonb,
	"priority_colors" jsonb,
	"theme_mode" text DEFAULT 'system',
	"border_radius" integer DEFAULT 6,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	"data_retention_days" integer DEFAULT 30,
	"allow_ai_processing" boolean DEFAULT true,
	"store_email_content" boolean DEFAULT true,
	"auto_delete_processed_emails" boolean DEFAULT false,
	"encrypt_sensitive_data" boolean DEFAULT true,
	"consent_version" text DEFAULT '1.0',
	"consent_timestamp" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "task_queue" (
	"id" serial PRIMARY KEY NOT NULL,
	"task_type" text NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"priority" integer DEFAULT 1 NOT NULL,
	"data" jsonb NOT NULL,
	"result" jsonb,
	"error" text,
	"retry_count" integer DEFAULT 0 NOT NULL,
	"max_retries" integer DEFAULT 3 NOT NULL,
	"last_attempt_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"scheduled_for" timestamp,
	"completed_at" timestamp,
	"locked_by" text,
	"locked_at" timestamp
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"email" text NOT NULL,
	"name" text,
	"picture" text,
	"access_token" text,
	"refresh_token" text,
	"expires_at" timestamp,
	"provider" text NOT NULL,
	"firebase_uid" text,
	"gmail_tokens" jsonb,
	"emails_processed" integer DEFAULT 0,
	"replies_sent" integer DEFAULT 0,
	"tier" text DEFAULT 'free',
	"reply_tone" text DEFAULT 'professional',
	"last_login" timestamp,
	"last_reply_date" timestamp,
	"refresh_attempts" integer DEFAULT 0,
	"last_token_refresh" timestamp,
	"auth_error_count" integer DEFAULT 0,
	"security_level" text DEFAULT 'standard',
	"token_invalid" boolean DEFAULT false,
	"last_token_error" text,
	"last_api_error" text,
	"last_connection_verified" timestamp,
	"token_update_status" text,
	"token_status" text DEFAULT 'unknown',
	"token_error_count" integer DEFAULT 0,
	"token_error_time" timestamp,
	"token_last_refreshed" timestamp,
	"role" text DEFAULT 'user',
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "achievements" ADD CONSTRAINT "achievements_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "settings" ADD CONSTRAINT "settings_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "achievements_user_id_idx" ON "achievements" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "achievements_type_idx" ON "achievements" USING btree ("type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "achievements_is_complete_idx" ON "achievements" USING btree ("is_complete");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "achievements_unlocked_at_idx" ON "achievements" USING btree ("unlocked_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "achievements_user_type_idx" ON "achievements" USING btree ("user_id","type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "achievements_user_complete_idx" ON "achievements" USING btree ("user_id","is_complete");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "achievements_level_idx" ON "achievements" USING btree ("level");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_user_id_idx" ON "emails" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_message_id_idx" ON "emails" USING btree ("message_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_received_at_idx" ON "emails" USING btree ("received_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_user_received_idx" ON "emails" USING btree ("user_id","received_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_user_archived_idx" ON "emails" USING btree ("user_id","is_archived");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_user_read_idx" ON "emails" USING btree ("user_id","is_read");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_user_trashed_idx" ON "emails" USING btree ("user_id","is_trashed");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_user_important_idx" ON "emails" USING btree ("user_id","is_important");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_archived_idx" ON "emails" USING btree ("is_archived");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_read_idx" ON "emails" USING btree ("is_read");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_trashed_idx" ON "emails" USING btree ("is_trashed");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_important_idx" ON "emails" USING btree ("is_important");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_thread_id_idx" ON "emails" USING btree ("thread_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_provider_idx" ON "emails" USING btree ("provider");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_sender_email_idx" ON "emails" USING btree ("sender_email");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_content_expires_idx" ON "emails" USING btree ("content_expires_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "emails_last_accessed_idx" ON "emails" USING btree ("last_accessed");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "settings_user_id_idx" ON "settings" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "settings_privacy_mode_idx" ON "settings" USING btree ("privacy_mode");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "settings_allow_ai_processing_idx" ON "settings" USING btree ("allow_ai_processing");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "settings_encrypt_sensitive_data_idx" ON "settings" USING btree ("encrypt_sensitive_data");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "settings_consent_version_idx" ON "settings" USING btree ("consent_version");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "settings_updated_at_idx" ON "settings" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "settings_data_retention_days_idx" ON "settings" USING btree ("data_retention_days");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "task_queue_status_idx" ON "task_queue" USING btree ("status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "task_queue_priority_idx" ON "task_queue" USING btree ("priority");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "task_queue_type_idx" ON "task_queue" USING btree ("task_type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "task_queue_scheduled_idx" ON "task_queue" USING btree ("scheduled_for");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_firebase_uid_idx" ON "users" USING btree ("firebase_uid");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_provider_idx" ON "users" USING btree ("provider");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_token_invalid_idx" ON "users" USING btree ("token_invalid");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_token_status_idx" ON "users" USING btree ("token_status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_expires_at_idx" ON "users" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_last_login_idx" ON "users" USING btree ("last_login");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_role_idx" ON "users" USING btree ("role");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_tier_idx" ON "users" USING btree ("tier");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_provider_token_status_idx" ON "users" USING btree ("provider","token_status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_token_invalid_provider_idx" ON "users" USING btree ("token_invalid","provider");