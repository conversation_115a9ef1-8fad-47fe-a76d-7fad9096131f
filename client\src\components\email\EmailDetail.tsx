import { format, formatDistanceToNow } from 'date-fns';
import {
  Archive,
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  Clock,
  Paperclip,
  RefreshCcw,
  Star,
  Trash2,
} from 'lucide-react';
import type React from 'react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useEmailDetail } from '@/context/EmailDetailContext';
import { useEmailActions } from '@/hooks/use-email-actions';
import { useEmailNavigation } from '@/hooks/use-email-navigation';
import { useEmailList } from '@/context/EmailListContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { CategoryBadge } from '../ui/badge-category';
import EmailReply from './EmailReply';
import EmailSummary from './EmailSummary';
import { GmailLabelSelector } from './GmailLabelSelector';
import StandardizedEmailContent from './StandardizedEmailContent';
import UrgencyBadge from './UrgencyBadge';
import './EmailContent.css';

const EmailDetail: React.FC = () => {
  // Use our custom hooks for cleaner code
  const { selectedEmail } = useEmailDetail();
  const { refreshEmails } = useEmailList();
  const {
    archiveEmail,
    isArchiving,
    trashEmail,
    isTrashing,
    markEmailAsImportant,
    isMarkingImportant,
    snoozeEmail,
    isSnoozing,
  } = useEmailActions();
  const { navigateBack, navigateNext, navigatePrevious, getCurrentIndex, getTotalCount } =
    useEmailNavigation();

  const getInitials = (name: string | null): string => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const handleArchive = () => {
    if (selectedEmail?.messageId) {
      archiveEmail(selectedEmail.messageId);
    }
  };

  const handleTrash = () => {
    if (selectedEmail?.messageId) {
      trashEmail(selectedEmail.messageId);
    }
  };

  const handleMarkImportant = () => {
    if (selectedEmail?.messageId) {
      markEmailAsImportant({
        messageId: selectedEmail.messageId,
        important: !selectedEmail.isImportant,
      });
    }
  };

  const handleSnooze = () => {
    if (selectedEmail?.messageId) {
      // Default snooze time is 24 hours from now
      const snoozeUntil = new Date();
      snoozeUntil.setHours(snoozeUntil.getHours() + 24);
      snoozeEmail({ messageId: selectedEmail.messageId || '', snoozeUntil });
    }
  };

  if (!selectedEmail) {
    return (
      <div className="hidden md:flex md:w-1/2 lg:w-3/5 flex-col bg-card items-center justify-center animate-in fade-in-50 duration-500">
        <div className="text-center p-8 max-w-md">
          <div className="mb-6 animate-pulse">
            <div className="inline-flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 shadow-md">
              <Paperclip className="h-8 w-8 text-primary" />
            </div>
          </div>
          <h3 className="text-xl font-medium mb-3 text-foreground">No email selected</h3>
          <p className="text-sm text-muted-foreground mb-6 max-w-xs mx-auto">
            Select an email from the list to view its contents, or use the actions below to manage
            your inbox
          </p>
          <div className="grid grid-cols-2 gap-4 mt-2">
            <Button
              variant="outline"
              className="flex items-center justify-center gap-2 hover:bg-primary/5 transition-colors"
              onClick={() => navigateBack()}
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to inbox</span>
            </Button>
            <Button
              variant="outline"
              className="flex items-center justify-center gap-2 hover:bg-primary/5 transition-colors"
              onClick={() => refreshEmails()}
            >
              <RefreshCcw className="h-4 w-4" />
              <span>Refresh</span>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const getTimeDisplay = (dateString: string | null | undefined) => {
    if (!dateString) return 'Unknown time';

    try {
      const date = new Date(dateString);
      const formattedTime = format(date, 'h:mm a');
      const relativeTime = formatDistanceToNow(date, { addSuffix: true });
      return `${formattedTime} (${relativeTime})`;
    } catch (error) {
      console.error('Invalid date format:', dateString, error);
      return 'Invalid date';
    }
  };

  return (
    <div className="w-full md:w-1/2 lg:w-3/5 flex flex-col bg-card overflow-hidden transition-all duration-150 ease-in-out email-detail-container max-w-full min-w-0 contain-content">
      <div className="flex-shrink-0 p-1 sm:p-2 border-b border-border flex items-center justify-between email-detail-header shadow-sm">
        <div className="flex items-center gap-1 overflow-hidden">
          <Button
            variant="ghost"
            size="sm"
            className="p-1 hover:bg-primary/10 transition-all flex-shrink-0"
            onClick={navigateBack}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="p-1 hover:bg-primary/10 transition-colors flex-shrink-0 md:relative group"
            onClick={handleArchive}
            disabled={isArchiving}
            title="Archive email"
          >
            <Archive className={`h-4 w-4 ${isArchiving ? 'animate-spin text-primary' : ''}`} />
            <span className="sr-only md:not-sr-only absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground text-[10px] px-1.5 py-0.5 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
              Archive
            </span>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="p-1 hover:bg-destructive/10 transition-colors flex-shrink-0 md:relative group"
            onClick={handleTrash}
            disabled={isTrashing}
            title="Move to trash"
          >
            <Trash2 className={`h-4 w-4 ${isTrashing ? 'animate-spin text-destructive' : ''}`} />
            <span className="sr-only md:not-sr-only absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-destructive text-destructive-foreground text-[10px] px-1.5 py-0.5 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
              Trash
            </span>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="p-1 hover:bg-warning/10 transition-colors flex-shrink-0 md:relative group"
            onClick={handleSnooze}
            disabled={isSnoozing}
            title="Snooze email"
          >
            <Clock className={`h-4 w-4 ${isSnoozing ? 'animate-spin text-warning' : ''}`} />
            <span className="sr-only md:not-sr-only absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-warning text-warning-foreground text-[10px] px-1.5 py-0.5 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
              Snooze
            </span>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className={`p-1 hover:bg-warning/10 transition-colors flex-shrink-0 md:relative group ${selectedEmail?.isImportant ? 'bg-warning/10' : ''}`}
            onClick={handleMarkImportant}
            disabled={isMarkingImportant}
            title={selectedEmail?.isImportant ? 'Unmark as important' : 'Mark as important'}
          >
            <Star
              className={`h-4 w-4 transition-all ${isMarkingImportant ? 'animate-spin text-warning' : ''} ${selectedEmail?.isImportant ? 'fill-warning text-warning' : ''}`}
            />
            <span className="sr-only md:not-sr-only absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-warning text-warning-foreground text-[10px] px-1.5 py-0.5 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
              {selectedEmail?.isImportant ? 'Unmark' : 'Important'}
            </span>
          </Button>
        </div>

        <div className="flex items-center text-xs text-muted-foreground bg-muted/50 rounded-full px-2 py-1 flex-shrink-0">
          <span className="hidden xs:inline">
            {getCurrentIndex()}/{getTotalCount()}
          </span>
          <div className="flex">
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-6 w-6 min-h-0 min-w-0 hover:bg-background/80 rounded-full flex-shrink-0"
              onClick={navigatePrevious}
              title="Previous email"
            >
              <ChevronLeft className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-6 w-6 min-h-0 min-w-0 hover:bg-background/80 rounded-full flex-shrink-0"
              onClick={navigateNext}
              title="Next email"
            >
              <ChevronRight className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Email Content */}
      <div className="email-detail-content animate-in fade-in-50 duration-150 p-2 overflow-y-auto overflow-x-hidden flex-grow">
        <div className="mb-3 sm:mb-4">
          <h1 className="text-base sm:text-lg font-semibold text-foreground mb-2 leading-tight break-words max-w-full">
            {selectedEmail.subject}
          </h1>

          <div className="flex flex-wrap gap-1 mb-2">
            {(() => {
              // Handle different possible formats of categories
              let categoryList: string[] = [];
              try {
                if (selectedEmail.categories) {
                  if (typeof selectedEmail.categories === 'string') {
                    categoryList = JSON.parse(selectedEmail.categories);
                  } else if (Array.isArray(selectedEmail.categories)) {
                    categoryList = selectedEmail.categories;
                  }
                }
              } catch (error) {
                console.error('Failed to parse categories in detail view:', error);
              }

              // Show urgency badge first if applicable
              const hasUrgentCategory = categoryList.includes('Urgent');
              const urgencyElement = (
                <UrgencyBadge
                  key="urgency-badge"
                  priority={selectedEmail.priority || ''}
                  isUrgent={hasUrgentCategory}
                />
              );

              // Filter out the urgent category since it's displayed separately
              const filteredCategories = categoryList.filter((cat: string) => cat !== 'Urgent');

              return [
                // First show urgency badge
                <div key="urgency-badge-wrapper">{urgencyElement}</div>,

                // Then show other categories
                ...filteredCategories.map((category, _index) => (
                  <div key={`category-${category}-wrapper`}>
                    <CategoryBadge key={category} category={category} />
                  </div>
                )),
              ];
            })()}
            <div>
              <span className="text-xs text-muted-foreground flex items-center bg-muted/40 px-2 py-1 rounded">
                <Clock className="h-3 w-3 mr-1" />
                {selectedEmail.receivedAt && getTimeDisplay(selectedEmail.receivedAt.toString())}
              </span>
            </div>
          </div>

          <div className="flex items-start mb-3 p-2 rounded-lg hover:bg-muted/30 transition-colors">
            <Avatar className="h-7 w-7 sm:h-9 sm:w-9 border border-border shadow-sm flex-shrink-0">
              <AvatarFallback className="bg-primary/10 text-primary font-medium text-xs">
                {getInitials(selectedEmail.sender)}
              </AvatarFallback>
            </Avatar>
            <div className="ml-2 min-w-0 flex-1 overflow-hidden">
              <div className="flex flex-col">
                <span className="font-medium text-foreground text-sm truncate">
                  {selectedEmail.sender}
                </span>
                <span className="text-xs text-muted-foreground truncate">
                  &lt;{selectedEmail.senderEmail}&gt;
                </span>
              </div>
              <div className="flex items-center text-xs text-muted-foreground mt-0.5">
                <span className="bg-muted px-1.5 py-0.5 rounded-full text-[10px] mr-1.5">
                  to me
                </span>
                <span className="text-[10px]">
                  {selectedEmail.receivedAt
                    ? new Date(selectedEmail.receivedAt).toLocaleString('en-US', {
                        hour: 'numeric',
                        minute: 'numeric',
                        month: 'short',
                        day: 'numeric',
                      })
                    : 'Unknown date'}
                </span>
              </div>
            </div>
          </div>

          {/* Gmail Labels Selector */}
          {selectedEmail.provider === 'gmail' && (
            <GmailLabelSelector
              emailId={selectedEmail.messageId || ''}
              currentLabels={selectedEmail.labelIds || []}
            />
          )}
        </div>

        <EmailSummary summary={selectedEmail.summary} messageId={selectedEmail.messageId || ''} />

        {/* AI Reply now placed here, between Summary and Full Message */}
        {selectedEmail && (
          <div className="mb-3 sm:mb-4 email-reply-mobile">
            <EmailReply email={selectedEmail} />
          </div>
        )}

        <div className="mb-3 overflow-hidden">
          <div className="flex items-center mb-2 text-xs text-muted-foreground">
            <div className="h-4 w-4 mr-1 flex items-center justify-center rounded-full bg-accent/50">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-2.5 w-2.5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-label="envelope"
              >
                <title>Envelope</title>
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M3 19v-8.93a2 2 0 01.89-1.664l7-4.666a2 2 0 012.22 0l7 4.666A2 2 0 0121 10.07V19M3 19a2 2 0 002 2h14a2 2 0 002-2M3 19l6.75-4.5M21 19l-6.75-4.5M3 10l6.75 4.5M21 10l-6.75 4.5m0 0l-1.14.76a2 2 0 01-2.22 0l-1.14-.76"
                />
              </svg>
            </div>
            <span className="font-medium">Full Message</span>
          </div>

          {selectedEmail.htmlContent || selectedEmail.originalContent ? (
            <div className="email-content-body max-w-full prose prose-sm dark:prose-invert prose-p:text-foreground prose-a:text-primary hover:prose-a:text-primary-focus prose-blockquote:text-muted-foreground break-words overflow-x-hidden">
              <StandardizedEmailContent
                html={selectedEmail.htmlContent ?? selectedEmail.originalContent}
              />
            </div>
          ) : (
            <div className="text-center py-12 text-muted-foreground">
              <p>This email has no content.</p>
            </div>
          )}
        </div>
      </div>

      <div className="flex-shrink-0 border-t border-border mt-auto p-2">
        <div className="flex items-center text-xs text-muted-foreground bg-muted/50 rounded-full px-2 py-1 flex-shrink-0">
          <span className="hidden xs:inline">
            {getCurrentIndex()}/{getTotalCount()}
          </span>
          <div className="flex">
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-6 w-6 min-h-0 min-w-0 hover:bg-background/80 rounded-full flex-shrink-0"
              onClick={navigatePrevious}
              title="Previous email"
            >
              <ChevronLeft className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-6 w-6 min-h-0 min-w-0 hover:bg-background/80 rounded-full flex-shrink-0"
              onClick={navigateNext}
              title="Next email"
            >
              <ChevronRight className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailDetail;
