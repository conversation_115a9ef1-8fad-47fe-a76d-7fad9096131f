import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { HelmetProvider } from 'react-helmet-async';

jest.mock('react', () => {
  const actual = jest.requireActual('react');
  return { __esModule: true, ...actual, default: actual };
});

// Mock sub-components to isolate layout behaviour
jest.mock('@/components/layout/AppHeader', () => ({
  __esModule: true,
  default: () => <header data-testid="app-header">header</header>,
}));

afterEach(() => jest.clearAllMocks());

jest.mock('@/components/layout/AppSidebar', () => ({
  __esModule: true,
  default: () => <aside data-testid="app-sidebar">sidebar</aside>,
}));

jest.mock('framer-motion', () => ({
  __esModule: true,
  motion: {
    main: ({ children, ...rest }: any) => <main {...rest}>{children}</main>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

import AppLayout from '@/components/layout/AppLayout';

describe('AppLayout', () => {
  const renderLayout = (children: React.ReactNode) =>
    render(
      <HelmetProvider>
        <AppLayout>{children}</AppLayout>
      </HelmetProvider>
    );

  it('renders header, sidebar and children', () => {
    renderLayout(<div data-testid="content">content</div>);
    expect(screen.getByTestId('app-header')).toBeInTheDocument();
    expect(screen.getByTestId('app-sidebar')).toBeInTheDocument();
    expect(screen.getByTestId('content')).toBeInTheDocument();
  });

  it('injects meta viewport tag via React Helmet', async () => {
    renderLayout(null);
    await waitFor(() => {
      const meta = document.querySelector('meta[name="viewport"]');
      expect(meta).not.toBeNull();
      expect(meta).toHaveAttribute('content', expect.stringContaining('width=device-width'));
    });
  });
}); 