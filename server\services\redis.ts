import type { RedisClientType } from 'redis';
import { createClient } from 'redis';
import logger from '../lib/logger';

let redisClient: RedisClientType | undefined;

/**
 * Initialize Redis connection with validated configuration
 * This should be called during server startup after environment validation
 */
export function initializeRedis(redisUrl?: string): void {
  if (redisClient) {
    logger.warn('Redis is already initialized.');
    return;
  }

  // Use provided URL or fall back to environment variable for backward compatibility
  const connectionUrl = redisUrl || process.env.REDIS_URL;

  if (connectionUrl) {
    try {
      const client = createClient({
        url: connectionUrl,
        socket: {
          reconnectStrategy: (retries) => Math.min(retries * 50, 500),
          connectTimeout: 10000,
        },
      });

      client.on('connect', () => {
        logger.info('Successfully connected to Redis', { url: connectionUrl.replace(/\/\/.*@/, '//***@') });
      });

      client.on('error', (err) => {
        logger.error('Redis connection error', { 
          error: String(err),
          service: 'redis',
          url: connectionUrl.replace(/\/\/.*@/, '//***@')
        });
        // On connection error, you might want to consider a graceful shutdown
        // or prevent new operations that depend on Redis.
        // For now, we just log it.
      });

      client.on('ready', () => {
        logger.info('Redis client is ready to receive commands');
      });

      client.on('reconnecting', () => {
        logger.info('Redis client is reconnecting');
      });

      client.on('end', () => {
        logger.warn('Redis connection ended');
      });

      // Connect the client
      client.connect().catch((error) => {
        logger.error('Failed to connect Redis client', { 
          error: String(error),
          service: 'redis'
        });
      });

      redisClient = client as RedisClientType;
    } catch (error) {
      logger.error('Failed to create Redis client instance', { 
        error: String(error),
        service: 'redis'
      });
      // This is a critical failure, the app should likely exit.
      throw new Error('Redis client could not be instantiated.');
    }
  } else {
    logger.warn('REDIS_URL not provided. Redis-dependent services will be unavailable.');
    // Not throwing an error here allows the app to run in a degraded mode
    // where Redis is not essential for all operations.
  }
}

/**
 * Returns the initialized Redis client instance.
 * Throws an error if Redis is not initialized, ensuring fail-fast behavior
 * for components that critically depend on it.
 * @returns {RedisClientType} The node-redis client instance.
 */
export function getRedisClient(): RedisClientType {
  if (!redisClient) {
    throw new Error(
      'Redis client has not been initialized. Check server startup and REDIS_URL environment variable.'
    );
  }
  return redisClient;
}

/**
 * Check if Redis is connected and ready
 */
export function isRedisConnected(): boolean {
  return redisClient?.isReady ?? false;
}

/**
 * Gracefully close Redis connection
 */
export async function closeRedisConnection(): Promise<void> {
  if (redisClient) {
    try {
      await redisClient.quit();
      logger.info('Redis connection closed gracefully');
    } catch (error) {
      logger.error('Error closing Redis connection', { 
        error: String(error),
        service: 'redis'
      });
    } finally {
      redisClient = undefined;
    }
  }
}

/**
 * Get Redis connection status and info
 */
export async function getRedisStatus(): Promise<{
  connected: boolean;
  ready: boolean;
  info?: Record<string, any>;
}> {
  if (!redisClient) {
    return { connected: false, ready: false };
  }

  try {
    const info = await redisClient.info();
    return {
      connected: redisClient.isOpen,
      ready: redisClient.isReady,
      info: {
        uptime: info.match(/uptime_in_seconds:(\d+)/)?.[1],
        connected_clients: info.match(/connected_clients:(\d+)/)?.[1],
        used_memory: info.match(/used_memory_human:(\S+)/)?.[1],
      },
    };
  } catch (error) {
    logger.error('Failed to get Redis status', { 
      error: String(error),
      service: 'redis'
    });
    return {
      connected: redisClient.isOpen,
      ready: redisClient.isReady,
    };
  }
}
