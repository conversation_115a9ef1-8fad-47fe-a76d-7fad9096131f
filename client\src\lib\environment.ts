/**
 * Client-side Environment Variable Access
 *
 * Provides type-safe access to environment variables on the client side.
 * Note: Only NODE_ENV is available in browser environments for security.
 */

/**
 * Client environment configuration interface
 */
export interface ClientEnvironmentConfig {
  NODE_ENV: 'development' | 'production' | 'test';
}

/**
 * Client environment service
 */
class ClientEnvironment {
  private static instance: ClientEnvironment;
  private config: ClientEnvironmentConfig;

  private constructor() {
    this.config = {
      NODE_ENV: (process.env.NODE_ENV as ClientEnvironmentConfig['NODE_ENV']) || 'development',
    };
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ClientEnvironment {
    if (!ClientEnvironment.instance) {
      ClientEnvironment.instance = new ClientEnvironment();
    }
    return ClientEnvironment.instance;
  }

  /**
   * Get environment variable
   */
  get<K extends keyof ClientEnvironmentConfig>(key: K): ClientEnvironmentConfig[K] {
    return this.config[key];
  }

  /**
   * Check if running in development mode
   */
  isDevelopment(): boolean {
    return this.config.NODE_ENV === 'development';
  }

  /**
   * Check if running in production mode
   */
  isProduction(): boolean {
    return this.config.NODE_ENV === 'production';
  }

  /**
   * Check if running in test mode
   */
  isTest(): boolean {
    return this.config.NODE_ENV === 'test';
  }

  /**
   * Get all configuration
   */
  getConfig(): ClientEnvironmentConfig {
    return { ...this.config };
  }
}

// Create singleton instance
const clientEnvironment = ClientEnvironment.getInstance();

// Export convenience functions
export const getClientEnvVar = <K extends keyof ClientEnvironmentConfig>(
  key: K
): ClientEnvironmentConfig[K] => clientEnvironment.get(key);

export const isDevelopment = (): boolean => clientEnvironment.isDevelopment();
export const isProduction = (): boolean => clientEnvironment.isProduction();
export const isTest = (): boolean => clientEnvironment.isTest();
export const getClientConfig = (): ClientEnvironmentConfig => clientEnvironment.getConfig();

export default clientEnvironment;
