import { useEffect } from 'react';
import authSyncManager from '@/lib/authSync';

/**
 * App Initializer
 *
 * This component is responsible for initializing application-wide services
 * that should start after the main React component tree is mounted.
 * This ensures that contexts and other providers are available.
 */
const AppInitializer: React.FC = () => {
  useEffect(() => {
    // Start services here
    authSyncManager.start();

    // Return a cleanup function to stop services when the app unmounts
    return () => {
      authSyncManager.stop();
    };
  }, []); // The empty dependency array ensures this runs only once on mount

  // This component does not render anything
  return null;
};

export default AppInitializer;
