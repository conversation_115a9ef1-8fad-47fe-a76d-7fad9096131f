/**
 * Auth Module Initializer
 *
 * This file provides the main entry point for initializing the entire
 * authentication system for the Express application.
 */

import type { Express } from 'express';
import logger from '../lib/logger';
import { initializeCsrfProtection } from '../middleware/csrf';
import { initializeFirebase } from './firebase';
import authRoutes from './routes';
import { configureSession } from './session';

/**
 * Initializes all authentication-related components for the Express app.
 *
 * @param app - The Express application instance.
 */
export async function initializeAuth(app: Express): Promise<void> {
  // 1. Initialize Firebase Admin SDK.
  // This is a prerequisite for any services that use Firebase.
  initializeFirebase();

  // 2. Configure and apply session management.
  // This must be configured before any routes that rely on user sessions.
  const sessionMiddleware = configureSession();
  app.use(sessionMiddleware);

  // 3. Initialize and apply CSRF protection.
  // This must come after session middleware, as it uses sessions to store secrets.
  initializeCsrfProtection(app);

  // 4. Mount the authentication routes under the '/api/auth' prefix.
  app.use('/api/auth', authRoutes);

  logger.info('Authentication module initialized successfully.');
}
