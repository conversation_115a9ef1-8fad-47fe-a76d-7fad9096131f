import { type gmail_v1, google } from 'googleapis';
import type { TokenService } from './tokenService';

/**
 * Returns the standard scopes required for Gmail API access.
 * @returns An array of scope strings.
 */
export function getGmailScopes(): string[] {
  return [
    'https://www.googleapis.com/auth/gmail.readonly',
    'https://www.googleapis.com/auth/gmail.modify',
    'https://www.googleapis.com/auth/gmail.labels',
  ];
}

/**
 * Creates a new Gmail API client instance.
 * @param tokenService - An instance of the TokenService.
 * @param tokens - The user's tokens.
 * @returns A Gmail API client.
 */
export function getGmailAPI(
  tokenService: TokenService,
  tokens: {
    access_token?: string | null;
    refresh_token?: string | null;
    expiry_date?: number | null;
  }
): gmail_v1.Gmail {
  const authClient = tokenService.getOAuth2Client();
  authClient.setCredentials({
    access_token: tokens.access_token,
    refresh_token: tokens.refresh_token,
    expiry_date: tokens.expiry_date,
  });
  return google.gmail({ version: 'v1', auth: authClient });
}
