/**
 * User Migration Utility
 *
 * This utility helps manage user identity transitions when migrating from
 * traditional authentication to Firebase.
 */

import * as admin from 'firebase-admin';
import logger from '../lib/logger';
import { storage } from '../storage';

/**
 * Synchronizes email provider tokens between user accounts
 * This is useful when a user has multiple accounts (e.g., from migration to Firebase)
 *
 * @param sourceUserId - The source user ID (old auth system)
 * @param targetUserId - The target user ID (Firebase auth)
 * @param overwrite - Whether to overwrite existing tokens
 * @returns Result of the operation
 */
async function syncEmailProviderTokens(
  sourceUserId: number,
  targetUserId: number,
  overwrite = false
) {
  try {
    // Get both users
    const sourceUser = await storage.getUser(sourceUserId);
    const targetUser = await storage.getUser(targetUserId);

    if (!sourceUser) {
      throw new Error(`Source user ${sourceUserId} not found`);
    }

    if (!targetUser) {
      throw new Error(`Target user ${targetUserId} not found`);
    }

    // Track what we've synchronized
    const result = {
      gmail: false,
      legacy: false,
    };

    // Get the updates to apply
    const updates: Record<string, any> = {};

    // Sync Gmail tokens if available
    if (sourceUser.gmailTokens && (!targetUser.gmailTokens || overwrite)) {
      updates.gmailTokens = sourceUser.gmailTokens;
      result.gmail = true;
    }

    // Sync legacy token fields for backwards compatibility
    if (overwrite || (!targetUser.accessToken && !targetUser.refreshToken)) {
      if (sourceUser.accessToken) {
        updates.accessToken = sourceUser.accessToken;
        result.legacy = true;
      }

      if (sourceUser.refreshToken) {
        updates.refreshToken = sourceUser.refreshToken;
        result.legacy = true;
      }

      if (sourceUser.expiresAt) {
        updates.expiresAt = sourceUser.expiresAt;
      }
    }

    // Only update if we have something to update
    if (Object.keys(updates).length > 0) {
      // Also clear any token error flags
      updates.tokenInvalid = false;
      updates.tokenInvalidReason = null;
      updates.circuitBreakerStatus = 'CLOSED';
      updates.circuitBreakerFailureCount = 0;
      updates.lastTokenError = null;

      // Update the target user
      await storage.updateUser(targetUserId, updates);
    } else {
    }

    return {
      sourceUserId,
      targetUserId,
      synchronized: result,
      tokensUpdated: Object.keys(updates).filter(
        (key) => key !== 'tokenInvalid' && key !== 'tokenInvalidReason'
      ),
    };
  } catch (error) {
    logger.error('Error synchronizing tokens:', error);
    throw error;
  }
}

/**
 * Migrates user session from an old user ID to their Firebase user ID
 *
 * @param req - Express request object
 * @param legacyUserId - The legacy user ID
 * @param firebaseUid - Firebase UID to find the new user ID
 * @returns Result of the operation
 */
async function migrateUserSession(req: any, legacyUserId: number, firebaseUid: string) {
  try {
    // Verify the Firebase UID exists
    const firebaseUser = await admin.auth().getUser(firebaseUid);
    if (!firebaseUser) {
      throw new Error(`Firebase user with UID ${firebaseUid} not found`);
    }

    // Get the legacy user
    const legacyUser = await storage.getUser(legacyUserId);
    if (!legacyUser) {
      throw new Error(`Legacy user with ID ${legacyUserId} not found`);
    }

    // Try to find a user with this Firebase UID
    let firebaseDbUser = await storage.getUserByFirebaseUid(firebaseUid);

    // If no user found with this Firebase UID, let's create one or update an existing one
    if (!firebaseDbUser) {
      // Try to find a user with the same email
      const userByEmail = await storage.getUserByEmail(firebaseUser.email!);

      if (userByEmail && userByEmail.id !== legacyUserId) {
        // Update this user with the Firebase UID
        await storage.updateUser(userByEmail.id, {
          firebaseUid: firebaseUid,
        });

        firebaseDbUser = { ...userByEmail, firebaseUid: firebaseUid };
      } else if (userByEmail && userByEmail.id === legacyUserId) {
        // This is the same user, just update with Firebase UID
        await storage.updateUser(legacyUserId, {
          firebaseUid: firebaseUid,
        });

        firebaseDbUser = { ...legacyUser, firebaseUid: firebaseUid };
      } else {
        // Create a new user
        firebaseDbUser = await storage.createUser({
          name: firebaseUser.displayName || 'Firebase User',
          email: firebaseUser.email!,
          provider: 'firebase',
          picture: firebaseUser.photoURL,
          firebaseUid: firebaseUid,
        });
      }
    }

    // Now we definitely have a Firebase user in our database

    // Sync tokens if they're different users
    if (firebaseDbUser.id !== legacyUserId) {
      await syncEmailProviderTokens(legacyUserId, firebaseDbUser.id, true);
    }

    // Update the session to use the Firebase user ID
    if (req.session) {
      req.session.userId = firebaseDbUser.id;
    }

    return {
      oldUserId: legacyUserId,
      newUserId: firebaseDbUser.id,
      email: firebaseUser.email,
      sessionUpdated: !!req.session,
    };
  } catch (error) {
    logger.error('Error migrating user session:', error);
    throw error;
  }
}

export { syncEmailProviderTokens, migrateUserSession };
