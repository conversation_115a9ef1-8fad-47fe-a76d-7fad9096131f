#!/usr/bin/env tsx

import { config } from 'dotenv';
import { storage } from '../server/storage';

config();

async function directMerge() {
  try {
    console.log('Starting direct merge: User 3 -> User 1');
    
    // Get both users
    const user1 = await storage.getUser(1);
    const user3 = await storage.getUser(3);
    
    if (!user1) {
      console.error('User 1 not found!');
      return;
    }
    
    if (!user3) {
      console.error('User 3 not found!');
      return;
    }
    
    console.log(`User 1: ${user1.email}, Provider: ${user1.provider}`);
    console.log(`User 3: ${user3.email}, Provider: ${user3.provider}`);
    
    // 1. Copy Gmail tokens from user 3 to user 1
    if (user3.gmailTokens) {
      console.log('Copying Gmail tokens from user 3 to user 1...');
      await storage.updateUser(1, {
        gmailTokens: user3.gmailTokens
      });
      console.log('✅ Gmail tokens copied');
    }
    
    // 2. Transfer all emails from user 3 to user 1
    console.log('Transferring emails from user 3 to user 1...');
    const user3Emails = await storage.getEmails(3, 1000, 0);
    console.log(`Found ${user3Emails.length} emails for user 3`);
    
    for (const email of user3Emails) {
      await storage.updateEmail(email.id, { userId: 1 });
    }
    console.log(`✅ Transferred ${user3Emails.length} emails`);
    
    // 3. Delete user 3
    console.log('Deleting user 3...');
    await storage.deleteUser(3);
    console.log('✅ User 3 deleted');
    
    console.log('\n🎉 MERGE COMPLETED SUCCESSFULLY');
    console.log('User 1 now has Gmail tokens and all emails from user 3');
    
  } catch (error) {
    console.error('❌ Error during merge:', error);
  }
}

directMerge(); 