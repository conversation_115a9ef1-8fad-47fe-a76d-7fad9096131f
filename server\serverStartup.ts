/**
 * Server Startup Module
 *
 * This module provides a standardized approach to starting the server
 * with proper error handling, port management, and service initialization.
 */

import { exec } from 'node:child_process';
import type { Server } from 'node:http';
import http from 'node:http';
import https from 'node:https';
import { promisify } from 'node:util';
import type { Express } from 'express';
import mkcert from 'mkcert';
import logger from './lib/logger';
import 'dotenv/config';

// Default ports configuration
const DEFAULT_PRIMARY_PORT = 5000;
const DEFAULT_FALLBACK_PORTS = [3000, 5001, 8080, 4000];

const _PORT = process.env.PORT ? Number.parseInt(process.env.PORT, 10) : 5000;

/**
 * Starts the application server, attempting to listen on the primary port
 * and falling back to other ports if the primary is in use.
 *
 * @param app - The Express application instance.
 * @param port - The port to attempt to listen on.
 * @param retryIndex - The current index in the fallbackPorts array.
 * @param fallbackPorts - An array of ports to try if the initial port is in use.
 * @returns A promise that resolves with the active server instance.
 */
async function startServer(
  app: Express,
  port: number = DEFAULT_PRIMARY_PORT,
  retryIndex = 0,
  fallbackPorts: number[] = DEFAULT_FALLBACK_PORTS
): Promise<Server> {
  try {
    logger.info(`[STARTUP] Attempting to start server on port ${port}...`);

    let server: http.Server | https.Server;

    if (process.env.NODE_ENV === 'development') {
      const ca = await mkcert.createCA({
        organization: 'InboxZeroAI Dev CA',
        countryCode: 'US',
        state: 'California',
        locality: 'San Francisco',
        validity: 365,
      });
      const cert = await mkcert.createCert({
        domains: ['127.0.0.1', 'localhost'],
        validity: 365,
        ca,
      });
      server = https.createServer({ key: cert.key, cert: cert.cert }, app);
      logger.info('[STARTUP] Configured HTTPS for development.');
    } else {
      server = http.createServer(app);
      logger.info('[STARTUP] Configured HTTP for production.');
    }

    return new Promise<Server>((resolve, reject) => {
      const handleServerError = (err: Error & { code?: string }) => {
        if (err.code === 'EADDRINUSE') {
          logger.warn(`[ERROR] Port ${port} is already in use.`);

          if (retryIndex < fallbackPorts.length) {
            logger.info(`[RETRY] Trying fallback port ${fallbackPorts[retryIndex]}...`);
            startServer(app, fallbackPorts[retryIndex], retryIndex + 1, fallbackPorts)
              .then(resolve)
              .catch(reject);
          } else {
            logger.error(
              '[ERROR] All available ports are in use. Please free up a port and restart the server.'
            );
            reject(new Error('All ports are in use.'));
          }
        } else {
          logger.error('[FATAL] Server error:', err);
          reject(err);
        }
      };

      server.listen(port, '0.0.0.0', () => {
        const protocol = process.env.NODE_ENV === 'development' ? 'https' : 'http';
        logger.info('========================================');
        logger.info(`🚀 Server successfully started at ${protocol}://localhost:${port}`);
        logger.info('========================================');
        resolve(server as Server);
      });

      server.on('error', handleServerError);
    });
  } catch (err) {
    logger.error(`[ERROR] Failed to start server on port ${port}:`, err);
    throw err;
  }
}

export default startServer;
