<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Email Selection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .email-list {
            width: 40%;
            float: left;
            border-right: 1px solid #ddd;
            padding-right: 20px;
        }
        .email-detail {
            width: 55%;
            float: right;
            padding-left: 20px;
        }
        .email-item {
            padding: 15px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        .email-item:hover {
            background-color: #f8f9fa;
        }
        .email-item.selected {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }
        .email-subject {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .email-sender {
            color: #666;
            font-size: 14px;
        }
        .detail-content {
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 300px;
        }
        .no-selection {
            text-align: center;
            color: #999;
            padding: 50px;
        }
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Email Selection Test</h1>
        <div class="status" id="status">Testing email selection functionality...</div>
        
        <div class="clearfix">
            <div class="email-list">
                <h3>Email List</h3>
                <div id="email-list-container">
                    <!-- Email items will be populated here -->
                </div>
            </div>
            
            <div class="email-detail">
                <h3>Email Detail</h3>
                <div class="detail-content" id="detail-content">
                    <div class="no-selection">
                        Click on an email from the list to view its details
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample email data
        const sampleEmails = [
            {
                id: 1,
                messageId: 'msg1',
                subject: 'Quarterly Report Due',
                sender: 'John Manager',
                senderEmail: '<EMAIL>',
                snippet: 'Please submit your quarterly report by Friday.',
                content: 'Hi there,\n\nThis is a reminder that your quarterly report is due this Friday. Please ensure all sections are completed with the latest data.\n\nBest regards,\nJohn'
            },
            {
                id: 2,
                messageId: 'msg2',
                subject: 'Team Meeting Tomorrow',
                sender: 'Sarah Team Lead',
                senderEmail: '<EMAIL>',
                snippet: 'Don\'t forget about our team meeting tomorrow at 2 PM.',
                content: 'Hello team,\n\nJust a quick reminder about our team meeting tomorrow at 2 PM in the conference room. We\'ll be discussing the new project timeline.\n\nSee you there!\nSarah'
            },
            {
                id: 3,
                messageId: 'msg3',
                subject: 'Welcome to the Platform',
                sender: 'Platform Support',
                senderEmail: '<EMAIL>',
                snippet: 'Welcome! Here\'s how to get started.',
                content: 'Welcome to our platform!\n\nWe\'re excited to have you on board. Here are some quick steps to get you started:\n\n1. Complete your profile\n2. Explore the dashboard\n3. Connect your accounts\n\nIf you have any questions, don\'t hesitate to reach out.\n\nBest,\nSupport Team'
            }
        ];

        let selectedMessageId = null;

        function renderEmailList() {
            const container = document.getElementById('email-list-container');
            container.innerHTML = '';

            sampleEmails.forEach(email => {
                const emailItem = document.createElement('div');
                emailItem.className = 'email-item';
                emailItem.dataset.messageId = email.messageId;
                
                if (selectedMessageId === email.messageId) {
                    emailItem.classList.add('selected');
                }

                emailItem.innerHTML = `
                    <div class="email-subject">${email.subject}</div>
                    <div class="email-sender">${email.sender}</div>
                    <div style="font-size: 12px; color: #888; margin-top: 5px;">${email.snippet}</div>
                `;

                emailItem.addEventListener('click', () => selectEmail(email.messageId));
                container.appendChild(emailItem);
            });
        }

        function selectEmail(messageId) {
            console.log('Selecting email:', messageId);
            
            // Update selected email ID
            selectedMessageId = messageId;
            
            // Update visual selection in list
            renderEmailList();
            
            // Find the selected email
            const email = sampleEmails.find(e => e.messageId === messageId);
            
            if (email) {
                // Update detail pane
                const detailContent = document.getElementById('detail-content');
                detailContent.innerHTML = `
                    <h4>${email.subject}</h4>
                    <p><strong>From:</strong> ${email.sender} &lt;${email.senderEmail}&gt;</p>
                    <p><strong>Message ID:</strong> ${email.messageId}</p>
                    <hr>
                    <div style="white-space: pre-wrap; line-height: 1.5;">${email.content}</div>
                `;
                
                // Update status
                document.getElementById('status').textContent = `✅ Selected email: "${email.subject}"`;
                document.getElementById('status').style.backgroundColor = '#d4edda';
                document.getElementById('status').style.color = '#155724';
                document.getElementById('status').style.borderColor = '#c3e6cb';
            } else {
                // Clear detail pane
                document.getElementById('detail-content').innerHTML = `
                    <div class="no-selection">Email not found</div>
                `;
                
                // Update status
                document.getElementById('status').textContent = `❌ Email not found: ${messageId}`;
                document.getElementById('status').style.backgroundColor = '#f8d7da';
                document.getElementById('status').style.color = '#721c24';
                document.getElementById('status').style.borderColor = '#f5c6cb';
            }
        }

        // Initialize the page
        function init() {
            console.log('Initializing email selection test...');
            renderEmailList();
            document.getElementById('status').textContent = '✅ Email selection test ready - click on any email to test selection';
            document.getElementById('status').style.backgroundColor = '#d4edda';
            document.getElementById('status').style.color = '#155724';
            document.getElementById('status').style.borderColor = '#c3e6cb';
        }

        // Start the test when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
