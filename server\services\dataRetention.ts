/**
 * Data Retention Service
 * 
 * Handles automatic data expiration, cleanup, and privacy compliance.
 * Provides functionality for GDPR compliance and user data management.
 */

import { and, eq, isNotNull, lte, sql } from 'drizzle-orm';
import { emails, settings, type Email, type Settings } from '../../shared/schema';
import { getDb } from '../db';
import logger from '../lib/logger';
import { storage } from '../storage';
import { encrypt, decrypt, isEncrypted } from '../utils/encryption';

export interface DataRetentionPolicy {
  emailContent: {
    maxAge: number; // days
    autoDelete: boolean;
  };
  emailMetadata: {
    maxAge: number; // days
    autoDelete: boolean;
  };
  aiSummaries: {
    maxAge: number; // days
    autoDelete: boolean;
  };
  userPreferences: {
    respectUserSettings: boolean;
    allowUserControl: boolean;
  };
}

export interface DataRetentionStats {
  emailsProcessed: number;
  contentExpired: number;
  emailsDeleted: number;
  encryptionApplied: number;
  errors: number;
  processingTimeMs: number;
}

export class DataRetentionService {
  private static readonly DEFAULT_RETENTION_DAYS = 30;
  private static readonly CONTENT_ONLY_RETENTION_DAYS = 7; // Keep content for shorter time
  private static readonly METADATA_RETENTION_DAYS = 90; // Keep metadata longer

  /**
   * Schedule content expiration for an email based on user settings
   */
  async scheduleContentExpiration(emailId: number, userSettings?: Settings): Promise<void> {
    try {
      const retentionDays = userSettings?.dataRetentionDays || DataRetentionService.DEFAULT_RETENTION_DAYS;
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + retentionDays);

      await storage.updateEmail(emailId, {
        contentExpiresAt: expirationDate,
        retentionDays: retentionDays,
      });

      logger.info('Scheduled content expiration for email', {
        emailId,
        expirationDate: expirationDate.toISOString(),
        retentionDays,
      });
    } catch (error) {
      logger.error('Failed to schedule content expiration', {
        emailId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Encrypt sensitive email content
   */
  async encryptEmailContent(email: Email): Promise<Email> {
    try {
      if (email.isContentEncrypted) {
        return email; // Already encrypted
      }

      const updates: Partial<Email> = {
        isContentEncrypted: true,
      };

      // Encrypt original content if it exists
      if (email.originalContent && !isEncrypted(email.originalContent)) {
        updates.originalContent = encrypt(email.originalContent);
      }

      // Encrypt HTML content if it exists
      if (email.htmlContent && !isEncrypted(email.htmlContent)) {
        updates.htmlContent = encrypt(email.htmlContent);
      }

      // Encrypt AI reply if it exists
      if (email.aiReply && !isEncrypted(email.aiReply)) {
        updates.aiReply = encrypt(email.aiReply);
      }

      // Encrypt summary if it exists
      if (email.summary && !isEncrypted(email.summary)) {
        updates.summary = encrypt(email.summary);
      }

      const updatedEmail = await storage.updateEmail(email.id, updates);
      
      logger.info('Encrypted email content', {
        emailId: email.id,
        fieldsEncrypted: Object.keys(updates).filter(key => key !== 'isContentEncrypted'),
      });

      return updatedEmail || email;
    } catch (error) {
      logger.error('Failed to encrypt email content', {
        emailId: email.id,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Decrypt email content for display
   */
  async decryptEmailContent(email: Email): Promise<Email> {
    try {
      if (!email.isContentEncrypted) {
        return email; // Not encrypted
      }

      const decryptedEmail = { ...email };

      // Decrypt original content
      if (email.originalContent && isEncrypted(email.originalContent)) {
        decryptedEmail.originalContent = decrypt(email.originalContent);
      }

      // Decrypt HTML content
      if (email.htmlContent && isEncrypted(email.htmlContent)) {
        decryptedEmail.htmlContent = decrypt(email.htmlContent);
      }

      // Decrypt AI reply
      if (email.aiReply && isEncrypted(email.aiReply)) {
        decryptedEmail.aiReply = decrypt(email.aiReply);
      }

      // Decrypt summary
      if (email.summary && isEncrypted(email.summary)) {
        decryptedEmail.summary = decrypt(email.summary);
      }

      return decryptedEmail;
    } catch (error) {
      logger.error('Failed to decrypt email content', {
        emailId: email.id,
        error: (error as Error).message,
      });
      // Return original email if decryption fails
      return email;
    }
  }

  /**
   * Clean up expired email content
   */
  async cleanupExpiredContent(): Promise<DataRetentionStats> {
    const startTime = Date.now();
    const stats: DataRetentionStats = {
      emailsProcessed: 0,
      contentExpired: 0,
      emailsDeleted: 0,
      encryptionApplied: 0,
      errors: 0,
      processingTimeMs: 0,
    };

    try {
      const db = await getDb();
      const now = new Date();

      logger.info('Starting cleanup of expired email content');

      // Find emails with expired content
      const expiredEmails = await db
        .select()
        .from(emails)
        .where(
          and(
            isNotNull(emails.contentExpiresAt),
            lte(emails.contentExpiresAt, now)
          )
        );

      logger.info(`Found ${expiredEmails.length} emails with expired content`);

      for (const email of expiredEmails) {
        try {
          stats.emailsProcessed++;

          // Clear sensitive content but keep metadata
          await storage.updateEmail(email.id, {
            originalContent: null,
            htmlContent: null,
            aiReply: null,
            isContentEncrypted: false,
            contentExpiresAt: null,
          });

          stats.contentExpired++;

          logger.debug('Expired email content', {
            emailId: email.id,
            originalExpiration: email.contentExpiresAt,
          });

        } catch (error) {
          stats.errors++;
          logger.error('Failed to expire email content', {
            emailId: email.id,
            error: (error as Error).message,
          });
        }
      }

      // Find emails that should be completely deleted (older than metadata retention)
      const metadataExpirationDate = new Date();
      metadataExpirationDate.setDate(metadataExpirationDate.getDate() - DataRetentionService.METADATA_RETENTION_DAYS);

      const emailsToDelete = await db
        .select()
        .from(emails)
        .where(
          and(
            lte(emails.receivedAt, metadataExpirationDate),
            eq(emails.isTrashed, true) // Only delete emails that are already in trash
          )
        );

      logger.info(`Found ${emailsToDelete.length} emails to completely delete`);

      for (const email of emailsToDelete) {
        try {
          await db.delete(emails).where(eq(emails.id, email.id));
          stats.emailsDeleted++;

          logger.debug('Deleted email completely', {
            emailId: email.id,
            receivedAt: email.receivedAt,
          });

        } catch (error) {
          stats.errors++;
          logger.error('Failed to delete email', {
            emailId: email.id,
            error: (error as Error).message,
          });
        }
      }

      stats.processingTimeMs = Date.now() - startTime;

      logger.info('Completed cleanup of expired content', {
        stats,
      });

      return stats;

    } catch (error) {
      stats.processingTimeMs = Date.now() - startTime;
      stats.errors++;
      
      logger.error('Failed to cleanup expired content', {
        error: (error as Error).message,
        stats,
      });

      throw error;
    }
  }

  /**
   * Apply encryption to existing unencrypted emails based on user settings
   */
  async applyEncryptionToExistingEmails(userId: number): Promise<DataRetentionStats> {
    const startTime = Date.now();
    const stats: DataRetentionStats = {
      emailsProcessed: 0,
      contentExpired: 0,
      emailsDeleted: 0,
      encryptionApplied: 0,
      errors: 0,
      processingTimeMs: 0,
    };

    try {
      // Get user settings
      const userSettings = await storage.getSettings(userId);
      if (!userSettings?.encryptSensitiveData) {
        logger.info('User has disabled encryption, skipping', { userId });
        return stats;
      }

      const db = await getDb();

      // Find unencrypted emails for this user
      const unencryptedEmails = await db
        .select()
        .from(emails)
        .where(
          and(
            eq(emails.userId, userId),
            eq(emails.isContentEncrypted, false),
            isNotNull(emails.originalContent) // Only emails with content
          )
        )
        .limit(100); // Process in batches

      logger.info(`Found ${unencryptedEmails.length} unencrypted emails for user ${userId}`);

      for (const email of unencryptedEmails) {
        try {
          stats.emailsProcessed++;
          await this.encryptEmailContent(email);
          stats.encryptionApplied++;

        } catch (error) {
          stats.errors++;
          logger.error('Failed to encrypt email', {
            emailId: email.id,
            userId,
            error: (error as Error).message,
          });
        }
      }

      stats.processingTimeMs = Date.now() - startTime;

      logger.info('Completed encryption application', {
        userId,
        stats,
      });

      return stats;

    } catch (error) {
      stats.processingTimeMs = Date.now() - startTime;
      stats.errors++;
      
      logger.error('Failed to apply encryption to existing emails', {
        userId,
        error: (error as Error).message,
        stats,
      });

      throw error;
    }
  }

  /**
   * Purge all user data (for GDPR compliance)
   */
  async purgeUserData(userId: number, includeMetadata = false): Promise<void> {
    try {
      logger.info('Starting user data purge', {
        userId,
        includeMetadata,
      });

      const db = await getDb();

      if (includeMetadata) {
        // Complete deletion - remove all emails
        await db.delete(emails).where(eq(emails.userId, userId));
        
        // Remove user settings
        await db.delete(settings).where(eq(settings.userId, userId));
        
        logger.info('Completed full user data purge', { userId });
      } else {
        // Partial deletion - clear sensitive content only
        await db
          .update(emails)
          .set({
            originalContent: null,
            htmlContent: null,
            aiReply: null,
            summary: null,
            isContentEncrypted: false,
            contentExpiresAt: null,
          })
          .where(eq(emails.userId, userId));

        logger.info('Completed partial user data purge (content only)', { userId });
      }

    } catch (error) {
      logger.error('Failed to purge user data', {
        userId,
        includeMetadata,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Update last accessed timestamp for an email
   */
  async updateLastAccessed(emailId: number): Promise<void> {
    try {
      await storage.updateEmail(emailId, {
        lastAccessed: new Date(),
      });
    } catch (error) {
      logger.error('Failed to update last accessed timestamp', {
        emailId,
        error: (error as Error).message,
      });
      // Don't throw - this is not critical
    }
  }

  /**
   * Get data retention statistics for a user
   */
  async getUserDataStats(userId: number): Promise<{
    totalEmails: number;
    encryptedEmails: number;
    emailsWithContent: number;
    emailsWithExpiration: number;
    oldestEmail: Date | null;
    newestEmail: Date | null;
  }> {
    try {
      const db = await getDb();

      const userEmails = await db
        .select({
          id: emails.id,
          isContentEncrypted: emails.isContentEncrypted,
          hasContent: sql<boolean>`CASE WHEN original_content IS NOT NULL THEN true ELSE false END`,
          hasExpiration: sql<boolean>`CASE WHEN content_expires_at IS NOT NULL THEN true ELSE false END`,
          receivedAt: emails.receivedAt,
        })
        .from(emails)
        .where(eq(emails.userId, userId));

      const totalEmails = userEmails.length;
      const encryptedEmails = userEmails.filter(e => e.isContentEncrypted).length;
      const emailsWithContent = userEmails.filter(e => e.hasContent).length;
      const emailsWithExpiration = userEmails.filter(e => e.hasExpiration).length;

      const dates = userEmails
        .map(e => e.receivedAt)
        .filter(date => date !== null)
        .map(date => new Date(date!));

      const oldestEmail = dates.length > 0 ? new Date(Math.min(...dates.map(d => d.getTime()))) : null;
      const newestEmail = dates.length > 0 ? new Date(Math.max(...dates.map(d => d.getTime()))) : null;

      return {
        totalEmails,
        encryptedEmails,
        emailsWithContent,
        emailsWithExpiration,
        oldestEmail,
        newestEmail,
      };

    } catch (error) {
      logger.error('Failed to get user data stats', {
        userId,
        error: (error as Error).message,
      });
      throw error;
    }
  }
}

// Export singleton instance
export const dataRetentionService = new DataRetentionService(); 