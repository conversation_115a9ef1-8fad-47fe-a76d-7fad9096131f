/**
 * Gmail OAuth Error Handling Pages
 *
 * This file contains templates for rendering error and success pages
 * related to Gmail OAuth flow.
 */

/**
 * Generate an HTML page for Gmail authorization errors
 *
 * @param {string} error - The error code from Google OAuth
 * @param {string} errorDescription - Optional detailed error description
 * @returns {string} HTML content for the error page
 */
export function getGmailErrorPage(error: string, errorDescription?: string): string {
  // Check if this is an "access_denied" error, which means the user cancelled the auth
  const isCancelled = error === 'access_denied';
  const _messageType = isCancelled ? 'gmail-connection-cancelled' : 'gmail-connection-failed';

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>${isCancelled ? 'Connection Cancelled' : 'Gmail Connection Failed'}</title>
        <style>
          body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f9fafb;
          }
          .container {
            text-align: center;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            background-color: white;
            max-width: 28rem;
          }
          .title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #111827;
          }
          .message {
            color: #6b7280;
            margin-bottom: 1.5rem;
          }
          .error {
            background-color: #fee2e2;
            border-radius: 0.25rem;
            padding: 0.75rem;
            margin-bottom: 1.5rem;
            color: #b91c1c;
            font-size: 0.875rem;
          }
          .icon {
            width: 4rem;
            height: 4rem;
            margin-bottom: 1rem;
            color: ${isCancelled ? '#6b7280' : '#ef4444'};
          }
          .btn {
            background-color: #3b82f6;
            color: white;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            border: none;
            cursor: pointer;
          }
          .btn:hover {
            background-color: #2563eb;
          }
          .countdown {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 1rem;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            ${
              isCancelled
                ? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />'
                : '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />'
            }
          </svg>
          <h1 class="title">${isCancelled ? 'Connection Cancelled' : 'Authentication Failed'}</h1>
          <p class="message">
            ${
              isCancelled
                ? 'You cancelled the Gmail connection process. No changes were made to your account.'
                : "We couldn't connect your Gmail account because an error occurred during authentication."
            }
          </p>
          ${!isCancelled && errorDescription ? `<div class="error">${error}: ${errorDescription}</div>` : ''}
          <button class="btn" id="returnButton">Return to App</button>
          <p class="countdown">You will be redirected in <span id="seconds">10</span> seconds...</p>
        </div>
        <script>
          // Look for return URL in localStorage (direct navigation approach)
          const redirectUrl = localStorage.getItem('oauth_redirect_url') || '/';
          
          // Mark authentication based on result
          if (${isCancelled}) {
            localStorage.setItem('oauth_cancelled', 'true');
            localStorage.setItem('oauth_completion_time', Date.now().toString());
          } else {
            localStorage.setItem('oauth_failure', 'true');
            localStorage.setItem('oauth_completion_time', Date.now().toString());
            localStorage.setItem('oauth_error_message', '${
              !isCancelled && errorDescription
                ? `${error}: ${errorDescription}`.replace(/'/g, "\\'")
                : 'Authentication failed'
            }');
          }
          
          // Auto redirect
          document.getElementById('returnButton').addEventListener('click', function() {
            window.location.href = redirectUrl;
          });
          
          // Countdown and redirect
          let seconds = 10;
          const interval = setInterval(() => {
            seconds--;
            document.getElementById('seconds').textContent = seconds.toString();
            
            if (seconds <= 0) {
              clearInterval(interval);
              window.location.href = redirectUrl;
            }
          }, 1000);
          
          // Fallback redirect
          setTimeout(() => {
            window.location.href = redirectUrl;
          }, 10500);
        </script>
      </body>
    </html>
  `;
}

/**
 * Generate an HTML page for successful Gmail authorization
 *
 * @param {string} email - The email address that was connected
 * @returns {string} HTML content for the success page
 */
export function getGmailSuccessPage(email: string): string {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Gmail Connected</title>
        <style>
          body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f9fafb;
          }
          .container {
            text-align: center;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            background-color: white;
            max-width: 28rem;
          }
          .title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #111827;
          }
          .message {
            color: #6b7280;
            margin-bottom: 1.5rem;
          }
          .icon {
            width: 4rem;
            height: 4rem;
            margin-bottom: 1rem;
            color: #10b981;
          }
          .email {
            font-weight: 500;
            color: #4b5563;
            margin-bottom: 1.5rem;
            padding: 0.5rem;
            background-color: #f3f4f6;
            border-radius: 0.25rem;
            display: inline-block;
          }
          .btn {
            background-color: #3b82f6;
            color: white;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            border: none;
            cursor: pointer;
          }
          .btn:hover {
            background-color: #2563eb;
          }
          .countdown {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 1rem;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          <h1 class="title">Gmail Connected Successfully</h1>
          <p class="message">Your Gmail account has been successfully connected to Inbox Zero.</p>
          <div class="email">${email}</div>
          <button class="btn" id="returnButton">Return to App</button>
          <p class="countdown">You will be redirected in <span id="seconds">5</span> seconds...</p>
        </div>
        <script>
          // Look for return URL in localStorage (direct navigation approach)
          const redirectUrl = localStorage.getItem('oauth_redirect_url') || '/';
          
          // Mark authentication as successful
          localStorage.setItem('oauth_success', 'true');
          localStorage.setItem('oauth_completion_time', Date.now().toString());
          
          // Auto redirect
          document.getElementById('returnButton').addEventListener('click', function() {
            window.location.href = redirectUrl;
          });
          
          // Countdown and redirect
          let seconds = 5;
          const interval = setInterval(() => {
            seconds--;
            document.getElementById('seconds').textContent = seconds.toString();
            
            if (seconds <= 0) {
              clearInterval(interval);
              window.location.href = redirectUrl;
            }
          }, 1000);
          
          // Fallback redirect
          setTimeout(() => {
            window.location.href = redirectUrl;
          }, 5500);
        </script>
      </body>
    </html>
  `;
}

/**
 * Generate an HTML page for server errors during Gmail authorization
 *
 * @param {Error} error - The error that occurred
 * @returns {string} HTML content for the error page
 */
export function getGmailServerErrorPage(error: Error): string {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Gmail Connection Error</title>
        <style>
          body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f9fafb;
          }
          .container {
            text-align: center;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            background-color: white;
            max-width: 32rem;
          }
          .title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #111827;
          }
          .message {
            color: #6b7280;
            margin-bottom: 1.5rem;
          }
          .error {
            background-color: #fee2e2;
            color: #b91c1c;
            padding: 1rem;
            border-radius: 0.25rem;
            margin-bottom: 1.5rem;
            font-family: monospace;
            font-size: 0.875rem;
            text-align: left;
            overflow-x: auto;
          }
          .icon {
            width: 4rem;
            height: 4rem;
            margin-bottom: 1rem;
            color: #ef4444;
          }
          .btn {
            background-color: #3b82f6;
            color: white;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            border: none;
            cursor: pointer;
          }
          .btn:hover {
            background-color: #2563eb;
          }
          .countdown {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 1rem;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h1 class="title">Gmail Connection Failed</h1>
          <p class="message">There was an error connecting your Gmail account. This could be due to a temporary issue or incorrect configuration.</p>
          <div class="error">${error.message}</div>
          <button class="btn" id="returnButton">Return to App</button>
          <p class="countdown">You will be redirected in <span id="seconds">10</span> seconds...</p>
        </div>
        <script>
          // Look for return URL in localStorage (direct navigation approach)
          const redirectUrl = localStorage.getItem('oauth_redirect_url') || '/';
          
          // Mark authentication as failed
          localStorage.setItem('oauth_failure', 'true');
          localStorage.setItem('oauth_completion_time', Date.now().toString());
          localStorage.setItem('oauth_error_message', 'Server error: ${error.message.replace(/'/g, "\\'")}');
          
          // Auto redirect
          document.getElementById('returnButton').addEventListener('click', function() {
            window.location.href = redirectUrl;
          });
          
          // Countdown and redirect
          let seconds = 10;
          const interval = setInterval(() => {
            seconds--;
            document.getElementById('seconds').textContent = seconds.toString();
            
            if (seconds <= 0) {
              clearInterval(interval);
              window.location.href = redirectUrl;
            }
          }, 1000);
          
          // Fallback redirect
          setTimeout(() => {
            window.location.href = redirectUrl;
          }, 10500);
        </script>
      </body>
    </html>
  `;
}

/**
 * Generate an HTML page for API errors during Gmail authorization flow
 *
 * @param {string} errorMessage - The error message to display
 * @param {string} errorCode - Optional error code or status code
 * @returns {string} HTML content for the error page
 */
export function getGmailApiErrorPage(errorMessage: string, errorCode?: string): string {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Gmail API Error</title>
        <style>
          body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f9fafb;
          }
          .container {
            text-align: center;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            background-color: white;
            max-width: 32rem;
          }
          .title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #111827;
          }
          .message {
            color: #6b7280;
            margin-bottom: 1.5rem;
          }
          .error {
            background-color: #fee2e2;
            color: #b91c1c;
            padding: 1rem;
            border-radius: 0.25rem;
            margin-bottom: 1.5rem;
            font-family: monospace;
            font-size: 0.875rem;
            text-align: left;
            overflow-x: auto;
          }
          .icon {
            width: 4rem;
            height: 4rem;
            margin-bottom: 1rem;
            color: #ef4444;
          }
          .btn {
            background-color: #3b82f6;
            color: white;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            border: none;
            cursor: pointer;
          }
          .btn:hover {
            background-color: #2563eb;
          }
          .countdown {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 1rem;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h1 class="title">Gmail Connection Error</h1>
          <p class="message">There was an error connecting to Gmail API. Please try again later.</p>
          <div class="error">${errorCode ? `${errorCode}: ` : ''}${errorMessage}</div>
          <button class="btn" id="returnButton">Return to App</button>
          <p class="countdown">You will be redirected in <span id="seconds">10</span> seconds...</p>
        </div>
        <script>
          // Look for return URL in localStorage (direct navigation approach)
          const redirectUrl = localStorage.getItem('oauth_redirect_url') || '/';
          
          // Mark authentication as failed
          localStorage.setItem('oauth_failure', 'true');
          localStorage.setItem('oauth_completion_time', Date.now().toString());
          localStorage.setItem('oauth_error_message', 'API error: ${errorCode ? `${errorCode}: ` : ''}${errorMessage.replace(/'/g, "\\'")}');
          
          // Auto redirect
          document.getElementById('returnButton').addEventListener('click', function() {
            window.location.href = redirectUrl;
          });
          
          // Countdown and redirect
          let seconds = 10;
          const interval = setInterval(() => {
            seconds--;
            document.getElementById('seconds').textContent = seconds.toString();
            
            if (seconds <= 0) {
              clearInterval(interval);
              window.location.href = redirectUrl;
            }
          }, 1000);
          
          // Fallback redirect
          setTimeout(() => {
            window.location.href = redirectUrl;
          }, 10500);
        </script>
      </body>
    </html>
  `;
}
