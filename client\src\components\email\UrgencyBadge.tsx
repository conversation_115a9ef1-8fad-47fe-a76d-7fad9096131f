import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export type UrgencyBadgeProps = {
  priority: string;
  isUrgent?: boolean; // Add isUrgent prop to support the category-based urgency
  className?: string;
};

export function UrgencyBadge({ priority, isUrgent = false, className = '' }: UrgencyBadgeProps) {
  // Map priorities to human-readable labels
  const priorityLabels: Record<string, string> = {
    high: 'Urgent',
    medium: 'Important',
    low: 'Normal',
    none: 'Low Priority',
  };

  // Use isUrgent prop to override priority if email is categorized as urgent
  const effectivePriority = isUrgent ? 'high' : priority;
  const label = priorityLabels[effectivePriority] || 'Unknown';

  // Only render if we have a priority or it's explicitly marked as urgent
  if (!effectivePriority && !isUrgent) {
    return null;
  }

  // Return the correct CSS class based on priority
  const getPriorityClass = () => {
    switch (effectivePriority) {
      case 'high':
        return 'badge-priority-high';
      case 'medium':
        return 'badge-priority-medium';
      case 'low':
        return 'badge-priority-low';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800/50 dark:text-gray-300 border-gray-200 dark:border-gray-700/30';
    }
  };

  return (
    <Badge
      variant="outline"
      className={cn(
        'text-xs font-medium rounded-md px-2 py-0.5 border',
        getPriorityClass(),
        className
      )}
    >
      {label}
    </Badge>
  );
}

export default UrgencyBadge;
