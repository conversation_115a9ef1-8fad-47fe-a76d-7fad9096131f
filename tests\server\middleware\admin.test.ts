/**
 * Middleware: isAdmin
 *
 * Test Cases
 * 1. Admin user → next() is called (HTTP 200 OK path)
 * 2. Non-admin user → 403 Forbidden returned, next() not called
 * 3. Missing user → 401 Unauthorized returned, next() not called
 */

import type { Request, Response, NextFunction } from 'express';
import { isAdmin } from '@server/middleware/admin';

// The admin middleware relies on a populated `req.user`.
// No need to mock storage — we directly craft the request object.

// Ensure the server logger is stubbed (handled globally in jestSetup),
// but we add an explicit mock here for clarity.
jest.mock('@server/lib/logger', () => ({
  __esModule: true,
  default: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

type Mutable<T> = { -readonly [P in keyof T]: T[P] };

function createMockResponse() {
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
  } as unknown as Mutable<Response>;
  return res;
}

describe('admin middleware (isAdmin)', () => {
  let next: jest.MockedFunction<NextFunction>;
  beforeEach(() => {
    next = jest.fn();
  });

  it('should allow access for admin users', async () => {
    const req = {
      user: { id: 1, role: 'admin', email: '<EMAIL>' },
    } as unknown as Mutable<Request>;

    const res = createMockResponse();

    await isAdmin(req, res, next);

    expect(res.status).not.toHaveBeenCalled();
    expect(next).toHaveBeenCalledTimes(1);
  });

  it('should reject non-admin users with 403', async () => {
    const req = {
      user: { id: 2, role: 'user', email: '<EMAIL>' },
    } as unknown as Mutable<Request>;

    const res = createMockResponse();

    await isAdmin(req, res, next);

    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith(
      expect.objectContaining({ message: 'Admin access required' })
    );
    expect(next).not.toHaveBeenCalled();
  });

  it('should reject requests with no authenticated user with 401', async () => {
    const req = {} as unknown as Mutable<Request>;
    const res = createMockResponse();

    await isAdmin(req, res, next);

    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith(
      expect.objectContaining({ message: 'Authentication required' })
    );
    expect(next).not.toHaveBeenCalled();
  });
}); 