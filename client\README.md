# InboxZeroAI Client

This is the React frontend for the InboxZeroAI application.

## Development Setup

### Prerequisites
- Node.js 18+
- npm 8+

### Install Dependencies
```bash
npm install
```

### Development Server
```bash
npm run dev:frontend
```

## CSS & Styling

This project uses **Tailwind CSS** for styling with custom CSS for complex layouts.

### Tailwind CSS Setup
- Configuration: `tailwind.config.ts`
- Main styles: `src/index.css`
- PostCSS config: `postcss.config.js`

### Editor Configuration
The `.vscode/` folder contains:
- `settings.json` - Disables CSS validation errors for Tailwind directives
- `extensions.json` - Recommends Tailwind CSS IntelliSense extension

### Resolving CSS Linting Issues
If you see "Unknown at rule @tailwind" or "@apply" errors in your editor:

1. **Install recommended extensions**: VS Code should prompt you to install the Tailwind CSS IntelliSense extension
2. **Reload VS Code**: After installing extensions, reload the window
3. **Check workspace settings**: The `.vscode/settings.json` file should automatically configure proper validation

### CSS Architecture
The project follows a layered CSS approach:
- **Base layer** (`@layer base`): CSS custom properties, global resets, and theme variables
- **Components layer** (`@layer components`): Reusable component styles
- **Utilities layer** (`@layer utilities`): Tailwind utility classes

#### Important: CSP Compliance
This project implements Content Security Policy (CSP) restrictions. The main CSS file has been refactored to avoid problematic `@apply` directives that use custom CSS variable classes. Instead, we use direct CSS with `hsl(var(--css-variable))` syntax.

### Theme System
The application supports light/dark themes using CSS custom properties:
- Theme variables are defined in `:root` and `.dark` selectors
- Colors use HSL format for better manipulation
- Priority colors are defined for email importance levels

### Mobile Optimization
Extensive mobile-first responsive design with:
- Responsive breakpoints following Tailwind's conventions
- Touch-friendly button sizes (minimum 38px touch targets)
- Optimized typography scaling
- Email content overflow handling

## Architecture

### Component Organization
```
src/components/
├── ui/           # Pure UI primitives (shadcn/ui based)
├── layout/       # App structure components
├── auth/         # Authentication-related components
├── email/        # Email feature components
└── error/        # Error handling components
```

### State Management
- **Global State**: React Contexts (AuthContext, ThemeContext)
- **Server State**: React Query for data fetching and caching
- **API Client**: Unified client at `src/lib/apiClient.ts`

### Styling Guidelines
1. Use Tailwind utility classes for spacing, colors, and layout
2. Create component-specific CSS classes in `index.css` for complex styles
3. Avoid inline styles where possible for CSP compliance
4. Use CSS custom properties for dynamic theming
5. Follow mobile-first responsive design principles

### Performance Considerations
- CSS containment properties for performance isolation
- Lazy loading for routes and heavy components
- Optimized image loading with progressive enhancement
- Minimal CSS bundle size through Tailwind's purging

## Troubleshooting

### Build Errors
If you encounter PostCSS/Tailwind errors:
1. Check that all `@apply` directives use only standard Tailwind classes
2. Ensure custom CSS variable classes are implemented with direct CSS
3. Run `npm run build:client` to test the build process

### Editor Issues
If VS Code shows CSS validation errors:
1. Ensure Tailwind CSS IntelliSense extension is installed
2. Check `.vscode/settings.json` is properly configured
3. Restart VS Code and reload the workspace

### Theme Issues
If theme switching doesn't work:
1. Check CSS custom properties are properly defined
2. Ensure `ThemeContext` is providing the correct theme state
3. Verify theme classes are applied to the document body

## Build

```bash
npm run build:client
```

The build output goes to `../dist/public/` and is served by the Express server. 