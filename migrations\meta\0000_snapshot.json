{"id": "ffa2b4a8-ddc7-4889-8752-93d6fa837649", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.achievements": {"name": "achievements", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "level": {"name": "level", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": true}, "unlocked_at": {"name": "unlocked_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "progress": {"name": "progress", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "max_progress": {"name": "max_progress", "type": "integer", "primaryKey": false, "notNull": true}, "is_complete": {"name": "is_complete", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"achievements_user_id_idx": {"name": "achievements_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "achievements_type_idx": {"name": "achievements_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "achievements_is_complete_idx": {"name": "achievements_is_complete_idx", "columns": [{"expression": "is_complete", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "achievements_unlocked_at_idx": {"name": "achievements_unlocked_at_idx", "columns": [{"expression": "unlocked_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "achievements_user_type_idx": {"name": "achievements_user_type_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "achievements_user_complete_idx": {"name": "achievements_user_complete_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_complete", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "achievements_level_idx": {"name": "achievements_level_idx", "columns": [{"expression": "level", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"achievements_user_id_users_id_fk": {"name": "achievements_user_id_users_id_fk", "tableFrom": "achievements", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.emails": {"name": "emails", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": true}, "thread_id": {"name": "thread_id", "type": "text", "primaryKey": false, "notNull": false}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": false}, "snippet": {"name": "snippet", "type": "text", "primaryKey": false, "notNull": false}, "sender": {"name": "sender", "type": "text", "primaryKey": false, "notNull": false}, "sender_email": {"name": "sender_email", "type": "text", "primaryKey": false, "notNull": false}, "received_at": {"name": "received_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_replied": {"name": "is_replied", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_trashed": {"name": "is_trashed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_important": {"name": "is_important", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "snoozed_until": {"name": "snoozed_until", "type": "timestamp", "primaryKey": false, "notNull": false}, "reply_date": {"name": "reply_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "reply_id": {"name": "reply_id", "type": "text", "primaryKey": false, "notNull": false}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false}, "categories": {"name": "categories", "type": "text[]", "primaryKey": false, "notNull": false, "default": []}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": false}, "ai_reply": {"name": "ai_reply", "type": "text", "primaryKey": false, "notNull": false}, "original_content": {"name": "original_content", "type": "text", "primaryKey": false, "notNull": false}, "html_content": {"name": "html_content", "type": "text", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": false}, "label_ids": {"name": "label_ids", "type": "text[]", "primaryKey": false, "notNull": false, "default": []}, "content_expires_at": {"name": "content_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_accessed": {"name": "last_accessed", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "is_content_encrypted": {"name": "is_content_encrypted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "retention_days": {"name": "retention_days", "type": "integer", "primaryKey": false, "notNull": false, "default": 30}}, "indexes": {"emails_user_id_idx": {"name": "emails_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_message_id_idx": {"name": "emails_message_id_idx", "columns": [{"expression": "message_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_received_at_idx": {"name": "emails_received_at_idx", "columns": [{"expression": "received_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_user_received_idx": {"name": "emails_user_received_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "received_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_user_archived_idx": {"name": "emails_user_archived_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_archived", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_user_read_idx": {"name": "emails_user_read_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_read", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_user_trashed_idx": {"name": "emails_user_trashed_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_trashed", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_user_important_idx": {"name": "emails_user_important_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_important", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_archived_idx": {"name": "emails_archived_idx", "columns": [{"expression": "is_archived", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_read_idx": {"name": "emails_read_idx", "columns": [{"expression": "is_read", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_trashed_idx": {"name": "emails_trashed_idx", "columns": [{"expression": "is_trashed", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_important_idx": {"name": "emails_important_idx", "columns": [{"expression": "is_important", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_thread_id_idx": {"name": "emails_thread_id_idx", "columns": [{"expression": "thread_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_provider_idx": {"name": "emails_provider_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_sender_email_idx": {"name": "emails_sender_email_idx", "columns": [{"expression": "sender_email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_content_expires_idx": {"name": "emails_content_expires_idx", "columns": [{"expression": "content_expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emails_last_accessed_idx": {"name": "emails_last_accessed_idx", "columns": [{"expression": "last_accessed", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.settings": {"name": "settings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "reply_tone": {"name": "reply_tone", "type": "text", "primaryKey": false, "notNull": false, "default": "'professional'"}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": false}, "custom_tone": {"name": "custom_tone", "type": "text", "primaryKey": false, "notNull": false}, "privacy_mode": {"name": "privacy_mode", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "notification_digest": {"name": "notification_digest", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "categories": {"name": "categories", "type": "jsonb", "primaryKey": false, "notNull": false}, "priority_colors": {"name": "priority_colors", "type": "jsonb", "primaryKey": false, "notNull": false}, "theme_mode": {"name": "theme_mode", "type": "text", "primaryKey": false, "notNull": false, "default": "'system'"}, "border_radius": {"name": "border_radius", "type": "integer", "primaryKey": false, "notNull": false, "default": 6}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "data_retention_days": {"name": "data_retention_days", "type": "integer", "primaryKey": false, "notNull": false, "default": 30}, "allow_ai_processing": {"name": "allow_ai_processing", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "store_email_content": {"name": "store_email_content", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "auto_delete_processed_emails": {"name": "auto_delete_processed_emails", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "encrypt_sensitive_data": {"name": "encrypt_sensitive_data", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "consent_version": {"name": "consent_version", "type": "text", "primaryKey": false, "notNull": false, "default": "'1.0'"}, "consent_timestamp": {"name": "consent_timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"settings_user_id_idx": {"name": "settings_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "settings_privacy_mode_idx": {"name": "settings_privacy_mode_idx", "columns": [{"expression": "privacy_mode", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "settings_allow_ai_processing_idx": {"name": "settings_allow_ai_processing_idx", "columns": [{"expression": "allow_ai_processing", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "settings_encrypt_sensitive_data_idx": {"name": "settings_encrypt_sensitive_data_idx", "columns": [{"expression": "encrypt_sensitive_data", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "settings_consent_version_idx": {"name": "settings_consent_version_idx", "columns": [{"expression": "consent_version", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "settings_updated_at_idx": {"name": "settings_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "settings_data_retention_days_idx": {"name": "settings_data_retention_days_idx", "columns": [{"expression": "data_retention_days", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"settings_user_id_users_id_fk": {"name": "settings_user_id_users_id_fk", "tableFrom": "settings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.task_queue": {"name": "task_queue", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "task_type": {"name": "task_type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}, "result": {"name": "result", "type": "jsonb", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "retry_count": {"name": "retry_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "max_retries": {"name": "max_retries", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "last_attempt_at": {"name": "last_attempt_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "scheduled_for": {"name": "scheduled_for", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "locked_by": {"name": "locked_by", "type": "text", "primaryKey": false, "notNull": false}, "locked_at": {"name": "locked_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"task_queue_status_idx": {"name": "task_queue_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_queue_priority_idx": {"name": "task_queue_priority_idx", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_queue_type_idx": {"name": "task_queue_type_idx", "columns": [{"expression": "task_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_queue_scheduled_idx": {"name": "task_queue_scheduled_idx", "columns": [{"expression": "scheduled_for", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "picture": {"name": "picture", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "firebase_uid": {"name": "firebase_uid", "type": "text", "primaryKey": false, "notNull": false}, "gmail_tokens": {"name": "gmail_tokens", "type": "jsonb", "primaryKey": false, "notNull": false}, "emails_processed": {"name": "emails_processed", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "replies_sent": {"name": "replies_sent", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "tier": {"name": "tier", "type": "text", "primaryKey": false, "notNull": false, "default": "'free'"}, "reply_tone": {"name": "reply_tone", "type": "text", "primaryKey": false, "notNull": false, "default": "'professional'"}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_reply_date": {"name": "last_reply_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_attempts": {"name": "refresh_attempts", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "last_token_refresh": {"name": "last_token_refresh", "type": "timestamp", "primaryKey": false, "notNull": false}, "auth_error_count": {"name": "auth_error_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "security_level": {"name": "security_level", "type": "text", "primaryKey": false, "notNull": false, "default": "'standard'"}, "token_invalid": {"name": "token_invalid", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "last_token_error": {"name": "last_token_error", "type": "text", "primaryKey": false, "notNull": false}, "last_api_error": {"name": "last_api_error", "type": "text", "primaryKey": false, "notNull": false}, "last_connection_verified": {"name": "last_connection_verified", "type": "timestamp", "primaryKey": false, "notNull": false}, "token_update_status": {"name": "token_update_status", "type": "text", "primaryKey": false, "notNull": false}, "token_status": {"name": "token_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'unknown'"}, "token_error_count": {"name": "token_error_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "token_error_time": {"name": "token_error_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "token_last_refreshed": {"name": "token_last_refreshed", "type": "timestamp", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "default": "'user'"}}, "indexes": {"users_firebase_uid_idx": {"name": "users_firebase_uid_idx", "columns": [{"expression": "firebase_uid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_provider_idx": {"name": "users_provider_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_token_invalid_idx": {"name": "users_token_invalid_idx", "columns": [{"expression": "token_invalid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_token_status_idx": {"name": "users_token_status_idx", "columns": [{"expression": "token_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_expires_at_idx": {"name": "users_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_last_login_idx": {"name": "users_last_login_idx", "columns": [{"expression": "last_login", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_role_idx": {"name": "users_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_tier_idx": {"name": "users_tier_idx", "columns": [{"expression": "tier", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_provider_token_status_idx": {"name": "users_provider_token_status_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "token_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_token_invalid_provider_idx": {"name": "users_token_invalid_provider_idx", "columns": [{"expression": "token_invalid", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}}, "enums": {}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}