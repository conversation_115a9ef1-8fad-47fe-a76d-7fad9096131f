/**
 * Database Query Performance Monitor
 *
 * This module provides comprehensive query monitoring including:
 * - Query execution time tracking
 * - Slow query detection and alerting
 * - Query pattern analysis
 * - Performance optimization recommendations
 * - Database health metrics
 */

import { performance } from 'node:perf_hooks';
import logger from '../lib/logger';

// Query performance metrics interface
export interface QueryMetrics {
  queryId: string;
  query: string;
  params?: any[];
  executionTime: number;
  timestamp: number;
  userId?: number;
  endpoint?: string;
  rowsAffected?: number;
  rowsReturned?: number;
  cacheHit?: boolean;
  error?: string;
}

// Query statistics interface
export interface QueryStats {
  totalQueries: number;
  avgExecutionTime: number;
  slowQueries: number;
  errorQueries: number;
  topSlowQueries: QueryMetrics[];
  queryPatterns: Record<
    string,
    {
      count: number;
      avgTime: number;
      totalTime: number;
    }
  >;
  performanceScore: number;
}

// Query monitoring configuration
export interface MonitorConfig {
  slowQueryThreshold: number; // ms - queries slower than this are flagged
  maxStoredQueries: number; // Maximum number of queries to store in memory
  enableDetailedLogging: boolean;
  enableAlerts: boolean;
  alertThreshold: number; // Number of slow queries before alerting
  patternDetection: boolean;
}

class QueryMonitor {
  private metrics: QueryMetrics[] = [];
  private config: MonitorConfig;
  private slowQueryCount = 0;
  private lastAlertTime = 0;
  private queryPatterns = new Map<string, { count: number; totalTime: number }>();

  constructor(config: Partial<MonitorConfig> = {}) {
    this.config = {
      slowQueryThreshold: 1000, // 1 second
      maxStoredQueries: 1000,
      enableDetailedLogging: process.env.NODE_ENV === 'development', // Use direct access to avoid circular dependency during startup
      enableAlerts: true,
      alertThreshold: 5,
      patternDetection: true,
      ...config,
    };

    // Start cleanup interval to prevent memory leaks
    setInterval(() => this.cleanup(), 60000); // Cleanup every minute
  }

  /**
   * Monitor a database query execution
   */
  public async monitorQuery<T>(
    queryId: string,
    query: string,
    queryFn: () => Promise<T>,
    context: {
      params?: any[];
      userId?: number;
      endpoint?: string;
      cacheHit?: boolean;
    } = {}
  ): Promise<T> {
    const startTime = performance.now();
    const timestamp = Date.now();
    let result: T;
    let error: string | undefined;
    let rowsAffected: number | undefined;
    let rowsReturned: number | undefined;

    try {
      result = await queryFn();

      // Extract row information if possible
      if (Array.isArray(result)) {
        rowsReturned = result.length;
      } else if (result && typeof result === 'object' && 'rowCount' in result) {
        rowsAffected = (result as any).rowCount;
      }

      return result;
    } catch (err) {
      error = (err as Error).message;
      throw err;
    } finally {
      const executionTime = performance.now() - startTime;

      // Create metrics record
      const metrics: QueryMetrics = {
        queryId,
        query: this.sanitizeQuery(query),
        params: this.sanitizeParams(context.params),
        executionTime,
        timestamp,
        userId: context.userId,
        endpoint: context.endpoint,
        rowsAffected,
        rowsReturned,
        cacheHit: context.cacheHit,
        error,
      };

      // Store metrics
      this.recordMetrics(metrics);

      // Log if needed
      if (this.config.enableDetailedLogging || executionTime > this.config.slowQueryThreshold) {
        this.logQuery(metrics);
      }

      // Check for slow query alerts
      if (executionTime > this.config.slowQueryThreshold) {
        this.handleSlowQuery(metrics);
      }

      // Update query patterns
      if (this.config.patternDetection) {
        this.updateQueryPatterns(queryId, executionTime);
      }
    }
  }

  /**
   * Record query metrics
   */
  private recordMetrics(metrics: QueryMetrics): void {
    this.metrics.push(metrics);

    // Maintain maximum size
    if (this.metrics.length > this.config.maxStoredQueries) {
      this.metrics = this.metrics.slice(-this.config.maxStoredQueries);
    }
  }

  /**
   * Handle slow query detection
   */
  private handleSlowQuery(metrics: QueryMetrics): void {
    this.slowQueryCount++;

    logger.warn('Slow query detected', {
      queryId: metrics.queryId,
      executionTime: metrics.executionTime,
      query: metrics.query,
      userId: metrics.userId,
      endpoint: metrics.endpoint,
    });

    // Send alert if threshold reached
    if (
      this.config.enableAlerts &&
      this.slowQueryCount >= this.config.alertThreshold &&
      Date.now() - this.lastAlertTime > 300000
    ) {
      // 5 minutes between alerts

      this.sendSlowQueryAlert();
      this.lastAlertTime = Date.now();
      this.slowQueryCount = 0; // Reset counter after alert
    }
  }

  /**
   * Send slow query alert
   */
  private sendSlowQueryAlert(): void {
    const recentSlowQueries = this.metrics
      .filter((m) => m.executionTime > this.config.slowQueryThreshold)
      .slice(-5); // Last 5 slow queries

    logger.error('Multiple slow queries detected', {
      slowQueryCount: this.slowQueryCount,
      recentQueries: recentSlowQueries.map((q) => ({
        queryId: q.queryId,
        executionTime: q.executionTime,
        timestamp: q.timestamp,
      })),
    });

    // Here you could integrate with alerting systems like:
    // - Slack notifications
    // - Email alerts
    // - PagerDuty
    // - Custom webhook
  }

  /**
   * Update query pattern statistics
   */
  private updateQueryPatterns(queryId: string, executionTime: number): void {
    const pattern = this.queryPatterns.get(queryId) || {
      count: 0,
      totalTime: 0,
    };
    pattern.count++;
    pattern.totalTime += executionTime;
    this.queryPatterns.set(queryId, pattern);
  }

  /**
   * Log query execution
   */
  private logQuery(metrics: QueryMetrics): void {
    const logLevel = metrics.error
      ? 'error'
      : metrics.executionTime > this.config.slowQueryThreshold
        ? 'warn'
        : 'debug';

    logger[logLevel]('Database query executed', {
      queryId: metrics.queryId,
      executionTime: `${metrics.executionTime.toFixed(2)}ms`,
      query: metrics.query,
      rowsReturned: metrics.rowsReturned,
      rowsAffected: metrics.rowsAffected,
      cacheHit: metrics.cacheHit,
      userId: metrics.userId,
      endpoint: metrics.endpoint,
      error: metrics.error,
    });
  }

  /**
   * Get comprehensive query statistics
   */
  public getStats(timeWindowMs?: number): QueryStats {
    const now = Date.now();
    const windowStart = timeWindowMs ? now - timeWindowMs : 0;

    const relevantMetrics = this.metrics.filter((m) => m.timestamp >= windowStart);

    if (relevantMetrics.length === 0) {
      return {
        totalQueries: 0,
        avgExecutionTime: 0,
        slowQueries: 0,
        errorQueries: 0,
        topSlowQueries: [],
        queryPatterns: {},
        performanceScore: 100,
      };
    }

    const totalQueries = relevantMetrics.length;
    const totalTime = relevantMetrics.reduce((sum, m) => sum + m.executionTime, 0);
    const avgExecutionTime = totalTime / totalQueries;
    const slowQueries = relevantMetrics.filter(
      (m) => m.executionTime > this.config.slowQueryThreshold
    ).length;
    const errorQueries = relevantMetrics.filter((m) => m.error).length;

    // Top 10 slowest queries
    const topSlowQueries = relevantMetrics
      .filter((m) => !m.error)
      .sort((a, b) => b.executionTime - a.executionTime)
      .slice(0, 10);

    // Query patterns
    const queryPatterns: Record<string, { count: number; avgTime: number; totalTime: number }> = {};
    for (const [queryId, _stats] of this.queryPatterns.entries()) {
      const relevantCount = relevantMetrics.filter((m) => m.queryId === queryId).length;
      if (relevantCount > 0) {
        const relevantTime = relevantMetrics
          .filter((m) => m.queryId === queryId)
          .reduce((sum, m) => sum + m.executionTime, 0);

        queryPatterns[queryId] = {
          count: relevantCount,
          avgTime: relevantTime / relevantCount,
          totalTime: relevantTime,
        };
      }
    }

    // Calculate performance score (0-100)
    const slowQueryRatio = slowQueries / totalQueries;
    const errorRatio = errorQueries / totalQueries;
    const avgSpeedScore = Math.max(0, 100 - avgExecutionTime / 10); // 10ms = 99 points
    const performanceScore = Math.max(
      0,
      Math.min(100, avgSpeedScore * (1 - slowQueryRatio * 0.5) * (1 - errorRatio))
    );

    return {
      totalQueries,
      avgExecutionTime,
      slowQueries,
      errorQueries,
      topSlowQueries,
      queryPatterns,
      performanceScore: Math.round(performanceScore),
    };
  }

  /**
   * Get optimization recommendations
   */
  public getOptimizationRecommendations(): string[] {
    const stats = this.getStats();
    const recommendations: string[] = [];

    if (stats.avgExecutionTime > 500) {
      recommendations.push(
        'Average query time is high. Consider adding database indexes or optimizing queries.'
      );
    }

    if (stats.slowQueries > 0) {
      recommendations.push(
        `${stats.slowQueries} slow queries detected. Review and optimize the slowest queries.`
      );
    }

    if (stats.errorQueries > 0) {
      recommendations.push(
        `${stats.errorQueries} query errors detected. Review error logs and fix failing queries.`
      );
    }

    const heavyQueries = Object.entries(stats.queryPatterns)
      .filter(([_, pattern]) => pattern.count > 10 && pattern.avgTime > 200)
      .sort((a, b) => b[1].totalTime - a[1].totalTime);

    if (heavyQueries.length > 0) {
      recommendations.push(
        `Heavy query patterns detected: ${heavyQueries[0][0]}. Consider caching or optimization.`
      );
    }

    if (stats.performanceScore < 70) {
      recommendations.push(
        'Overall database performance is below optimal. Consider comprehensive optimization.'
      );
    }

    return recommendations;
  }

  /**
   * Clear old metrics and patterns
   */
  private cleanup(): void {
    const cutoffTime = Date.now() - 24 * 60 * 60 * 1000; // 24 hours ago

    // Remove old metrics
    this.metrics = this.metrics.filter((m) => m.timestamp > cutoffTime);

    // Clean up query patterns that haven't been used recently
    const activePatterns = new Set(
      this.metrics.filter((m) => m.timestamp > cutoffTime).map((m) => m.queryId)
    );

    for (const [queryId] of this.queryPatterns.entries()) {
      if (!activePatterns.has(queryId)) {
        this.queryPatterns.delete(queryId);
      }
    }

    logger.debug('Query monitor cleanup completed', {
      metricsRetained: this.metrics.length,
      patternsRetained: this.queryPatterns.size,
    });
  }

  /**
   * Sanitize query for logging (remove sensitive data)
   */
  private sanitizeQuery(query: string): string {
    // Remove potential sensitive data patterns
    return query
      .replace(/password\s*=\s*['"][^'"]*['"]/gi, "password='***'")
      .replace(/token\s*=\s*['"][^'"]*['"]/gi, "token='***'")
      .replace(/key\s*=\s*['"][^'"]*['"]/gi, "key='***'")
      .substring(0, 500); // Limit length
  }

  /**
   * Sanitize parameters for logging
   */
  private sanitizeParams(params?: any[]): any[] | undefined {
    if (!params) return undefined;

    return params.map((param) => {
      if (typeof param === 'string') {
        // Hide potential sensitive strings
        if (
          param.length > 50 ||
          /password|token|key|secret/i.test(param) ||
          /^[A-Za-z0-9+/=]{20,}$/.test(param)
        ) {
          return '***';
        }
      }
      return param;
    });
  }

  /**
   * Reset all statistics
   */
  public reset(): void {
    this.metrics = [];
    this.queryPatterns.clear();
    this.slowQueryCount = 0;
    this.lastAlertTime = 0;

    logger.info('Query monitor statistics reset');
  }

  /**
   * Export metrics for external analysis
   */
  public exportMetrics(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = 'queryId,executionTime,timestamp,userId,endpoint,rowsReturned,error';
      const rows = this.metrics.map(
        (m) =>
          `${m.queryId},${m.executionTime},${m.timestamp},${m.userId || ''},${m.endpoint || ''},${m.rowsReturned || ''},${m.error || ''}`
      );
      return [headers, ...rows].join('\n');
    }

    return JSON.stringify(
      {
        stats: this.getStats(),
        metrics: this.metrics,
        exportTime: Date.now(),
      },
      null,
      2
    );
  }
}

// Export singleton instance
export const queryMonitor = new QueryMonitor();

// Helper function for easy query monitoring
export async function monitoredQuery<T>(
  queryId: string,
  query: string,
  queryFn: () => Promise<T>,
  context?: {
    params?: any[];
    userId?: number;
    endpoint?: string;
    cacheHit?: boolean;
  }
): Promise<T> {
  return queryMonitor.monitorQuery(queryId, query, queryFn, context);
}

export default queryMonitor;
