/**
 * Browser Utility Functions
 *
 * This module provides utilities for interacting with web browsers.
 */

import { execSync } from 'node:child_process';
import { existsSync } from 'node:fs';
import { join } from 'node:path';

/**
 * Opens a URL or file in the default browser
 *
 * @param {string} urlOrPath - URL or file path to open
 * @returns {boolean} - true if successful, false otherwise
 */
export function openBrowser(urlOrPath) {
  try {
    // Determine if it's a URL or a file path
    let target = urlOrPath;

    // If it doesn't start with http(s)://, assume it's a local file
    if (!urlOrPath.startsWith('http://') && !urlOrPath.startsWith('https://')) {
      // Convert to absolute path if necessary
      if (!urlOrPath.startsWith('/')) {
        target = join(process.cwd(), urlOrPath);
      }

      // Check if the file exists
      if (!existsSync(target)) {
        throw new Error(`File not found: ${target}`);
      }

      // Convert to file:// URL
      target = `file://${target}`;
    }

    // Open the browser with platform-specific command
    let command;

    switch (process.platform) {
      case 'darwin': // macOS
        command = `open "${target}"`;
        break;
      case 'win32': // Windows
        command = `start "" "${target}"`;
        break;
      default: // Linux and others
        command = `xdg-open "${target}"`;
        break;
    }

    execSync(command);
    return true;
  } catch (error) {
    console.error('Failed to open browser:', error.message);
    return false;
  }
}

/**
 * Checks if a browser is available on the system
 *
 * @returns {boolean} - true if a browser is available, false otherwise
 */
export function isBrowserAvailable() {
  try {
    switch (process.platform) {
      case 'darwin': // macOS
        execSync('which open');
        break;
      case 'win32': // Windows
        execSync('where start');
        break;
      default: // Linux and others
        execSync('which xdg-open');
        break;
    }
    return true;
  } catch (_error) {
    return false;
  }
}
