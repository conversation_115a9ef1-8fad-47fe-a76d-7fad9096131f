import { render, screen } from '@testing-library/react';
import type React from 'react';

// ------------------ mocks -------------------
const mockUseAuth = jest.fn();

jest.mock('@/context/AuthContext', () => ({
  __esModule: true,
  useAuth: () => mockUseAuth(),
}));

// Mock Redirect from wouter so we can inspect output
jest.mock('wouter', () => ({
  Redirect: ({ to }: { to: string }) => <div data-testid="redirect">{to}</div>,
}));

// Mock LoadingScreen to a simple div for easy assertions
jest.mock('@/components/ui/LoadingScreen', () => ({
  __esModule: true,
  default: ({ message }: { message?: string }) => <div data-testid="loading">{message}</div>,
}));

// ------------------ import after mocks -------------------

import PrivateRoute from '@/components/auth/PrivateRoute';

const DummyComponent: React.FC = () => <div data-testid="protected">protected</div>;

describe('PrivateRoute', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders LoadingScreen when loading and no user', () => {
    mockUseAuth.mockReturnValue({ user: null, loading: true });
    render(<PrivateRoute component={DummyComponent} />);
    expect(screen.getByTestId('loading')).toBeInTheDocument();
  });

  it('redirects to /login when unauthenticated', () => {
    mockUseAuth.mockReturnValue({ user: null, loading: false });
    render(<PrivateRoute component={DummyComponent} />);
    const redirectEl = screen.getByTestId('redirect');
    expect(redirectEl).toHaveTextContent('/login');
  });

  it('renders protected component when authenticated', () => {
    mockUseAuth.mockReturnValue({ user: { id: 1 }, loading: false });
    render(<PrivateRoute component={DummyComponent} />);
    expect(screen.getByTestId('protected')).toBeInTheDocument();
  });
}); 