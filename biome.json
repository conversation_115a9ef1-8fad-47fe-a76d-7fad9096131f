{"$schema": "https://biomejs.dev/schemas/2.0.0/schema.json", "files": {"includes": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "experimentalScannerIgnores": ["**/node_modules/", "dist/", "**/build/", "coverage/", "**/.vscode/", "**/.git/", "**/package-lock.json", "**/*.min.js", "**/*.bundle.js", "client/dist/", "documentation/"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "attributePosition": "auto"}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noExtraBooleanCast": "error", "noUselessCatch": "error", "noUselessConstructor": "error", "noUselessFragments": "error", "noUselessLabel": "error", "noUselessLoneBlockStatements": "error", "noUselessRename": "error", "noUselessSwitchCase": "error", "noUselessTernary": "error", "noUselessThisAlias": "error", "noForEach": "off"}, "correctness": {"noChildrenProp": "error", "noConstAssign": "error", "noConstantCondition": "error", "noConstructorReturn": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidConstructorSuper": "error", "noInvalidBuiltinInstantiation": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedVariables": "warn", "useIsNan": "error", "useValidTypeof": "error"}, "style": {"noCommonJs": "off", "useConst": "warn", "useDefaultParameterLast": "warn", "useExponentiationOperator": "error", "useForOf": "error", "useImportType": "error", "useNodejsImportProtocol": "error", "useNumberNamespace": "error", "useSelfClosingElements": "error", "useShorthandAssign": "error", "useTemplate": "error", "noParameterAssign": "error", "useAsConstAssertion": "error", "useEnumInitializers": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "noInferrableTypes": "warn", "noUselessElse": "warn"}, "suspicious": {"noArrayIndexKey": "error", "noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDoubleEquals": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noSelfCompare": "error", "noShadowRestrictedNames": "error", "noSparseArray": "error", "noUnsafeNegation": "error", "useGetterReturn": "error", "noExplicitAny": "off"}}}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "attributePosition": "auto"}, "globals": ["console", "process"]}, "json": {"formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "trailingCommas": "none"}}, "overrides": [{"includes": ["**/*.test.{ts,tsx,js,jsx}", "**/*.spec.{ts,tsx,js,jsx}", "**/setupTests.{ts,js}", "**/jest.config.{ts,js}", "**/vitest.config.{ts,js}"], "linter": {"rules": {"suspicious": {"noConsole": "off", "noExplicitAny": "off"}}}, "javascript": {"globals": ["jest", "expect", "beforeEach", "after<PERSON>ach", "describe", "it"]}}]}