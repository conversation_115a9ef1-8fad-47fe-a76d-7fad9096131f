import { act, renderHook } from '@testing-library/react';
import { useEmailFilters } from '@/hooks/use-email-filters';
import type { Email } from '@/types/email';

const baseMockEmail: Email = {
  id: 0,
  userId: 1,
  messageId: '',
  threadId: null,
  subject: '',
  snippet: '',
  sender: '',
  senderEmail: '',
  receivedAt: new Date(),
  isRead: false,
  isArchived: false,
  isReplied: false,
  isTrashed: false,
  isImportant: false,
  snoozedUntil: null,
  replyDate: null,
  replyId: null,
  summary: null,
  categories: [],
  priority: null,
  aiReply: null,
  originalContent: null,
  htmlContent: null,
  provider: null,
  labelIds: [],
  contentExpiresAt: null,
  lastAccessed: null,
  isContentEncrypted: false,
  retentionDays: null,
};

const mockEmails: Email[] = [
  {
    ...baseMockEmail,
    id: 1,
    subject: 'Welcome to the app',
    sender: 'Admin',
    snippet: 'Getting started guide',
    isRead: true,
    isArchived: false,
    priority: 'medium',
    categories: ['updates'],
    messageId: 'msg-1',
  },
  {
    ...baseMockEmail,
    id: 2,
    subject: 'Your weekly report',
    sender: 'Analytics',
    snippet: 'Summary of your activity',
    isRead: false,
    isArchived: false,
    priority: 'high',
    categories: ['reports'],
    receivedAt: new Date(new Date().setDate(new Date().getDate() - 3)),
    messageId: 'msg-2',
  },
  {
    ...baseMockEmail,
    id: 3,
    subject: 'Project Alpha discussion',
    sender: 'Team Lead',
    snippet: 'Important updates required',
    isRead: false,
    isArchived: false,
    priority: 'high',
    categories: ['work', 'important'],
    receivedAt: new Date(new Date().setDate(new Date().getDate() - 8)),
    messageId: 'msg-3',
  },
  {
    ...baseMockEmail,
    id: 4,
    subject: 'Archived newsletter',
    sender: 'Marketing',
    snippet: 'Old news',
    isRead: true,
    isArchived: true,
    priority: 'low',
    categories: ['promotions'],
    receivedAt: new Date(new Date().setDate(new Date().getDate() - 40)),
    messageId: 'msg-4',
  },
  {
    ...baseMockEmail,
    id: 5,
    subject: 'Unread and low priority',
    sender: 'Notifications',
    snippet: 'Your account has been updated',
    isRead: false,
    isArchived: false,
    priority: 'low',
    categories: ['updates'],
    messageId: 'msg-5',
  },
];

describe('useEmailFilters', () => {
  // Set a fixed date for time-based tests
  beforeAll(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2023-10-27T10:00:00Z'));
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it('should initialize with default filters and all emails', () => {
    const { result } = renderHook(() => useEmailFilters(mockEmails));
    expect(result.current.filters.status).toBe('all');
    expect(result.current.searchQuery).toBe('');
    expect(result.current.filteredEmails.length).toBe(mockEmails.length);
    expect(result.current.hasActiveFilters).toBe(false);
  });

  it('should filter by status: unread', () => {
    const { result } = renderHook(() => useEmailFilters(mockEmails));
    act(() => {
      result.current.setFilters((prev) => ({ ...prev, status: 'unread' }));
    });
    expect(result.current.filteredEmails.length).toBe(3);
    expect(result.current.filteredEmails.every((e) => !e.isRead)).toBe(true);
    expect(result.current.hasActiveFilters).toBe(true);
  });

  it('should filter by status: archived', () => {
    const { result } = renderHook(() => useEmailFilters(mockEmails));
    act(() => {
      result.current.setFilters((prev) => ({ ...prev, status: 'archived' }));
    });
    expect(result.current.filteredEmails.length).toBe(1);
    expect(result.current.filteredEmails[0].id).toBe(4);
  });

  it('should filter by priority: high', () => {
    const { result } = renderHook(() => useEmailFilters(mockEmails));
    act(() => {
      result.current.setFilters((prev) => ({ ...prev, priority: 'high' }));
    });
    expect(result.current.filteredEmails.length).toBe(2);
    expect(result.current.filteredEmails.every((e) => e.priority === 'high')).toBe(true);
  });

  it('should filter by a single category', () => {
    const { result } = renderHook(() => useEmailFilters(mockEmails));
    act(() => {
      result.current.setFilters((prev) => ({ ...prev, categories: ['updates'] }));
    });
    expect(result.current.filteredEmails.length).toBe(2);
    expect(result.current.filteredEmails.every((e) => e.categories?.includes('updates'))).toBe(
      true
    );
  });

  it('should filter by multiple categories (OR logic)', () => {
    const { result } = renderHook(() => useEmailFilters(mockEmails));
    act(() => {
      result.current.setFilters((prev) => ({ ...prev, categories: ['reports', 'work'] }));
    });
    expect(result.current.filteredEmails.length).toBe(2);
    expect(result.current.filteredEmails.map((e) => e.id).sort()).toEqual([2, 3]);
  });

  it('should filter by search query', () => {
    const { result } = renderHook(() => useEmailFilters(mockEmails));
    act(() => {
      result.current.setSearchQuery('report');
    });
    expect(result.current.filteredEmails.length).toBe(1);
    expect(result.current.filteredEmails[0].id).toBe(2);
    expect(result.current.hasActiveFilters).toBeTruthy();
  });

  it('should combine multiple filters (e.g., unread and high priority)', () => {
    const { result } = renderHook(() => useEmailFilters(mockEmails));
    act(() => {
      result.current.setFilters((prev) => ({ ...prev, status: 'unread', priority: 'high' }));
    });
    expect(result.current.filteredEmails.length).toBe(2);
    expect(result.current.filteredEmails.map((e) => e.id).sort()).toEqual([2, 3]);
  });

  it('should clear all filters and search query', () => {
    const { result } = renderHook(() => useEmailFilters(mockEmails));

    // Apply some filters first
    act(() => {
      result.current.setFilters((prev) => ({ ...prev, status: 'unread' }));
      result.current.setSearchQuery('test');
    });
    expect(result.current.hasActiveFilters).toBeTruthy();

    // Clear filters
    act(() => {
      result.current.clearFilters();
    });

    expect(result.current.filters.status).toBe('all');
    expect(result.current.searchQuery).toBe('');
    expect(result.current.filteredEmails.length).toBe(mockEmails.length);
    expect(result.current.hasActiveFilters).toBe(false);
  });

  it('should return an empty array if the initial email list is empty or undefined', () => {
    const { result, rerender } = renderHook(({ emails }) => useEmailFilters(emails), {
      initialProps: { emails: [] },
    });
    expect(result.current.filteredEmails.length).toBe(0);

    rerender({ emails: [] });
    expect(result.current.filteredEmails.length).toBe(0);
  });
});
