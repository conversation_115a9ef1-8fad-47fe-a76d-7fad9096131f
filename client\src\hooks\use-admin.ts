/**
 * Admin Access Hook
 *
 * This hook provides a way to check if the current user has admin privileges.
 * It uses a combination of caching and API verification to minimize API calls.
 */

import { useCallback, useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import apiClient from '@/lib/apiClient';

/**
 * Hook to check if the current user is an admin
 */
export const useAdmin = () => {
  const { user } = useAuth();
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<unknown | null>(null);

  const checkAdminStatus = useCallback(async () => {
    if (!user) {
      setIsAdmin(false);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      // We don't need the response data, just the success of the request.
      // Any successful response from an /api/admin route confirms admin access.
      await apiClient.get('/api/admin/system-health');
      setIsAdmin(true);
    } catch (err: any) {
      // A 401 or 403 error is the expected outcome for non-admins.
      if (err.response?.status === 401 || err.response?.status === 403) {
        setIsAdmin(false);
      } else {
        // For other errors (e.g., network), we log them.
        setError(err);
        setIsAdmin(false);
      }
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Check admin status on initial load or when user changes
  useEffect(() => {
      checkAdminStatus();
  }, [checkAdminStatus]);

  return {
    isAdmin,
    isLoading,
    error,
    checkAdminStatus,
  };
};
