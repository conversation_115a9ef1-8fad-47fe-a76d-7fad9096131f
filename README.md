# InboxZeroAI

This repository contains the source for the InboxZeroAI application.

## Environment Setup

1. Install **Node.js >=18** and **npm >=8**.
2. Install dependencies:
   ```bash
   npm install
   ```
3. Copy `.env.example` to `.env` and fill in the required values. The example file explains the variables:
   ```
   # Environment Configuration Example
   #
   # This file provides an example of the environment variables needed by the application.
   # Copy this file to .env and replace the placeholder values with your actual credentials
   # and configuration.
   # DO NOT COMMIT YOUR ACTUAL .env FILE TO VERSION CONTROL if it contains sensitive data.
   ```
4. (Optional) verify your environment with:
   ```bash
   npm run check-env
   ```
5. Initialize the database if needed:
   ```bash
   npm run db:setup
   ```

## Development Tooling

This project uses **BiomeJS** for all linting and formatting. It's recommended to install the [Biome VS Code extension](https://marketplace.visualstudio.com/items?itemName=biomejs.biome) for the best development experience.

- `npm run lint` – run Biome linter
- `npm run lint:fix` – run Biome linter with auto-fix
- `npm run format` – run Biome formatter
- `npm run format:check` – check formatting without applying changes

## Building and Running

- Development server:
  ```bash
  npm run dev
  ```
  Use `npm run dev:clean` if you need to kill any old processes first.
- Production build:
  ```bash
  npm run build
  npm run start
  ```
  You can also preview the built client with `npm run preview`.

## Available Scripts

- `npm run dev` – start the server with `tsx` in development
- `npm run dev:clean` – cleanup old processes then start development server
- `npm run build` – build client and server bundles
- `npm run start` – start the bundled server in production mode
- `npm run start:dev` – run the bundled server in development mode
- `npm run type-check` – type-check the project
- `npm run db:generate` – generate Drizzle ORM types
- `npm run db:push` – apply migrations
- `npm run db:studio` – open Drizzle Studio
- `npm run db:setup` – create basic tables
- `npm run check-env` – verify environment variables
- `npm run test-db` – test the database connection
- `npm test` – run the Jest test suite

## Advanced Topics

Additional documentation is available in the `server/docs` directory and the `scripts` folder. For example:
- [Migrate to Supabase](scripts/migrate-to-supabase.md) – using Supabase as the database
- [Circuit Breaker Integration Guide](server/docs/integration-guide.md) – adding resilience patterns

