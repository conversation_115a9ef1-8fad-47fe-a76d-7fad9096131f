import type { EmailFilters } from '@/types/email';
import type { Email } from '../types/email';

/**
 * Safely converts a date string or object to a Date instance
 * @param dateString Date string, Date object, or null
 * @returns Date object
 */
export const safeDate = (dateString: string | Date | null): Date => {
  if (dateString === null) return new Date();
  if (dateString instanceof Date) return dateString;
  try {
    return new Date(dateString);
  } catch (_e) {
    return new Date();
  }
};

/**
 * Applies search query filtering to emails
 * @param emails List of emails to filter
 * @param searchQuery Search query string
 * @returns Filtered emails
 */
export const applySearchFilter = (emails: Email[], searchQuery: string): Email[] => {
  if (!searchQuery) return emails;

  const query = searchQuery.toLowerCase();
  return emails.filter((email) => {
    const searchableText = [
      email.subject || '',
      email.snippet || '',
      email.sender || '',
      email.senderEmail || '',
    ]
      .join(' ')
      .toLowerCase();

    return searchableText.includes(query);
  });
};

/**
 * Normalize the `categories` field into a string array.
 * The backend may return `string[]` or a JSON-encoded string; this util
 * ensures downstream consumers can rely on a consistent type and avoids
 * runtime `some is not a function` errors that currently break rendering.
 */
export const normalizeCategories = (raw: unknown): string[] => {
  if (!raw) return [];
  if (Array.isArray(raw)) return raw.filter((c): c is string => typeof c === 'string');
  if (typeof raw === 'string') {
    try {
      const parsed = JSON.parse(raw);
      return Array.isArray(parsed) ? parsed.filter((c): c is string => typeof c === 'string') : [];
    } catch {
      // Fallback: treat the string as a single category label
      return [raw];
    }
  }
  return [];
};

/**
 * Applies category filtering to emails
 * @param emails List of emails to filter
 * @param categories List of categories to filter by
 * @returns Filtered emails
 */
export const applyCategoryFilter = (emails: Email[], categories: string[]): Email[] => {
  if (categories.length === 0) return emails;

  return emails.filter((email) => {
    const emailCategories = normalizeCategories((email as any).categories);
    return emailCategories.some((cat) => categories.includes(cat));
  });
};

/**
 * Applies status filtering to emails
 * @param emails List of emails to filter
 * @param status Status to filter by ('all', 'read', 'unread', 'archived', 'trashed', 'important', 'snoozed')
 * @returns Filtered emails
 */
export const applyStatusFilter = (emails: Email[], status: string): Email[] => {
  // Treat missing, empty or "all" as no filter.
  if (!status || status === 'all') return emails;

  return emails.filter((email) => {
    const isRead = (email as any).isRead ?? (email as any).read ?? false;
    if (status === 'read') return isRead;
    if (status === 'unread') return !isRead;
    if (status === 'archived') return email.isArchived;
    if (status === 'trashed') return email.isTrashed;
    if (status === 'important') return (email as any).isImportant || (email as any).isStarred;
    if (status === 'snoozed') {
      return (
        (email as any).snoozedUntil ?? (email as any).snoozeUntil ?? null
      ) !== null;
    }
    return true;
  });
};

/**
 * Applies priority filtering to emails
 * @param emails List of emails to filter
 * @param priority Priority to filter by ('all', 'high', 'medium', 'low')
 * @returns Filtered emails
 */
export const applyPriorityFilter = (emails: Email[], priority: string): Email[] => {
  if (priority === 'all') return emails;

  return emails.filter((email) => email.priority === priority);
};

/**
 * Applies time range filtering to emails
 * @param emails List of emails to filter
 * @param timeRange Time range to filter by ('all', 'today', 'this_week', 'this_month')
 * @returns Filtered emails
 */
export const applyTimeRangeFilter = (emails: Email[], timeRange: string): Email[] => {
  if (timeRange === 'all') return emails;

  const now = new Date();

  return emails.filter((email) => {
    const emailDate = safeDate(email.receivedAt);

    if (timeRange === 'today') {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return emailDate >= today;
    }
    if (timeRange === 'this_week') {
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay()); // Start of the week (Sunday)
      weekStart.setHours(0, 0, 0, 0);
      return emailDate >= weekStart;
    }
    if (timeRange === 'this_month') {
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      return emailDate >= monthStart;
    }

    return true;
  });
};

/**
 * Applies all filters to a list of emails
 * @param emails List of emails to filter
 * @param filters Filters to apply
 * @param searchQuery Search query string
 * @returns Filtered emails
 */
export const filterEmails = (
  emails: Email[],
  filters: EmailFilters,
  searchQuery: string
): Email[] => {
  let filteredEmails = emails;

  // Apply search filter
  filteredEmails = applySearchFilter(filteredEmails, searchQuery);

  // Apply category filter
  filteredEmails = applyCategoryFilter(filteredEmails, filters.categories);

  // Apply status filter
  filteredEmails = applyStatusFilter(filteredEmails, filters.status);

  // Apply priority filter
  filteredEmails = applyPriorityFilter(filteredEmails, filters.priority);

  // Apply time range filter
  filteredEmails = applyTimeRangeFilter(filteredEmails, filters.timeRange);

  return filteredEmails;
};
