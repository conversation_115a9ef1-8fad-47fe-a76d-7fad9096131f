import session from 'express-session';
import type { SessionOptions } from 'express-session';

// -------------------- Mocks --------------------

// Capture the options passed into express-session
let capturedOptions: SessionOptions | undefined;

// Mock express-session to return a stub middleware and expose MemoryStore
jest.mock('express-session', () => {
  // Define MockMemoryStore inside the mock to avoid hoisting issues
  class MockMemoryStore {
    public data: Record<string, unknown> = {};
  }

  const mockSession = jest.fn((options: SessionOptions) => {
    capturedOptions = options;
    return (_req: any, _res: any, next: () => void) => typeof next === 'function' && next();
  });
  
  class MemoryStore extends MockMemoryStore {}
  (mockSession as any).MemoryStore = MemoryStore;
  
  return {
    __esModule: true,
    default: mockSession,
    MemoryStore,
  };
});

// Mock connect-redis module
jest.mock('connect-redis', () => ({
  __esModule: true,
  RedisStore: jest.fn(),
}));

// Mock the Redis service that the session now uses
jest.mock('../../../server/services/redis', () => ({
  __esModule: true,
  getRedisClient: jest.fn(() => ({
    connect: jest.fn(() => Promise.resolve()),
    on: jest.fn(),
  })),
}));

// Mock environment validator to supply env vars dynamically via process.env
jest.mock('../../../server/lib/environmentValidator', () => ({
  __esModule: true,
  getEnvVar: (key: string) => process.env[key] || '',
}));

// Mock logger to silence output
jest.mock('../../../server/lib/logger', () => ({
  __esModule: true,
  default: {
    warn: jest.fn(),
    info: jest.fn(),
    error: jest.fn(),
  },
}));

// Add real crypto mock restore before other mocks
jest.mock('node:crypto', () => {
  const actual = jest.requireActual('node:crypto');
  return {
    __esModule: true,
    default: actual,
    ...actual,
  };
});

// -------------------- Import SUT --------------------

import { configureSession } from '../../../server/auth/session';
import { RedisStore } from 'connect-redis';

// -------------------- Tests --------------------

describe('Session Configuration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    capturedOptions = undefined;
  });

  it('creates session with MemoryStore fallback in development', () => {
    delete process.env.SESSION_SECRET;
    delete process.env.REDIS_URL;
    process.env.NODE_ENV = 'development';

    const middleware = configureSession();

    expect(typeof middleware).toBe('function');
    expect(capturedOptions).toBeDefined();
    
    // Cookie maxAge set to 7 days
    expect(capturedOptions!.cookie!.maxAge).toBe(7 * 24 * 60 * 60 * 1000);
    // Secret should be auto-generated (hex string length 64)
    expect((capturedOptions!.secret as string).length).toBe(64);
    // Should use MemoryStore in development
    expect(capturedOptions!.store!.constructor.name).toBe('MemoryStore');
  });

  it('uses RedisStore in production when REDIS_URL is set', () => {
    process.env.SESSION_SECRET = 'x'.repeat(40);
    process.env.REDIS_URL = 'redis://localhost:6379';
    process.env.NODE_ENV = 'production';

    configureSession();

    // RedisStore constructor should have been called once
    expect(RedisStore).toHaveBeenCalledTimes(1);
    // secure cookies in production
    expect(capturedOptions!.cookie!.secure).toBe(true);
  });

  it('throws error in production when REDIS_URL is missing', () => {
    process.env.SESSION_SECRET = 'x'.repeat(40);
    delete process.env.REDIS_URL;
    process.env.NODE_ENV = 'production';

    expect(() => configureSession()).toThrow('Redis configuration required for production session management.');
  });

  it('honours rolling cookies for session expiration refresh', () => {
    process.env.SESSION_SECRET = 'x'.repeat(40);
    delete process.env.REDIS_URL;
    process.env.NODE_ENV = 'development';

    configureSession();

    expect(capturedOptions!.rolling).toBe(true);
    expect(capturedOptions!.resave).toBe(false);
    expect(capturedOptions!.saveUninitialized).toBe(false);
  });
}); 