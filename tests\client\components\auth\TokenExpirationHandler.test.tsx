import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import type React from 'react';

// --------- mocks ---------
const mockForceSync = jest.fn().mockResolvedValue(undefined);
jest.mock('@/lib/authSync', () => ({
  __esModule: true,
  default: { forceSync: () => mockForceSync() },
}));

const mockLogout = jest.fn();
const mockClearAuthError = jest.fn();
const authState = { authError: null } as any;
const setAuthError = (error: any) => {
  authState.authError = error;
};

jest.mock('@/context/AuthContext', () => ({
  __esModule: true,
  useAuth: () => ({
    authError: authState.authError,
    clearAuthError: () => mockClearAuthError(),
    logout: () => mockLogout(),
  }),
}));

const mockToast = jest.fn();
jest.mock('@/hooks/use-toast', () => ({
  __esModule: true,
  useToast: () => ({ toast: (...args: any[]) => mockToast(...args) }),
}));

// -----------------
import TokenExpirationHandler from '@/components/auth/TokenExpirationHandler';

describe('TokenExpirationHandler', () => {
  afterEach(() => {
    jest.clearAllMocks();
    authState.authError = null;
  });

  it('opens modal when token error occurs', () => {
    setAuthError({ code: 'auth/id-token-expired', message: 'expired' });
    render(<TokenExpirationHandler />);
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  it('calls forceSync and closes on refresh success', async () => {
    setAuthError({ code: 'auth/id-token-expired', message: 'expired' });
    render(<TokenExpirationHandler />);

    fireEvent.click(screen.getByRole('button', { name: /refresh/i }));

    await waitFor(() => {
      expect(mockForceSync).toHaveBeenCalled();
      expect(mockToast).toHaveBeenCalledWith(
        expect.objectContaining({ title: 'Session Refreshed' })
      );
    });
  });

  it('logs user out when refresh fails', async () => {
    mockForceSync.mockRejectedValueOnce(new Error('fail'));
    setAuthError({ code: 'auth/id-token-expired', message: 'expired' });
    render(<TokenExpirationHandler />);

    fireEvent.click(screen.getByRole('button', { name: /refresh/i }));

    await waitFor(() => {
      expect(mockLogout).toHaveBeenCalled();
    });
  });

  it('logout button triggers logout directly', () => {
    setAuthError({ code: 'auth/id-token-expired', message: 'expired' });
    render(<TokenExpirationHandler />);

    fireEvent.click(screen.getByRole('button', { name: /login/i }));
    expect(mockLogout).toHaveBeenCalled();
  });
}); 