// Mock the @google/genai library
const mockGenerateContent = jest.fn();
jest.mock('@google/genai', () => ({
  GoogleGenAI: jest.fn().mockImplementation(() => ({
    getGenerativeModel: jest.fn().mockReturnValue({
      generateContent: mockGenerateContent,
    }),
  })),
}));

describe('Gemini API Service', () => {
  beforeEach(() => {
    // Reset modules to ensure a clean state for every test
    jest.resetModules();
    // Set a dummy API key to prevent the real client from throwing an error
    process.env.GEMINI_API_KEY = 'test-key';

    // Clear mocks before each test
    mockGenerateContent.mockClear();
  });

  describe('extractTextFromHtml', () => {
    it('should return plain text as-is', () => {
      const { extractTextFromHtml } = require('@server/services/gemini');
      const text = 'This is a simple text email.';
      expect(extractTextFromHtml(text)).toBe(text);
    });

    it('should strip basic HTML tags', () => {
      const { extractTextFromHtml } = require('@server/services/gemini');
      const html = '<p>This is a <b>test</b>.</p>';
      expect(extractTextFromHtml(html)).toBe('This is a test.');
    });

    it('should convert block elements to newlines', () => {
      const { extractTextFromHtml } = require('@server/services/gemini');
      const html = '<div>First line</div><div>Second line</div>';
      expect(extractTextFromHtml(html)).toBe('First line\n Second line');
    });

    it('should preserve links with their URL', () => {
      const { extractTextFromHtml } = require('@server/services/gemini');
      const html = '<a href="https://example.com">Click here</a>';
      expect(extractTextFromHtml(html)).toBe('Click here [https://example.com]');
    });

    it('should remove script and style tags completely', () => {
      const { extractTextFromHtml } = require('@server/services/gemini');
      const html = '<style>p { color: red; }</style><p>Hello</p><script>alert("world")</script>';
      expect(extractTextFromHtml(html)).toBe('Hello');
    });

    it('should handle complex HTML content', () => {
      const { extractTextFromHtml } = require('@server/services/gemini');
      const html = `
          <body>
            <h1>Welcome!</h1>
            <p>Here is a list:</p>
            <ul>
              <li>Item 1</li>
              <li>Item 2</li>
            </ul>
            <a href="https://test.com">Learn more</a>
          </body>
        `;
      const expected =
        '**Welcome!**\n Here is a list: \n • Item 1 \n • Item 2 \n Learn more [https://test.com]';
      expect(extractTextFromHtml(html)).toBe(expected);
    });
  });

  describe('summarizeEmailWithGemini', () => {
    it('should call the Gemini API and return a summary', async () => {
      const { summarizeEmailWithGemini } = require('@server/services/gemini');
      const mockSummary = 'This is a mock summary.';
      mockGenerateContent.mockResolvedValue({
        response: { text: () => mockSummary },
      });

      const content = 'This is the full email content that needs to be summarized.';
      const summary = await summarizeEmailWithGemini(content);

      expect(summary).toBe(mockSummary);
      expect(mockGenerateContent).toHaveBeenCalledTimes(1);
      // Optional: Check if the prompt is constructed correctly
      expect(mockGenerateContent.mock.calls[0][0]).toContain('Summarize the following email');
    });

    it('should return a fallback message on API error', async () => {
      const { summarizeEmailWithGemini } = require('@server/services/gemini');
      mockGenerateContent.mockRejectedValue(new Error('API failed'));
      const content = 'This email will cause an error.';
      const summary = await summarizeEmailWithGemini(content);
      expect(summary).toContain('Summary unavailable');
    });
  });

  describe('unifiedEmailProcessing Caching', () => {
    it('should cache the result of a unified processing call', async () => {
      const { unifiedEmailProcessing } = require('@server/services/gemini');
      const mockResponse = {
        summary: 'Test summary',
        categories: ['Work'],
        reply: 'Test reply',
      };
      // Mock the complex multi-prompt response
      mockGenerateContent.mockResolvedValue({
        response: { text: () => JSON.stringify(mockResponse) },
      });

      const subject = 'Cache Test';
      const content = 'This is a test of the caching mechanism.';
      const sender = '<EMAIL>';

      // First call - should call the API
      await unifiedEmailProcessing(content, subject, sender);
      expect(mockGenerateContent).toHaveBeenCalledTimes(1);

      // Second call - should return from cache
      await unifiedEmailProcessing(content, subject, sender);
      expect(mockGenerateContent).toHaveBeenCalledTimes(1); // Should still be 1
    });
  });
});
