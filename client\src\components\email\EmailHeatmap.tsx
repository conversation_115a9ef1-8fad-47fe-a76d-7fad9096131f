import type React from 'react';
import { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useEmailList } from '@/context/EmailListContext';
import { useTheme } from '@/context/ThemeContext';
import { hexToRGB } from '@/lib/utils';
import type { Email } from '@/types/email';

interface HeatmapTile {
  id: number;
  color: string;
  email: Email;
}

// Memoized empty state component to prevent unnecessary re-renders
const EmptyHeatmap = memo(() => (
  <div className="text-center p-4 text-muted-foreground">No emails to display</div>
));
EmptyHeatmap.displayName = 'EmptyHeatmap';

// Memoized heatmap tile component to optimize rendering of individual tiles
const HeatmapTile = memo(({ tile }: { tile: HeatmapTile }) => {
  const ref = useRef<HTMLDivElement>(null);

  // Use an effect to set the background color programmatically
  useEffect(() => {
    if (ref.current) {
      ref.current.style.setProperty('--tile-bg-color', tile.color);
    }
  }, [tile.color]);

  // Memoize the sender display name to avoid recalculating on every render
  const senderDisplayName = useMemo(() => {
    return tile.email?.sender
      ? typeof tile.email.sender === 'string' && tile.email.sender.includes(' ')
        ? tile.email.sender.split(' ')[0]
        : tile.email.sender
      : 'Unknown';
  }, [tile.email?.sender]);

  return (
    <div
      ref={ref}
      key={tile.id}
      className="email-heatmap-tile aspect-square rounded-md p-3 cursor-pointer transition-transform hover:scale-105 flex flex-col justify-between overflow-hidden bg-[var(--tile-bg-color)]"
      title={`${tile.email?.subject || 'No Subject'} - ${tile.email?.sender || 'Unknown'}`}
    >
      <div className="text-white text-xs font-medium truncate">{senderDisplayName}</div>
      <div className="text-white text-sm font-semibold line-clamp-2 mt-1">
        {tile.email?.subject || 'No Subject'}
      </div>
    </div>
  );
});
HeatmapTile.displayName = 'HeatmapTile';

const EmailHeatmap: React.FC = () => {
  const { emails } = useEmailList();

  // Memoized function to calculate priority score based on email properties
  const calculatePriorityScore = useCallback((email: Email): number => {
    let score = 0;

    // Safely handle categories
    const categories = Array.isArray(email.categories) ? email.categories : [];

    // Add points for urgency categories
    if (categories.includes('Urgent')) score += 5;
    if (categories.includes('Work')) score += 3;
    if (categories.includes('Personal')) score += 2;
    if (categories.includes('Promotions')) score -= 1;

    // Safely handle receivedAt date
    let daysSinceReceived = 7; // Default to a week old if no date
    try {
      if (email.receivedAt) {
        const emailDate =
          email.receivedAt instanceof Date ? email.receivedAt : new Date(email.receivedAt);

        // Check if date is valid
        if (!Number.isNaN(emailDate.getTime())) {
          daysSinceReceived = Math.floor(
            (Date.now() - emailDate.getTime()) / (1000 * 60 * 60 * 24)
          );
        }
      }
    } catch (_error) {
      // Use default value on error
    }

    score += Math.max(0, 3 - daysSinceReceived);

    // Unread emails get higher priority
    if (!email.isRead) score += 2;

    // If there's an explicit priority field, use it (with type safety)
    if (email.priority === 'high') score += 3;
    if (email.priority === 'medium') score += 1;
    if (email.priority === 'low') score -= 1;

    return Math.max(1, Math.min(10, score)); // Clamp score between 1-10
  }, []);

  // Helper function to convert score to color - memoized to avoid recreating this function
  const getColorForScore = useCallback((score: number): string => {
    try {
      let priorityLevel: string;
      let intensity: number;

      // Determine priority level and intensity based on score
      if (score <= 3) {
        priorityLevel = 'low';
        intensity = Math.max(0.5, score / 3); // 0.5 to 1.0 opacity
      } else if (score <= 6) {
        priorityLevel = 'medium';
        intensity = Math.max(0.6, (score - 3) / 3); // 0.6 to 1.0 opacity
      } else {
        priorityLevel = 'high';
        intensity = Math.max(0.7, (score - 6) / 4); // 0.7 to 1.0 opacity
      }

      // Use more visually appealing colors for the heatmap
      const heatmapColors = {
        low: '#10b981', // emerald-500 - brighter green
        medium: '#2563eb', // blue-600 - vibrant blue instead of amber/brown
        high: '#e11d48', // rose-600 - vibrant rose instead of standard red
      };

      const priorityColor = heatmapColors[priorityLevel as keyof typeof heatmapColors] || '#6b7280';
      return hexToRGB(priorityColor, intensity);
    } catch (error) {
      console.error('Error calculating heatmap color:', error);
      return 'rgba(128, 128, 128, 0.7)'; // Default gray color on error
    }
  }, []);

  // Memoize the heatmap tile generation to prevent recalculation on every render
  const heatmapTiles = useMemo(() => {
    // Ensure emails is an array
    if (!Array.isArray(emails) || emails.length === 0) {
      return [];
    }

    try {
      const tilesData = emails
        // Safely filter archived emails
        .filter((email) => email && typeof email === 'object' && !email.isArchived)
        .map((email) => {
          try {
            const score = calculatePriorityScore(email);
            return {
              id: email.id,
              color: getColorForScore(score),
              email,
              score, // Store the score for sorting
            };
          } catch (_error) {
            // If processing a single email fails, use default values but keep it in the list
            return {
              id: email.id,
              color: 'rgba(128, 128, 128, 0.7)', // Default gray color
              email,
              score: 0,
            };
          }
        })
        .sort((a, b) => (b.score || 0) - (a.score || 0)); // Sort by score directly, avoiding recalculation

      return tilesData;
    } catch (_error) {
      // Return empty array if processing fails entirely
      return [];
    }
  }, [emails, calculatePriorityScore, getColorForScore]);

  return (
    <div className="p-6">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Email Priority Heatmap</CardTitle>
          <p className="text-sm text-muted-foreground">
            Visualize which emails need your attention first
          </p>
        </CardHeader>
        <CardContent>
          {heatmapTiles.length === 0 ? (
            <EmptyHeatmap />
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
              {heatmapTiles.map((tile) => (
                <HeatmapTile key={tile.id} tile={tile} />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Use memo to prevent the entire component from re-rendering unnecessarily
export default memo(EmailHeatmap);
