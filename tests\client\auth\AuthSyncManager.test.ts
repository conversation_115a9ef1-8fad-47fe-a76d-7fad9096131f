import { act } from '@testing-library/react';

// ---------------- mocks ------------------

const mockInvalidateQueries = jest.fn().mockResolvedValue(undefined);
jest.mock('@/lib/queryClient', () => ({
  __esModule: true,
  queryClient: {
    invalidateQueries: () => mockInvalidateQueries(),
  },
}));

const mockOnAuthStateChanged = jest.fn();
// Return unsubscribe dummy
mockOnAuthStateChanged.mockImplementation((_auth, cb) => {
  // store callback for manual trigger
  AuthCallbackHolder.cb = cb;
  return () => {};
});

jest.mock('firebase/auth', () => ({
  getAuth: () => ({}),
  onAuthStateChanged: (...args: any[]) => mockOnAuthStateChanged(...args),
}));

// holder to expose callback
const AuthCallbackHolder: { cb?: (user: unknown) => void } = {};

// Mock logger to silence output
jest.mock('@/lib/logger', () => ({
  __esModule: true,
  default: { info: jest.fn(), warn: jest.fn(), error: jest.fn(), debug: jest.fn() },
}));

// ---------------- import after mocks ---------------
import authSyncManager from '@/lib/authSync';

describe('AuthSyncManager', () => {
  afterEach(() => {
    authSyncManager.stop();
    jest.clearAllMocks();
  });

  it('invalidates queries when Firebase auth state changes', async () => {
    authSyncManager.start();

    // Trigger stored callback to simulate auth state change
    await act(async () => {
      AuthCallbackHolder.cb?.({ uid: 'user' });
    });

    expect(mockInvalidateQueries).toHaveBeenCalled();
  });
}); 