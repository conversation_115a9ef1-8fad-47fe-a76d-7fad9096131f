/**
 * User Service
 *
 * This service centralizes the logic for finding or creating users
 * from OAuth provider profiles, preventing race conditions and code duplication.
 */

import type { Auth } from 'googleapis';
import type { InsertUser, User } from '../../shared/schema';
import logger from '../lib/logger';
import { storage } from '../storage';

// A standardized profile shape from any OAuth provider
export interface ProviderProfile {
  email: string;
  name?: string | null;
  picture?: string | null;
  provider: string;
  tokens: Auth.Credentials;
  existingUserId?: number;
}

/**
 * Finds an existing user by email or creates a new one if not found.
 * This function is designed to be the single source of truth for user provisioning
 * from an external provider.
 *
 * @param profile - The standardized user profile from the OAuth provider.
 * @returns The found or newly created user.
 */
export async function findOrCreateUser(profile: ProviderProfile): Promise<User> {
  if (!profile.email) {
    throw new Error('Cannot find or create a user without an email address.');
  }

  const tokenData = JSON.stringify(profile.tokens);

  // If a user is already logged in, prioritize their existing ID.
  if (profile.existingUserId) {
    const loggedInUser = await storage.getUserById(profile.existingUserId);
    if (loggedInUser) {
      logger.info('Linking new provider to existing logged-in user.', {
        userId: loggedInUser.id,
        existingProvider: loggedInUser.provider,
        newProvider: profile.provider,
        email: profile.email,
      });
      
      // Update the user with new provider tokens but preserve the original provider
      const updatedUser = await storage.updateUser(loggedInUser.id, {
        name: profile.name || loggedInUser.name,
        picture: profile.picture || loggedInUser.picture,
        gmailTokens: tokenData,
        lastLogin: new Date(),
        // Preserve the original email and provider - don't overwrite Firebase auth
        email: loggedInUser.email || profile.email,
        // CRITICAL: Don't overwrite the provider if it's already 'firebase'
        // This preserves the Firebase authentication while adding Gmail tokens
        provider: loggedInUser.provider === 'firebase' ? 'firebase' : profile.provider,
      });
      return updatedUser || loggedInUser;
    }
  }

  // Fallback for new sign-ups or unauthenticated connections.
  const existingUserByEmail = await storage.getUserByEmail(profile.email);

  if (existingUserByEmail) {
    // User exists, update their details and tokens.
    logger.info('Existing user found by email, linking provider.', {
      userId: existingUserByEmail.id,
      existingProvider: existingUserByEmail.provider,
      newProvider: profile.provider,
      email: profile.email,
      hasFirebaseUid: !!existingUserByEmail.firebaseUid,
    });
    
    const updatedUser = await storage.updateUser(existingUserByEmail.id, {
      name: profile.name || existingUserByEmail.name,
      picture: profile.picture || existingUserByEmail.picture,
      gmailTokens: tokenData,
      lastLogin: new Date(),
      // CRITICAL: Preserve Firebase provider and don't overwrite it
      // If the existing user has Firebase auth, keep that as the primary provider
      provider: existingUserByEmail.provider === 'firebase' ? 'firebase' : profile.provider,
    });
    return updatedUser || existingUserByEmail;
  }
  
  // User does not exist, create a new one.
  logger.info('New user detected, creating account.', {
    email: profile.email,
    provider: profile.provider,
  });
  const newUser: InsertUser = {
    email: profile.email,
    name: profile.name || '',
    provider: profile.provider,
    picture: profile.picture || null,
    gmailTokens: tokenData,
    tier: 'free',
    lastLogin: new Date(),
  };
  return storage.createUser(newUser);
}
