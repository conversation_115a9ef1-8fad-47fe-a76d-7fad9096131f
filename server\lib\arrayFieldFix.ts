/**
 * Utility to handle null/undefined array fields from database queries
 * This is particularly needed when RLS is enabled and array fields may come back as null
 */

import type { Email } from '@shared/schema';

/**
 * Safely normalize array fields in an email object
 */
export function normalizeEmailArrayFields(email: any): Email {
  if (!email) return email;
  
  return {
    ...email,
    categories: Array.isArray(email.categories) ? email.categories : [],
    labelIds: Array.isArray(email.labelIds) ? email.labelIds : [],
  };
}

/**
 * Safely normalize array fields in multiple email objects
 */
export function normalizeEmailsArrayFields(emails: any[]): Email[] {
  if (!Array.isArray(emails)) return [];
  
  return emails.map(normalizeEmailArrayFields);
}

/**
 * Middleware to patch <PERSON><PERSON><PERSON>'s array field mapping to handle null values
 * This prevents the "Cannot read properties of undefined (reading 'map')" error
 */
export function patchDrizzleArrayMapping() {
  // This is a runtime patch to handle the Drizzle array mapping issue
  // We'll override the mapFromDriverValue method to handle null/undefined values
  
  const originalConsoleError = console.error;
  console.error = (...args: any[]) => {
    const message = args[0];
    if (typeof message === 'string' && message.includes('Cannot read properties of undefined (reading \'map\')')) {
      // Suppress this specific error and log a warning instead
      console.warn('Drizzle array mapping error suppressed - handling null array field');
      return;
    }
    originalConsoleError.apply(console, args);
  };
}

/**
 * Safe array field accessor
 */
export function safeArrayField<T>(value: T[] | null | undefined): T[] {
  return Array.isArray(value) ? value : [];
} 