import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Off, <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rash } from 'lucide-react';
/**
 * Email Provider Status Manager Component
 *
 * A production-grade component that handles:
 * 1. Service unavailability with graceful degradation
 * 2. Circuit breaker pattern for client-side resilience
 * 3. Retry mechanisms with exponential backoff
 * 4. Clear user feedback and self-healing actions
 */
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import apiClient from '@/lib/apiClient';

// Base time for retry attempts (in ms)
const BASE_RETRY_DELAY = 2000;
// Maximum retry delay (in ms)
const MAX_RETRY_DELAY = 30000;
// Maximum number of automatic retries
const MAX_AUTO_RETRIES = 5;

interface EmailProviderStatusManagerProps {
  onReconnected?: () => void;
  onFailed?: () => void;
  showRetryButton?: boolean;
  onManualRetry?: () => void;
  className?: string;
}

enum ConnectionState {
  CHECKING = 'checking',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  CONNECTED = 'connected',
  FAILED = 'failed',
}

export const EmailProviderStatusManager: React.FC<EmailProviderStatusManagerProps> = ({
  onReconnected,
  onFailed,
  showRetryButton = true,
  onManualRetry,
  className = '',
}) => {
  const [connectionState, setConnectionState] = useState<ConnectionState>(ConnectionState.CHECKING);
  const [retryCount, setRetryCount] = useState(0);
  const [retryDelayMs, setRetryDelayMs] = useState(0);
  const [progress, setProgress] = useState(0);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { toast } = useToast();

  // Calculate delay with exponential backoff
  const calculateDelay = useCallback((attempt: number): number => {
    // Exponential backoff formula with jitter: base * 2^attempt + random
    return Math.min(BASE_RETRY_DELAY * 2 ** attempt + Math.random() * 1000, MAX_RETRY_DELAY);
  }, []);

  // Function to check server status
  const checkConnection = useCallback(
    async ({ bypassCache = false } = {}) => {
      try {
        setConnectionState(ConnectionState.CHECKING);
        // Using a generic status endpoint, can be made more specific
        await apiClient.get('/api/status', {
          headers: bypassCache ? { 'Cache-Control': 'no-cache' } : {},
        });
        setConnectionState(ConnectionState.CONNECTED);
        if (retryCount > 0) {
          // Only show toast on re-connection
          toast({
            title: 'Connection Restored',
            description: 'Successfully reconnected to the email service.',
          });
        }
        onReconnected?.();
        setRetryCount(0); // Reset on success
        return true;
      } catch (error) {
        console.error('Error checking connection:', error);
        setConnectionState(ConnectionState.DISCONNECTED);
        setErrorMessage(error instanceof Error ? error.message : String(error));
        return false;
      }
    },
    [onReconnected, retryCount, toast]
  );

  // Function to attempt reconnection with exponential backoff
  const attemptReconnection = useCallback(async () => {
    if (retryCount >= MAX_AUTO_RETRIES) {
      setConnectionState(ConnectionState.FAILED);
      onFailed?.();
      return;
    }

    setConnectionState(ConnectionState.RECONNECTING);
    const newRetryCount = retryCount + 1;
    setRetryCount(newRetryCount);

    const delay = calculateDelay(newRetryCount - 1);
    setRetryDelayMs(delay);
    setProgress(0);

    toast({
      title: 'Reconnecting...',
      description: `Attempt ${newRetryCount} of ${MAX_AUTO_RETRIES}. Please wait...`,
    });

    const startTime = Date.now();
    const progressInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const newProgress = Math.min((elapsed / delay) * 100, 100);
      setProgress(newProgress);

      if (elapsed >= delay) {
        clearInterval(progressInterval);
      }
    }, 100);

    await new Promise((resolve) => setTimeout(resolve, delay));
    clearInterval(progressInterval);

    const isConnected = await checkConnection({ bypassCache: true });
    if (!isConnected) {
      attemptReconnection();
    }
  }, [retryCount, checkConnection, onFailed, calculateDelay, toast]);

  // Manual retry handler
  const handleManualRetry = useCallback(async () => {
    // Reset state for a fresh attempt
    setRetryCount(0);
    setErrorMessage(null);

    // If custom retry handler provided, use that
    if (onManualRetry) {
      onManualRetry();
    }

    // Otherwise, start our own reconnection attempt
    else {
      attemptReconnection();
    }
  }, [onManualRetry, attemptReconnection]);

  // Setup continuous server monitoring
  useEffect(() => {
    // Start by checking connection immediately
    checkConnection();

    // Then set up monitoring that will notify us of changes
    const intervalId = setInterval(() => {
      if (connectionState === ConnectionState.CONNECTED) {
        checkConnection();
      }
    }, 60000); // Check every 60 seconds

    // Clean up monitoring on unmount
    return () => {
      clearInterval(intervalId);
    };
  }, [connectionState, checkConnection]);

  // Render different UI based on connection state
  if (connectionState === ConnectionState.CONNECTED) {
    return null; // Don't render anything when connected successfully
  }

  if (connectionState === ConnectionState.CHECKING) {
    return (
      <Card className={`w-full shadow-md ${className}`}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center space-x-3">
            <Loader2 className="h-5 w-5 animate-spin text-primary" />
            <p>Checking connection to email service...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (connectionState === ConnectionState.RECONNECTING) {
    return (
      <Card className={`w-full shadow-md ${className}`}>
        <CardHeader className="pb-2">
          <CardTitle className="text-base flex items-center">
            <RefreshCw className="h-5 w-5 text-warning mr-2 animate-spin" />
            Reconnecting to Email Service
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-2">
            Attempt {retryCount} of {MAX_AUTO_RETRIES}. Waiting for service to become available...
          </p>
          <Progress value={progress} className="h-1 mb-2" />
          <p className="text-xs text-muted-foreground">
            Next attempt in {Math.round((retryDelayMs * (100 - progress)) / 1000)}s
          </p>
        </CardContent>
      </Card>
    );
  }

  if (connectionState === ConnectionState.FAILED) {
    return (
      <Card className={`w-full shadow-md border-destructive/20 ${className}`}>
        <CardHeader className="pb-2">
          <CardTitle className="text-base flex items-center">
            <ServerCrash className="h-5 w-5 text-destructive mr-2" />
            Service Unavailable
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">
            We've tried several times, but the email service is currently unavailable. This is
            likely a temporary issue.
          </p>
          {errorMessage && (
            <Alert variant="destructive" className="mb-3">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error Details</AlertTitle>
              <AlertDescription className="text-xs font-mono">{errorMessage}</AlertDescription>
            </Alert>
          )}
        </CardContent>
        <CardFooter>
          {showRetryButton && (
            <Button onClick={handleManualRetry} className="w-full">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          )}
        </CardFooter>
      </Card>
    );
  }

  // Default: DISCONNECTED state
  return (
    <Card className={`w-full shadow-md border-warning/20 ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-base flex items-center">
          <CloudOff className="h-5 w-5 text-warning mr-2" />
          Email Service Disconnected
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-2">
          We're having trouble connecting to the email service. This could be due to:
        </p>
        <ul className="text-sm text-muted-foreground list-disc list-inside mb-3">
          <li>Temporary service disruption</li>
          <li>Network connectivity issues</li>
          <li>Server maintenance</li>
        </ul>
        {errorMessage && (
          <Alert variant="destructive" className="mb-3">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error Details</AlertTitle>
            <AlertDescription className="text-xs font-mono">{errorMessage}</AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleManualRetry} className="flex-1 mr-2">
          <RefreshCw className="mr-2 h-4 w-4" />
          Reconnect Now
        </Button>
        <Button variant="default" onClick={() => attemptReconnection()} className="flex-1">
          <Clock className="mr-2 h-4 w-4" />
          Auto-Retry
        </Button>
      </CardFooter>
    </Card>
  );
};

export default EmailProviderStatusManager;
