import {
  <PERSON>ert<PERSON>ircle,
  AlertTriangle,
  CheckCircle,
  Clock,
  HelpCircle,
  RefreshCw,
  XCircle,
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

export type ConnectionStatus =
  | 'connected'
  | 'disconnected'
  | 'error'
  | 'expired'
  | 'revoked'
  | 'unknown'
  | 'connecting'
  | 'refreshing';

interface ConnectionStatusIndicatorProps {
  status: ConnectionStatus;
  lastUpdated?: Date | string | null;
  error?: string | null;
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  inline?: boolean;
}

/**
 * Enhanced Connection Status Indicator Component
 *
 * This component provides a visual indicator of connection status with a tooltip
 * showing detailed information. It's designed to be highly visible and provide
 * clear status information at a glance.
 */
export function ConnectionStatusIndicator({
  status,
  lastUpdated,
  error,
  className,
  showText = false,
  size = 'md',
  inline = false,
}: ConnectionStatusIndicatorProps) {
  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  let icon;
  let color;
  let statusText;
  let tooltipText;

  switch (status) {
    case 'connected':
      icon = <CheckCircle className={sizeClasses[size]} />;
      color = 'text-green-500';
      statusText = 'Connected';
      tooltipText = lastUpdated
        ? `Successfully connected. Last verified: ${new Date(lastUpdated).toLocaleString()}`
        : 'Successfully connected';
      break;

    case 'disconnected':
      icon = <XCircle className={sizeClasses[size]} />;
      color = 'text-gray-400';
      statusText = 'Disconnected';
      tooltipText = 'Not connected. Connect your account to enable email functionality.';
      break;

    case 'error':
      icon = <AlertCircle className={sizeClasses[size]} />;
      color = 'text-red-500';
      statusText = 'Error';
      tooltipText = error || 'Connection error occurred. Please try reconnecting.';
      break;

    case 'expired':
      icon = <Clock className={sizeClasses[size]} />;
      color = 'text-amber-500';
      statusText = 'Expired';
      tooltipText = 'Your connection has expired. Please reconnect your account.';
      break;

    case 'revoked':
      icon = <AlertTriangle className={sizeClasses[size]} />;
      color = 'text-red-500';
      statusText = 'Revoked';
      tooltipText = 'Access has been revoked. Please reconnect your account.';
      break;

    case 'refreshing':
      icon = <RefreshCw className={cn(sizeClasses[size], 'animate-spin')} />;
      color = 'text-blue-500';
      statusText = 'Refreshing';
      tooltipText = 'Refreshing connection status...';
      break;

    case 'connecting':
      icon = <RefreshCw className={cn(sizeClasses[size], 'animate-spin')} />;
      color = 'text-blue-500';
      statusText = 'Connecting';
      tooltipText = 'Establishing connection...';
      break;

    default:
      icon = <HelpCircle className={sizeClasses[size]} />;
      color = 'text-gray-400';
      statusText = 'Unknown';
      tooltipText = 'Connection status unknown';
      break;
  }

  const statusIndicator = (
    <div className={cn('flex items-center gap-1.5', inline ? 'inline-flex' : '', className)}>
      <span className={color}>{icon}</span>
      {showText && (
        <span className={cn('font-medium', color, textSizeClasses[size])}>{statusText}</span>
      )}
    </div>
  );

  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>{statusIndicator}</TooltipTrigger>
        <TooltipContent>
          <p className="max-w-xs text-sm">{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
