import { motion, useAnimation } from 'framer-motion';
import type React from 'react';
import { useEffect, useRef } from 'react';

interface AnimatedListItemProps {
  children: React.ReactNode;
  isSelected?: boolean;
  onClick?: () => void;
  className?: string;
  index?: number;
}

const AnimatedListItem: React.FC<AnimatedListItemProps> = ({
  children,
  isSelected = false,
  onClick,
  className = '',
  index = 0,
}) => {
  const controls = useAnimation();
  const previousSelectedRef = useRef<boolean>(isSelected);

  // Initial staggered animation
  useEffect(() => {
    controls.start({
      opacity: 1,
      y: 0,
      transition: {
        delay: index * 0.05, // Stagger based on index
        duration: 0.3,
        ease: 'easeOut',
      },
    });
  }, [controls, index]);

  // Animation when selection status changes
  useEffect(() => {
    if (isSelected !== previousSelectedRef.current) {
      if (isSelected) {
        controls.start({
          scale: [1, 1.02, 1],
          borderLeftWidth: '4px',
          transition: { duration: 0.2 },
        });
      } else {
        controls.start({
          scale: 1,
          borderLeftWidth: '0px',
          transition: { duration: 0.2 },
        });
      }
      previousSelectedRef.current = isSelected;
    }
  }, [isSelected, controls]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={controls}
      whileHover={{
        backgroundColor: 'rgba(0, 0, 0, 0.02)',
        transition: { duration: 0.1 },
      }}
      whileTap={{ scale: 0.98 }}
      className={`${className} ${isSelected ? 'border-l-primary bg-primary/10' : ''}`}
      onClick={onClick}
    >
      {children}
    </motion.div>
  );
};

export default AnimatedListItem;
