import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import FixEmailSummariesButton from './FixEmailSummariesButton';
import { VerifyEmailSummariesButton } from './VerifyEmailSummariesButton';

/**
 * Email Tools component that provides maintenance and troubleshooting tools
 * for fixing email summaries and related issues.
 */
export function EmailTools() {
  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Email Tools</CardTitle>
        <CardDescription className="text-xs">
          Maintenance tools for email processing
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3 pt-0">
        <div className="text-xs text-muted-foreground mb-2">
          If you notice issues with email summaries, use these tools to fix them:
        </div>

        {/* Fix Email Summaries button for immediate processing */}
        <div className="w-full">
          <FixEmailSummariesButton />
          <div className="text-xs text-muted-foreground mt-1">Fix emails with obvious errors</div>
        </div>

        {/* Verify Email Summaries button for more comprehensive scanning */}
        <div className="w-full">
          <VerifyEmailSummariesButton />
          <div className="text-xs text-muted-foreground mt-1">Deep scan to find hidden errors</div>
        </div>
      </CardContent>
    </Card>
  );
}

export default EmailTools;
