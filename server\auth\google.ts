/**
 * Google OAuth Service
 *
 * Handles Google-specific OAuth 2.0 flows, including URL generation,
 * callback processing, and token refreshing.
 */

import crypto from 'node:crypto';
import { type Auth, google } from 'googleapis';
import type { RedisClientType } from 'redis';
import type { User } from '../../shared/schema';
import { getEnvVar } from '../lib/environmentValidator';
import logger from '../lib/logger';
import { getRedisClient } from '../services/redis';
import { findOrCreateUser, type ProviderProfile } from './user.service';

// --- Configuration ---

const STATE_TOKEN_EXPIRY_SECONDS = 10 * 60; // 10 minutes

// --- State Management (CSRF Protection) ---

/**
 * Generates a secure, single-use state token and stores it in Redis.
 * This is a critical step for preventing CSRF attacks.
 * @param redis - The Redis client instance.
 * @param userId - Optional user ID to associate with the state.
 * @returns The generated state token.
 */
async function generateAndStoreStateToken(
  redis: RedisClientType,
  userId?: number
): Promise<string> {
  const token = crypto.randomBytes(32).toString('hex');
  const key = `csrf-state:${token}`;
  const value = userId ? String(userId) : 'anonymous'; // Store userId or a placeholder

  // Set the token with an expiration; it's a single-use token.
  await redis.setEx(key, STATE_TOKEN_EXPIRY_SECONDS, value);

  return token;
}

/**
 * Validates a given state token by checking its existence in Redis.
 * If the token exists, it is deleted to ensure it can only be used once.
 * @param redis - The Redis client instance.
 * @param token - The state token from the OAuth callback.
 * @returns A promise resolving to an object containing the token's validity and the associated userId.
 */
async function validateAndConsumeStateToken(
  redis: RedisClientType,
  token: string
): Promise<{ valid: boolean; userId?: number }> {
  const key = `csrf-state:${token}`;

  // Atomically get and delete the token
  const result = await redis.getDel(key);

  if (!result) {
    return { valid: false };
  }

  const userId = result === 'anonymous' ? undefined : Number(result);
  return { valid: true, userId };
}

// --- Cached Configuration ---
let googleOAuthConfig:
  | {
      clientId: string;
      clientSecret: string;
      scopes: string[];
    }
  | undefined;

function getBaseOAuthConfig() {
  if (googleOAuthConfig) {
    return googleOAuthConfig;
  }
  const clientId = getEnvVar('GOOGLE_CLIENT_ID');
  const clientSecret = getEnvVar('GOOGLE_CLIENT_SECRET');

  if (!clientId || !clientSecret) {
    logger.error(
      'Google OAuth environment variables (GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET) are not fully configured.'
    );
    throw new Error('Google OAuth credentials not configured.');
  }

  const config = {
    clientId,
    clientSecret,
    scopes: [
      'openid',
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/gmail.readonly',
      'https://www.googleapis.com/auth/gmail.modify',
    ],
  };
  googleOAuthConfig = config;
  return config;
}

function createOAuth2Client(redirectUri?: string): Auth.OAuth2Client {
  const { clientId, clientSecret } = getBaseOAuthConfig();
  // If no redirectUri is provided, it can be set later.
  // This is useful for flows that don't require an immediate redirect.
  return new google.auth.OAuth2(clientId, clientSecret, redirectUri);
}

// --- Public Service Functions ---

/**
 * Generates a Google OAuth authorization URL.
 */
export async function generateGoogleAuthUrl(
  req: import('express').Request,
  options: { userId?: number; promptConsent?: boolean } = {}
): Promise<string> {
  const redis = getRedisClient();
  const { scopes } = getBaseOAuthConfig();
  // Use the explicit client origin for the redirect URI
  const clientOrigin = process.env.CLIENT_ORIGIN;
  const redirectUri = `${clientOrigin}/api/auth/google/callback`;

  const oauth2Client = createOAuth2Client(redirectUri);

  const state = await generateAndStoreStateToken(redis, options.userId);

  const authUrlOptions: Auth.GenerateAuthUrlOpts = {
    access_type: 'offline',
    scope: scopes,
    state,
  };

  // Only force the consent prompt if explicitly requested.
  // This is crucial for a good user experience on subsequent logins.
  if (options.promptConsent) {
    authUrlOptions.prompt = 'consent';
  }

  return oauth2Client.generateAuthUrl(authUrlOptions);
}

/**
 * Handles the Google OAuth callback, exchanging a code for tokens
 * and creating or updating the user.
 */
export async function handleGoogleCallback(
  req: import('express').Request,
  code: string,
  state: string
): Promise<User> {
  const redis = getRedisClient();

  const stateValidation = await validateAndConsumeStateToken(redis, state);

  if (!stateValidation.valid) {
    throw new Error('Invalid or expired state token. Potential CSRF attack.');
  }

  // Use the explicit client origin for the redirect URI to match
  const clientOrigin = process.env.CLIENT_ORIGIN;
  const redirectUri = `${clientOrigin}/api/auth/google/callback`;
  const oauth2Client = createOAuth2Client(redirectUri);

  const { tokens } = await oauth2Client.getToken(code);
  oauth2Client.setCredentials(tokens);

  const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client });
  const { data: profile } = await oauth2.userinfo.get();

  if (!profile.email) {
    throw new Error('No email address found in Google profile.');
  }

  const userPayload: ProviderProfile = {
    email: profile.email,
    name: profile.name,
    picture: profile.picture,
    provider: 'google',
    tokens: tokens,
    // Associate with the user from the state if one existed
    // Note: You might need to adjust your user creation logic based on this
    existingUserId: stateValidation.userId,
  };

  return findOrCreateUser(userPayload);
}

/**
 * Refreshes a user's Google access token using their refresh token.
 */
export async function refreshGoogleTokens(refreshToken: string): Promise<Auth.Credentials> {
  if (!refreshToken) {
    throw new Error('Cannot refresh tokens without a refresh token.');
  }

  const oauth2Client = createOAuth2Client();
  oauth2Client.setCredentials({ refresh_token: refreshToken });

  const { credentials } = await oauth2Client.refreshAccessToken();
  return credentials;
}
