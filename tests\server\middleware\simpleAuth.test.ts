/**
 * Middleware: simpleAuth
 *
 * Test Cases
 * 1. Valid session authentication → next() is called, req.user is populated, req.authMethod === 'session'
 * 2. Invalid session (no user) with `required: true` → returns 401, next() not called
 *
 * NOTE:
 * - External dependencies `@server/storage` and `@server/auth/firebase` are mocked to avoid
 *   hitting the real database and Firebase SDK during unit tests.
 */

// ------------------------------
// Mocks MUST be declared before importing the module under test so that the
// dependency graph is patched correctly.
// ------------------------------

const mockGetUser = jest.fn();
const mockGetUserByFirebaseUid = jest.fn();

jest.mock('@server/storage', () => ({
  __esModule: true,
  storage: {
    getUser: (...args: unknown[]) => mockGetUser(...args),
    getUserByFirebaseUid: (...args: unknown[]) => mockGetUserByFirebaseUid(...args),
  },
}));

jest.mock('@server/auth/firebase', () => ({
  __esModule: true,
  verifyFirebaseToken: jest.fn(),
}));

import type { Request, Response, NextFunction } from 'express';
import { simpleAuth } from '@server/middleware/simpleAuth';

// ------------------------------
// Helper
// ------------------------------

type Mutable<T> = { -readonly [P in keyof T]: T[P] };

function createMockResponse() {
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
  } as unknown as Mutable<Response>;
  return res;
}

// ------------------------------
// Tests
// ------------------------------

describe('simpleAuth middleware', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should authenticate a user via session and populate req.user & req.authMethod', async () => {
    // Arrange
    const user = { id: 1, email: '<EMAIL>', role: 'user' } as any;
    mockGetUser.mockResolvedValueOnce(user);

    const req = {
      session: { userId: 1 },
      headers: {},
      path: '/test',
      method: 'GET',
    } as unknown as Mutable<Request>;

    const res = createMockResponse();
    const next = jest.fn() as NextFunction;

    // Act
    await simpleAuth({ required: true })(req, res, next);

    // Assert
    expect(mockGetUser).toHaveBeenCalledWith(1);
    expect(req.user).toEqual(user);
    expect(req.authMethod).toBe('session');
    expect(next).toHaveBeenCalledTimes(1);
    expect(res.status).not.toHaveBeenCalled();
  });

  it('should reject request with 401 when session is invalid and auth is required', async () => {
    // Arrange
    mockGetUser.mockResolvedValueOnce(undefined); // No user found

    const req = {
      session: { userId: 999 },
      headers: {},
      path: '/protected',
      method: 'GET',
    } as unknown as Mutable<Request>;

    const res = createMockResponse();
    const next = jest.fn() as NextFunction;

    // Act
    await simpleAuth({ required: true })(req, res, next);

    // Assert
    expect(mockGetUser).toHaveBeenCalledWith(999);
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith(
      expect.objectContaining({ code: 'AUTHENTICATION_REQUIRED' })
    );
    expect(next).not.toHaveBeenCalled();
  });
});
