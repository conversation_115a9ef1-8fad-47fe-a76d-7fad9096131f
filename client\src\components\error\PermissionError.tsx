import { ArrowLeft, FileQuestion, MessageSquareMore, ShieldAlert } from 'lucide-react';
import type React from 'react';
import { useLocation } from 'wouter';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

const SUPPORT_EMAIL = '<EMAIL>';

interface PermissionErrorProps {
  resource?: string;
  action?: string;
  message?: string;
  requiredTier?: string;
  contactEmail?: string;
  onGoBack?: () => void;
}

/**
 * Permission Error component for handling 403 Forbidden errors
 * Shows a clear message about missing permissions and provides action options
 */
export const PermissionError: React.FC<PermissionErrorProps> = ({
  resource = 'resource',
  action = 'access',
  message,
  requiredTier,
  contactEmail = SUPPORT_EMAIL,
  onGoBack,
}) => {
  const [, navigate] = useLocation();

  const handleGoBack = () => {
    if (onGoBack) {
      onGoBack();
    } else {
      // Use browser's back functionality as fallback
      window.history.back();
    }
  };

  const handleUpgrade = () => {
    navigate('/settings?tab=account&action=upgrade');
  };

  const handleContactSupport = () => {
    const subject = encodeURIComponent('Permission Issue');
    const body = encodeURIComponent(`I'm having trouble accessing ${resource}`);
    const mailtoUrl = `mailto:${contactEmail}?subject=${subject}&body=${body}`;
    
    // mailto links are acceptable for external navigation
    window.location.href = mailtoUrl;
  };

  return (
    <div className="max-w-lg mx-auto p-4">
      <div className="bg-white shadow-sm rounded-lg border p-6">
        <div className="flex items-center justify-center mb-6">
          <div className="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center text-red-500">
            <ShieldAlert size={32} />
          </div>
        </div>

        <h2 className="text-xl font-semibold text-center mb-2">Permission Denied</h2>

        <Alert variant="destructive" className="mb-4">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>Access Restricted</AlertTitle>
          <AlertDescription>
            {message || `You don't have permission to ${action} this ${resource}.`}
          </AlertDescription>
        </Alert>

        {requiredTier && (
          <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-4">
            <div className="flex">
              <FileQuestion className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="font-medium text-amber-800">Upgrade Required</h3>
                <p className="text-sm text-amber-700 mt-1">
                  This feature requires the <strong>{requiredTier}</strong> plan. Upgrade your
                  account to gain access to this and other premium features.
                </p>
                <Button
                  onClick={handleUpgrade}
                  className="mt-3 bg-amber-600 hover:bg-amber-700 text-white"
                  size="sm"
                >
                  Upgrade Now
                </Button>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-3">
          <div className="flex justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={handleGoBack}
              className="flex items-center"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleContactSupport}
              className="flex items-center"
            >
              <MessageSquareMore className="mr-2 h-4 w-4" />
              Contact Support
            </Button>
          </div>

          <div className="text-xs text-gray-500 mt-4 pt-3 border-t">
            <p>
              If you believe this is an error, please contact our support team at{' '}
              <a href={`mailto:${contactEmail}`} className="text-blue-600 hover:underline">
                {contactEmail}
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
