@import "tailwindcss";

@layer base {
  :root {
    /* Base colors */
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;
    
    /* Card and surfaces */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    
    /* Brand colors */
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    
    /* Secondary colors */
    --secondary: 214 32% 91%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    
    /* UI element colors */
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    
    /* Forms and inputs */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    
    /* Success and warning colors */
    --success: 142 71% 45%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 210 40% 98%;
    --info: 221 83% 53%;
    --info-foreground: 210 40% 98%;
    
    /* Email priority colors */
    --priority-high: 350 89% 60%;
    --priority-medium: 217 91% 60%;
    --priority-low: 142 71% 45%;
    
    /* Border radius */
    --radius: 0.5rem;
  }
 
  .dark {
    /* Base colors */
    --background: 0 0% 3.9%;
    --foreground: 210 40% 98%;
    
    /* Card and surfaces */
    --card: 0 0% 7%;
    --card-foreground: 210 40% 98%;
    --popover: 0 0% 7%;
    --popover-foreground: 210 40% 98%;
    
    /* Brand colors */
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    
    /* Secondary colors */
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    
    /* UI element colors */
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    
    /* Forms and inputs */
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    /* Success and warning colors */
    --success: 142 71% 45%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 210 40% 98%;
    --info: 221 83% 53%;
    --info-foreground: 210 40% 98%;
    
    /* Email priority colors */
    --priority-high: 350 89% 60%;
    --priority-medium: 217 91% 60%;
    --priority-low: 142 71% 45%;
  }

  * {
    border-color: hsl(var(--border));
    max-width: 100%;
  }
  
  html {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
    overflow-x: hidden;
    max-width: 100vw;
    touch-action: manipulation;
  }

  body {
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    -webkit-font-smoothing: antialiased;
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    overflow-x: hidden;
    max-width: 100vw;
    -moz-osx-font-smoothing: grayscale;
    position: relative;
  }

  /* Ensure proper height handling for app layout */
  body.app-layout-active {
    @apply h-screen overflow-hidden;
  }
}

@layer components {
  /* Header styles - used by AppHeader component */
  .app-header {
    background-color: hsl(var(--card));
    border-bottom: 1px solid hsl(var(--border));
    padding: 0.5rem 0.75rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  }
  
  @media (min-width: 640px) {
    .app-header {
      padding: 0.75rem 1rem;
    }
  }
  
  /* Consistent spacing utilities - used by Section component */
  .space-section {
    @apply mb-6 sm:mb-8;
  }
  
  /* Email item styles - used by EmailItem component */
  .email-item {
    padding: 0.625rem;
    border-bottom: 1px solid hsl(var(--border));
    cursor: pointer;
    transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }
  
  @media (min-width: 640px) {
    .email-item {
      padding: 1rem;
    }
  }
  
  .email-item:hover {
    background-color: hsl(var(--accent) / 0.4);
  }
  
  .email-item:active {
    background-color: hsl(var(--accent) / 0.6);
  }
  
  .email-item.selected {
    background-color: hsl(var(--accent));
    border-left: 2px solid hsl(var(--primary));
    padding-left: calc(0.625rem - 2px);
  }
  
  @media (min-width: 640px) {
    .email-item.selected {
      padding-left: calc(1rem - 2px);
    }
  }
  
  .email-item.unread {
    @apply font-medium;
  }
  
  .email-item.read {
    @apply opacity-80;
  }
  
  /* Add focus styles for keyboard navigation */
  .email-item:focus-visible {
    outline: none;
    z-index: 10;
    box-shadow: 0 0 0 2px hsl(var(--primary) / 0.5);
  }
  
  /* Priority badges - used by UrgencyBadge component */
  .badge-priority-high {
    background-color: hsl(var(--priority-high) / 0.1);
    color: hsl(var(--priority-high));
    border-color: hsl(var(--priority-high));
  }
  
  .badge-priority-medium {
    background-color: hsl(var(--priority-medium) / 0.1);
    color: hsl(var(--priority-medium));
    border-color: hsl(var(--priority-medium));
  }
  
  .badge-priority-low {
    background-color: hsl(var(--priority-low) / 0.1);
    color: hsl(var(--priority-low));
    border-color: hsl(var(--priority-low));
  }
  
  /* Status indicators - used by various components */
  .status-indicator {
    @apply inline-block h-2 w-2 rounded-full;
  }
  
  .status-processing {
    background-color: hsl(var(--warning));
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .status-error {
    background-color: hsl(var(--destructive));
  }
  
  .status-success {
    background-color: hsl(var(--success));
  }
}

/* Email HTML content styles - used by email display components */
.email-html-content {
  @apply max-w-full overflow-hidden relative text-sm sm:text-base leading-normal;
  width: 100% !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

.email-html-content img {
  @apply max-w-full h-auto rounded-sm my-2;
  width: auto !important;
  max-width: 100% !important;
}

.email-html-content a {
  color: hsl(var(--primary));
  word-break: break-word;
  transition: color 150ms cubic-bezier(0.4, 0, 0.2, 1);
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.email-html-content a:hover {
  color: hsl(var(--primary) / 0.8);
  text-decoration: underline;
}

.email-html-content a:focus-visible {
  box-shadow: 0 0 0 2px hsl(var(--primary)), 0 0 0 4px hsl(var(--primary) / 0.2);
}

.dark .email-html-content a {
  color: hsl(var(--primary) / 0.9);
}

.dark .email-html-content a:hover {
  color: hsl(var(--primary) / 0.7);
}

.email-html-content * {
  max-width: 100% !important;
  box-sizing: border-box !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

.email-html-content p,
.email-html-content div {
  @apply mb-3;
  width: auto !important;
  max-width: 100% !important;
  white-space: normal !important;
}

.email-html-content table {
  @apply mb-3 w-full max-w-full;
  overflow-x: auto !important;
  display: block !important;
  table-layout: auto !important;
}

.email-html-content blockquote {
  padding-left: 1rem;
  border-left: 4px solid hsl(var(--muted));
  font-style: italic;
  color: hsl(var(--muted-foreground));
  margin: 0.5rem 0;
  max-width: 100% !important;
}

.email-html-content td,
.email-html-content th {
  padding: 0.5rem;
  border: 1px solid hsl(var(--border));
  max-width: 100% !important;
  white-space: normal !important;
}

.email-html-content [style*="position: absolute"] {
  position: relative !important;
}

.email-html-content iframe {
  max-width: 100% !important;
  height: auto !important;
}

.email-html-content pre {
  white-space: pre-wrap;
  overflow-x: auto;
  max-width: 100%;
  padding: 0.5rem;
  background-color: hsl(var(--muted) / 0.3);
  border-radius: 0.125rem;
  margin: 0.5rem 0;
  white-space: pre-wrap !important;
}

.email-html-content code {
  background-color: hsl(var(--muted) / 0.5);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  white-space: pre-wrap;
  word-break: break-all;
  max-width: 100% !important;
}

/* Standardized email content - used by StandardizedEmailContent component */
.standardized-email-content {
  @apply w-full max-w-full overflow-hidden px-1 sm:px-2;
}

.email-content-body {
  @apply w-full max-w-full overflow-hidden break-words text-[11px] sm:text-xs md:text-sm leading-relaxed;
  word-wrap: break-word !important;
}

.standardized-email-content img {
  max-width: 100% !important;
  height: auto !important;
  margin: 0.5rem auto;
}

.standardized-email-content table {
  max-width: 100% !important;
  font-size: 0.7rem !important;
  display: block;
  overflow-x: auto;
}

.standardized-email-content p,
.standardized-email-content div,
.standardized-email-content span,
.standardized-email-content a {
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

/* External content warning - used by StandardizedEmailContent component */
.external-content-warning {
  padding: 0.375rem;
  font-size: 10px;
  border-radius: 0.375rem;
  background-color: hsl(var(--warning) / 0.1);
  color: hsl(var(--warning-foreground));
  border: 1px solid hsl(var(--warning) / 0.2);
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  max-width: 100%;
  overflow: hidden;
}

@media (min-width: 640px) {
  .external-content-warning {
    padding: 0.5rem;
    font-size: 0.75rem;
    line-height: 1rem;
    margin-top: 0.75rem;
    gap: 0.5rem;
  }
}

/* Email summary styling - used by EmailItem component */
.email-summary {
  @apply max-w-full overflow-hidden text-xs;
  max-height: 2.5rem;
  word-break: break-word;
}

.email-summary-inner {
  @apply line-clamp-2 max-w-full break-words;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

/* Email detail container - used by EmailDetail component */
.email-detail-container {
  @apply w-full overflow-hidden;
}

.email-detail-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: hsl(var(--card) / 0.95);
  backdrop-filter: blur(8px);
}

/* CSS containment for performance - used by layout components */
.contain-content {
  contain: content;
}

.contain-strict {
  contain: strict;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  /* Improve mobile table rendering in emails */
  .email-html-content table,
  .standardized-email-content table {
    @apply table-fixed w-full;
  }
  
  .email-html-content th,
  .email-html-content td,
  .standardized-email-content th,
  .standardized-email-content td {
    @apply break-words max-w-[50vw] text-xs;
    word-break: break-word;
  }
  
  /* Ensure inputs are properly sized on mobile to prevent iOS zoom */
  input, select, textarea {
    font-size: 16px !important;
  }
  
  /* Improve touch targets for better mobile interaction */
  .clickable, a, button, [role="button"], 
  input[type="checkbox"], input[type="radio"],
  select, .select-trigger {
    @apply min-h-[38px] min-w-[38px];
  }
  
  /* Make sure buttons have proper touch targets */
  button, .button {
    @apply text-xs py-1.5 px-2;
    min-height: 32px;
    touch-action: manipulation;
  }
}

/* Extra small device optimizations */
@media (max-width: 375px) {
  .email-html-content, 
  .standardized-email-content {
    @apply text-xs;
    font-size: 0.75rem !important;
  }
}