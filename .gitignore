# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist
dist-ssr

# Environment files
.env
.env*.local
.env.development.local
.env.test.local
.env.production.local
.env~
/server/.env
/client/.env
/config/.env

# Log files
*.log
logs/
server.log
startup*.log
error.log

# System files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Testing
coverage/
.nyc_output/
test-results.json

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Generated files
vite.config.ts.*
*.tar.gz

# Application specific
server/public/

# Temporary test files
test-production.js

# Ignored files
service-account.json
.vercel
nul
