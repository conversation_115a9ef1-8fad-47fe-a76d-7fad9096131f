import crypto from 'node:crypto';
import type { Request } from 'express';

// ---------------------------
// Mocks
// ---------------------------

// Stub environment variables required by the Google OAuth service
process.env.GOOGLE_CLIENT_ID = 'TEST_CLIENT_ID';
process.env.GOOGLE_CLIENT_SECRET = 'TEST_CLIENT_SECRET';

// ---- Redis Mock ----
const mockSetEx = jest.fn();
const mockGetDel = jest.fn();
const mockRedis = {
  setEx: mockSetEx,
  getDel: mockGetDel,
};

jest.mock('../../../server/services/redis', () => ({
  __esModule: true,
  getRedisClient: jest.fn(() => mockRedis),
}));

// ---- googleapis Mock ----
const mockGenerateAuthUrl = jest.fn();
const mockGetToken = jest.fn();
const mockSetCredentials = jest.fn();
const mockRefreshAccessToken = jest.fn();
const mockOAuth2Client = {
  generateAuthUrl: mockGenerateAuthUrl,
  getToken: mockGetToken,
  setCredentials: mockSetCredentials,
  refreshAccessToken: mockRefreshAccessToken,
};
const mockUserInfoGet = jest.fn();
const mockOauth2 = jest.fn(() => ({
  userinfo: { get: mockUserInfoGet },
}));

jest.mock('googleapis', () => ({
  __esModule: true,
  google: {
    auth: {
      OAuth2: jest.fn(() => mockOAuth2Client),
    },
    oauth2: mockOauth2,
  },
}));

// ---- findOrCreateUser Mock ----
const mockFindOrCreateUser = jest.fn();
jest.mock('../../../server/auth/user.service', () => ({
  __esModule: true,
  findOrCreateUser: mockFindOrCreateUser,
}));

// ---- crypto Mock ----
// Provide a deterministic randomBytes for state token generation
jest.mock('node:crypto', () => ({
  __esModule: true,
  default: {
    randomBytes: jest.fn(() => Buffer.from('statetoken')),
  },
}));

// ---- environmentValidator Mock ----
jest.mock('../../../server/lib/environmentValidator', () => ({
  __esModule: true,
  getEnvVar: (key: string) => {
    // Provide only the vars used by google.ts; return empty string for others
    if (key === 'GOOGLE_CLIENT_ID') return 'TEST_CLIENT_ID';
    if (key === 'GOOGLE_CLIENT_SECRET') return 'TEST_CLIENT_SECRET';
    return '';
  },
}));

// ---------------------------
// Import module under test AFTER mocks
// ---------------------------
import {
  generateGoogleAuthUrl,
  handleGoogleCallback,
  refreshGoogleTokens,
} from '../../../server/auth/google';

// ---------------------------
// Test Suite
// ---------------------------

describe('Google OAuth Service', () => {
  const baseReq = {
    protocol: 'https',
    get: jest.fn().mockImplementation((header: string) => {
      if (header === 'host') return 'example.com';
      return '';
    }),
  } as unknown as Request;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // ------------------------------------------------------------
  // OAuth Flow Initiation
  // ------------------------------------------------------------
  it('should generate an OAuth URL and store a state token', async () => {
    mockGenerateAuthUrl.mockImplementation(({ state }) => `https://auth.url?state=${state}`);

    const url = await generateGoogleAuthUrl(baseReq, { userId: 42 });

    expect(url).toBe('https://auth.url?state=7374617465746f6b656e'); // hex of "statetoken"

    // Redis interaction
    expect(mockSetEx).toHaveBeenCalledTimes(1);
    const [key, expiry, value] = mockSetEx.mock.calls[0];
    expect(key).toMatch(/^csrf-state:/);
    expect(expiry).toBeGreaterThan(0);
    expect(value).toBe('42');

    // OAuth2 client should be asked to generate URL with same state
    expect(mockGenerateAuthUrl).toHaveBeenCalledWith(
      expect.objectContaining({ state: expect.any(String) })
    );
  });

  // ------------------------------------------------------------
  // Callback Handling – Successful Path
  // ------------------------------------------------------------
  it('should exchange code for tokens, extract profile, and provision user', async () => {
    // Arrange
    mockGetDel.mockResolvedValue('anonymous');
    const tokens = {
      access_token: 'ACCESS',
      refresh_token: 'REFRESH',
      id_token: 'ID',
      expiry_date: Date.now() + 3600 * 1000,
    } as any;
    mockGetToken.mockResolvedValue({ tokens });

    const profile = {
      email: '<EMAIL>',
      name: 'Test User',
      picture: 'pic.jpg',
    };
    mockUserInfoGet.mockResolvedValue({ data: profile });

    const provisionedUser = {
      id: 99,
      email: profile.email,
      name: profile.name,
    } as any;
    mockFindOrCreateUser.mockResolvedValue(provisionedUser);

    // Act
    const result = await handleGoogleCallback(baseReq, 'AUTH_CODE', 'any_state');

    // Assert
    expect(mockGetDel).toHaveBeenCalledTimes(1);
    expect(mockGetToken).toHaveBeenCalledWith('AUTH_CODE');
    expect(mockUserInfoGet).toHaveBeenCalledTimes(1);
    expect(mockFindOrCreateUser).toHaveBeenCalledWith(
      expect.objectContaining({
        email: profile.email,
        provider: 'google',
        tokens,
      })
    );
    expect(result).toBe(provisionedUser);
  });

  // ------------------------------------------------------------
  // Callback Handling – Invalid State Token
  // ------------------------------------------------------------
  it('should throw an error for invalid or expired state token', async () => {
    mockGetDel.mockResolvedValue(null); // token not found

    await expect(handleGoogleCallback(baseReq, 'AUTH_CODE', 'bad_state')).rejects.toThrow(
      /Invalid or expired state token/
    );
  });

  // ------------------------------------------------------------
  // Token Refreshing
  // ------------------------------------------------------------
  it('should refresh Google tokens given a valid refresh token', async () => {
    const refreshedCreds = { access_token: 'NEW_TOKEN' } as any;
    mockRefreshAccessToken.mockResolvedValue({ credentials: refreshedCreds });

    const result = await refreshGoogleTokens('REFRESH_TOKEN');

    expect(mockOAuth2Client.setCredentials).toHaveBeenCalledWith({ refresh_token: 'REFRESH_TOKEN' });
    expect(mockRefreshAccessToken).toHaveBeenCalledTimes(1);
    expect(result).toEqual(refreshedCreds);
  });

  it('should throw an error when no refresh token is provided to refreshGoogleTokens', async () => {
    await expect(refreshGoogleTokens('')).rejects.toThrow(/Cannot refresh tokens/);
  });
}); 