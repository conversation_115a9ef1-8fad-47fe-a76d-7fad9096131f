import { Router, type Request, type Response } from 'express';
import logger from '../lib/logger';
import { ErrorCode, sendError, sendSuccess } from '../lib/standardizedResponses';
import { catchAsync } from '../utils/errorHandler';
import { unifiedEmailProcessing, generateReplyWithGemini, explainCategorizationWithGemini } from '../services/gemini';
import { storage } from '../storage';
import { EmailService } from '../services/email-v2';

const router = Router();
const emailService = new EmailService();

/**
 * Regenerate the AI-generated summary for a single email.
 * POST /api/ai/regenerate-summary/by-message-id/:messageId
 *
 * Response: { summary: string }
 */
router.post(
  '/regenerate-summary/by-message-id/:messageId',
  catchAsync(async (req: Request, res: Response) => {
    const timer = logger.startTimer();
    const operationId = `regenerate-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    const userId = req.user!.id;
    const messageId = req.params.messageId;

    logger.ai.operationStart('regenerate-summary', operationId, {
      userId,
      messageId,
      endpoint: '/api/ai/regenerate-summary'
    });

    const email = await emailService.getEmailByMessageId(userId, messageId);

    if (!email) {
      const duration = timer();
      const error = new Error('Email not found or access denied');
      logger.ai.operationFail('regenerate-summary', operationId, error, duration, {
        userId,
        messageId,
        reason: 'email_not_found_or_unauthorized'
      });
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found or access denied', { messageId });
    }

    logger.ai.info('Processing email regeneration request', {
      operationId,
      userId,
      messageId,
      subject: email.subject?.substring(0, 50) + '...',
      contentLength: email.originalContent?.length || 0
    });

    try {
      // Use the unified processing function
      const aiResult = await unifiedEmailProcessing(
        email.originalContent || '',
        email.subject || '',
        email.sender || 'Unknown'
      );

      // Update the email with new AI data
      await storage.updateEmailByMessageId(userId, messageId, {
        summary: aiResult.summary,
        categories: aiResult.categories as any,
        aiReply: aiResult.reply,
      });

      const duration = timer();
      logger.ai.operationComplete('regenerate-summary', operationId, duration, {
        userId,
        messageId,
        summaryLength: aiResult.summary.length,
        categoriesCount: aiResult.categories.length,
        replyLength: aiResult.reply.length,
        hasError: !!aiResult.error
      });

      logger.email.info('Successfully regenerated email summary', undefined, userId, {
        messageId,
        operationId,
        summary: aiResult.summary.substring(0, 50) + '...',
        categories: aiResult.categories
      });

      return sendSuccess(res, {
        summary: aiResult.summary,
        categories: aiResult.categories,
        reply: aiResult.reply,
        ...(aiResult.error && { error: aiResult.error }),
      });
    } catch (error) {
      const duration = timer();
      logger.ai.operationFail('regenerate-summary', operationId, error, duration, {
        userId,
        messageId,
        errorType: error instanceof Error ? error.name : 'unknown',
        subject: email.subject?.substring(0, 50) + '...'
      });

      logger.email.error('Failed to regenerate summary', error, undefined, userId, {
        messageId,
        operationId,
        subject: email.subject,
        contentLength: email.originalContent?.length || 0
      });

      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to regenerate summary');
    }
  })
);

/**
 * Generate an AI reply for a single email.
 * POST /api/ai/generate-reply/:messageId
 *
 * Response: { reply: string, fromCache: boolean }
 */
router.post(
  '/generate-reply/:messageId',
  catchAsync(async (req: Request, res: Response) => {
    const timer = logger.startTimer();
    const operationId = `generate-reply-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    const userId = req.user!.id;
    const messageId = req.params.messageId;

    logger.ai.operationStart('generate-reply', operationId, {
      userId,
      messageId,
      endpoint: '/api/ai/generate-reply'
    });

    const email = await emailService.getEmailByMessageId(userId, messageId);

    if (!email) {
      const duration = timer();
      const error = new Error('Email not found or access denied');
      logger.ai.operationFail('generate-reply', operationId, error, duration, {
        userId,
        messageId,
        reason: 'email_not_found_or_unauthorized'
      });
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found or access denied');
    }

    // Check if reply already exists
    if (email.aiReply && email.aiReply.trim() !== '') {
      const duration = timer();
      logger.ai.operationComplete('generate-reply', operationId, duration, {
        userId,
        messageId,
        fromCache: true,
        replyLength: email.aiReply.length
      });
      return sendSuccess(res, { reply: email.aiReply, fromCache: true });
    }

    logger.ai.info('Generating new AI reply', {
      operationId,
      userId,
      messageId,
      subject: email.subject?.substring(0, 50) + '...',
      contentLength: email.originalContent?.length || 0
    });

    try {
      // Generate new reply
      const reply = await generateReplyWithGemini(
        email.originalContent || '',
        email.subject || '',
        email.sender || 'Unknown'
      );

      // Update the email with the new reply
      await storage.updateEmailByMessageId(userId, messageId, { aiReply: reply });

      const duration = timer();
      logger.ai.operationComplete('generate-reply', operationId, duration, {
        userId,
        messageId,
        fromCache: false,
        replyLength: reply.length
      });

      return sendSuccess(res, { reply, fromCache: false });
    } catch (error) {
      const duration = timer();
      logger.ai.operationFail('generate-reply', operationId, error, duration, {
        userId,
        messageId,
        errorType: error instanceof Error ? error.name : 'unknown',
        subject: email.subject?.substring(0, 50) + '...'
      });

      logger.email.error('Failed to generate reply', error, undefined, userId, {
        messageId,
        operationId,
        subject: email.subject,
        contentLength: email.originalContent?.length || 0
      });

      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to generate AI reply');
    }
  })
);

/**
 * Regenerate an AI reply for a single email with a specific tone.
 * POST /api/ai/regenerate-reply/by-message-id/:messageId
 *
 * Body: { tone: string }
 * Response: string (the new reply)
 */
router.post(
  '/regenerate-reply/by-message-id/:messageId',
  catchAsync(async (req: Request, res: Response) => {
    const timer = logger.startTimer();
    const operationId = `regenerate-reply-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    const userId = req.user!.id;
    const messageId = req.params.messageId;
    const { tone = 'professional' } = req.body;

    logger.ai.operationStart('regenerate-reply', operationId, {
      userId,
      messageId,
      tone,
      endpoint: '/api/ai/regenerate-reply'
    });

    const email = await emailService.getEmailByMessageId(userId, messageId);

    if (!email) {
      const duration = timer();
      const error = new Error('Email not found or access denied');
      logger.ai.operationFail('regenerate-reply', operationId, error, duration, {
        userId,
        messageId,
        reason: 'email_not_found_or_unauthorized'
      });
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found or access denied');
    }

    logger.ai.info('Regenerating AI reply with tone', {
      operationId,
      userId,
      messageId,
      tone,
      subject: email.subject?.substring(0, 50) + '...',
      contentLength: email.originalContent?.length || 0
    });

    try {
      // Generate new reply with specified tone
      const reply = await generateReplyWithGemini(
        email.originalContent || '',
        email.subject || '',
        email.sender || 'Unknown',
        tone
      );

      // Update the email with the new reply
      await storage.updateEmailByMessageId(userId, messageId, { aiReply: reply });

      const duration = timer();
      logger.ai.operationComplete('regenerate-reply', operationId, duration, {
        userId,
        messageId,
        tone,
        replyLength: reply.length
      });

      return sendSuccess(res, reply);
    } catch (error) {
      const duration = timer();
      logger.ai.operationFail('regenerate-reply', operationId, error, duration, {
        userId,
        messageId,
        tone,
        errorType: error instanceof Error ? error.name : 'unknown',
        subject: email.subject?.substring(0, 50) + '...'
      });

      logger.email.error('Failed to regenerate reply', error, undefined, userId, {
        messageId,
        operationId,
        tone,
        subject: email.subject,
        contentLength: email.originalContent?.length || 0
      });

      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to regenerate AI reply');
    }
  })
);

/**
 * Explain why an email was categorized in a certain way.
 * POST /api/ai/explain-categories/by-message-id/:messageId
 *
 * Response: string (explanation)
 */
router.post(
  '/explain-categories/by-message-id/:messageId',
  catchAsync(async (req: Request, res: Response) => {
    const timer = logger.startTimer();
    const operationId = `explain-categories-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    const userId = req.user!.id;
    const messageId = req.params.messageId;

    logger.ai.operationStart('explain-categories', operationId, {
      userId,
      messageId,
      endpoint: '/api/ai/explain-categories'
    });

    const email = await emailService.getEmailByMessageId(userId, messageId);

    if (!email) {
      const duration = timer();
      const error = new Error('Email not found or access denied');
      logger.ai.operationFail('explain-categories', operationId, error, duration, {
        userId,
        messageId,
        reason: 'email_not_found_or_unauthorized'
      });
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found or access denied');
    }

    logger.ai.info('Explaining email categorization', {
      operationId,
      userId,
      messageId,
      subject: email.subject?.substring(0, 50) + '...',
      contentLength: email.originalContent?.length || 0
    });

    try {
      // Generate explanation (dummy for now)
      const explanation = `This email was categorized as '${email.categories?.join(', ') || 'uncategorized'}' based on its content.`;

      const duration = timer();
      logger.ai.operationComplete('explain-categories', operationId, duration, {
        userId,
        messageId,
        explanationLength: explanation.length
      });

      return sendSuccess(res, explanation);
    } catch (error) {
      const duration = timer();
      logger.ai.operationFail('explain-categories', operationId, error, duration, {
        userId,
        messageId,
        errorType: error instanceof Error ? error.name : 'unknown',
        subject: email.subject?.substring(0, 50) + '...'
      });

      logger.email.error('Failed to explain categories', error, undefined, userId, {
        messageId,
        operationId,
        subject: email.subject,
        contentLength: email.originalContent?.length || 0
      });

      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to explain categories');
    }
  })
);

export default router;