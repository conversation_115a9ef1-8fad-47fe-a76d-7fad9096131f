import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Check, Edit, Loader2, Plus, RefreshCw, Tag, Trash2, X } from 'lucide-react';
import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { toast } from '@/hooks/use-toast';
import apiClient from '@/lib/apiClient';

type GmailLabelSelectorProps = {
  emailId: string;
  currentLabels?: string[];
  readOnly?: boolean;
};

type GmailLabel = {
  id: string;
  name: string;
  type?: string;
};

export function GmailLabelSelector({
  emailId,
  currentLabels = [],
  readOnly = false,
}: GmailLabelSelectorProps) {
  const [newLabelName, setNewLabelName] = useState('');
  const [editingLabel, setEditingLabel] = useState<GmailLabel | null>(null);
  const [isCreatingLabel, setIsCreatingLabel] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  // Fetch Gmail Labels
  const {
    data: labels = [],
    isLoading,
    isError,
    refetch,
  } = useQuery<GmailLabel[]>({
    queryKey: ['/api/providers/gmail/labels'],
    staleTime: 60 * 1000, // 1 minute
    refetchOnWindowFocus: false,
  });

  const createLabelMutation = useMutation<GmailLabel, Error, string>({
    mutationFn: (name) => apiClient.post('/api/providers/gmail/labels', { name }),
    onSuccess: () => {
      setNewLabelName('');
      setIsCreatingLabel(false);
      queryClient.invalidateQueries({ queryKey: ['/api/providers/gmail/labels'] });
      toast({
        title: 'Label created',
        description: 'The label has been created successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to create label',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const updateLabelMutation = useMutation<GmailLabel, Error, { id: string; name: string }>({
    mutationFn: ({ id, name }) => apiClient.put(`/api/providers/gmail/labels/${id}`, { name }),
    onSuccess: () => {
      setEditingLabel(null);
      setIsEditDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/providers/gmail/labels'] });
      toast({
        title: 'Label updated',
        description: 'The label has been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to update label',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const deleteLabelMutation = useMutation<void, Error, string>({
    mutationFn: (id) => apiClient.delete(`/api/providers/gmail/labels/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/providers/gmail/labels'] });
      toast({
        title: 'Label deleted',
        description: 'The label has been deleted successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to delete label',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const addLabelMutation = useMutation<void, Error, string>({
    mutationFn: (labelId) => apiClient.post(`/api/emails/${emailId}/labels`, { labelId }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['emails', 'detail', emailId] });
      toast({
        title: 'Label added',
        description: 'The label has been added to the email.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to add label',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const removeLabelMutation = useMutation<void, Error, string>({
    mutationFn: (labelId) => apiClient.delete(`/api/emails/${emailId}/labels/${labelId}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['emails', 'detail', emailId] });
      toast({
        title: 'Label removed',
        description: 'The label has been removed from the email.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to remove label',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Filter system labels
  const userLabels = labels.filter((label) => label.type !== 'system');

  // Handle creating a new label
  const handleCreateLabel = () => {
    if (!newLabelName.trim()) {
      toast({
        title: 'Label name required',
        description: 'Please enter a name for the label.',
        variant: 'destructive',
      });
      return;
    }
    createLabelMutation.mutate(newLabelName.trim());
  };

  // Handle updating a label
  const handleUpdateLabel = () => {
    if (!editingLabel || !editingLabel.name.trim()) {
      toast({
        title: 'Label name required',
        description: 'Please enter a name for the label.',
        variant: 'destructive',
      });
      return;
    }
    updateLabelMutation.mutate({
      id: editingLabel.id,
      name: editingLabel.name.trim(),
    });
  };

  // Check if a label is already applied to the email
  const isLabelApplied = (labelId: string) => {
    return currentLabels.includes(labelId);
  };

  // Toggle a label on the email
  const toggleLabel = (labelId: string) => {
    if (isLabelApplied(labelId)) {
      removeLabelMutation.mutate(labelId);
    } else {
      addLabelMutation.mutate(labelId);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>Loading labels...</span>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center gap-2 text-sm text-destructive">
        <span>Failed to load labels.</span>
        <Button variant="ghost" size="sm" onClick={() => refetch()}>
          <RefreshCw className="h-4 w-4 mr-1" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="mb-4">
      <div className="flex flex-wrap gap-2 mb-2">
        {userLabels
          .filter((label: GmailLabel) => isLabelApplied(label.id))
          .map((label: GmailLabel) => (
            <Badge key={label.id} variant="outline" className="flex items-center gap-1">
              <Tag className="h-3 w-3" />
              {label.name}
              {!readOnly && (
                <button
                  type="button"
                  className="ml-1 hover:text-destructive"
                  onClick={() => removeLabelMutation.mutate(label.id)}
                  aria-label={`Remove ${label.name} label`}
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </Badge>
          ))}
      </div>

      {!readOnly && (
        <div className="flex items-center gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="gap-1">
                <Tag className="h-4 w-4" />
                Manage Labels
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-4">
              <h4 className="text-sm font-medium mb-2">Labels</h4>

              {/* Create new label section */}
              {isCreatingLabel ? (
                <div className="flex items-center gap-2 mb-3">
                  <Input
                    value={newLabelName}
                    onChange={(e) => setNewLabelName(e.target.value)}
                    placeholder="Enter label name"
                    className="h-8 text-sm"
                    autoFocus
                  />
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-8 w-8"
                    disabled={createLabelMutation.isPending}
                    onClick={handleCreateLabel}
                  >
                    {createLabelMutation.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Check className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-8 w-8"
                    onClick={() => setIsCreatingLabel(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  className="mb-3"
                  onClick={() => setIsCreatingLabel(true)}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  New Label
                </Button>
              )}

              {/* Label list */}
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {userLabels.length === 0 ? (
                  <p className="text-sm text-muted-foreground">
                    No labels found. Create one to get started.
                  </p>
                ) : (
                  userLabels.map((label: GmailLabel) => (
                    <div key={label.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id={`label-${label.id}`}
                          checked={isLabelApplied(label.id)}
                          onChange={() => toggleLabel(label.id)}
                          className="h-4 w-4"
                        />
                        <label htmlFor={`label-${label.id}`} className="text-sm cursor-pointer">
                          {label.name}
                        </label>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-6 w-6">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="lucide lucide-more-horizontal"
                            >
                              <title>More actions</title>
                              <circle cx="12" cy="12" r="1" />
                              <circle cx="19" cy="12" r="1" />
                              <circle cx="5" cy="12" r="1" />
                            </svg>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setEditingLabel(label);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => deleteLabelMutation.mutate(label.id)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  ))
                )}
              </div>
            </PopoverContent>
          </Popover>

          {/* Edit label dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Label</DialogTitle>
                <DialogDescription>Update the name of your Gmail label.</DialogDescription>
              </DialogHeader>
              <div className="py-4">
                <Input
                  value={editingLabel?.name || ''}
                  onChange={(e) =>
                    setEditingLabel((prev) => (prev ? { ...prev, name: e.target.value } : null))
                  }
                  placeholder="Enter label name"
                  className="w-full"
                />
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleUpdateLabel} disabled={updateLabelMutation.isPending}>
                  {updateLabelMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    'Save Changes'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      )}
    </div>
  );
}
