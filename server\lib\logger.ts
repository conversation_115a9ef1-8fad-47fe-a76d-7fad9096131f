// Store request context in AsyncLocalStorage
import { AsyncLocalStorage } from 'node:async_hooks';
import { createLogger, format, transports } from 'winston';

const contextStorage = new AsyncLocalStorage<Map<string, any>>();

// Determine environment - direct access to avoid circular dependency
const isDevelopment = process.env.NODE_ENV !== 'production';

// Create a Winston logger instance
const logger = createLogger({
  level: isDevelopment ? 'debug' : 'info', // Force debug level in development to debug task queue issues
  format: format.combine(
    format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss',
    }),
    format.errors({ stack: true }),
    format.splat(),
    format.json()
  ),
  defaultMeta: { service: 'inbox-zero-api' },
  transports: [
    // Write to console
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple() // Use a simple, robust formatter for development
      ),
    }),
  ],
});

// Log file transport only in production
if (!isDevelopment) {
  logger.add(
    new transports.File({
      filename: 'error.log',
      level: 'error',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      format: format.combine(
        format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss',
        }),
        format.json()
      ),
    })
  );
}

// Sanitize potentially sensitive information
function sanitize(
  obj: any,
  sensitiveKeys: string[] = ['token', 'password', 'secret', 'key', 'apiKey', 'credential', 'auth']
): any {
  if (!obj) return obj;

  if (typeof obj === 'object') {
    const result = Array.isArray(obj) ? [...obj] : { ...obj };

    for (const key in result) {
      if (sensitiveKeys.some((sensitive) => key.toLowerCase().includes(sensitive.toLowerCase()))) {
        // Mask sensitive values
        if (typeof result[key] === 'string') {
          result[key] = `${result[key].substring(0, 4)}****`;
        } else {
          result[key] = '[REDACTED]';
        }
      } else if (typeof result[key] === 'object') {
        // Recursively sanitize nested objects
        result[key] = sanitize(result[key], sensitiveKeys);
      }
    }
    return result;
  }

  return obj;
}

// Function to get trace ID from context or generate a new one
function _getTraceId(): string {
  const context = contextStorage.getStore();
  if (context?.has('traceId')) {
    return context.get('traceId');
  }
  return `trace-${Math.random().toString(36).substring(2, 15)}`;
}

// Helper to extract error details
function extractErrorDetails(error: unknown): {
  message: string;
  stack?: string;
  code?: string;
  name?: string;
} {
  if (error instanceof Error) {
    return {
      message: error.message,
      stack: isDevelopment ? error.stack : undefined,
      name: error.name,
      code: (error as any).code,
    };
  }
  return { message: String(error) };
}

// Create a middleware to set request context
export function loggerContextMiddleware(req: any, _res: any, next: () => void) {
  const traceId =
    req.headers['x-trace-id'] || `trace-${Math.random().toString(36).substring(2, 15)}`;

  // Don't set userId here - it will be set after authentication
  // Create a new context for this request
  const context = new Map<string, any>();
  context.set('traceId', traceId);
  context.set('method', req.method);
  context.set('path', req.path);

  contextStorage.run(context, () => {
    next();
  });
}

// Helper function to update context with user ID after authentication
export function updateLoggerContext(userId: number | string) {
  const context = contextStorage.getStore();
  if (context) {
    context.set('userId', userId);
  }
}

// Export standardized logger functions with context handling
const enhancedLogger = {
  debug: (message: string, meta: unknown = {}) => {
    const context = contextStorage.getStore();
    const contextData = context ? Object.fromEntries(context.entries()) : {};

    logger.debug(message, {
      ...sanitize(meta as any),
      ...contextData,
      timestamp: new Date().toISOString(),
    });
  },

  info: (message: string, meta: unknown = {}) => {
    const context = contextStorage.getStore();
    const contextData = context ? Object.fromEntries(context.entries()) : {};

    logger.info(message, {
      ...sanitize(meta as any),
      ...contextData,
      timestamp: new Date().toISOString(),
    });
  },

  warn: (message: string, meta: unknown = {}) => {
    const context = contextStorage.getStore();
    const contextData = context ? Object.fromEntries(context.entries()) : {};

    logger.warn(message, {
      ...sanitize(meta as any),
      ...contextData,
      timestamp: new Date().toISOString(),
    });
  },

  error: (
    messageOrError: string | Error | Record<string, any>,
    param2?: unknown,
    param3?: unknown
  ) => {
    const context = contextStorage.getStore();
    const contextData = context ? Object.fromEntries(context.entries()) : {};

    // --- Determine argument roles -------------------------------------------------
    let message: string; // final log message
    let err: Error | undefined; // captured Error instance (if any)
    let meta: Record<string, any> = {};

    const isErrorInstance = (val: unknown): val is Error => val instanceof Error;

    // Case 1 – first arg is an Error ------------------------------------------------
    if (isErrorInstance(messageOrError)) {
      message = messageOrError.message;
      err = messageOrError;
      if (param2 && typeof param2 === 'object') meta = param2 as Record<string, any>;
    } else if (typeof messageOrError === 'string') {
      // Case 2 – first arg is a string (normal usage)
      message = messageOrError;
      if (isErrorInstance(param2)) {
        // pattern: logger.error(msg, error [, meta])
        err = param2;
        if (param3 && typeof param3 === 'object') meta = param3 as Record<string, any>;
      } else {
        // pattern: logger.error(msg, meta)
        if (param2 && typeof param2 === 'object') meta = param2 as Record<string, any>;
      }
    } else {
      // Case 3 – first arg is a plain object (legacy misuse)
      const obj = messageOrError as Record<string, any>;
      const extractedMsg =
        obj.message ||
        (obj.error && (obj.error.message || obj.error)) ||
        obj.err?.message;

      message = typeof extractedMsg === 'string' ? extractedMsg : 'Unexpected error';
      meta = { original: sanitize(obj) };

      if (isErrorInstance(param2)) {
        err = param2;
      }
    }

    // -----------------------------------------------------------------------------
    const errorDetails = err ? extractErrorDetails(err) : undefined;

    logger.error(message, {
      ...sanitize(meta),
      ...contextData,
      ...(errorDetails ? { error: errorDetails } : {}),
      timestamp: new Date().toISOString(),
    });
  },

  fatal: (message: string, meta: unknown = {}) => {
    // Alias for critical errors – logs at error level for now
    const context = contextStorage.getStore();
    const contextData = context ? Object.fromEntries(context.entries()) : {};

    logger.error(message, {
      ...sanitize(meta as any),
      ...contextData,
      level: 'fatal',
      timestamp: new Date().toISOString(),
    });
  },

  // Special loggers for task queue operations
  taskQueue: {
    debug: (message: string, taskId?: number, meta: object = {}) => {
      enhancedLogger.debug(`[TaskQueue] ${message}`, {
        ...meta,
        taskId,
        component: 'taskQueue',
      });
    },

    info: (message: string, taskId?: number, meta: object = {}) => {
      enhancedLogger.info(`[TaskQueue] ${message}`, {
        ...meta,
        taskId,
        component: 'taskQueue',
      });
    },

    warn: (message: string, taskId?: number, meta: object = {}) => {
      enhancedLogger.warn(`[TaskQueue] ${message}`, {
        ...meta,
        taskId,
        component: 'taskQueue',
      });
    },

    error: (message: string, error: Error | unknown, taskId?: number, meta: object = {}) => {
      enhancedLogger.error(`[TaskQueue] ${message}`, error, {
        ...meta,
        taskId,
        component: 'taskQueue',
      });
    },

    taskStart: (taskId: number, taskType: string, meta: object = {}) => {
      enhancedLogger.info(`[TaskQueue] Starting task ${taskId} (${taskType})`, {
        ...meta,
        taskId,
        taskType,
        taskEvent: 'start',
        component: 'taskQueue',
        startTime: Date.now(),
      });
    },

    taskComplete: (taskId: number, taskType: string, duration: number, meta: object = {}) => {
      enhancedLogger.info(`[TaskQueue] Completed task ${taskId} (${taskType}) in ${duration}ms`, {
        ...meta,
        taskId,
        taskType,
        taskEvent: 'complete',
        component: 'taskQueue',
        durationMs: duration,
      });
    },

    taskFail: (
      taskId: number,
      taskType: string,
      error: Error | unknown,
      duration: number,
      meta: object = {}
    ) => {
      const errorDetails = extractErrorDetails(error);
      enhancedLogger.error(
        `[TaskQueue] Failed task ${taskId} (${taskType}) after ${duration}ms: ${errorDetails.message}`,
        error,
        {
          ...meta,
          taskId,
          taskType,
          taskEvent: 'fail',
          component: 'taskQueue',
          durationMs: duration,
        }
      );
    },
  },

  // Special loggers for email processing
  email: {
    debug: (message: string, emailId?: number, userId?: number, meta: object = {}) => {
      enhancedLogger.debug(`[Email] ${message}`, {
        ...meta,
        emailId,
        userId,
        component: 'email',
      });
    },

    info: (message: string, emailId?: number, userId?: number, meta: object = {}) => {
      enhancedLogger.info(`[Email] ${message}`, {
        ...meta,
        emailId,
        userId,
        component: 'email',
      });
    },

    warn: (message: string, emailId?: number, userId?: number, meta: object = {}) => {
      enhancedLogger.warn(`[Email] ${message}`, {
        ...meta,
        emailId,
        userId,
        component: 'email',
      });
    },

    error: (
      message: string,
      error: Error | unknown,
      emailId?: number,
      userId?: number,
      meta: object = {}
    ) => {
      enhancedLogger.error(`[Email] ${message}`, error, {
        ...meta,
        emailId,
        userId,
        component: 'email',
      });
    },
  },

  // Special loggers for AI services and operations
  ai: {
    debug: (message: string, meta: object = {}) => {
      enhancedLogger.debug(`[AI] ${message}`, {
        ...meta,
        component: 'ai',
      });
    },

    info: (message: string, meta: object = {}) => {
      enhancedLogger.info(`[AI] ${message}`, {
        ...meta,
        component: 'ai',
      });
    },

    warn: (message: string, meta: object = {}) => {
      enhancedLogger.warn(`[AI] ${message}`, {
        ...meta,
        component: 'ai',
      });
    },

    error: (message: string, error: Error | unknown, meta: object = {}) => {
      enhancedLogger.error(`[AI] ${message}`, error, {
        ...meta,
        component: 'ai',
      });
    },

    // Operation lifecycle tracking
    operationStart: (
      operationType: string,
      operationId: string,
      meta: object = {}
    ) => {
      enhancedLogger.info(`[AI] Starting ${operationType} operation`, {
        ...meta,
        operationType,
        operationId,
        operationEvent: 'start',
        component: 'ai',
        startTime: Date.now(),
      });
    },

    operationComplete: (
      operationType: string,
      operationId: string,
      duration: number,
      meta: object = {}
    ) => {
      enhancedLogger.info(`[AI] Completed ${operationType} operation in ${duration}ms`, {
        ...meta,
        operationType,
        operationId,
        operationEvent: 'complete',
        component: 'ai',
        durationMs: duration,
      });
    },

    operationFail: (
      operationType: string,
      operationId: string,
      error: Error | unknown,
      duration: number,
      meta: object = {}
    ) => {
      const errorDetails = extractErrorDetails(error);
      enhancedLogger.error(
        `[AI] Failed ${operationType} operation after ${duration}ms: ${errorDetails.message}`,
        error,
        {
          ...meta,
          operationType,
          operationId,
          operationEvent: 'fail',
          component: 'ai',
          durationMs: duration,
        }
      );
    },

    // API call tracking
    apiCall: (
      service: string,
      endpoint: string,
      method: string = 'POST',
      meta: object = {}
    ) => {
      enhancedLogger.debug(`[AI] API call to ${service}`, {
        ...meta,
        service,
        endpoint,
        method,
        component: 'ai',
        apiCallEvent: 'request',
      });
    },

    apiResponse: (
      service: string,
      endpoint: string,
      statusCode: number,
      duration: number,
      meta: object = {}
    ) => {
      const level = statusCode >= 400 ? 'warn' : 'debug';
      enhancedLogger[level](`[AI] ${service} API response ${statusCode} in ${duration}ms`, {
        ...meta,
        service,
        endpoint,
        statusCode,
        durationMs: duration,
        component: 'ai',
        apiCallEvent: 'response',
      });
    },

    // Content processing tracking
    contentProcessing: (
      contentType: string,
      contentLength: number,
      processingType: string,
      meta: object = {}
    ) => {
      enhancedLogger.debug(`[AI] Processing ${contentType} content (${contentLength} chars) for ${processingType}`, {
        ...meta,
        contentType,
        contentLength,
        processingType,
        component: 'ai',
      });
    },

    // Cache operations
    cacheHit: (cacheKey: string, operationType: string, meta: object = {}) => {
      enhancedLogger.debug(`[AI] Cache hit for ${operationType}`, {
        ...meta,
        cacheKey: cacheKey.substring(0, 16) + '...', // Truncate for privacy
        operationType,
        component: 'ai',
        cacheEvent: 'hit',
      });
    },

    cacheMiss: (cacheKey: string, operationType: string, meta: object = {}) => {
      enhancedLogger.debug(`[AI] Cache miss for ${operationType}`, {
        ...meta,
        cacheKey: cacheKey.substring(0, 16) + '...', // Truncate for privacy
        operationType,
        component: 'ai',
        cacheEvent: 'miss',
      });
    },

    cacheStore: (cacheKey: string, operationType: string, ttl?: number, meta: object = {}) => {
      enhancedLogger.debug(`[AI] Storing in cache for ${operationType}`, {
        ...meta,
        cacheKey: cacheKey.substring(0, 16) + '...', // Truncate for privacy
        operationType,
        ttl,
        component: 'ai',
        cacheEvent: 'store',
      });
    },

    // Circuit breaker events
    circuitOpen: (service: string, failureCount: number, meta: object = {}) => {
      enhancedLogger.warn(`[AI] Circuit breaker opened for ${service} after ${failureCount} failures`, {
        ...meta,
        service,
        failureCount,
        component: 'ai',
        circuitEvent: 'open',
      });
    },

    circuitHalfOpen: (service: string, meta: object = {}) => {
      enhancedLogger.info(`[AI] Circuit breaker half-open for ${service}`, {
        ...meta,
        service,
        component: 'ai',
        circuitEvent: 'half-open',
      });
    },

    circuitClosed: (service: string, meta: object = {}) => {
      enhancedLogger.info(`[AI] Circuit breaker closed for ${service}`, {
        ...meta,
        service,
        component: 'ai',
        circuitEvent: 'closed',
      });
    },

    // Model and prompt tracking
    modelUsage: (
      model: string,
      promptTokens: number,
      completionTokens: number,
      totalTokens: number,
      meta: object = {}
    ) => {
      enhancedLogger.info(`[AI] Model usage: ${model}`, {
        ...meta,
        model,
        promptTokens,
        completionTokens,
        totalTokens,
        component: 'ai',
      });
    },

    promptValidation: (
      promptType: string,
      promptLength: number,
      isValid: boolean,
      meta: object = {}
    ) => {
      const level = isValid ? 'debug' : 'warn';
      enhancedLogger[level](`[AI] Prompt validation for ${promptType}: ${isValid ? 'passed' : 'failed'}`, {
        ...meta,
        promptType,
        promptLength,
        isValid,
        component: 'ai',
      });
    },

    // Response parsing and validation
    responseValidation: (
      responseType: string,
      isValid: boolean,
      validationErrors?: string[],
      meta: object = {}
    ) => {
      const level = isValid ? 'debug' : 'warn';
      enhancedLogger[level](`[AI] Response validation for ${responseType}: ${isValid ? 'passed' : 'failed'}`, {
        ...meta,
        responseType,
        isValid,
        validationErrors,
        component: 'ai',
      });
    },
  },

  // Helper to start a timer for operation duration tracking
  startTimer: (): (() => number) => {
    const startTime = Date.now();
    return () => Date.now() - startTime;
  },
};

export default enhancedLogger;