/**
 * User Migration Routes
 *
 * These routes handle the migration of user data when transitioning from
 * traditional auth to Firebase auth.
 */

import { eq } from 'drizzle-orm';
import express, { type Request, type Response } from 'express';
import * as admin from 'firebase-admin';
import { users } from '../../shared/schema';
import { getDb } from '../db';
import { storage } from '../storage';
import { migrateUserSession, syncEmailProviderTokens } from '../utils/user-migration';

const router = express.Router();

/**
 * Helper to get user details from Firebase UID
 */
async function getUserFromFirebaseUid(firebaseUid: string): Promise<any> {
  try {
    const db = await getDb();
    // First try to get the user from our database
    const userByFirebaseUid = await db.query.users.findFirst({
      where: eq(users.firebaseUid, firebaseUid),
    });

    if (userByFirebaseUid) {
      return userByFirebaseUid;
    }

    // If not found in our database, get details from Firebase
    const firebaseUser = await admin.auth().getUser(firebaseUid);
    if (!firebaseUser) {
      throw new Error(`Firebase user not found for UID: ${firebaseUid}`);
    }

    // Try to find the user by email
    const userByEmail = await db.query.users.findFirst({
      where: eq(users.email, firebaseUser.email!),
    });

    if (userByEmail) {
      // Link existing user to Firebase UID
      await db
        .update(users)
        .set({
          firebaseUid: firebaseUid,
        })
        .where(eq(users.id, userByEmail.id));
      return { ...userByEmail, firebaseUid: firebaseUid };
    }

    // User not found in our system at all
    throw new Error(`No matching user found for Firebase UID: ${firebaseUid}`);
  } catch (error) {
    console.error('Error finding user from Firebase UID:', error);
    throw error;
  }
}

/**
 * @route POST /api/user-migration/sync-tokens
 * @description Syncs email provider tokens between user accounts
 * @access Private (Admin)
 */
router.post('/sync-tokens', async (req: Request, res: Response): Promise<void> => {
  try {
    const { sourceUserId, targetUserId, overwrite = false } = req.body;

    if (!sourceUserId || !targetUserId) {
      res.status(400).json({
        success: false,
        message: 'Source and target user IDs are required',
      });
      return;
    }

    // Check if user is admin or has permission
    // In a real app, you would check permissions here

    const result = await syncEmailProviderTokens(sourceUserId, targetUserId, overwrite);

    res.json({
      success: true,
      message: 'Tokens synchronized successfully',
      data: result,
    });
  } catch (error) {
    console.error('Error syncing tokens:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to sync tokens',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

/**
 * @route POST /api/user-migration/migrate-session
 * @description Migrates user session from legacy ID to Firebase user ID
 * @access Private (Authenticated)
 */
router.post('/migrate-session', async (req: Request, res: Response): Promise<void> => {
  try {
    const { firebaseUid } = req.body;

    if (!firebaseUid) {
      res.status(400).json({
        success: false,
        message: 'Firebase UID is required',
      });
      return;
    }

    if (!req.session || !req.session.userId) {
      res.status(401).json({
        success: false,
        message: 'Not authenticated',
      });
      return;
    }

    const legacyUserId = req.session.userId;

    const result = await migrateUserSession(req, legacyUserId, firebaseUid);

    res.json({
      success: true,
      message: 'User session migrated successfully',
      data: result,
    });
  } catch (error) {
    console.error('Error migrating user session:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to migrate user session',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

/**
 * @route POST /api/user-migration/fix-gmail-connection
 * @description Fixes Gmail connection by resetting token status and syncing tokens
 * @access Private (Authenticated)
 */
router.post('/fix-gmail-connection', async (req: Request, res: Response): Promise<void> => {
  try {
    const { firebaseUid, email } = req.body;

    if (!firebaseUid) {
      res.status(400).json({
        success: false,
        message: 'Firebase UID is required',
      });
      return;
    }

    // 1. Get Firebase user by UID
    const firebaseUser = await admin.auth().getUser(firebaseUid);
    if (!firebaseUser) {
      res.status(404).json({
        success: false,
        message: 'Firebase user not found',
      });
      return;
    }

    // 2. Find all users with matching email in our database
    const userEmail = email || firebaseUser.email!;
    const matchingUsers = await storage.getUsersByEmail(userEmail);

    // If no matching users, return error
    if (!matchingUsers || matchingUsers.length === 0) {
      res.status(404).json({
        success: false,
        message: 'No matching users found with this email',
      });
      return;
    }

    // 3. Find the current Firebase user in our database
    const currentFirebaseUser = matchingUsers.find((u) => u.firebaseUid === firebaseUid);

    // 4. Find legacy users (non-Firebase or different Firebase UID)
    const legacyUsers = matchingUsers.filter((u) => u.id !== (currentFirebaseUser?.id || 0));

    // 5. We need one of these users to exist
    if (!currentFirebaseUser && legacyUsers.length === 0) {
      res.status(404).json({
        success: false,
        message: 'No suitable users found to fix connection',
      });
      return;
    }

    // 6. Update the Firebase User record if it exists but doesn't have Firebase UID
    if (currentFirebaseUser && !currentFirebaseUser.firebaseUid) {
      await storage.updateUser(currentFirebaseUser.id, {
        firebaseUid: firebaseUid,
      });
    }

    const result = {
      migrated: false,
      tokensSynced: false,
      resetStatus: false,
      currentUserId: currentFirebaseUser?.id,
      legacyUserIds: legacyUsers.map((u) => u.id),
    };

    // 7. If we have both current and legacy users, sync tokens between them
    if (currentFirebaseUser && legacyUsers.length > 0) {
      // Find the legacy user with valid tokens
      const legacyUserWithTokens = legacyUsers.find(
        (u) => u.gmailTokens || u.refreshToken || u.accessToken
      );

      if (legacyUserWithTokens) {
        // Sync tokens from legacy user to current Firebase user
        await syncEmailProviderTokens(
          legacyUserWithTokens.id,
          currentFirebaseUser.id,
          true // Overwrite existing tokens
        );

        result.tokensSynced = true;
      }
    }

    // 8. Reset token status for the current user if it exists
    if (currentFirebaseUser) {
      // Reset the token_invalid and circuit breaker status
      await storage.updateUser(currentFirebaseUser.id, {
        tokenInvalid: false,
        lastTokenError: null,
      });

      result.resetStatus = true;
    }

    // 9. Make sure the Firebase UID is set in the current session
    if (req.session) {
      // If no current Firebase user but legacy exists, create a new record
      if (!currentFirebaseUser && legacyUsers.length > 0) {
        const legacyUser = legacyUsers[0];

        // Create a new user record with the Firebase UID
        const newUser = await storage.createUser({
          name: firebaseUser.displayName || legacyUser.name,
          email: userEmail,
          provider: 'google',
          picture: firebaseUser.photoURL || legacyUser.picture,
          firebaseUid: firebaseUid,
          // Copy tokens from legacy user if available
          gmailTokens: legacyUser.gmailTokens as any,
          accessToken: legacyUser.accessToken,
          refreshToken: legacyUser.refreshToken,
          expiresAt: legacyUser.expiresAt,
        });

        // Update the session with the new user ID
        req.session.userId = newUser.id;
        result.currentUserId = newUser.id;
      } else if (currentFirebaseUser) {
        // Make sure the session has the correct user ID
        req.session.userId = currentFirebaseUser.id;
      }

      result.migrated = true;
    }

    res.json({
      success: true,
      message: 'Gmail connection fixed successfully',
      data: result,
    });
  } catch (error) {
    console.error('Error fixing Gmail connection:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fix Gmail connection',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

/**
 * @route POST /api/user-migration/migrate-outlook-connection
 * @description Migrates Outlook connection by resetting token status and syncing tokens
 * @access Private (Authenticated)
 */
router.post('/migrate-outlook-connection', async (req: Request, res: Response): Promise<void> => {
  try {
    const { firebaseUid } = req.body;

    if (!firebaseUid) {
      res.status(400).json({
        success: false,
        message: 'Firebase UID is required',
      });
      return;
    }

    // 1. Get user from our database using Firebase UID
    const currentUser = await getUserFromFirebaseUid(firebaseUid);
    if (!currentUser) {
      res.status(404).json({
        success: false,
        message: 'Current user not found',
      });
      return;
    }

    // 2. Find all legacy users with matching email
    const legacyUsers = (await storage.getUsersByEmail(currentUser.email)).filter(
      (u) => u.id !== currentUser.id
    );

    // 3. Find a legacy user with Outlook tokens
    const legacyUserWithTokens = legacyUsers.find((u) => u.refreshToken || u.accessToken);

    if (!legacyUserWithTokens) {
      res.status(404).json({
        success: false,
        message: 'No legacy user with Outlook tokens found to migrate from.',
      });
      return;
    }

    // 4. Update the current user's record with legacy tokens
    const updatedUser = await storage.updateUser(currentUser.id, {
      provider: 'outlook',
      accessToken: legacyUserWithTokens.accessToken,
      refreshToken: legacyUserWithTokens.refreshToken,
      expiresAt: legacyUserWithTokens.expiresAt,
    });

    if (!updatedUser) {
      res.status(500).json({
        success: false,
        message: 'Failed to update user with migrated tokens.',
      });
      return;
    }

    res.json({
      success: true,
      message: 'Outlook connection migrated successfully',
      data: {
        migratedUserId: updatedUser.id,
        sourceUserId: legacyUserWithTokens.id,
      },
    });
  } catch (error) {
    console.error('Error migrating Outlook connection:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to migrate Outlook connection',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

/**
 * @route POST /api/user-migration/create-firebase-user
 * @description Creates a Firebase user from an existing legacy user
 * @access Private (Admin)
 */
router.post('/create-firebase-user', async (req: Request, res: Response): Promise<void> => {
  try {
    const { legacyUserId } = req.body;

    if (!legacyUserId) {
      res.status(400).json({
        success: false,
        message: 'Legacy user ID is required',
      });
      return;
    }

    // 1. Get the legacy user from the database
    const legacyUser = await storage.getUser(legacyUserId);
    if (!legacyUser) {
      res.status(404).json({
        success: false,
        message: 'Legacy user not found',
      });
      return;
    }

    // 2. Create the user in Firebase
    const firebaseUser = await admin.auth().createUser({
      email: legacyUser.email,
      displayName: legacyUser.name || undefined,
      photoURL: legacyUser.picture || undefined,
    });

    // 3. Update the legacy user with the Firebase UID
    await storage.updateUser(legacyUserId, {
      firebaseUid: firebaseUser.uid,
    });

    res.json({
      success: true,
      message: 'Firebase user created successfully',
      data: {
        firebaseUid: firebaseUser.uid,
        legacyUserId: legacyUserId,
      },
    });
  } catch (error) {
    console.error('Error creating Firebase user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create Firebase user',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

export default router;
