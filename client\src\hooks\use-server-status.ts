import { useQuery } from '@tanstack/react-query';
import { useEffect, useRef } from 'react';
import apiClient from '../lib/apiClient';
import { useToast } from './use-toast';

interface ServerStatus {
  status: 'ok' | 'error';
  timestamp: string;
}

const checkServerStatus = async (): Promise<ServerStatus> => {
  // We can skip auth for this public health check endpoint.
  return apiClient.get<ServerStatus>('/api/status/health', { skipAuth: true });
};

export const useServerStatus = (
  options: { refetchInterval?: number; onStatusChange?: (isAvailable: boolean) => void } = {}
) => {
  const { toast } = useToast();
  const { refetchInterval = 60000, onStatusChange } = options;

  const { data, error, isLoading } = useQuery<ServerStatus, Error>({
    queryKey: ['serverStatus'],
    queryFn: checkServerStatus,
    refetchInterval,
    refetchOnWindowFocus: true,
    // Retry with exponential backoff on failure.
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  const isAvailable = data?.status === 'ok' && !error;
  const prevStatusRef = useRef<boolean | undefined>();

  useEffect(() => {
    const previousStatus = prevStatusRef.current;

    if (onStatusChange && isAvailable !== previousStatus) {
      onStatusChange(isAvailable);
    }

    if (previousStatus === false && isAvailable === true) {
      toast({
        title: 'Connection Restored',
        description: 'Successfully reconnected to the server.',
      });
    }

    prevStatusRef.current = isAvailable;
  }, [isAvailable, onStatusChange, toast]);

  return {
    isAvailable,
    isLoading,
    error,
    lastChecked: data?.timestamp,
  };
};
