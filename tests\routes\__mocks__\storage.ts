// tests/routes/__mocks__/storage.ts
// This path assumes your jest config roots might pick this up automatically,
// or you use jest.mock('../../server/storage') directly in the test.
// For clarity, I'll do the latter in the test file modification.

export const storage = {
  getUser: jest.fn(),
  updateUser: jest.fn(),
  // Add any other functions from storage.ts that are used by the routes
  // For example, if it had these (based on potential usage, ensure they match actual storage.ts):
  // createEmail: jest.fn(),
  // getEmail: jest.fn(),
  // getSettings: jest.fn(),
  // createSettings: jest.fn(),
  // updateSettings: jest.fn(),
};
