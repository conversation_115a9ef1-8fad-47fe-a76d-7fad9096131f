import type {
  Achievement,
  Email,
  InsertAchievement,
  InsertEmail,
  InsertSettings,
  InsertUser,
  Settings,
  User,
} from '@shared/schema';
import type { Credentials } from 'google-auth-library';

// Phase 2: Query Optimization - Bulk Operations Interface
interface BulkEmailUpdate {
  id: number;
  updates: Partial<Email>;
}

interface BulkEmailResult {
  successful: number;
  failed: number;
  errors: Array<{ id: number; error: string }>;
}

export interface IStorage {
  // Core operations
  testConnection(): Promise<boolean>;

  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getUsersByEmail(email: string): Promise<User[]>;
  getUserByFirebaseUid(firebaseUid: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, user: Partial<User>): Promise<User | undefined>;
  updateUserTokens(id: number, tokens: Credentials): Promise<User | undefined>;
  deleteUser(id: number): Promise<boolean>;
  getAllUsers(): Promise<User[]>;

  // Email operations
  getEmails(
    userId: number,
    limit?: number,
    offset?: number,
    filters?: { archived?: boolean; important?: boolean; snoozed?: boolean }
  ): Promise<Email[]>;
  getEmail(id: number): Promise<Email | undefined>;
  getEmailByMessageId(userId: number, messageId: string): Promise<Email | undefined>;
  getEmailsByIds(ids: number[], userId: number): Promise<Email[]>;
  createEmail(email: InsertEmail): Promise<Email>;
  updateEmail(id: number, email: Partial<Email>): Promise<Email | undefined>;
  markEmailAsRead(id: number): Promise<boolean>;
  markEmailAsArchived(id: number): Promise<boolean>;
  getEmailCount(
    userId: number,
    filters?: { archived?: boolean; important?: boolean; snoozed?: boolean; trashed?: boolean }
  ): Promise<number>;
  updateEmailByMessageId(userId: number, messageId: string, data: Partial<Email>): Promise<Email | undefined>;

  // Phase 2: Bulk operations for improved performance
  bulkUpdateEmails?(updates: BulkEmailUpdate[], userId: number): Promise<BulkEmailResult>;
  bulkCreateEmails?(emails: InsertEmail[]): Promise<Email[]>;

  // Phase 4: Advanced optimization methods
  getEmailsOptimized?(
    userId: number,
    limit?: number,
    offset?: number,
    filters?: { archived?: boolean; important?: boolean; snoozed?: boolean; trashed?: boolean },
    options?: { enableStreaming?: boolean; enableCompression?: boolean }
  ): Promise<{ emails: Email[]; compressionResult?: any }>;
  streamEmails?(
    userId: number,
    filters?: { archived?: boolean; important?: boolean; snoozed?: boolean; trashed?: boolean }
  ): AsyncGenerator<Email[], void, unknown>;
  startCacheWarming?(): void;
  stopCacheWarming?(): void;
  getAdvancedMetrics?(): any;
  updateOptimizationSettings?(settings: any): void;

  // Settings operations
  getSettings(userId: number): Promise<Settings | undefined>;
  createSettings(settings: InsertSettings): Promise<Settings>;
  updateSettings(userId: number, settings: Partial<Settings>): Promise<Settings | undefined>;

  // Achievement operations
  getAchievements(userId: number): Promise<Achievement[]>;
  getAchievement(id: number): Promise<Achievement | undefined>;
  createAchievement(achievement: InsertAchievement): Promise<Achievement>;
  updateAchievement(
    id: number,
    achievement: Partial<Achievement>
  ): Promise<Achievement | undefined>;
  getAchievementByNameAndUser(userId: number, name: string): Promise<Achievement | undefined>;

  // Task queue stats
  getTaskQueueStats(): Promise<Record<string, unknown>>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private emails: Map<number, Email>;
  private settings: Map<number, Settings>;
  private achievements: Map<number, Achievement>;
  private userCurrentId: number;
  private emailCurrentId: number;
  private settingsCurrentId: number;
  private achievementCurrentId: number;

  constructor() {
    this.users = new Map();
    this.emails = new Map();
    this.settings = new Map();
    this.achievements = new Map();
    this.userCurrentId = 1;
    this.emailCurrentId = 1;
    this.settingsCurrentId = 1;
    this.achievementCurrentId = 1;
  }

  // Core operations
  async testConnection(): Promise<boolean> {
    // In-memory storage is always connected
    return true;
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find((user) => user.email === email);
  }

  async getUsersByEmail(email: string): Promise<User[]> {
    return Array.from(this.users.values()).filter((user) => user.email === email);
  }

  async getUserByFirebaseUid(firebaseUid: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find((user) => user.firebaseUid === firebaseUid);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userCurrentId++;
    const user: User = {
      ...insertUser,
      id,
      // Ensure stricter Drizzle columns have sensible defaults
      gmailTokens: (insertUser as any).gmailTokens ?? null,
      provider: insertUser.provider ?? 'firebase',
      emailsProcessed: 0,
      repliesSent: 0,
      tokenStatus: (insertUser as any).tokenStatus ?? 'unknown',
    } as User;
    this.users.set(id, user);
    return user;
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const user = await this.getUser(id);
    if (!user) return undefined;

    const updatedUser = { ...user, ...userData };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async updateUserTokens(id: number, tokens: Credentials): Promise<User | undefined> {
    const user = await this.getUser(id);
    if (!user) return undefined;

    const updatedUser = { ...user, gmailTokens: JSON.stringify(tokens) };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async deleteUser(id: number): Promise<boolean> {
    return this.users.delete(id);
  }

  async getAllUsers(): Promise<User[]> {
    return Array.from(this.users.values());
  }

  // Email operations
  async getEmails(
    userId: number,
    limit = 50,
    offset = 0,
    filters?: { archived?: boolean; important?: boolean; snoozed?: boolean; trashed?: boolean }
  ): Promise<Email[]> {
    let userEmails = Array.from(this.emails.values()).filter((email) => email.userId === userId);

    // Apply filters
    if (filters) {
      // Handle trashed filter
      if (filters.trashed === true) {
        // Trash view: only show trashed emails
        userEmails = userEmails.filter((email) => email.isTrashed === true);
      } else {
        // For all other views: exclude trashed emails by default
        userEmails = userEmails.filter((email) => !email.isTrashed);
      }

      // Handle archive filter
      if (filters.archived !== undefined) {
        userEmails = userEmails.filter((email) => email.isArchived === filters.archived);
      } else if (!filters.important && !filters.snoozed && !filters.trashed) {
        // Default inbox behavior - show non-archived emails
        userEmails = userEmails.filter((email) => !email.isArchived);
      }

      // Important filter
      if (filters.important) {
        userEmails = userEmails.filter((email) => email.isImportant === true);
      }

      // Snoozed filter
      if (filters.snoozed) {
        userEmails = userEmails.filter(
          (email) => email.snoozedUntil !== null && email.snoozedUntil !== undefined
        );
      }
    } else {
      // Default behavior (inbox view) - only show non-archived and non-trashed emails
      userEmails = userEmails.filter((email) => !email.isArchived && !email.isTrashed);
    }

    // Sort by received date (most recent first)
    userEmails.sort((a, b) => {
      const dateA = a.receivedAt ? new Date(a.receivedAt).getTime() : 0;
      const dateB = b.receivedAt ? new Date(b.receivedAt).getTime() : 0;
      return dateB - dateA; // Most recent first
    });

    return userEmails.slice(offset, offset + limit);
  }

  async getEmail(id: number): Promise<Email | undefined> {
    return this.emails.get(id);
  }

  async getEmailByMessageId(userId: number, messageId: string): Promise<Email | undefined> {
    return Array.from(this.emails.values()).find(
      (email) => email.userId === userId && email.messageId === messageId
    );
  }

  async getEmailsByIds(ids: number[], userId: number): Promise<Email[]> {
    return Array.from(this.emails.values()).filter(
      (email) => ids.includes(email.id) && email.userId === userId
    );
  }

  async createEmail(insertEmail: InsertEmail): Promise<Email> {
    const id = this.emailCurrentId++;
    const email: Email = {
      ...insertEmail,
      id,
      summary: insertEmail.summary || null,
      // Provide defaults for optional-but-non-null fields
      categories: insertEmail.categories ?? [],
      provider: insertEmail.provider ?? null,
      labelIds: insertEmail.labelIds ?? [],
    } as Email;
    this.emails.set(id, email);
    return email;
  }

  async updateEmail(id: number, emailData: Partial<Email>): Promise<Email | undefined> {
    const email = await this.getEmail(id);
    if (!email) return undefined;

    const updatedEmail = { ...email, ...emailData };
    this.emails.set(id, updatedEmail);
    return updatedEmail;
  }

  async markEmailAsRead(id: number): Promise<boolean> {
    const email = await this.getEmail(id);
    if (!email) return false;

    email.isRead = true;
    this.emails.set(id, email);
    return true;
  }

  async markEmailAsArchived(id: number): Promise<boolean> {
    const email = await this.getEmail(id);
    if (!email) return false;

    email.isArchived = true;
    this.emails.set(id, email);
    return true;
  }

  async getEmailCount(
    userId: number,
    filters?: { archived?: boolean; important?: boolean; snoozed?: boolean; trashed?: boolean }
  ): Promise<number> {
    let userEmails = Array.from(this.emails.values()).filter((email) => email.userId === userId);

    // Apply filters (same logic as getEmails)
    if (filters) {
      // Handle trashed filter
      if (filters.trashed === true) {
        // Trash view: only count trashed emails
        userEmails = userEmails.filter((email) => email.isTrashed === true);
      } else {
        // For all other views: exclude trashed emails by default
        userEmails = userEmails.filter((email) => !email.isTrashed);
      }

      // Handle archive filter
      if (filters.archived !== undefined) {
        userEmails = userEmails.filter((email) => email.isArchived === filters.archived);
      } else if (!filters.important && !filters.snoozed && !filters.trashed) {
        // Default inbox behavior - count non-archived emails
        userEmails = userEmails.filter((email) => !email.isArchived);
      }

      // Important filter
      if (filters.important) {
        userEmails = userEmails.filter((email) => email.isImportant === true);
      }

      // Snoozed filter
      if (filters.snoozed) {
        userEmails = userEmails.filter(
          (email) => email.snoozedUntil !== null && email.snoozedUntil !== undefined
        );
      }
    } else {
      // Default behavior (inbox view) - only count non-archived and non-trashed emails
      userEmails = userEmails.filter((email) => !email.isArchived && !email.isTrashed);
    }

    return userEmails.length;
  }

  async updateEmailByMessageId(userId: number, messageId: string, data: Partial<Email>): Promise<Email | undefined> {
    const email = await this.getEmailByMessageId(userId, messageId);
    if (!email) return undefined;
    return this.updateEmail(email.id, data);
  }

  // Settings operations
  async getSettings(userId: number): Promise<Settings | undefined> {
    return Array.from(this.settings.values()).find((setting) => setting.userId === userId);
  }

  async createSettings(insertSettings: InsertSettings): Promise<Settings> {
    const id = this.settingsCurrentId++;
    const settings: Settings = {
      ...insertSettings,
      id,
      replyTone: insertSettings.replyTone || 'professional',
      categories: insertSettings.categories ?? null,
      priorityColors: insertSettings.priorityColors ?? null,
      themeMode: insertSettings.themeMode ?? 'system',

      borderRadius: insertSettings.borderRadius ?? 6,
    } as Settings;
    this.settings.set(id, settings);
    return settings;
  }

  async updateSettings(
    userId: number,
    settingsData: Partial<Settings>
  ): Promise<Settings | undefined> {
    const settings = await this.getSettings(userId);
    if (!settings) return undefined;

    const updatedSettings = { ...settings, ...settingsData };
    this.settings.set(settings.id, updatedSettings);
    return updatedSettings;
  }

  // Achievement operations
  async getAchievements(userId: number): Promise<Achievement[]> {
    return Array.from(this.achievements.values())
      .filter((achievement) => achievement.userId === userId)
      .sort((a, b) => {
        return new Date(b.unlockedAt).getTime() - new Date(a.unlockedAt).getTime();
      });
  }

  async getAchievement(id: number): Promise<Achievement | undefined> {
    return this.achievements.get(id);
  }

  async getAchievementByNameAndUser(
    userId: number,
    name: string
  ): Promise<Achievement | undefined> {
    return Array.from(this.achievements.values()).find(
      (achievement) => achievement.userId === userId && achievement.name === name
    );
  }

  async createAchievement(insertAchievement: InsertAchievement): Promise<Achievement> {
    const id = this.achievementCurrentId++;
    const achievement: Achievement = {
      ...insertAchievement,
      id,
      progress: insertAchievement.progress || 0,
      level: insertAchievement.level || 1,
      unlockedAt: insertAchievement.unlockedAt || new Date(),
      isComplete: insertAchievement.isComplete || false,
    };
    this.achievements.set(id, achievement);
    return achievement;
  }

  async updateAchievement(
    id: number,
    achievementData: Partial<Achievement>
  ): Promise<Achievement | undefined> {
    const achievement = await this.getAchievement(id);
    if (!achievement) return undefined;

    const updatedAchievement = { ...achievement, ...achievementData };
    this.achievements.set(id, updatedAchievement);
    return updatedAchievement;
  }

  // Task queue stats
  async getTaskQueueStats(): Promise<Record<string, unknown>> {
    // Implementation needed
    throw new Error('Method not implemented');
  }
}

// Import the DbStorage implementation
import { DbStorage } from './db-storage';

// Use DbStorage for persistence
export const storage = new DbStorage();

// Phase 4: Initialize cache warming on startup
try {
  const dbStorage = storage as any;
  if (typeof dbStorage.startCacheWarming === 'function') {
    dbStorage.startCacheWarming();
    console.log('[STORAGE] Phase 4 cache warming initialized');
  }
} catch (error) {
  console.warn('[STORAGE] Failed to initialize cache warming:', error);
}
