#!/usr/bin/env tsx

import { config } from 'dotenv';
import { storage } from '../server/storage';

// Load environment variables
config();

async function detailedCheck() {
  try {
    console.log('=== DETAILED USER CHECK ===\n');
    
    const allUsers = await storage.getAllUsers();
    console.log(`Total users found: ${allUsers.length}\n`);
    
    allUsers.forEach((user, index) => {
      console.log(`User ${index + 1}:`);
      console.log(`  ID: ${user.id}`);
      console.log(`  Email: ${user.email}`);
      console.log(`  Provider: ${user.provider}`);
      console.log(`  Firebase UID: ${user.firebaseUid || 'null'}`);
      console.log(`  Has Gmail Tokens: ${!!user.gmailTokens}`);
      if (user.gmailTokens) {
        try {
          const tokens = JSON.parse(user.gmailTokens);
          console.log(`  Gmail Token Type: ${tokens.token_type || 'unknown'}`);
          console.log(`  Gmail Access Token: ${tokens.access_token ? 'present' : 'missing'}`);
        } catch (e) {
          console.log(`  Gmail Tokens: Invalid JSON`);
        }
      }
      console.log(`  Created: ${user.createdAt || 'unknown'}`);
      console.log(`  Role: ${user.role || 'user'}`);
      console.log('  ---\n');
    });
    
    // Check for potential duplicates by email
    const emailGroups = allUsers.reduce((acc, user) => {
      if (!acc[user.email]) acc[user.email] = [];
      acc[user.email].push(user);
      return acc;
    }, {} as Record<string, typeof allUsers>);
    
    console.log('=== EMAIL GROUPING ===');
    Object.entries(emailGroups).forEach(([email, users]) => {
      console.log(`Email: ${email} (${users.length} user${users.length > 1 ? 's' : ''})`);
      if (users.length > 1) {
        console.log('  ⚠️  POTENTIAL DUPLICATES:');
        users.forEach(user => {
          console.log(`    - User ${user.id}: ${user.provider}, Firebase: ${user.firebaseUid || 'null'}, Gmail: ${!!user.gmailTokens}`);
        });
      }
      console.log('');
    });
    
  } catch (error) {
    console.error('Error:', error);
  }
}

detailedCheck(); 