#!/usr/bin/env tsx

import { config } from 'dotenv';
import { storage } from '../server/storage';

// Load environment variables
config();

async function manualMerge() {
  try {
    console.log('Starting manual user merge...');
    
    // Get all users
    const allUsers = await storage.getAllUsers();
    console.log(`Found ${allUsers.length} users`);
    
    // Find users by email (assuming your email)
    const usersByEmail = allUsers.reduce((acc, user) => {
      if (!acc[user.email]) acc[user.email] = [];
      acc[user.email].push(user);
      return acc;
    }, {} as Record<string, typeof allUsers[0][]>);
    
    for (const [email, users] of Object.entries(usersByEmail)) {
      if (users.length > 1) {
        console.log(`\nFound ${users.length} users for email: ${email}`);
        
        // Find Firebase and Google users
        const firebaseUser = users.find(u => u.provider === 'firebase' || u.firebaseUid);
        const googleUser = users.find(u => u.provider === 'google' && u.gmailTokens);
        
        if (firebaseUser && googleUser && firebaseUser.id !== googleUser.id) {
          console.log(`Merging Google user ${googleUser.id} into Firebase user ${firebaseUser.id}`);
          
          // Update Firebase user with Gmail tokens
          await storage.updateUser(firebaseUser.id, {
            gmailTokens: googleUser.gmailTokens,
            provider: 'firebase', // Keep as Firebase
          });
          
          console.log(`Updated Firebase user ${firebaseUser.id} with Gmail tokens`);
          
          // Transfer emails from Google user to Firebase user
          const googleUserEmails = await storage.getEmails(googleUser.id, 1000, 0);
          console.log(`Found ${googleUserEmails.length} emails for Google user`);
          
          for (const email of googleUserEmails) {
            await storage.updateEmail(email.id, { userId: firebaseUser.id });
          }
          
          console.log(`Transferred ${googleUserEmails.length} emails to Firebase user`);
          
          // Delete the Google user
          await storage.deleteUser(googleUser.id);
          console.log(`Deleted Google user ${googleUser.id}`);
          
          console.log('✅ Merge completed successfully');
        }
      }
    }
    
    console.log('\nMerge process completed');
    
  } catch (error) {
    console.error('Error during merge:', error);
  }
}

manualMerge(); 