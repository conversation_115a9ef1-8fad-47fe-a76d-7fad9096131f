import { Plus } from 'lucide-react';
import * as React from 'react';
import { Link, useLocation } from 'wouter';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  type NavItem as NavigationItem,
  primaryNavItems,
  secondaryNavItems,
  utilityNavItems,
} from '@/config/navigation';
import { useAuth } from '@/context/AuthContext';
import { useAdmin } from '@/hooks/use-admin';
import { useIsMobile } from '@/hooks/use-mobile';
import { useStats } from '@/hooks/use-stats';
import { cn } from '@/lib/utils';

// Constants
const SIDEBAR_WIDTH = '14rem'; // Adjusted for optimal fit
const SIDEBAR_WIDTH_MOBILE = '18rem';

// Types
type NavItem = NavigationItem & {
  id?: string;
  href?: string;
  items?: NavItem[];
  label?: string;
  count?: number;
};

type SidebarContextType = {
  isMobile: boolean;
  openMobile: boolean;
  setOpenMobile: React.Dispatch<React.SetStateAction<boolean>>;
};

// Context
const SidebarContext = React.createContext<SidebarContextType | null>(null);

const useSidebar = () => {
  const context = React.useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
};

// Provider
const SidebarProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isMobile = useIsMobile();
  const [openMobile, setOpenMobile] = React.useState(false);

  const value = React.useMemo(
    () => ({ isMobile, openMobile, setOpenMobile }),
    [isMobile, openMobile]
  );

  return <SidebarContext.Provider value={value}>{children}</SidebarContext.Provider>;
};

// Main Sidebar Component
const Sidebar: React.FC = () => {
  const { isMobile, openMobile, setOpenMobile } = useSidebar();

  const sidebarContent = <SidebarContent />;

  if (isMobile) {
    return (
      <Sheet open={openMobile} onOpenChange={setOpenMobile}>
        <SheetContent
          side="left"
          className="w-[var(--sidebar-width-mobile)] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden"
          style={{ '--sidebar-width-mobile': SIDEBAR_WIDTH_MOBILE } as React.CSSProperties}
        >
          <div className="flex h-full flex-col">{sidebarContent}</div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <aside
      className="flex h-full flex-col bg-sidebar text-sidebar-foreground border-r"
      style={{ width: SIDEBAR_WIDTH }}
    >
      {sidebarContent}
    </aside>
  );
};

const SidebarHeader = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(
  ({ className, ...props }, ref) => (
    <div
      className={cn('flex h-14 items-center border-b p-3', className)}
      ref={ref}
      {...props}
    />
  )
);
SidebarHeader.displayName = 'SidebarHeader';

const SidebarBody = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(
  ({ className, ...props }, ref) => (
    <div
      className={cn('flex-1 overflow-y-auto overflow-x-hidden p-3', className)}
      ref={ref}
      {...props}
    />
  )
);
SidebarBody.displayName = 'SidebarBody';

// Sidebar Item
const SidebarItem = React.forwardRef<
  HTMLAnchorElement,
  React.ComponentProps<'a'> & { item: NavItem }
>(({ item, className, children, ...props }, _ref) => {
  const [location] = useLocation();
  const { isMobile } = useSidebar();
  const href = item.href ?? item.path;
  const isActive = href ? location.startsWith(href) : false;
  const label = children ?? item.name;

  const content = (
    <div className="flex w-full items-center justify-between">
      <div className="flex items-center gap-x-3">
        {item.icon && <item.icon className="h-4 w-4" />}
        {!isMobile && (
          <span className="min-w-0 flex-1 overflow-hidden text-ellipsis whitespace-nowrap text-sm font-medium">
            {label}
          </span>
        )}
      </div>
      {!isMobile && item.count !== undefined && (
        <Badge variant="secondary" className="h-5 rounded-md px-1.5 text-xs">
          {item.count}
        </Badge>
      )}
    </div>
  );

  const itemContent = isMobile ? (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className="flex h-10 w-10 items-center justify-center">
          {item.icon && <item.icon className="h-5 w-5" />}
        </div>
      </TooltipTrigger>
      <TooltipContent side="right">{item.name}</TooltipContent>
    </Tooltip>
  ) : (
    content
  );

  const sharedClasses = cn(
    'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
    'h-9',
    isMobile ? 'w-10 p-0' : 'w-full justify-start px-3',
    isActive
      ? 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
      : 'hover:bg-accent hover:text-accent-foreground',
    className
  );

  if (!href) {
    return (
      <Button variant={isActive ? 'secondary' : 'ghost'} className={sharedClasses} title={item.name}>
        <div>{itemContent}</div>
      </Button>
    );
  }

  return (
    <TooltipProvider>
      <Link href={href} {...props} title={item.name} className={sharedClasses}>
        {itemContent}
      </Link>
    </TooltipProvider>
  );
});
SidebarItem.displayName = 'SidebarItem';

// Sidebar Content (The actual navigation)
const SidebarContent: React.FC = () => {
  const { user } = useAuth();
  const { isAdmin } = useAdmin();
  const { stats } = useStats();

  const renderNavItem = (item: NavItem) => {
    if (item.adminOnly && !isAdmin) return null;
    return <SidebarItem key={item.id || item.path || item.name} item={item} />;
  };

  return (
    <>
      <SidebarHeader>
        <div className="flex items-center justify-between">
          <h1 className="text-lg font-semibold tracking-tight">InboxZero</h1>
        </div>
      </SidebarHeader>
      <SidebarBody>
        <div className="space-y-1">
          <SidebarItem
            item={{
              id: 'compose',
              name: 'Compose',
              path: '/compose',
              icon: Plus,
              category: 'primary' as const,
              href: '/compose',
            }}
          >
            Compose
          </SidebarItem>
          {(primaryNavItems as NavItem[]).map(renderNavItem)}
        </div>
      </SidebarBody>
    </>
  );
};

// Final exported component
const AppSidebar: React.FC = () => {
  return (
    <SidebarProvider>
      <Sidebar />
    </SidebarProvider>
  );
};

export default AppSidebar;
export { useSidebar };
