import type React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

/**
 * EmailListItemSkeleton component
 * Displays a loading placeholder for an email item in the list
 */
export const EmailListItemSkeleton: React.FC = () => {
  return (
    <div className="border-b px-4 py-3 hover:bg-gray-50 flex flex-col animate-pulse">
      <div className="flex justify-between items-start mb-1">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-3 w-16" />
      </div>
      <Skeleton className="h-4 w-full mb-1" />
      <Skeleton className="h-3 w-3/4" />
    </div>
  );
};

/**
 * EmailListSkeleton component
 * Displays multiple email list item skeletons
 */
export const EmailListSkeleton: React.FC<{ count?: number }> = ({ count = 5 }) => {
  return (
    <div className="space-y-0 divide-y divide-border">
      {Array.from({ length: count }, (_, i) => `list-${i}`).map((key) => (
        <div key={key} className="p-4 animate-pulse">
          <div className="flex justify-between items-start">
            <div className="space-y-2 flex-1">
              <div className="flex items-center gap-2">
                <Skeleton className="h-3 w-3 rounded-full shrink-0" />
                <Skeleton className="h-4 w-[180px] sm:w-[250px]" />
              </div>
              <Skeleton className="h-3 w-[120px] sm:w-[150px]" />
            </div>
            <Skeleton className="h-3 w-16 shrink-0" />
          </div>
          <div className="mt-2 space-y-1.5">
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-[90%]" />
            <Skeleton className="h-3 w-[65%]" />
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * EmailDetailSkeleton component
 * Displays a loading placeholder for the email detail view
 */
export const EmailDetailSkeleton: React.FC = () => {
  return (
    <div className="border rounded-md p-4 md:p-6 space-y-4 animate-pulse">
      <div className="flex justify-between items-start">
        <div className="space-y-2">
          <Skeleton className="h-6 w-64" />
          <div className="flex items-center space-x-2">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-40" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
        </div>
        <Skeleton className="h-4 w-20" />
      </div>

      <div className="space-y-3 pt-4 border-t">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-5/6" />
        <Skeleton className="h-4 w-full" />
      </div>

      <div className="pt-4 border-t">
        <div className="flex space-x-2">
          <Skeleton className="h-9 w-24 rounded-md" />
          <Skeleton className="h-9 w-24 rounded-md" />
        </div>
      </div>
    </div>
  );
};

/**
 * EmailReplySkeleton component
 * Displays a loading placeholder for the email reply section
 */
export const EmailReplySkeleton: React.FC = () => {
  return (
    <div className="border rounded-md p-4 mt-4 space-y-3 animate-pulse">
      <div className="flex items-center justify-between">
        <Skeleton className="h-5 w-40" />
        <Skeleton className="h-4 w-20" />
      </div>

      <Skeleton className="h-24 w-full" />

      <div className="flex justify-end space-x-2 pt-2">
        <Skeleton className="h-9 w-24 rounded-md" />
        <Skeleton className="h-9 w-24 rounded-md" />
      </div>
    </div>
  );
};

/**
 * BatchEmailSkeleton component
 * Displays a loading placeholder for batch mode emails
 */
export const BatchEmailSkeleton: React.FC = () => {
  return (
    <div className="border rounded-md overflow-hidden mb-4 animate-pulse">
      <div className="p-3 border-b bg-gray-50">
        <div className="flex justify-between items-center">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-4 w-16" />
        </div>
      </div>

      <div className="p-4 space-y-3">
        <Skeleton className="h-5 w-3/4" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-5/6" />

        <div className="flex flex-wrap gap-2 pt-2">
          <Skeleton className="h-6 w-16 rounded-full" />
          <Skeleton className="h-6 w-20 rounded-full" />
        </div>
      </div>

      <div className="px-4 py-3 border-t bg-gray-50 flex justify-end space-x-2">
        <Skeleton className="h-8 w-20 rounded-md" />
        <Skeleton className="h-8 w-20 rounded-md" />
      </div>
    </div>
  );
};

/**
 * BatchModeSkeleton component
 * Displays a loading placeholder for the entire batch mode view
 */
export const BatchModeSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-2">
        <Skeleton className="h-6 w-40" />
        <Skeleton className="h-9 w-32 rounded-md" />
      </div>

      {Array.from({ length: count }, (_, index) => `batch-${index}`).map((key) => (
        <BatchEmailSkeleton key={key} />
      ))}
    </div>
  );
};
