import { Archive } from 'lucide-react';
import type React from 'react';
import { useEffect } from 'react';

import SimpleEmailList from '@/components/email/SimpleEmailList';
import AppLayout from '@/components/layout/AppLayout';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { useEmailList } from '@/context/EmailListContext';
import LoadingScreen from '@/components/ui/LoadingScreen';
import { defaultFilters } from '@/components/email/EmailFilter';

const ArchivePage: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const { emails, isLoading, refreshEmails, setFilters } = useEmailList();

  // Set filters for this page on mount and reset on unmount
  useEffect(() => {
    setFilters({ ...defaultFilters, status: 'archived' });

    // Reset filters when the component unmounts
    return () => {
      setFilters(defaultFilters);
    };
  }, [setFilters]);

  if (authLoading || isLoading) {
    return <LoadingScreen message="Loading archived emails..." />;
  }

  return (
    <AppLayout>
      <div className="h-full flex flex-col">
        <div className="archive-header border-b border-border p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Archive className="h-5 w-5 mr-2 text-muted-foreground" />
              <h1 className="text-lg sm:text-xl font-semibold text-foreground">Archive</h1>
              <div className="ml-2 text-xs sm:text-sm text-muted-foreground">
                {emails.length} messages
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refreshEmails()}
                disabled={isLoading}
                className="text-xs py-1 px-2 sm:px-3 h-8"
              >
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 overflow-hidden">
          <SimpleEmailList
            emails={emails}
            isLoading={isLoading}
            emptyStateIcon={<Archive className="h-12 w-12 text-muted-foreground/30" />}
            emptyStateTitle="Your archive is empty"
            emptyStateDescription="Archived emails will appear here."
            onRefresh={() => refreshEmails()}
          />
        </div>
      </div>
    </AppLayout>
  );
};

export default ArchivePage;
