import { act, renderHook, waitFor } from '@testing-library/react';
import type React from 'react';

// --------------------------- Mocks ---------------------------

// Mock apiClient used by the context
const mockApiClient = {
  get: jest.fn(),
  post: jest.fn(),
};
jest.mock('@/lib/apiClient', () => ({
  __esModule: true,
  default: mockApiClient,
}));

// Mock Firebase auth (only onAuthStateChanged needed for these tests)
const mockOnAuthStateChanged = jest.fn(() => jest.fn());
jest.mock('firebase/auth', () => ({
  onAuthStateChanged: (...args: any[]) => {
    const unsubscribe = jest.fn();
    // invoke callback immediately with null user to simulate logged-out initial state
    if (args.length >= 2) {
      const cb = args[1];
      cb(null);
    } else if (args.length === 1) {
      args[0](null);
    }
    return unsubscribe;
  },
}));

// Mock router
const mockNavigate = jest.fn();
jest.mock('wouter', () => ({
  useLocation: () => ['/', mockNavigate],
}));

// ---------------------- Import after mocks -------------------

import { AuthProvider, useAuth } from '@/context/AuthContext';

// Utility wrapper for renderHook
const wrapper = ({ children }: { children: React.ReactNode }) => (
  <AuthProvider>{children}</AuthProvider>
);

describe('AuthContext (modern API)', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('initialises with user=null (loading false after immediate callback)', () => {
    const { result } = renderHook(() => useAuth(), { wrapper });
    expect(result.current.loading).toBe(false);
    expect(result.current.user).toBeNull();
    expect(result.current.error).toBeNull();
  });

  it('refetchUser sets user on success', async () => {
    const mockUser = { id: 42, email: '<EMAIL>', name: 'U', role: 'user' } as any;
    mockApiClient.get.mockResolvedValueOnce({ isAuthenticated: true, user: mockUser });

    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      await result.current.refetchUser();
    });

    expect(mockApiClient.get).toHaveBeenCalledWith('/api/auth/status');
    expect(result.current.user).toEqual(mockUser);
    expect(result.current.loading).toBe(false);
  });

  it('logout clears user and navigates to login', async () => {
    mockApiClient.post.mockResolvedValueOnce({ success: true });

    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      await result.current.logout();
    });

    expect(mockApiClient.post).toHaveBeenCalledWith('/api/auth/logout');
    expect(result.current.user).toBeNull();
    expect(mockNavigate).toHaveBeenCalledWith('/login', { replace: true });
  });

  it.skip('clearAuthError resets authError', () => {});
}); 