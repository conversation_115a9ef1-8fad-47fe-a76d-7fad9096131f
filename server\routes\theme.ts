import { Router } from 'express';
import { sendSuccess } from '../lib/standardizedResponses';
import { requireAuth } from '../middleware/simpleAuth';

const router = Router();

// For now, we are not persisting theme to the database.
// These endpoints will mimic the behavior of the old inline routes.
let currentAppearance = 'system';

/**
 * GET /api/theme
 * Retrieves the current theme setting.
 * Note: This endpoint allows public access for theme retrieval
 */
router.get('/', (_req, res) => {
  // In the future, this could fetch the theme from user settings in the DB.
  res.json({ appearance: currentAppearance });
});

/**
 * PATCH /api/theme
 * Updates the theme setting.
 * Note: Requires authentication for modifications
 */
router.patch('/', requireAuth, (req, res) => {
  const { appearance } = req.body;

  if (appearance && ['light', 'dark', 'system'].includes(appearance)) {
    // In the future, this would save the theme to the user's settings in the DB.
    currentAppearance = appearance;
    sendSuccess(res, { theme: { appearance: currentAppearance } }, 'Theme updated successfully.');
  } else {
    res.status(400).json({ success: false, message: 'Invalid appearance value.' });
  }
});

export default router; 