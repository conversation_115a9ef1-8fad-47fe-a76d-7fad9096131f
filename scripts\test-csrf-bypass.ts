/**
 * CSRF Bypass Verification Script
 *
 * Tests that Firebase authentication endpoints properly bypass CSRF protection
 * while other endpoints still require CSRF tokens.
 */

import http from 'node:http';
import https from 'node:https';

interface TestConfig {
  host: string;
  port: number;
  protocol: 'http' | 'https';
}

interface TestEndpoints {
  bypass: string[];
  protected: string[];
}

interface TestResult {
  endpoint: string;
  status: 'PASSED' | 'FAILED' | 'ERROR';
  reason?: string;
  statusCode?: number;
  error?: string;
}

interface TestResults {
  bypassTests: TestResult[];
  protectedTests: TestResult[];
  summary: { passed: number; failed: number };
}

interface RequestResponse {
  statusCode: number;
  headers: http.IncomingHttpHeaders;
  body: string;
}

// Configuration
const TEST_CONFIG: Record<string, TestConfig> = {
  development: {
    host: 'localhost',
    port: 5000,
    protocol: 'http',
  },
  production: {
    host: process.env.PRODUCTION_HOST || 'your-production-domain.com',
    port: Number(process.env.PRODUCTION_PORT) || 443,
    protocol: 'https',
  },
};

// Test endpoints
const TEST_ENDPOINTS: TestEndpoints = {
  // Should bypass CSRF
  bypass: ['/api/auth/firebase/verify', '/api/auth/register', '/api/auth/test-post'],
  // Should require CSRF
  protected: ['/api/user/me', '/api/emails/sync'],
};

// Mock Firebase token for testing (this won't actually authenticate, but tests CSRF bypass)
const MOCK_FIREBASE_TOKEN =
  'eyJhbGciOiJSUzI1NiIsImtpZCI6InRlc3QiLCJ0eXAiOiJKV1QifQ.eyJpc3MiOiJ0ZXN0IiwiYXVkIjoidGVzdCIsInN1YiI6InRlc3QiLCJpYXQiOjE2MDAwMDAwMDAsImV4cCI6MTYwMDAwMzYwMH0.test';

function makeRequest(
  options: http.RequestOptions,
  data: string | null = null
): Promise<RequestResponse> {
  return new Promise((resolve, reject) => {
    const client = options.protocol === 'https:' ? https : http;

    const req = client.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode || 0,
          headers: res.headers,
          body: body,
        });
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(data);
    }

    req.end();
  });
}

async function testCsrfBypass(environment: string): Promise<TestResults> {
  const config = TEST_CONFIG[environment];
  console.log(`\n🧪 Testing CSRF bypass for ${environment.toUpperCase()} environment`);
  console.log(`   Target: ${config.protocol}://${config.host}:${config.port}`);
  console.log('='.repeat(60));

  const results: TestResults = {
    bypassTests: [],
    protectedTests: [],
    summary: { passed: 0, failed: 0 },
  };

  // Test 1: Health check
  console.log('\n📋 1. Health Check');
  try {
    const healthResponse = await makeRequest({
      hostname: config.host,
      port: config.port,
      path: '/api/health',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (healthResponse.statusCode === 200) {
      console.log('   ✅ Server is responding');
    } else {
      console.log(`   ❌ Server health check failed: ${healthResponse.statusCode}`);
      return results;
    }
  } catch (error) {
    console.log(
      `   ❌ Cannot connect to server: ${error instanceof Error ? error.message : String(error)}`
    );
    return results;
  }

  // Test 2: CSRF bypass endpoints (should work without CSRF token)
  console.log('\n📋 2. Testing CSRF Bypass Endpoints');
  for (const endpoint of TEST_ENDPOINTS.bypass) {
    console.log(`   Testing: ${endpoint}`);

    try {
      const response = await makeRequest(
        {
          hostname: config.host,
          port: config.port,
          path: endpoint,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        },
        JSON.stringify({
          idToken: MOCK_FIREBASE_TOKEN,
          test: 'data',
        })
      );

      // For bypass endpoints, we expect either:
      // - 200/201: Success (for test endpoints)
      // - 401: Unauthorized (Firebase token invalid, but CSRF was bypassed)
      // - 400: Bad request (but not CSRF error)
      // We should NOT get 403 with "invalid csrf token"

      const isCSRFError =
        response.statusCode === 403 || response.body?.toLowerCase().includes('csrf');

      if (isCSRFError) {
        console.log('   ❌ CSRF bypass FAILED - endpoint is protected by CSRF');
        results.bypassTests.push({ endpoint, status: 'FAILED', reason: 'CSRF protection active' });
        results.summary.failed++;
      } else {
        console.log(
          `   ✅ CSRF bypass SUCCESS - endpoint bypassed CSRF (status: ${response.statusCode})`
        );
        results.bypassTests.push({ endpoint, status: 'PASSED', statusCode: response.statusCode });
        results.summary.passed++;
      }
    } catch (error) {
      console.log(
        `   ❌ Request failed: ${error instanceof Error ? error.message : String(error)}`
      );
      results.bypassTests.push({
        endpoint,
        status: 'ERROR',
        error: error instanceof Error ? error.message : String(error),
      });
      results.summary.failed++;
    }
  }

  // Test 3: Get CSRF token for protected endpoint tests
  console.log('\n📋 3. Getting CSRF Token');
  let csrfToken: string | null = null;
  try {
    const csrfResponse = await makeRequest({
      hostname: config.host,
      port: config.port,
      path: '/api/auth/csrf',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (csrfResponse.statusCode === 200) {
      const csrfData = JSON.parse(csrfResponse.body);
      csrfToken = csrfData.csrfToken;
      if (csrfToken) {
        console.log(`   ✅ CSRF token obtained: ${csrfToken.substring(0, 20)}...`);
      } else {
        console.log('   ❌ CSRF token not found in response');
      }
    } else {
      console.log(`   ❌ Failed to get CSRF token: ${csrfResponse.statusCode}`);
    }
  } catch (error) {
    console.log(
      `   ❌ Error getting CSRF token: ${error instanceof Error ? error.message : String(error)}`
    );
  }

  // Test 4: Protected endpoints (should require CSRF token)
  console.log('\n📋 4. Testing CSRF Protected Endpoints');
  for (const endpoint of TEST_ENDPOINTS.protected) {
    console.log(`   Testing: ${endpoint}`);

    // Test without CSRF token (should fail)
    try {
      const responseWithoutCSRF = await makeRequest(
        {
          hostname: config.host,
          port: config.port,
          path: endpoint,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        },
        JSON.stringify({ test: 'data' })
      );

      const isCSRFError =
        responseWithoutCSRF.statusCode === 403 ||
        responseWithoutCSRF.body?.toLowerCase().includes('csrf');

      if (isCSRFError) {
        console.log('   ✅ CSRF protection ACTIVE - endpoint correctly requires CSRF token');
        results.protectedTests.push({
          endpoint,
          status: 'PASSED',
          reason: 'CSRF protection active',
        });
        results.summary.passed++;
      } else {
        console.log('   ❌ CSRF protection FAILED - endpoint does not require CSRF token');
        results.protectedTests.push({ endpoint, status: 'FAILED', reason: 'No CSRF protection' });
        results.summary.failed++;
      }
    } catch (error) {
      console.log(
        `   ❌ Request failed: ${error instanceof Error ? error.message : String(error)}`
      );
      results.protectedTests.push({
        endpoint,
        status: 'ERROR',
        error: error instanceof Error ? error.message : String(error),
      });
      results.summary.failed++;
    }
  }

  return results;
}

// Main execution
async function main(): Promise<void> {
  const environment = process.env.NODE_ENV === 'production' ? 'production' : 'development';

  console.log('🔒 CSRF Bypass Verification Script');
  console.log('==================================');

  const results = await testCsrfBypass(environment);

  console.log('\n📊 FINAL RESULTS');
  console.log('================');
  console.log(`✅ Passed: ${results.summary.passed}`);
  console.log(`❌ Failed: ${results.summary.failed}`);

  if (results.summary.failed > 0) {
    console.log('\n⚠️  Some tests failed. Please review the CSRF configuration.');
    process.exit(1);
  } else {
    console.log('\n🎉 All CSRF tests passed!');
    process.exit(0);
  }
}

main().catch((error) => {
  console.error('❌ Script execution failed:', error);
  process.exit(1);
});
