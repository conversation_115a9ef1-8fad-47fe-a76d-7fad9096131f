import { Loader2 } from 'lucide-react';
import type React from 'react';
import { useMemo, useState } from 'react';
import {
  Area,
  AreaChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useMemoryMonitor } from './context/MemoryMonitorContext';

export const ChartTabs: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const {
    memoryStatsQuery,
    memoryHistoryQuery,
    poolStatsQuery,
    systemResourcesQuery,
    triggerGarbageCollection,
  } = useMemoryMonitor();

  const { data: memoryStats, isLoading: isLoadingStats } = memoryStatsQuery;
  const { data: memoryHistory, isLoading: isLoadingHistory } = memoryHistoryQuery;
  const { data: poolStats, isLoading: isLoadingPool } = poolStatsQuery;
  const { data: systemResources, isLoading: isLoadingSystem } = systemResourcesQuery;

  const chartData = useMemo(() => {
    if (!memoryHistory?.stats) return [];
    return memoryHistory.stats.map((stat) => {
      const timestamp = new Date(stat.timestamp);
      const time = !Number.isNaN(timestamp.getTime())
        ? timestamp.toLocaleTimeString()
        : 'Invalid Time';
      return {
        time,
        heapUsed: stat.heapUsed,
        heapTotal: stat.heapTotal,
        rss: stat.rss,
      };
    });
  }, [memoryHistory]);

  const memoryLeakStatus = useMemo(() => {
    if (!memoryHistory?.leakDetection) {
      return { detected: false, confidence: 0, details: 'No data available' };
    }
    return memoryHistory.leakDetection;
  }, [memoryHistory]);

  const currentStatus = useMemo(() => {
    if (!memoryStats) return 'Unknown';
    return memoryStats.pressureLevel;
  }, [memoryStats]);

  const memoryRecommendations = useMemo(() => {
    switch (currentStatus) {
      case 'CRITICAL':
        return [
          'Immediate action required: Reduce application load',
          'Consider restarting the application',
        ];
      case 'HIGH':
        return ['Review memory-intensive operations', 'Check for memory leaks'];
      case 'MEDIUM':
        return ['Monitor memory trends', 'Review caching strategies'];
      default:
        return ['Memory usage is healthy. No action required.'];
    }
  }, [currentStatus]);

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid grid-cols-4 mb-4">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="history">History</TabsTrigger>
        <TabsTrigger value="resources">Resources</TabsTrigger>
        <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
      </TabsList>
      <TabsContent value="overview" className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Current Memory Metrics</CardTitle>
              <CardDescription>Detailed breakdown of memory usage</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingStats ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : memoryStats ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Heap Memory</div>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <div className="text-xs text-muted-foreground">Used</div>
                          <div className="text-base font-medium">{memoryStats.heapUsed} MB</div>
                        </div>
                        <div>
                          <div className="text-xs text-muted-foreground">Total</div>
                          <div className="text-base font-medium">{memoryStats.heapTotal} MB</div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm font-medium">External Memory</div>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <div className="text-xs text-muted-foreground">C++ Objects</div>
                          <div className="text-base font-medium">{memoryStats.external} MB</div>
                        </div>
                        <div>
                          <div className="text-xs text-muted-foreground">ArrayBuffers</div>
                          <div className="text-base font-medium">{memoryStats.arrayBuffers} MB</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="pt-4 border-t">
                    <div className="text-sm font-medium mb-2">Memory Pressure Level</div>
                    <div className="flex items-center space-x-2">
                      <Badge
                        className={
                          currentStatus === 'LOW'
                            ? 'bg-green-500'
                            : currentStatus === 'MEDIUM'
                              ? 'bg-yellow-500'
                              : currentStatus === 'HIGH'
                                ? 'bg-orange-500'
                                : 'bg-red-500'
                        }
                      >
                        {currentStatus}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {currentStatus === 'LOW' && 'Memory usage is optimal'}
                        {currentStatus === 'MEDIUM' && 'Memory usage is moderately high'}
                        {currentStatus === 'HIGH' && 'Memory usage is high, consider optimization'}
                        {currentStatus === 'CRITICAL' &&
                          'Memory usage is critical, immediate action required'}
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-muted-foreground">
                  Failed to load memory metrics
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" onClick={triggerGarbageCollection}>
                Trigger Garbage Collection
              </Button>
            </CardFooter>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Database Connection Pool</CardTitle>
              <CardDescription>Database resource utilization</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingPool ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : poolStats ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Connection Status</div>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <div className="text-xs text-muted-foreground">Active</div>
                          <div className="text-base font-medium">{poolStats.active}</div>
                        </div>
                        <div>
                          <div className="text-xs text-muted-foreground">Idle</div>
                          <div className="text-base font-medium">{poolStats.idle}</div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Pool Configuration</div>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <div className="text-xs text-muted-foreground">Min Size</div>
                          <div className="text-base font-medium">{poolStats.poolMin}</div>
                        </div>
                        <div>
                          <div className="text-xs text-muted-foreground">Max Size</div>
                          <div className="text-base font-medium">{poolStats.poolMax}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="pt-4 border-t">
                    <div className="text-sm font-medium mb-2">Pool Utilization</div>
                    <Progress
                      value={((poolStats.active + poolStats.idle) / poolStats.poolMax) * 100}
                      className="h-2"
                    />
                    <div className="text-xs mt-1 text-right text-muted-foreground">
                      {poolStats.total} of {poolStats.poolMax} connections (
                      {(((poolStats.active + poolStats.idle) / poolStats.poolMax) * 100).toFixed(1)}
                      %)
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-muted-foreground">
                  Failed to load pool statistics
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Memory Usage Trend</CardTitle>
            <CardDescription>Real-time memory allocation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              {isLoadingHistory ? (
                <div className="flex justify-center items-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : chartData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={chartData}>
                    <defs>
                      <linearGradient id="colorHeapUsed" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                        <stop offset="95%" stopColor="#8884d8" stopOpacity={0.2} />
                      </linearGradient>
                      <linearGradient id="colorRSS" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#82ca9d" stopOpacity={0.8} />
                        <stop offset="95%" stopColor="#82ca9d" stopOpacity={0.2} />
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                    <XAxis dataKey="time" stroke="#888" />
                    <YAxis stroke="#888" unit="MB" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(22, 22, 22, 0.9)',
                        border: '1px solid #444',
                        borderRadius: '4px',
                        color: '#ddd',
                      }}
                      formatter={(value: any) => [`${value} MB`, undefined]}
                    />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="heapUsed"
                      name="Heap Used"
                      stroke="#8884d8"
                      fillOpacity={1}
                      fill="url(#colorHeapUsed)"
                    />
                    <Area
                      type="monotone"
                      dataKey="rss"
                      name="Total Memory (RSS)"
                      stroke="#82ca9d"
                      fillOpacity={1}
                      fill="url(#colorRSS)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              ) : (
                <div className="text-center text-muted-foreground h-full flex items-center justify-center">
                  Not enough data for trend analysis.
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="history">
        <Card>
          <CardHeader>
            <CardTitle>Historical Memory Usage</CardTitle>
            <CardDescription>Heap and RSS usage over the last hour.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-96">
              {isLoadingHistory ? (
                <div className="flex justify-center items-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : chartData.length > 1 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                    <XAxis dataKey="time" stroke="#888" />
                    <YAxis stroke="#888" unit="MB" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(22, 22, 22, 0.9)',
                        border: '1px solid #444',
                        borderRadius: '4px',
                        color: '#ddd',
                      }}
                      formatter={(value: any) => [`${value} MB`, undefined]}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="heapTotal"
                      name="Heap Total"
                      stroke="#ff7300"
                      activeDot={{ r: 8 }}
                    />
                    <Line type="monotone" dataKey="heapUsed" name="Heap Used" stroke="#8884d8" />
                    <Line type="monotone" dataKey="rss" name="RSS Memory" stroke="#82ca9d" />
                    <Line type="monotone" dataKey="external" name="External" stroke="#d88884" />
                    <Line
                      type="monotone"
                      dataKey="arrayBuffers"
                      name="ArrayBuffers"
                      stroke="#84d888"
                    />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <div className="text-center text-muted-foreground h-full flex items-center justify-center">
                  Not enough data for historical analysis.
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="resources">
        <Card>
          <CardHeader>
            <CardTitle>System Resources</CardTitle>
            <CardDescription>Overall server CPU and Memory usage.</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingSystem ? (
              <div className="flex justify-center items-center h-60">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : systemResources ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">System Memory</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Usage</span>
                      <span>
                        {systemResources.memory.usedPercent.toFixed(1)}% (
                        {systemResources.memory.totalMB - systemResources.memory.freeMB} MB /{' '}
                        {systemResources.memory.totalMB} MB)
                      </span>
                    </div>
                    <Progress value={systemResources.memory.usedPercent} className="h-2" />
                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div>
                        <div className="text-xs text-muted-foreground">Total Memory</div>
                        <div className="text-base font-medium">
                          {systemResources.memory.totalMB} MB
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-muted-foreground">Free Memory</div>
                        <div className="text-base font-medium">
                          {systemResources.memory.freeMB} MB
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">CPU Resources</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>CPU Usage</span>
                      <span>{systemResources.cpu.usagePercent.toFixed(1)}%</span>
                    </div>
                    <Progress value={systemResources.cpu.usagePercent} className="h-2" />
                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div>
                        <div className="text-xs text-muted-foreground">CPU Cores</div>
                        <div className="text-base font-medium">{systemResources.cpu.cores}</div>
                      </div>
                      <div>
                        <div className="text-xs text-muted-foreground">Load Average</div>
                        <div className="text-base font-medium">
                          {systemResources.cpu.loadAverage
                            .map((l: number) => l.toFixed(2))
                            .join(' | ')}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-muted-foreground h-60 flex items-center justify-center">
                Failed to load system resource data.
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="recommendations">
        <Card>
          <CardHeader>
            <CardTitle>Analysis & Recommendations</CardTitle>
            <CardDescription>
              Based on current memory status and historical patterns.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start space-x-4 rounded-lg border p-4">
              <div
                className={`text-2xl font-bold ${
                  memoryLeakStatus.detected ? 'text-red-500' : 'text-green-500'
                }`}
              >
                {memoryLeakStatus.detected ? 'Potential Leak Detected' : 'No Leaks Detected'}
              </div>
              <div className="flex-1 space-y-1">
                <p className="text-sm font-medium leading-none">
                  {memoryLeakStatus.detected
                    ? `Confidence: ${memoryLeakStatus.confidence.toFixed(0)}%`
                    : 'Memory patterns appear stable.'}
                </p>
                <p className="text-sm text-muted-foreground">{memoryLeakStatus.details}</p>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">Recommendations:</h4>
              <ul className="list-disc list-inside space-y-2 text-sm text-muted-foreground">
                {memoryRecommendations.map((rec, i) => (
                  <li key={i}>{rec}</li>
                ))}
              </ul>
            </div>
          </CardContent>
          <CardFooter>
            <p className="text-xs text-muted-foreground">
              Note: These recommendations are auto-generated and should be used as a guide, not a
              definitive solution.
            </p>
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default ChartTabs;
