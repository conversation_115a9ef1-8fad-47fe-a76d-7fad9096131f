export interface Achievement {
  id: string;
  name: string;
  description: string;
  achieved: boolean;
  progress: {
    current: number;
    target: number;
  };
  points: number;
}

export interface AchievementCategory {
  id: string;
  name: string;
  achievements: Achievement[];
}

export interface AchievementsData {
  totalPoints: number;
  level: number;
  categories: AchievementCategory[];
} 