/**
 * Memory Monitoring API Routes
 *
 * Provides endpoints for memory monitoring dashboard and memory management.
 */

import os from 'node:os';
import express, { type NextFunction, type Request, type Response } from 'express';
import logger from '../lib/logger';
import { isAdmin } from '../middleware/admin';
import { databasePoolManager } from '../utils/databasePoolManager';
import { memoryManager } from '../utils/memoryManager';

// Initialize router
const router = express.Router();

// Apply admin middleware to all routes
router.use((req: Request, res: Response, next: NextFunction) => {
  isAdmin(req, res, next);
});

/**
 * Get current memory statistics
 */
router.get('/stats', async (_req: Request, res: Response): Promise<void> => {
  try {
    const stats = memoryManager.getMemoryStats();
    res.json(stats);
  } catch (error) {
    logger.error('Error fetching memory stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch memory statistics',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

/**
 * Get memory usage history
 */
router.get('/history', async (req: Request, res: Response): Promise<void> => {
  try {
    // Get limit from query parameter, default to 50
    const limit = Number.parseInt(req.query.limit as string) || 50;

    // Get memory history data
    const stats = memoryManager.getMemoryHistory(limit);

    // Get memory leak detection
    const leakDetection = memoryManager.detectMemoryLeaks();

    res.json({
      stats,
      leakDetection,
    });
  } catch (error) {
    logger.error('Error fetching memory history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch memory history',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

/**
 * Get database pool statistics
 */
router.get('/pool-stats', async (_req: Request, res: Response): Promise<void> => {
  try {
    const stats = databasePoolManager.getPoolStats();
    res.json(stats);
  } catch (error) {
    logger.error('Error fetching pool stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch database pool statistics',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

/**
 * Get system resource information
 */
router.get('/system-resources', async (_req: Request, res: Response): Promise<void> => {
  try {
    // Get total and free memory
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;

    // Get CPU info
    const cpuCores = os.cpus().length;
    const loadAverage = os.loadavg();

    // Estimate CPU usage (simple approximation)
    // More accurate measurement would require sampling over time
    const cpuUsagePercent = Math.min(100, (loadAverage[0] / cpuCores) * 100);

    res.json({
      memory: {
        totalMB: Math.round(totalMemory / 1024 / 1024),
        freeMB: Math.round(freeMemory / 1024 / 1024),
        usedPercent: (usedMemory / totalMemory) * 100,
      },
      cpu: {
        cores: cpuCores,
        loadAverage,
        usagePercent: cpuUsagePercent,
      },
      uptime: os.uptime(),
    });
  } catch (error) {
    logger.error('Error fetching system resources:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system resources',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

/**
 * Trigger garbage collection
 */
router.post('/gc', async (_req: Request, res: Response): Promise<void> => {
  try {
    logger.info('Manual garbage collection triggered by admin');

    // Run memory cleanup
    await memoryManager.runCleanup(true);

    // Return success
    res.json({
      success: true,
      message: 'Garbage collection triggered successfully',
    });
  } catch (error) {
    logger.error('Error triggering garbage collection:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to trigger garbage collection',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

/**
 * Get cache size recommendations
 */
router.get('/cache-recommendations', async (_req: Request, res: Response): Promise<void> => {
  try {
    const recommendations = memoryManager.getCacheSizeRecommendation();
    res.json(recommendations);
  } catch (error) {
    logger.error('Error fetching cache recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch cache recommendations',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

/**
 * Get resource pool recommendations
 */
router.get('/pool-recommendations', async (_req: Request, res: Response): Promise<void> => {
  try {
    const recommendations = memoryManager.getResourcePoolRecommendation();
    res.json(recommendations);
  } catch (error) {
    logger.error('Error fetching pool recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pool recommendations',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

/**
 * Start memory monitoring
 */
router.post('/monitoring/start', async (req: Request, res: Response): Promise<void> => {
  try {
    const intervalMs = req.body.intervalMs || 60000;
    memoryManager.startMonitoring(intervalMs);

    res.json({
      success: true,
      message: `Memory monitoring started with interval: ${intervalMs}ms`,
    });
  } catch (error) {
    logger.error('Error starting memory monitoring:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to start memory monitoring',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

/**
 * Stop memory monitoring
 */
router.post('/monitoring/stop', async (_req: Request, res: Response): Promise<void> => {
  try {
    memoryManager.stopMonitoring();

    res.json({
      success: true,
      message: 'Memory monitoring stopped',
    });
  } catch (error) {
    logger.error('Error stopping memory monitoring:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to stop memory monitoring',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

export default router;
