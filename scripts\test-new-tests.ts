#!/usr/bin/env node

/**
 * Test runner for newly created Jest tests
 * This script runs all the tests we've created to verify they work correctly
 */

import { execSync } from 'node:child_process';

const testFiles = [
  'tests/server/middleware/csrf.test.ts',
  'tests/server/middleware/simpleAuth.test.ts',
  'tests/client/lib/apiClient.test.ts',
  'tests/client/context/AuthContext.test.tsx',
  'tests/server/routes/emails.test.ts',
  'tests/server/utils/circuitBreaker.test.ts',
];

console.log('🧪 Running newly created Jest tests...\n');

let passedTests = 0;
let failedTests = 0;

for (const testFile of testFiles) {
  console.log(`\n📋 Testing: ${testFile}`);
  console.log('='.repeat(50));

  try {
    // Run the test with minimal output
    const result = execSync(
      `npm test -- --testPathPattern="${testFile}" --passWithNoTests --silent`,
      {
        cwd: process.cwd(),
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe'],
      }
    );

    console.log(`✅ PASSED: ${testFile}`);
    passedTests++;

    // Extract test summary if available
    const lines = result.split('\n');
    const summaryLine = lines.find((line) => line.includes('Tests:') || line.includes('passed'));
    if (summaryLine) {
      console.log(`   ${summaryLine.trim()}`);
    }
  } catch (error) {
    console.log(`❌ FAILED: ${testFile}`);
    failedTests++;

    // Show error details
    const execError = error as any;
    const output = execError.stdout || execError.stderr || execError.message || '';
    const lines = output.split('\n');

    // Find and show the test summary
    const summaryLine = lines.find((line) => line.includes('Tests:') || line.includes('failed'));
    if (summaryLine) {
      console.log(`   ${summaryLine.trim()}`);
    }

    // Show first few error lines
    const errorLines = lines
      .filter(
        (line) =>
          line.includes('●') ||
          line.includes('Expected') ||
          line.includes('Received') ||
          line.includes('Error:')
      )
      .slice(0, 3);

    if (errorLines.length > 0) {
      console.log('   Error details:');
      for (const line of errorLines) {
        console.log(`   ${line.trim()}`);
      }
    }
  }
}

console.log(`\n${'='.repeat(50)}`);
console.log('📊 TEST SUMMARY');
console.log('='.repeat(50));
console.log(`✅ Passed: ${passedTests}`);
console.log(`❌ Failed: ${failedTests}`);
console.log(`📁 Total:  ${testFiles.length}`);

if (failedTests === 0) {
  console.log('\n🎉 All tests are working correctly!');
  process.exit(0);
} else {
  console.log(`\n⚠️  ${failedTests} test file(s) need attention.`);
  console.log('\nTo debug individual tests, run:');
  console.log('npm test -- --testPathPattern="<test-file>" --verbose');
  process.exit(1);
}
