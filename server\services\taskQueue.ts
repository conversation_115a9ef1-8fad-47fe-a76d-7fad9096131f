/**
 * Task Queue Service
 *
 * This service handles the background processing of tasks in a queue.
 * It provides functions to enqueue tasks, process the queue, and handle retries.
 */

import type { InsertTaskQueue, TaskQueue } from '@shared/schema';
import { taskQueue } from '@shared/schema';
import { and, asc, desc, eq, isNull, lte, sql } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import { getDb } from '../db';
import logger from '../lib/logger';

// Worker ID for this instance
const WORKER_ID = `worker-${uuidv4()}`;

// Task processing constants
const LOCK_TIMEOUT_MS = 5 * 60 * 1000; // 5 minutes
const MAX_TASKS_PER_BATCH = 10;

// Configuration interface
interface TaskQueueConfig {
  pollingInterval: number;
  isProduction: boolean;
  isQuickStart: boolean;
}

// Default configuration
let config: TaskQueueConfig = {
  pollingInterval: 15000, // 15 seconds default
  isProduction: false,
  isQuickStart: false,
};

/**
 * Initialize task queue configuration
 * This should be called during server startup after environment validation
 */
export function initializeTaskQueue(taskConfig: Partial<TaskQueueConfig> = {}): void {
  config = {
    ...config,
    ...taskConfig,
  };

  // Set default polling interval based on environment if not explicitly provided
  if (!taskConfig.pollingInterval) {
    if (config.isQuickStart) {
      config.pollingInterval = 60000; // 1 minute in quick start mode
    } else if (config.isProduction) {
      config.pollingInterval = 5000; // 5 seconds in production
    } else {
      config.pollingInterval = 15000; // 15 seconds in development
    }
  }

  logger.info('Task queue initialized', {
    workerId: WORKER_ID,
    pollingInterval: config.pollingInterval,
    isProduction: config.isProduction,
    isQuickStart: config.isQuickStart,
  });
}

// Task types
export enum TaskType {
  EMAIL_SUMMARY = 'email_summary',
  EMAIL_CATEGORIZATION = 'email_categorization',
  REPLY_GENERATION = 'reply_generation',
  BATCH_PROCESSING = 'batch_processing',
}

// Task status
export enum TaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  RETRY = 'retry',
}

// Task priority
export enum TaskPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  URGENT = 4,
}

// Task handlers registry
type TaskHandler = (task: TaskQueue) => Promise<any>;
const taskHandlers: Record<string, TaskHandler> = {};

/**
 * Register a task handler
 *
 * @param taskType The type of task
 * @param handler The handler function
 */
export function registerTaskHandler(taskType: string, handler: TaskHandler) {
  taskHandlers[taskType] = handler;
  logger.info(`Registered task handler for ${taskType}`);
}

/**
 * Enqueue a task
 *
 * @param taskData The task data
 * @returns The created task
 */
export async function enqueueTask(taskData: InsertTaskQueue): Promise<TaskQueue> {
  try {
    const db = await getDb();
    const [task] = await db.insert(taskQueue).values(taskData).returning();

    logger.info(`Enqueued task: ${task.id} (${task.taskType})`);
    return task;
  } catch (error) {
    logger.error(`Failed to enqueue task: ${error}`);
    throw error;
  }
}

/**
 * Get the next batch of tasks to process
 *
 * @returns The next batch of tasks
 */
async function getNextTasks(): Promise<TaskQueue[]> {
  try {
    const db = await getDb();
    // Get tasks that are:
    // 1. Pending or scheduled for retry
    // 2. Not locked OR locked but timed out
    // 3. Scheduled for now or in the past (or not scheduled at all)
    const now = new Date();
    const lockTimeout = new Date(now.getTime() - LOCK_TIMEOUT_MS);

    logger.debug(
      `Looking for tasks with status 'pending' or 'retry', not locked or lock timed out, and scheduled now or in the past`
    );

    const tasks = await db
      .select()
      .from(taskQueue)
      .where(
        and(
          // Status is pending or retry
          sql`${taskQueue.status} IN ('pending', 'retry')`,

          // Not locked OR lock timed out
          sql`(${taskQueue.lockedBy} IS NULL OR ${taskQueue.lockedAt} < ${lockTimeout.toISOString()})`,

          // Not scheduled for the future
          sql`(${taskQueue.scheduledFor} IS NULL OR ${taskQueue.scheduledFor} <= ${now.toISOString()})`
        )
      )
      .orderBy(
        desc(taskQueue.priority), // Higher priority first
        asc(taskQueue.createdAt) // Older tasks first
      )
      .limit(MAX_TASKS_PER_BATCH);

    if (tasks.length > 0) {
      logger.info(
        `Found ${tasks.length} tasks to process. First task ID: ${tasks[0].id}, type: ${tasks[0].taskType}`
      );
    } else {
      logger.debug('No tasks found to process');
    }

    return tasks;
  } catch (error) {
    logger.error(`Failed to get next tasks: ${error}`);
    return [];
  }
}

/**
 * Lock a task for processing
 *
 * @param taskId The ID of the task to lock
 * @returns Whether the lock was successful
 */
async function lockTask(taskId: number): Promise<boolean> {
  const timer = logger.startTimer();

  try {
    const db = await getDb();
    const now = new Date();

    // Lock the task by updating it with worker ID and lock time
    const [updatedTask] = await db
      .update(taskQueue)
      .set({
        lockedBy: WORKER_ID,
        lockedAt: now,
        status: TaskStatus.PROCESSING,
        lastAttemptAt: now,
      })
      .where(
        // Make sure the task is still unlocked when we update it
        and(eq(taskQueue.id, taskId), isNull(taskQueue.lockedBy))
      )
      .returning();

    const duration = timer();
    const success = !!updatedTask;

    if (success) {
      logger.taskQueue.debug('Task locked successfully', taskId, {
        workerId: WORKER_ID,
        lockTime: now.toISOString(),
        duration,
        taskType: updatedTask.taskType,
      });
    } else {
      logger.taskQueue.debug('Task lock failed (already locked or not found)', taskId, {
        workerId: WORKER_ID,
        duration,
      });
    }

    return success;
  } catch (error) {
    const duration = timer();
    logger.taskQueue.error('Error locking task', error, taskId, {
      workerId: WORKER_ID,
      duration,
    });
    return false;
  }
}

/**
 * Complete a task successfully
 *
 * @param taskId The ID of the task to complete
 * @param result The result of the task
 */
async function completeTask(taskId: number, result: any): Promise<void> {
  const timer = logger.startTimer();

  try {
    const db = await getDb();
    const now = new Date();

    await db
      .update(taskQueue)
      .set({
        status: TaskStatus.COMPLETED,
        completedAt: now,
        result: result ? JSON.stringify(result) : null,
        lockedBy: null,
        lockedAt: null,
      })
      .where(eq(taskQueue.id, taskId));

    const duration = timer();
    logger.taskQueue.debug('Task completed', taskId, {
      workerId: WORKER_ID,
      duration,
      hasResult: !!result,
    });
  } catch (error) {
    const duration = timer();
    logger.taskQueue.error('Error completing task', error, taskId, {
      workerId: WORKER_ID,
      duration,
    });
  }
}

/**
 * Mark a task as failed
 *
 * @param taskId The ID of the task that failed
 * @param error The error that caused the failure
 */
async function failTask(taskId: number, error: any): Promise<void> {
  const timer = logger.startTimer();

  try {
    const db = await getDb();
    const now = new Date();

    // Get current task to check retry count
    const [currentTask] = await db
      .select()
      .from(taskQueue)
      .where(eq(taskQueue.id, taskId))
      .limit(1);

    if (!currentTask) {
      logger.taskQueue.warn('Task not found when trying to fail it', taskId, {
        workerId: WORKER_ID,
      });
      return;
    }

    const retryCount = (currentTask.retryCount || 0) + 1;
    const maxRetries = currentTask.maxRetries || 3;

    // Determine if we should retry or mark as failed
    if (retryCount <= maxRetries) {
      // Schedule for retry with exponential backoff
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount - 1), 30000); // Max 30 seconds
      const scheduledFor = new Date(now.getTime() + retryDelay);

      await db
        .update(taskQueue)
        .set({
          status: TaskStatus.RETRY,
          retryCount,
          scheduledFor,
          error: String(error).substring(0, 1000),
          lockedBy: null,
          lockedAt: null,
          lastAttemptAt: now,
        })
        .where(eq(taskQueue.id, taskId));

      logger.taskQueue.info('Task scheduled for retry', taskId, {
        workerId: WORKER_ID,
        retryCount,
        maxRetries,
        retryDelay,
        scheduledFor: scheduledFor.toISOString(),
        error: String(error).substring(0, 200),
      });
    } else {
      // Mark as permanently failed
      await db
        .update(taskQueue)
        .set({
          status: TaskStatus.FAILED,
          retryCount,
          error: String(error).substring(0, 1000),
          lockedBy: null,
          lockedAt: null,
          lastAttemptAt: now,
        })
        .where(eq(taskQueue.id, taskId));

      logger.taskQueue.error('Task permanently failed after max retries', error, taskId, {
        workerId: WORKER_ID,
        retryCount,
        maxRetries,
      });
    }

    const duration = timer();
    logger.taskQueue.debug('Task failure processed', taskId, {
      workerId: WORKER_ID,
      duration,
      retryCount,
      maxRetries,
      willRetry: retryCount <= maxRetries,
    });
  } catch (failError) {
    const duration = timer();
    logger.taskQueue.error('Error failing task', failError, taskId, {
      workerId: WORKER_ID,
      duration,
      originalError: String(error).substring(0, 200),
    });
  }
}

/**
 * Process a single task
 *
 * @param task The task to process
 */
export async function processTask(task: TaskQueue): Promise<void> {
  const timer = logger.startTimer();

  try {
    // Check if handler exists
    const handler = taskHandlers[task.taskType];
    if (!handler) {
      throw new Error(`No handler registered for task type ${task.taskType}`);
    }

    logger.taskQueue.debug('Invoking handler for task', task.id, {
      workerId: WORKER_ID,
      taskType: task.taskType,
      retryCount: task.retryCount || 0,
    });

    // Execute the task handler
    const result = await handler(task);

    // Mark task as completed
    await completeTask(task.id, result);

    const duration = timer();
    logger.taskQueue.info('Task processed successfully', task.id, {
      workerId: WORKER_ID,
      taskType: task.taskType,
      duration,
      retryCount: task.retryCount || 0,
    });
  } catch (taskError) {
    const duration = timer();
    logger.taskQueue.error('Task processing failed', taskError, task.id, {
      workerId: WORKER_ID,
      taskType: task.taskType,
      duration,
      retryCount: task.retryCount || 0,
    });

    // Mark task as failed (this will handle retry logic)
    await failTask(task.id, taskError);
  }
}

/**
 * Process the task queue (single iteration)
 *
 * @returns The number of tasks processed
 */
export async function processQueue(): Promise<void> {
  const timer = logger.startTimer();

  try {
    logger.taskQueue.debug('Fetching next batch of tasks from queue', undefined, {
      workerId: WORKER_ID,
      maxBatch: MAX_TASKS_PER_BATCH,
    });

    const tasks = await getNextTasks();

    if (tasks.length === 0) {
      logger.taskQueue.debug('No tasks found in queue to process', undefined, {
        workerId: WORKER_ID,
      });
      return;
    }

    logger.taskQueue.info('Processing batch of tasks', undefined, {
      workerId: WORKER_ID,
      taskCount: tasks.length,
      taskIds: tasks.map((t) => t.id),
    });

    // Process tasks sequentially to avoid overwhelming the system
    for (const task of tasks) {
      try {
        logger.taskQueue.debug('Attempting to lock task', task.id, {
          workerId: WORKER_ID,
          taskType: task.taskType,
        });

        // Try to lock the task
        const locked = await lockTask(task.id);
        if (!locked) {
          logger.taskQueue.debug('Could not lock task, skipping', task.id, {
            workerId: WORKER_ID,
          });
          continue;
        }

        logger.taskQueue.debug('Successfully locked task, processing now', task.id, {
          workerId: WORKER_ID,
          taskType: task.taskType,
        });

        // Process the task
        await processTask(task);

        logger.taskQueue.debug('Task processing complete', task.id, {
          workerId: WORKER_ID,
          taskType: task.taskType,
        });
      } catch (taskError) {
        logger.taskQueue.error('Error processing individual task', taskError, task.id, {
          workerId: WORKER_ID,
          taskType: task.taskType,
        });
        // Continue with next task even if this one fails
      }
    }

    const duration = timer();
    logger.taskQueue.info('Queue processing cycle completed', undefined, {
      workerId: WORKER_ID,
      tasksProcessed: tasks.length,
      duration,
    });
  } catch (error) {
    const duration = timer();
    logger.taskQueue.error('Error processing queue', error, undefined, {
      workerId: WORKER_ID,
      duration,
    });
  }
}

// Track processor state
let processorInterval: NodeJS.Timeout | null = null;
let totalTasksProcessed = 0;
let startTime = Date.now();

/**
 * Start the task queue processor
 *
 * @returns The interval handle for stopping the processor
 */
export async function startTaskQueueProcessor(): Promise<NodeJS.Timeout> {
  if (processorInterval) {
    logger.taskQueue.warn('Task queue processor is already running', undefined, {
      workerId: WORKER_ID,
    });
    return processorInterval;
  }

  logger.taskQueue.info('Starting task queue processor', undefined, {
    workerId: WORKER_ID,
    pollingInterval: config.pollingInterval,
    isProduction: config.isProduction,
    isQuickStart: config.isQuickStart,
  });

  startTime = Date.now();
  totalTasksProcessed = 0;

  // Start the processing loop
  processorInterval = setInterval(async () => {
    try {
      logger.taskQueue.debug('Processor checking for tasks', undefined, {
        workerId: WORKER_ID,
        uptime: (Date.now() - startTime) / 1000,
        totalProcessed: totalTasksProcessed,
      });

      await processQueue();
      totalTasksProcessed++;

      // Log more detailed debug info
      logger.taskQueue.debug('Processing cycle complete', undefined, {
        workerId: WORKER_ID,
        cycleNumber: totalTasksProcessed,
        uptime: (Date.now() - startTime) / 1000,
      });
    } catch (error) {
      logger.taskQueue.error('Error in task queue processor cycle', error, undefined, {
        workerId: WORKER_ID,
        cycleNumber: totalTasksProcessed,
        uptime: (Date.now() - startTime) / 1000,
      });
    }
  }, config.pollingInterval);

  // Handle graceful shutdown
  process.on('SIGTERM', () => {
    logger.taskQueue.info('Received SIGTERM signal, shutting down task queue processor', undefined, {
      workerId: WORKER_ID,
      uptime: (Date.now() - startTime) / 1000,
      cyclesProcessed: totalTasksProcessed,
      timestamp: new Date().toISOString(),
    });
    if (processorInterval) {
      clearInterval(processorInterval);
      processorInterval = null;
    }
  });

  process.on('SIGINT', () => {
    logger.taskQueue.info('Received SIGINT signal, shutting down task queue processor', undefined, {
      workerId: WORKER_ID,
      uptime: (Date.now() - startTime) / 1000,
      cyclesProcessed: totalTasksProcessed,
      timestamp: new Date().toISOString(),
    });
    if (processorInterval) {
      clearInterval(processorInterval);
      processorInterval = null;
    }
  });

  return processorInterval;
}

/**
 * Stop the task queue processor
 */
export function stopTaskQueueProcessor(): void {
  if (processorInterval) {
    clearInterval(processorInterval);
    processorInterval = null;
    logger.taskQueue.info('Task queue processor stopped', undefined, {
      workerId: WORKER_ID,
      uptime: (Date.now() - startTime) / 1000,
      cyclesProcessed: totalTasksProcessed,
    });
  }
}

/**
 * Get task statistics
 *
 * @returns Statistics about the task queue
 */
export async function getTaskQueueStats() {
  const timer = logger.startTimer();

  try {
    const db = await getDb();
    // Get counts by status
    const statusCounts = await db
      .select({
        status: taskQueue.status,
        count: sql<number>`count(*)`,
      })
      .from(taskQueue)
      .groupBy(taskQueue.status);

    // Get counts by type
    const typeCounts = await db
      .select({
        type: taskQueue.taskType,
        count: sql<number>`count(*)`,
      })
      .from(taskQueue)
      .groupBy(taskQueue.taskType);

    // Get counts by priority
    const priorityCounts = await db
      .select({
        priority: taskQueue.priority,
        count: sql<number>`count(*)`,
      })
      .from(taskQueue)
      .groupBy(taskQueue.priority);

    // Get information about oldest pending tasks
    const oldestPendingTasks = await db
      .select({
        id: taskQueue.id,
        taskType: taskQueue.taskType,
        createdAt: taskQueue.createdAt,
        priority: taskQueue.priority,
      })
      .from(taskQueue)
      .where(eq(taskQueue.status, TaskStatus.PENDING))
      .orderBy(asc(taskQueue.createdAt))
      .limit(5);

    // Get information about stuck processing tasks (locked for more than 5 minutes)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const stuckProcessingTasks = await db
      .select({
        id: taskQueue.id,
        taskType: taskQueue.taskType,
        lockedAt: taskQueue.lockedAt,
        lockedBy: taskQueue.lockedBy,
      })
      .from(taskQueue)
      .where(
        and(
          eq(taskQueue.status, TaskStatus.PROCESSING),
          sql`${taskQueue.lockedAt} <= ${fiveMinutesAgo.toISOString()}`
        )
      )
      .orderBy(asc(taskQueue.lockedAt))
      .limit(5);

    // Format the results
    const stats = {
      status: Object.fromEntries(statusCounts.map((item) => [item.status, item.count])),
      type: Object.fromEntries(typeCounts.map((item) => [item.type, item.count])),
      priority: Object.fromEntries(priorityCounts.map((item) => [item.priority, item.count])),
      totalTasks: statusCounts.reduce((sum, item) => sum + item.count, 0),
      oldestPendingTasks: oldestPendingTasks.map((task) => ({
        id: task.id,
        type: task.taskType,
        age: Math.round((Date.now() - new Date(task.createdAt).getTime()) / 1000),
        priority: task.priority,
      })),
      stuckProcessingTasks: stuckProcessingTasks.map((task) => ({
        id: task.id,
        type: task.taskType,
        lockedSince: Math.round((Date.now() - new Date(task.lockedAt!).getTime()) / 1000),
        lockedBy: task.lockedBy,
      })),
      processor: {
        workerId: WORKER_ID,
        isRunning: !!processorInterval,
        uptime: processorInterval ? (Date.now() - startTime) / 1000 : 0,
        cyclesProcessed: totalTasksProcessed,
        pollingInterval: config.pollingInterval,
      },
    };

    const duration = timer();
    logger.taskQueue.debug('Task queue stats retrieved', undefined, {
      queryTime: duration,
      totalTasks: stats.totalTasks,
      pendingTasks: stats.status[TaskStatus.PENDING] || 0,
      processingTasks: stats.status[TaskStatus.PROCESSING] || 0,
      stuckTaskCount: stuckProcessingTasks.length,
    });

    return stats;
  } catch (error) {
    const duration = timer();
    logger.taskQueue.error('Error retrieving task queue stats', error, undefined, {
      queryTime: duration,
    });
    throw error;
  }
}

/**
 * Clean up old completed tasks
 *
 * @param olderThan Tasks older than this date will be deleted (default: 7 days)
 * @returns The number of tasks deleted
 */
export async function cleanupOldTasks(olderThan = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) {
  const timer = logger.startTimer();

  try {
    const db = await getDb();
    logger.taskQueue.info('Starting cleanup of old completed tasks', undefined, {
      cleanupDate: olderThan.toISOString(),
      daysOld: Math.round((Date.now() - olderThan.getTime()) / (24 * 60 * 60 * 1000)),
    });

    // First count how many will be deleted
    const countResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(taskQueue)
      .where(
        and(
          eq(taskQueue.status, TaskStatus.COMPLETED),
          sql`${taskQueue.completedAt} <= ${olderThan.toISOString()}`
        )
      );

    const countToDelete = countResult[0]?.count || 0;

    if (countToDelete === 0) {
      logger.taskQueue.info('No old completed tasks to clean up', undefined, {
        cleanupDate: olderThan.toISOString(),
      });
      return 0;
    }

    // Then delete them
    const result = await db
      .delete(taskQueue)
      .where(
        and(
          eq(taskQueue.status, TaskStatus.COMPLETED),
          sql`${taskQueue.completedAt} <= ${olderThan.toISOString()}`
        )
      )
      .returning({ id: taskQueue.id });

    const count = result.length;
    const duration = timer();

    logger.taskQueue.info('Completed cleanup of old tasks', undefined, {
      cleanupDate: olderThan.toISOString(),
      daysOld: Math.round((Date.now() - olderThan.getTime()) / (24 * 60 * 60 * 1000)),
      deletedCount: count,
      expectedCount: countToDelete,
      durationMs: duration,
    });

    return count;
  } catch (error) {
    const duration = timer();

    logger.taskQueue.error('Failed to clean up old tasks', error, undefined, {
      cleanupDate: olderThan.toISOString(),
      durationMs: duration,
    });

    throw error;
  }
}
