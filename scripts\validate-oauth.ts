/**
 * OAuth Diagnostics Utility
 *
 * This file provides diagnostic tools for OAuth configuration
 * in local development environments.
 */

import axios from 'axios';

// ANSI color codes for console output
const COLORS = {
  RESET: '\x1b[0m',
  BRIGHT: '\x1b[1m',
  DIM: '\x1b[2m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
};

function log(message: string, color = COLORS.RESET): void {
  // eslint-disable-next-line no-console
  console.log(color, message, COLORS.RESET);
}

function logSuccess(message: string): void {
  log(`✅ ${message}`, COLORS.GREEN);
}

function logError(message: string): void {
  log(`❌ ${message}`, COLORS.RED);
}

function logWarning(message: string): void {
  log(`⚠️  ${message}`, COLORS.YELLOW);
}

function logInfo(message: string): void {
  log(`ℹ️  ${message}`, COLORS.BLUE);
}

function logHeader(message: string): void {
  log(`\n${'='.repeat(50)}`, COLORS.CYAN);
  log(`${message}`, COLORS.CYAN);
  log(`${'='.repeat(50)}`, COLORS.CYAN);
}

/**
 * Get the domain for this environment
 */
export function getDomain(): string {
  // Check for explicit domain in environment
  if (process.env.DOMAIN) {
    return process.env.DOMAIN;
  }

  // Default to localhost for local development
  return 'localhost';
}

/**
 * Get the full base URL for the application
 */
export function getBaseUrl(): string {
  const domain = getDomain();
  if (domain === 'localhost') {
    // Check for custom port
    const port = process.env.PORT || '5000';
    return `http://localhost:${port}`;
  }

  // Production URL (always HTTPS)
  return `https://${domain}`;
}

/**
 * Get the redirect URI for OAuth
 */
export function getRedirectUri(provider = 'google'): string {
  const baseUrl = getBaseUrl();
  return `${baseUrl}/auth/${provider}/callback`;
}

/**
 * Check if Google OAuth credentials are properly configured
 */
export async function checkCredentials(): Promise<boolean> {
  logHeader('Checking Google OAuth Credentials');

  // Check for required environment variables
  const missingVars = [];

  if (!process.env.GOOGLE_CLIENT_ID) {
    missingVars.push('GOOGLE_CLIENT_ID');
  }

  if (!process.env.GOOGLE_CLIENT_SECRET) {
    missingVars.push('GOOGLE_CLIENT_SECRET');
  }

  if (missingVars.length > 0) {
    logError(`Missing required environment variables: ${missingVars.join(', ')}`);
    return false;
  }

  // Log partial client ID for verification
  const clientId = process.env.GOOGLE_CLIENT_ID!;
  logInfo(`Client ID: ${clientId.substring(0, 8)}...${clientId.substring(clientId.length - 5)}`);

  // Check credentials format
  if (!clientId.endsWith('.apps.googleusercontent.com')) {
    logWarning(
      'Client ID does not end with ".apps.googleusercontent.com", which is unusual for Google OAuth'
    );
  }

  logSuccess('Google OAuth credentials found in environment variables');
  return true;
}

/**
 * Show all authorized redirect URIs that should be configured
 */
export function showAuthorizedRedirectURIs(): void {
  logHeader('Required Authorized Redirect URIs');

  const baseUrl = getBaseUrl();

  log('Add the following URI to your Google Cloud Console:', COLORS.RESET);
  log(`${getRedirectUri('google')}`, COLORS.GREEN);

  log('\nFor Gmail integration, also add:', COLORS.RESET);
  log(`${baseUrl}/api/email-providers/gmail/callback`, COLORS.GREEN);
  log(`${baseUrl}/api/gmail/callback`, COLORS.GREEN);

  logInfo('Make sure ALL these URIs are added to your Google Cloud Console');
}

/**
 * Construct a Google authorization URL for testing
 */
export function constructAuthUrl(): string {
  const clientId = process.env.GOOGLE_CLIENT_ID;
  if (!clientId) {
    throw new Error('GOOGLE_CLIENT_ID environment variable is not set');
  }

  const redirectUri = getRedirectUri();
  const scope = 'email profile https://www.googleapis.com/auth/gmail.readonly';

  return `https://accounts.google.com/o/oauth2/v2/auth?client_id=${encodeURIComponent(
    clientId
  )}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=${encodeURIComponent(
    scope
  )}&access_type=offline&prompt=consent`;
}

/**
 * Test Google OAuth connection
 */
export async function testOAuthConnection(): Promise<boolean> {
  logHeader('Testing Google OAuth Connection');

  try {
    // First ensure credentials are configured
    const credentialsOk = await checkCredentials();
    if (!credentialsOk) {
      return false;
    }

    // Construct the auth URL (this will also validate the redirect URI)
    const authUrl = constructAuthUrl();
    logInfo(`Authorization URL: ${authUrl.substring(0, 80)}...`);

    // Simulate a connection to Google's OAuth endpoints
    logInfo('Connecting to Google OAuth endpoints...');

    try {
      const _response = await axios.get('https://accounts.google.com/o/oauth2/v2/auth', {
        timeout: 5000,
        params: {
          client_id: process.env.GOOGLE_CLIENT_ID,
          redirect_uri: getRedirectUri(),
          response_type: 'code',
          scope: 'email profile',
          access_type: 'offline',
          prompt: 'none', // Using 'none' to avoid actual login prompt
        },
      });

      logSuccess('Successfully connected to Google OAuth endpoints');
      return true;
    } catch (error: any) {
      // Expected error due to 'prompt=none', but check it's a redirect
      if (error.response && (error.response.status === 302 || error.response.status === 400)) {
        logSuccess('Successfully connected to Google OAuth endpoints (received expected redirect)');
        return true;
      }

      logError(`Error connecting to Google OAuth endpoints: ${error.message}`);
      return false;
    }
  } catch (error: any) {
    logError(`OAuth Connection Test Failed: ${error.message}`);
    return false;
  }
}

/**
 * Display detailed environment information
 */
export function displayEnvironmentInfo(): void {
  logHeader('Environment Information');

  // Application environment
  log(`Node.js Version: ${process.version}`, COLORS.BLUE);
  log(`Environment: ${process.env.NODE_ENV || 'development'}`, COLORS.BLUE);

  // URLs
  log(`Base URL: ${getBaseUrl()}`, COLORS.GREEN);
  log(`Google Redirect URI: ${getRedirectUri()}`, COLORS.GREEN);

  // OAuth configuration
  if (process.env.GOOGLE_CLIENT_ID) {
    const clientId = process.env.GOOGLE_CLIENT_ID;
    log(
      `Google Client ID: ${clientId.substring(0, 10)}...${clientId.substring(clientId.length - 5)}`,
      COLORS.GREEN
    );
  } else {
    log('Google Client ID: Not set', COLORS.RED);
  }

  if (process.env.GOOGLE_CLIENT_SECRET) {
    log(`Google Client Secret: ${COLORS.GREEN}Set${COLORS.RESET}`);
  } else {
    log(`Google Client Secret: ${COLORS.RED}Not set${COLORS.RESET}`);
  }
}

/**
 * Run a comprehensive diagnostic of OAuth configuration
 */
export async function runOAuthDiagnostic(): Promise<void> {
  logHeader('OAuth Diagnostic Results');

  // Display environment info
  displayEnvironmentInfo();

  // Check credentials
  await checkCredentials();

  // Show redirect URIs
  showAuthorizedRedirectURIs();

  // Test connection
  await testOAuthConnection();

  logHeader('Diagnostic Complete');
  log('To test the full auth flow, run the application and visit:', COLORS.YELLOW);
  log(constructAuthUrl(), COLORS.WHITE);
}
