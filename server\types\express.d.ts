import 'express-serve-static-core';
import 'express-session';
import type { User } from '../../shared/schema';

declare module 'express-serve-static-core' {
  interface Request {
    user?: User;
    authMethod?: 'session' | 'firebase';
  }
}

declare module 'express-session' {
  interface SessionData {
    userId?: number;
    user?: User;
    csrfToken?: string;
    returnTo?: string;
    lastSeen?: number;
  }
}
