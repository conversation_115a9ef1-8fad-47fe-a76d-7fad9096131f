/**
 * Login Page
 *
 * This page handles user authentication with Firebase.
 * It provides email/password login and registration, with an option for Google sign-in.
 */

import { AlertCircle, Chrome } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { SignInForm } from '@/components/auth/SignInForm';
import { SignUpForm } from '@/components/auth/SignUpForm';
import { PasswordResetForm } from '@/components/auth/PasswordResetForm';
import type { SignUpFormValues } from '@/components/auth/SignUpForm';
import type { SignInFormValues } from '@/components/auth/SignInForm';
import type { PasswordResetFormValues } from '@/components/auth/PasswordResetForm';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  resetPassword,
  signInWithEmail,
  signInWithGoogle,
  signUpWithEmail,
} from '@/lib/firebaseAuth';
import LoadingScreen from '@/components/ui/LoadingScreen';
import { useAuth } from '@/context/AuthContext';

const LoginPage: React.FC = () => {
  const { loading, error, user } = useAuth();
  const [, navigate] = useLocation();

  const [activeTab, setActiveTab] = useState<string>('signin');
  const [authInProgress, setAuthInProgress] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [resetSent, setResetSent] = useState(false);

  // Redirect authenticated users
  useEffect(() => {
    if (user && !loading && !authInProgress) {
      const urlParams = new URLSearchParams(window.location.search);
      const redirectFromUrl = urlParams.get('redirect');
      const redirectFromStorage = localStorage.getItem('redirectAfterLogin');

      if (redirectFromUrl) {
        const decodedPath = decodeURIComponent(redirectFromUrl);
        navigate(decodedPath);
      } else if (redirectFromStorage) {
        localStorage.removeItem('redirectAfterLogin');
        navigate(redirectFromStorage);
      } else {
        navigate('/dashboard');
      }
    }
  }, [user, loading, authInProgress, navigate]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setFormError(null);
    setResetSent(false);
  };

  const handleSignIn = async (values: SignInFormValues) => {
    try {
      setAuthInProgress(true);
      setFormError(null);
      await signInWithEmail(values.email, values.password);
    } catch (err: any) {
      if (err.code === 'auth/user-not-found' || err.code === 'auth/wrong-password') {
        setFormError('Invalid email or password. Please try again.');
      } else if (err.code === 'auth/too-many-requests') {
        setFormError(
          'Too many failed login attempts. Please try again later or reset your password.'
        );
      } else {
        setFormError(err.message || 'Sign in failed. Please try again.');
      }
    } finally {
      setAuthInProgress(false);
    }
  };

  const handleSignUp = async (values: SignUpFormValues) => {
    try {
      setAuthInProgress(true);
      setFormError(null);
      await signUpWithEmail(values.email, values.password, values.displayName);
    } catch (err: any) {
      if (err.code === 'auth/email-already-in-use') {
        setFormError('This email is already in use. Please try logging in instead.');
      } else {
        setFormError(err.message || 'Sign up failed. Please try again.');
      }
    } finally {
      setAuthInProgress(false);
    }
  };

  const handlePasswordReset = async (values: PasswordResetFormValues) => {
    try {
      setAuthInProgress(true);
      setFormError(null);
      await resetPassword(values.email);
      setResetSent(true);
    } catch (err: any) {
      if (err.code === 'auth/user-not-found') {
        setResetSent(true);
      } else {
        setFormError(err.message || 'Failed to send reset link. Please try again later.');
      }
    } finally {
      setAuthInProgress(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setAuthInProgress(true);
      setFormError(null);
      await signInWithGoogle();
    } catch (err: any) {
      if (err.code === 'auth/popup-closed-by-user') {
        setFormError('Sign-in cancelled. Please try again.');
      } else {
        setFormError(err.message || 'Google sign-in failed. Please try again.');
      }
    } finally {
      setAuthInProgress(false);
    }
  };

  if (loading && !authInProgress) {
    return <LoadingScreen message="Checking authentication..." />;
  }

  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center space-y-1">
          <CardTitle className="text-2xl font-bold">Welcome to Inbox Zero</CardTitle>
          <CardDescription>Sign in or create an account to continue</CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {(error || formError) && (
            <div className="p-3 bg-destructive/10 text-destructive rounded-md text-sm flex items-start gap-2">
              <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <span>{formError || error}</span>
            </div>
          )}

          <Tabs defaultValue="signin" value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="signin">Sign In</TabsTrigger>
              <TabsTrigger value="signup">Sign Up</TabsTrigger>
              <TabsTrigger value="reset">Reset</TabsTrigger>
            </TabsList>

            <TabsContent value="signin">
              <SignInForm onSubmit={handleSignIn} isSubmitting={authInProgress} />
            </TabsContent>
            <TabsContent value="signup">
              <SignUpForm onSubmit={handleSignUp} isSubmitting={authInProgress} />
            </TabsContent>
            <TabsContent value="reset">
              <PasswordResetForm
                onSubmit={handlePasswordReset}
                isSubmitting={authInProgress}
                resetSent={resetSent}
              />
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex flex-col gap-4">
          <div className="relative w-full">
            <Separator />
            <span className="absolute left-1/2 -translate-x-1/2 -top-2.5 bg-background px-2 text-xs uppercase text-muted-foreground">
              Or continue with
            </span>
          </div>
          <Button
            variant="outline"
            className="w-full"
            onClick={handleGoogleSignIn}
            disabled={authInProgress}
          >
            <Chrome className="mr-2 h-4 w-4" />
            Sign in with Google
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default LoginPage;
