/**
 * Authenticated API Client
 *
 * This module provides a unified API client that automatically handles:
 * - Firebase ID token authentication via Authorization headers
 * - CSRF token management for mutation requests with strict validation
 * - Session-based authentication fallback
 * - Proper error handling and retries
 * - Request/response interceptors
 */

import auth from './firebase';

interface ApiRequestOptions {
  headers?: Record<string, string>;
  skipAuth?: boolean;
  skipCsrf?: boolean; // Allow explicit CSRF bypass for specific endpoints
  retries?: number;
  timeout?: number;
  keepalive?: boolean; // Add keepalive option
}

interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

interface CsrfTokenResponse {
  csrfToken: string;
  expiresAt?: string;
}

// Cache CSRF token to avoid unnecessary requests
let csrfTokenCache: { token: string; expiresAt: number } | null = null;

/**
 * Get Firebase ID token from the current user
 */
async function getFirebaseIdToken(): Promise<string | null> {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return null;
    }

    const idToken = await currentUser.getIdToken(false); // Don't force refresh unless needed

    return idToken;
  } catch (error) {
    console.error('[API] Failed to get Firebase ID token:', error);
    return null;
  }
}

/**
 * Get CSRF token for mutation requests with strict validation
 * Only uses authenticated endpoint - no fallback to public endpoint
 */
async function getCsrfToken(): Promise<string> {
  // Check if we have a valid cached token
  if (csrfTokenCache && csrfTokenCache.expiresAt > Date.now()) {
    return csrfTokenCache.token;
  }

  try {
    const response = await fetch('/api/auth/csrf', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      throw new Error(
        `CSRF token request failed: ${response.status} ${response.statusText} - ${errorText}`
      );
    }

    const data: CsrfTokenResponse = await response.json();

    if (!data.csrfToken || typeof data.csrfToken !== 'string' || data.csrfToken.length < 10) {
      throw new Error('Invalid CSRF token received from server');
    }

    // Cache the token with expiration (default 1 hour if not provided)
    const expiresAt = data.expiresAt
      ? new Date(data.expiresAt).getTime()
      : Date.now() + 60 * 60 * 1000;
    csrfTokenCache = {
      token: data.csrfToken,
      expiresAt: expiresAt,
    };

    return data.csrfToken;
  } catch (error) {
    console.error('[API] Critical error getting CSRF token:', error);
    // Clear any invalid cached token
    csrfTokenCache = null;
    throw new Error(
      `CSRF protection unavailable: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Clear CSRF token cache (useful for testing or when token is known to be invalid)
 */
export function clearCsrfTokenCache(): void {
  csrfTokenCache = null;
}

/**
 * Build request headers with authentication and CSRF tokens
 */
async function buildHeaders(
  method: string,
  customHeaders: Record<string, string> = {},
  options: ApiRequestOptions = {}
): Promise<Record<string, string>> {
  const headers: Record<string, string> = {
    ...customHeaders,
  };

  // Only attach Content-Type for requests that include a body (non-GET/HEAD)
  const methodsRequiringBody = ['POST', 'PUT', 'PATCH', 'DELETE'];
  if (methodsRequiringBody.includes(method.toUpperCase()) && !headers['Content-Type']) {
    headers['Content-Type'] = 'application/json';
  }

  // Add Firebase Authorization header (unless explicitly skipped)
  if (!options.skipAuth) {
    const idToken = await getFirebaseIdToken();
    if (idToken) {
      headers.Authorization = `Bearer ${idToken}`;
    }
  }

  // Add CSRF token for mutation requests (unless explicitly skipped)
  const isMutationRequest = ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method.toUpperCase());
  if (isMutationRequest && !options.skipCsrf) {
    try {
      const csrfToken = await getCsrfToken();
      headers['X-CSRF-Token'] = csrfToken;
    } catch (csrfError) {
      console.error(`[API] Failed to get CSRF token for ${method} request:`, csrfError);
      throw new Error(
        `CSRF protection required but unavailable for ${method} request: ${
          csrfError instanceof Error ? csrfError.message : String(csrfError)
        }`
      );
    }
  }

  return headers;
}

/**
 * Validate response for security issues
 */
function validateResponse(response: Response, url: string, method: string): void {
  // Check for CSRF-related errors
  if (response.status === 403) {
    const csrfErrorPatterns = ['CSRF', 'csrf', 'Cross-Site Request Forgery', 'Invalid token'];
    const errorText = response.statusText || '';

    if (csrfErrorPatterns.some((pattern) => errorText.includes(pattern))) {
      console.warn(`[API] CSRF error detected for ${method} ${url}, clearing token cache`);
      clearCsrfTokenCache();
    }
  }

  // Check for authentication errors
  if (response.status === 401) {
    console.warn(`[API] Authentication error for ${method} ${url}`);
  }
}

/**
 * Execute an API request with retries and strict security validation
 */
async function executeRequest<T>(
  url: string,
  method: string,
  data?: any,
  options: ApiRequestOptions = {}
): Promise<ApiResponse<T>> {
  const maxRetries = options.retries ?? 2;
  let attempt = 0;
  let lastError: Error | null = null;

  while (attempt <= maxRetries) {
    try {
      const headers = await buildHeaders(method, options.headers, options);

      const requestConfig: RequestInit = {
        method,
        headers,
        credentials: 'include',
        keepalive: options.keepalive, // Pass keepalive option
      };

      if (data && method !== 'GET') {
        requestConfig.body = JSON.stringify(data);
      }

      // Add timeout if specified
      let timeoutId: NodeJS.Timeout | null = null;
      if (options.timeout) {
        const controller = new AbortController();
        timeoutId = setTimeout(() => controller.abort(), options.timeout);
        requestConfig.signal = controller.signal;
      }

      const response = await fetch(url, requestConfig);

      // Clear timeout if request completed
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // Validate response for security issues
      validateResponse(response, url, method);

      // Handle different response types
      let responseData: T;
      const contentType = response.headers.get('content-type');

      if (response.status === 204) {
        responseData = {} as T;
      } else if (contentType?.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = (await response.text()) as unknown as T;
      }

      // Check if response is OK
      if (!response.ok) {
        const error = new Error(
          (responseData as any)?.message || response.statusText || `HTTP ${response.status}`
        );
        (error as any).status = response.status;
        (error as any).data = responseData;
        (error as any).response = {
          status: response.status,
          statusText: response.statusText,
          data: responseData,
        };
        throw error;
      }

      return {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      };
    } catch (error: any) {
      lastError = error;
      console.error(`[API] ${method} ${url} failed (attempt ${attempt + 1}):`, error.message);

      // Handle specific retry scenarios with strict conditions
      const shouldRetry =
        attempt < maxRetries &&
        (error.name === 'AbortError' || // Timeout
          (error.status >= 500 && error.status < 600) || // Server errors only
          error.status === 401); // Auth errors retry once to handle token refresh

      // Special handling for CSRF errors - clear cache and retry once
      if (
        error.status === 403 &&
        error.message?.toLowerCase().includes('csrf') &&
        attempt < maxRetries
      ) {
        console.warn('[API] CSRF error detected, clearing cache and retrying');
        clearCsrfTokenCache();
        attempt++;
        const delay = 1000; // Short delay for CSRF retry
        await new Promise((resolve) => setTimeout(resolve, delay));
        continue;
      }

      if (shouldRetry) {
        attempt++;
        const delay = Math.min(1000 * 2 ** (attempt - 1), 5000); // Exponential backoff with max 5s

        await new Promise((resolve) => setTimeout(resolve, delay));
        continue;
      }

      // If we are not retrying, throw the last captured error.
      throw lastError;
    }
  }

  // This should theoretically not be reached if an error occurs,
  // but as a fallback, we throw the last known error.
  if (lastError) {
    throw lastError;
  }

  throw new Error(`Maximum retries exceeded for ${method} ${url}`);
}

/**
 * Wrapper class for the API client methods
 */
class ApiClient {
  /**
   * Perform a GET request
   */
  async get<T = any>(url: string, options: ApiRequestOptions = {}): Promise<T> {
    const response = await executeRequest<T>(url, 'GET', undefined, options);
    return response.data;
  }

  /**
   * Perform a POST request
   */
  async post<T = any>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {
    const response = await executeRequest<T>(url, 'POST', data, options);
    return response.data;
  }

  /**
   * Perform a PUT request
   */
  async put<T = any>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {
    const response = await executeRequest<T>(url, 'PUT', data, options);
    return response.data;
  }

  /**
   * Perform a PATCH request
   */
  async patch<T = any>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {
    const response = await executeRequest<T>(url, 'PATCH', data, options);
    return response.data;
  }

  /**
   * Perform a DELETE request
   */
  async delete<T = any>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {
    const response = await executeRequest<T>(url, 'DELETE', data, options);
    return response.data;
  }

  /**
   * A generic request method for more complex use cases
   */
  async request<T = any>(
    method: string,
    url: string,
    data?: any,
    options: ApiRequestOptions = {}
  ): Promise<T> {
    const response = await executeRequest<T>(url, method, data, options);
    return response.data;
  }
}

// Create and export the default API client instance
const apiClient = new ApiClient();
export default apiClient;

// Export utility functions
export { getFirebaseIdToken, getCsrfToken };

// Export types
export type { ApiRequestOptions, ApiResponse };
