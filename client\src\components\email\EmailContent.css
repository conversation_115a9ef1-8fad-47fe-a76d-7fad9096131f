/**
 * Email Content Styling
 * 
 * These styles ensure proper containment and consistent appearance
 * for HTML email content rendered in our application with dark mode support
 */

/* Container for email content */
.email-content-container {
  max-width: 100%;
  overflow-x: hidden;
  border-radius: var(--radius);
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border: 1px solid hsl(var(--border));
}

.email-content-header {
  padding: 1rem;
  border-bottom: 1px solid hsl(var(--border));
}

.email-content-body {
  padding: 1rem;
  overflow-wrap: break-word;
  word-break: break-word;
}

.email-content-footer {
  padding: 1rem;
  border-top: 1px solid hsl(var(--border));
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.email-html-content {
  max-width: 100%;
  overflow-x: auto;
  color: hsl(var(--foreground));
}

.email-html-content img {
  max-width: 100%;
  height: auto;
}

.email-html-content a {
  color: hsl(var(--primary));
  text-decoration: none;
}

.email-html-content a:hover {
  text-decoration: underline;
}

.email-html-content h1,
.email-html-content h2,
.email-html-content h3,
.email-html-content h4,
.email-html-content h5,
.email-html-content h6 {
  color: hsl(var(--foreground));
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.email-html-content p {
  margin: 1em 0;
}

.email-html-content blockquote {
  border-left: 3px solid hsl(var(--border));
  padding-left: 1rem;
  color: hsl(var(--muted-foreground));
  font-style: italic;
  margin: 1em 0;
}

.email-html-content pre,
.email-html-content code {
  background-color: hsl(var(--muted));
  border-radius: var(--radius);
  padding: 0.2em 0.4em;
  font-family: monospace;
  font-size: 0.9em;
}

.email-html-content pre {
  padding: 1em;
  overflow-x: auto;
}

.email-html-content pre code {
  background-color: transparent;
  padding: 0;
}

.email-html-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.email-html-content th,
.email-html-content td {
  border: 1px solid hsl(var(--border));
  padding: 0.5em;
}

.email-html-content th {
  background-color: hsl(var(--muted));
}

/* Dark mode overrides */
.dark .email-html-content {
  color: hsl(var(--foreground));
}

.dark .email-html-content a {
  color: hsl(var(--primary));
}

.dark .email-html-content th {
  background-color: hsl(var(--muted));
}

/* Force certain color schemes in embedded emails */
.email-html-content [style*="color: white"],
.email-html-content [style*="color:#fff"],
.email-html-content [style*="color: #fff"],
.email-html-content [style*="color:#ffffff"],
.email-html-content [style*="color: #ffffff"] {
  color: hsl(var(--foreground)) !important;
}

.dark .email-html-content [style*="color: black"],
.dark .email-html-content [style*="color:#000"],
.dark .email-html-content [style*="color: #000"],
.dark .email-html-content [style*="color:#000000"],
.dark .email-html-content [style*="color: #000000"] {
  color: hsl(var(--foreground)) !important;
}

.email-html-content [style*="background: white"],
.email-html-content [style*="background:#fff"],
.email-html-content [style*="background: #fff"],
.email-html-content [style*="background:#ffffff"],
.email-html-content [style*="background: #ffffff"],
.email-html-content [style*="background-color: white"],
.email-html-content [style*="background-color:#fff"],
.email-html-content [style*="background-color: #fff"],
.email-html-content [style*="background-color:#ffffff"],
.email-html-content [style*="background-color: #ffffff"] {
  background-color: hsl(var(--card)) !important;
}

.dark .email-html-content [style*="background: black"],
.dark .email-html-content [style*="background:#000"],
.dark .email-html-content [style*="background: #000"],
.dark .email-html-content [style*="background:#000000"],
.dark .email-html-content [style*="background: #000000"],
.dark .email-html-content [style*="background-color: black"],
.dark .email-html-content [style*="background-color:#000"],
.dark .email-html-content [style*="background-color: #000"],
.dark .email-html-content [style*="background-color:#000000"],
.dark .email-html-content [style*="background-color: #000000"] {
  background-color: hsl(var(--card)) !important;
}


/* Mobile adjustments */
@media (max-width: 640px) {
  .email-html-content {
    font-size: 13px;
    
    & table {
      font-size: 12px;
    }
    
    & h1 {
      font-size: 1.5em;
    }
    
    & h2 {
      font-size: 1.3em;
    }
    
    & h3 {
      font-size: 1.1em;
    }
  }
}