/**
 * Email List Context
 *
 * This context provides state management for email lists including:
 * - Fetching emails from the server
 * - Filtering and searching emails
 * - Refreshing and syncing emails
 * - Handling pagination
 * - Auto-refreshing emails with processing status
 */

import type { Email } from '@shared/schema';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import React, {
  createContext,
  type ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { defaultFilters } from '@/components/email/EmailFilter';
import type { EmailFilters } from '@/types/email';
import { usePermission } from '@/hooks/use-permission';
import { useToast } from '@/hooks/use-toast';
import apiClient from '@/lib/apiClient';
import { filterEmails } from '@/lib/emailFilters';
import { sampleEmails } from '@/lib/sampleData';
import { isProduction } from '@/lib/environment';
import logger from '@/lib/logger';
import { useAuth } from '@/context/AuthContext';
import { useProviderStatus } from '@/hooks/use-provider-status';
import type { PaginatedEmailsResponse } from '@/types/email';

interface ProcessingStatus {
  success: boolean;
  processing: number;
  errors: number;
  pending?: number;
  completed?: number;
  totalTasks?: number;
  oldestPendingTasks?: Array<{
    id: number;
    type: string;
    age: number;
    priority: number;
  }>;
  stuckProcessingTasks?: Array<{
    id: number;
    type: string;
    lockedSince: number;
    lockedBy: string;
  }>;
  lastSyncTime?: string;
}

interface EmailListContextType {
  emails: Email[];
  filteredEmails: Email[];
  isLoading: boolean;
  error: Error | null;
  filters: EmailFilters;
  searchQuery: string;
  currentPage: number;
  totalPages: number;
  totalEmails: number;
  refreshEmails: (force?: boolean) => void;
  refreshProcessingEmails: () => void;
  setFilters: (filters: EmailFilters) => void;
  setSearchQuery: (query: string) => void;
  goToPage: (page: number) => void;
  nextPage: () => void;
  prevPage: () => void;
}

const EmailListContext = createContext<EmailListContextType | undefined>(undefined);

// Use named function components to avoid Fast Refresh incompatibility
export function EmailListProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const { gmailProvider } = useProviderStatus();
  const isGmailConnected = gmailProvider?.isConnected || false;
  const { toast } = useToast();
  const [filters, setFilters] = useState<EmailFilters>({
    status: 'all',
    priority: 'all',
    categories: [],
    timeRange: 'all',
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const queryClient = useQueryClient();
  const { handlePermissionError } = usePermission();

  // Determine if we're in test mode based on the email
  const isTestMode =
    user && (user.email === '<EMAIL>' || user.email === '<EMAIL>');

  // Debug user state
  useEffect(() => {
    logger.info('[EmailList] User state changed', {
      hasUser: !!user,
      userId: user?.id,
      userEmail: user?.email,
      userType: typeof user,
      userKeys: user ? Object.keys(user) : [],
      isTestMode,
      isGmailConnected,
      timestamp: new Date().toISOString()
    });
  }, [user, isTestMode, isGmailConnected]);

  logger.info('[EmailList] Current state before queries', {
    hasUser: !!user,
    userId: user?.id,
    userEmail: user?.email,
    isTestMode,
    timestamp: new Date().toISOString()
  });

  // Fetch real emails if we have a real user
  const {
    data: emailsData,
    isLoading: emailsLoading,
    error: emailsError,
    refetch: refetchEmails,
    dataUpdatedAt,
    isFetching,
  } = useQuery<PaginatedEmailsResponse>({
    queryKey: ['emails', user?.id || 'no-user', { page: currentPage, limit: 20, filters, searchQuery }],
    queryFn: async () => {
      logger.info('[EmailList] Fetching emails from API...', {
        userId: user?.id,
        userIdType: typeof user?.id,
        userObject: user ? { id: user.id, email: user.email } : null,
        page: currentPage,
        timestamp: new Date().toISOString(),
        triggeredBy: 'queryFn'
      });

      // Build query parameters
      const params = new URLSearchParams({
        limit: '20',
        offset: ((currentPage - 1) * 20).toString(),
        view: 'list', // OPTIMIZATION: Use list view for minimal data transfer (85% reduction)
      });

      // Add filters to query params
      if (filters.status === 'archived') params.append('archived', 'true');
      if (filters.status === 'important') params.append('important', 'true');
      if (filters.status === 'snoozed') params.append('snoozed', 'true');
      if (filters.status === 'trashed') params.append('trashed', 'true');
      if (filters.priority !== 'all') params.append('priority', filters.priority);
      if (filters.categories.length > 0) params.append('categories', filters.categories.join(','));
      if (searchQuery) params.append('search', searchQuery);

      // Make the API call
      const result = await apiClient.get<PaginatedEmailsResponse>(`/api/emails?${params.toString()}`);
      
      logger.info('[EmailList] Received emails from API:', {
        emailCount: result?.emails?.length || 0,
        totalEmails: result?.totalEmails || 0,
        userId: user?.id,
        userObject: user ? { id: user.id, email: user.email } : null,
        timestamp: new Date().toISOString(),
        firstEmailSubject: result?.emails?.[0]?.subject || 'none',
        lastEmailSubject: result?.emails?.[result?.emails?.length - 1]?.subject || 'none'
      });
      
      return result;
    },
    enabled: !!user && !isTestMode, // Temporarily back to original - just need user and not test mode
    retry: (failureCount, error: any) => {
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
    refetchOnWindowFocus: true,
    staleTime: 0, // Always fetch fresh data to catch backend automatic sync updates
    refetchInterval: user && !isTestMode ? 60000 : false, // Changed to 60 seconds to reduce server load
  });

  // Debug React Query state changes
  useEffect(() => {
    logger.info('[EmailList] React Query state changed', {
      isFetching,
      isLoading: emailsLoading,
      hasData: !!emailsData,
      dataUpdatedAt: new Date(dataUpdatedAt).toISOString(),
      emailCount: emailsData?.emails?.length || 0,
      timestamp: new Date().toISOString()
    });
  }, [isFetching, emailsLoading, emailsData, dataUpdatedAt]);

  const { data: processingStatus } = useQuery<ProcessingStatus>({
    queryKey: ['emails', 'processing-status', user?.id || 'no-user'],
    queryFn: () => {
      logger.info('[EmailList] Fetching processing status...', {
        userId: user?.id,
        userObject: user ? { id: user.id, email: user.email } : null,
        timestamp: new Date().toISOString()
      });
      return apiClient.get<ProcessingStatus>('/api/emails/processing-status');
    },
    enabled: !!user && !isTestMode && isGmailConnected, // Back to original - just need user, not test mode, and Gmail connected
    staleTime: 0, // Always fetch fresh data to detect processing changes quickly
    refetchInterval: (query) => {
      const status = query.state.data;
      // Less aggressive polling: 10 seconds when processing, 60 seconds when idle
      return status && status.processing > 0 ? 10000 : 60000;
    },
  });

  const syncMutation = useMutation<void, Error>({
    mutationFn: () => {
      logger.info('[EmailList] Triggering manual sync...', {
        userId: user?.id,
        userObject: user ? { id: user.id, email: user.email } : null,
        timestamp: new Date().toISOString()
      });
      return apiClient.post('/api/emails/sync');
    },
    onSuccess: () => {
      toast({
        title: 'Sync requested',
        description: 'Your emails will be synced shortly.',
      });
      // Immediately invalidate all email queries for this user
      logger.info('[EmailList] Invalidating email queries after successful sync', {
        userId: user?.id,
        timestamp: new Date().toISOString()
      });
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id || 'no-user'] });
      
      // Also refetch immediately to get new data
      setTimeout(() => {
        logger.info('[EmailList] Refetching emails after sync delay');
        refetchEmails();
      }, 2000); // Wait 2 seconds for backend to process
    },
    onError: async (error) => {
      logger.error('[EmailList] Sync failed', {
        error: error.message,
        userId: user?.id,
        timestamp: new Date().toISOString()
      });
      const wasHandled = await handlePermissionError(error.message);
      if (!wasHandled) {
        toast({
          title: 'Sync Failed',
          description: error.message || 'Unable to sync with email provider.',
          variant: 'destructive',
        });
      }
    },
  });

  // Use API data when available, otherwise fall back to sample data for test users or in dev when no user
  const emails: Email[] = useMemo(() => {
    // If we have API data, use it
    if (emailsData?.emails && Array.isArray(emailsData.emails)) {
      logger.info('[EmailList] Using API emails data', {
        emailCount: emailsData.emails.length,
        userId: user?.id,
        timestamp: new Date().toISOString()
      });
      return emailsData.emails;
    }

    // For test users or development without a user, use sample data
    if (isTestMode || (!user && !isProduction())) {
      logger.info('[EmailList] Using sample emails data', {
        emailCount: sampleEmails.length,
        isTestMode,
        hasUser: !!user,
        isProduction: isProduction(),
        timestamp: new Date().toISOString()
      });
      return sampleEmails as Email[];
    }

    // If user is authenticated but we have no data yet (and not loading),
    // it might mean they have no emails. Return empty array.
    logger.info('[EmailList] Using empty emails array', {
      hasUser: !!user,
      hasEmailsData: !!emailsData,
      isLoading: emailsLoading,
      userId: user?.id,
      timestamp: new Date().toISOString()
    });
    return [];
  }, [emailsData, isTestMode, user]);

  // Apply filters and search to emails
  const filteredEmails = useMemo(() => {
    logger.info('[EmailList] Applying filters to emails', {
      emailCount: emails.length,
      filters,
      searchQuery,
      timestamp: new Date().toISOString()
    });

    const filtered = filterEmails(emails, filters, searchQuery);

    logger.info('[EmailList] Filtered emails result', {
      originalCount: emails.length,
      filteredCount: filtered.length,
      timestamp: new Date().toISOString()
    });

    return filtered;
  }, [emails, filters, searchQuery]);

  const totalPages = emailsData?.totalPages ?? 1;
  const totalEmails = emailsData?.totalEmails ?? 0;

  // Navigation functions - memoized to prevent unnecessary re-renders
  const goToPage = useCallback(
    (page: number) => {
      if (page > 0 && page <= totalPages) {
        setCurrentPage(page);
      }
    },
    [totalPages]
  );

  const nextPage = useCallback(() => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  }, [currentPage, totalPages]);

  const prevPage = useCallback(() => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  }, [currentPage]);

  const refreshEmails = useCallback(
    (force = false) => {
      logger.info('[EmailList] Refresh emails called', {
        force,
        userId: user?.id,
        userObject: user ? { id: user.id, email: user.email } : null,
        timestamp: new Date().toISOString()
      });
      
      if (force) {
        // Force sync from email provider
        syncMutation.mutate();
      } else {
        // Just refetch current data
        logger.info('[EmailList] Performing manual refetch');
        // Clear cache first to ensure fresh data
        queryClient.invalidateQueries({ queryKey: ['emails', user?.id || 'no-user'] });
        refetchEmails();
      }
    },
    [refetchEmails, syncMutation.mutate, queryClient, user?.id]
  );

  const refreshProcessingEmails = useCallback(() => {
    if (processingStatus?.processing && processingStatus.processing > 0) {
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id || 'no-user'] });
    }
  }, [processingStatus?.processing, queryClient, user?.id]);

  /*
   * Automatically request a sync the first time we detect that Gmail is connected
   * but there are zero emails in the cache.  This removes the manual step of
   * clicking a separate "Sync Emails" button after OAuth.
   * 
   * Added safeguards to prevent infinite loops:
   * - Only trigger once per session using a ref
   * - Don't trigger if there was a recent sync error
   * - Add a cooldown period between sync attempts
   * - Don't trigger for test users
   */
  const hasTriggeredAutoSync = useRef(false);
  const lastSyncError = useRef<number>(0);
  
  useEffect(() => {
    // Don't auto-sync for test users
    if (isTestMode) return;
    
    const now = Date.now();
    const cooldownPeriod = 60000; // 60 seconds cooldown to match sync interval
    const shouldTriggerSync = 
      !emailsLoading && 
      isGmailConnected && 
      emails.length === 0 && 
      !syncMutation.isPending &&
      !hasTriggeredAutoSync.current &&
      (now - lastSyncError.current) > cooldownPeriod &&
      user && // Ensure user is authenticated
      !emailsError; // Don't sync if there's already an error
      
    if (shouldTriggerSync) {
      hasTriggeredAutoSync.current = true;
      syncMutation.mutate();
    }
  }, [isGmailConnected, emails.length, emailsLoading, syncMutation, isTestMode, user, emailsError]);
  
  // Track sync errors to prevent infinite retries
  useEffect(() => {
    if (syncMutation.isError) {
      lastSyncError.current = Date.now();
    }
  }, [syncMutation.isError]);

  useEffect(() => {
    if (processingStatus?.processing && processingStatus.processing > 0) {
      logger.info('[EmailList] Processing detected, will invalidate cache in 5 seconds...', {
        processing: processingStatus.processing,
        timestamp: new Date().toISOString()
      });
      
      const timer = setTimeout(() => {
        logger.info('[EmailList] Invalidating email queries due to processing status');
        queryClient.invalidateQueries({ queryKey: ['emails', user?.id || 'no-user'] });
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [processingStatus?.processing, queryClient, user?.id]);

  // Additional effect to catch backend automatic sync updates more reliably
  useEffect(() => {
    if (!user || isTestMode || !isGmailConnected) {
      logger.info('[EmailList] Additional polling disabled', {
        hasUser: !!user,
        hasUserId: !!user?.id,
        isTestMode,
        isGmailConnected,
        timestamp: new Date().toISOString()
      });
      return;
    }

    logger.info('[EmailList] Setting up additional 60-second polling for backend sync updates', {
      userId: user?.id,
      timestamp: new Date().toISOString()
    });

    // Set up an additional polling mechanism to catch backend sync updates
    // This runs every 60 seconds to ensure we catch new emails without being too aggressive
    const syncCheckInterval = setInterval(() => {
      // Only refetch if we're not currently loading and there's no error
      if (!emailsLoading && !emailsError) {
        logger.info('[EmailList] Manual refetch triggered by 60-second interval', {
          userId: user?.id,
          timestamp: new Date().toISOString()
        });
        refetchEmails();
      }
    }, 60000); // Check every 60 seconds

    return () => {
      logger.info('[EmailList] Cleaning up additional polling interval', {
        userId: user?.id,
        timestamp: new Date().toISOString()
      });
      clearInterval(syncCheckInterval);
    };
  }, [user, isTestMode, isGmailConnected, emailsLoading, emailsError, refetchEmails]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = React.useMemo(
    () => ({
      emails,
      filteredEmails,
      isLoading: emailsLoading,
      error: emailsError,
      filters,
      searchQuery,
      currentPage,
      totalPages,
      totalEmails,
      refreshEmails,
      refreshProcessingEmails,
      setFilters,
      setSearchQuery,
      goToPage,
      nextPage,
      prevPage,
    }),
    [
      emails,
      filteredEmails,
      emailsLoading,
      emailsError,
      filters,
      searchQuery,
      currentPage,
      totalPages,
      totalEmails,
      refreshEmails,
      refreshProcessingEmails,
      goToPage,
      nextPage,
      prevPage,
    ]
  );

  return <EmailListContext.Provider value={contextValue}>{children}</EmailListContext.Provider>;
}

// Use a named function to avoid Fast Refresh incompatibility
export function useEmailList() {
  const context = useContext(EmailListContext);
  if (context === undefined) {
    throw new Error('useEmailList must be used within an EmailListProvider');
  }
  return context;
}
