import OpenAI from 'openai';
import { getEnvVar } from '../lib/environmentValidator';
import logger from '../lib/logger';

const OPENAI_MODEL = getEnvVar('OPENAI_MODEL') || 'gpt-4o'; // Default to gpt-4o if not specified

const openai = new OpenAI({ apiKey: getEnvVar('OPENAI_API_KEY') });

// Email summarization function
export async function summarizeEmail(emailContent: string): Promise<string> {
  try {
    const prompt = `Please summarize the following email concisely (under 80 words) while maintaining key points, details, and action items:\n\n${emailContent}`;

    const response = await openai.chat.completions.create({
      model: OPENAI_MODEL,
      messages: [{ role: 'user', content: prompt }],
    });

    return response.choices[0].message.content || 'Unable to generate summary.';
  } catch (error) {
    logger.error('Error summarizing email with OpenAI', { 
      error: String(error), 
      service: 'openai',
      contentLength: emailContent.length 
    });
    return 'Error generating summary.';
  }
}

// Email categorization function
export async function categorizeEmail(emailContent: string, subject: string): Promise<string[]> {
  try {
    const prompt = `
      Analyze the following email content and subject. 
      Categorize it into one or more of these categories: Work, Personal, Urgent, Promotions.
      Provide the result as a JSON array containing only the categories.

      Subject: ${subject}
      Email: ${emailContent}
    `;

    const response = await openai.chat.completions.create({
      model: OPENAI_MODEL,
      messages: [{ role: 'user', content: prompt }],
      response_format: { type: 'json_object' },
    });

    const result = JSON.parse(response.choices[0].message.content || '{}');
    return Array.isArray(result.categories) ? result.categories : ['Work'];
  } catch (error) {
    logger.error('Error categorizing email with OpenAI', { 
      error: String(error), 
      service: 'openai',
      subject: subject.substring(0, 50),
      contentLength: emailContent.length 
    });
    return ['Work']; // Default fallback category
  }
}

// Generate AI reply
export async function generateReply(
  emailContent: string,
  subject: string,
  sender: string,
  tone = 'professional',
  customTone?: string
): Promise<string> {
  try {
    let toneInstruction = `Use a ${tone} tone.`;

    // If customTone is provided, use it for personalization
    if (customTone && customTone.trim().length > 0) {
      toneInstruction = `
        Adapt the reply to match this writing style and tone:
        "${customTone}"

        Pay close attention to the following in the examples:
        - Sentence structure and length
        - Word choice and vocabulary
        - Level of formality/informality
        - Use of specific phrases or expressions
        - Overall communication style
      `;
    }

    const prompt = `
      Generate a concise reply to the email below.
      Subject: ${subject}
      From: ${sender}
      Email content: ${emailContent}

      ${toneInstruction} Be helpful and direct. Keep the response under 150 words.
      Do not include any salutations like "Hi [Name]" or signatures - just the reply content.
    `;

    const response = await openai.chat.completions.create({
      model: OPENAI_MODEL,
      messages: [{ role: 'user', content: prompt }],
    });

    return response.choices[0].message.content || "I'll get back to you soon.";
  } catch (error) {
    logger.error('Error generating reply with OpenAI', { 
      error: String(error), 
      service: 'openai',
      subject: subject.substring(0, 50),
      sender: sender.substring(0, 50),
      tone,
      contentLength: emailContent.length 
    });
    return "I'll get back to you soon.";
  }
}

// Regenerate reply with a different tone
export async function regenerateReply(
  emailContent: string,
  subject: string,
  sender: string,
  tone: string,
  customTone?: string
): Promise<string> {
  return generateReply(emailContent, subject, sender, tone, customTone);
}

// Explain AI's categorization decision
export async function explainCategorization(
  emailContent: string,
  subject: string,
  categories: string[]
): Promise<string> {
  try {
    const prompt = `
      This email was categorized as: ${categories.join(', ')}

      Subject: ${subject}
      Email content: ${emailContent}

      Explain in 2-3 sentences why this categorization was chosen.
    `;

    const response = await openai.chat.completions.create({
      model: OPENAI_MODEL,
      messages: [{ role: 'user', content: prompt }],
    });

    return (
      response.choices[0].message.content || 'Category assigned based on email content and subject.'
    );
  } catch (error) {
    logger.error('Error explaining categorization with OpenAI', { 
      error: String(error), 
      service: 'openai',
      subject: subject.substring(0, 50),
      categories: categories.join(', '),
      contentLength: emailContent.length 
    });
    return 'Category assigned based on email content and subject.';
  }
}

// Generate a personalized daily email digest summary
export async function generateDailySummary(
  emailStats: any,
  importantEmails: any[],
  timeRange: string,
  _userPreferences?: any
): Promise<{
  summary: string;
  insights: string[];
  suggestions: string[];
}> {
  try {
    // Format the email data for the AI
    const importantEmailsText = importantEmails
      .map(
        (email) =>
          `Subject: ${email.subject}\nFrom: ${email.sender}\nSummary: ${email.summary || email.snippet}\n`
      )
      .join('\n');

    // Format category stats
    const categoriesText = Object.entries(emailStats.categoryCounts)
      .map(([category, count]) => `${category}: ${count}`)
      .join(', ');

    const prompt = `
      You're analyzing email data for a user's ${timeRange === 'today' ? 'daily' : timeRange === 'week' ? 'weekly' : 'monthly'} digest.

      STATS:
      - Total emails received: ${emailStats.totalReceived}
      - Emails processed: ${emailStats.totalProcessed} (${Math.round((emailStats.totalProcessed / emailStats.totalReceived) * 100) || 0}%)
      - Estimated time spent: ${emailStats.timeSpent} minutes
      - Categories: ${categoriesText}

      IMPORTANT EMAILS:
      ${importantEmailsText}

      Based on this information, please provide:
      1. A 3-4 sentence personalized summary of their email activity
      2. 3 key insights about their email patterns
      3. 2-3 actionable suggestions to improve email productivity

      Format your response as a JSON object with the following structure:
      {
        "summary": "personalized summary here",
        "insights": ["insight 1", "insight 2", "insight 3"],
        "suggestions": ["suggestion 1", "suggestion 2"]
      }

      Keep the tone friendly, supportive, and use the second person ("you").
    `;

    const response = await openai.chat.completions.create({
      model: OPENAI_MODEL,
      messages: [{ role: 'user', content: prompt }],
      response_format: { type: 'json_object' },
    });

    // Parse the JSON response
    const result = JSON.parse(response.choices[0].message.content || '{}');

    return {
      summary: result.summary || 'Summary of your email activity for this period.',
      insights: result.insights || ['No specific insights available for this period.'],
      suggestions: result.suggestions || ['Consider scheduling time for email processing.'],
    };
  } catch (error) {
    logger.error('Error generating daily summary with OpenAI', { 
      error: String(error), 
      service: 'openai',
      timeRange,
      emailCount: importantEmails.length 
    });
    return {
      summary: 'Summary of your email activity for this period.',
      insights: ['No specific insights available for this period.'],
      suggestions: ['Consider scheduling time for email processing.'],
    };
  }
}
