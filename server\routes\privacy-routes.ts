/**
 * Privacy Management Routes
 * 
 * Provides API endpoints for managing user privacy settings,
 * data retention, and GDPR compliance.
 */

import { Router } from 'express';
import type { Request, Response } from 'express';
import { z } from 'zod';
import logger from '../lib/logger';
import { simpleAuth } from '../middleware/simpleAuth';
import { dataRetentionService } from '../services/dataRetention';
import { storage } from '../storage';
import { catchAsync } from '../utils/errorHandler';
import { sendError, sendSuccess, ErrorCode } from '../lib/standardizedResponses';

const router = Router();

// Apply authentication middleware to all routes
router.use(simpleAuth());

// Validation schemas
const updatePrivacySettingsSchema = z.object({
  dataRetentionDays: z.number().min(1).max(365).optional(),
  allowAIProcessing: z.boolean().optional(),
  storeEmailContent: z.boolean().optional(),
  autoDeleteProcessedEmails: z.boolean().optional(),
  encryptSensitiveData: z.boolean().optional(),
});

const purgeDataSchema = z.object({
  includeMetadata: z.boolean().default(false),
  confirmPurge: z.boolean(),
});

/**
 * GET /api/privacy/settings
 * Get current privacy settings for the user
 */
router.get('/settings', catchAsync(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  try {
    const settings = await storage.getSettings(userId);
    
    if (!settings) {
      return sendError(res, ErrorCode.NOT_FOUND, 'User settings not found');
    }

    const privacySettings = {
      dataRetentionDays: settings.dataRetentionDays || 30,
      allowAIProcessing: settings.allowAIProcessing ?? true,
      storeEmailContent: settings.storeEmailContent ?? true,
      autoDeleteProcessedEmails: settings.autoDeleteProcessedEmails ?? false,
      encryptSensitiveData: settings.encryptSensitiveData ?? true,
      consentVersion: settings.consentVersion || '1.0',
      consentTimestamp: settings.consentTimestamp,
    };

    return sendSuccess(res, privacySettings, 'Privacy settings retrieved successfully');

  } catch (error) {
    logger.error('Failed to get privacy settings', {
      userId,
      error: (error as Error).message,
    });
    return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to retrieve privacy settings');
  }
}));

/**
 * PUT /api/privacy/settings
 * Update privacy settings for the user
 */
router.put('/settings', catchAsync(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  try {
    const validatedData = updatePrivacySettingsSchema.parse(req.body);

    // Get current settings
    let settings = await storage.getSettings(userId);
    
    if (!settings) {
      // Create default settings if they don't exist
      settings = await storage.createSettings({
        userId,
        ...validatedData,
        consentVersion: '1.0',
        consentTimestamp: new Date(),
      });
    } else {
      // Update existing settings
      settings = await storage.updateSettings(userId, {
        ...validatedData,
        updatedAt: new Date(),
      });
    }

    if (!settings) {
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to update privacy settings');
    }

    // If encryption was enabled, apply it to existing emails
    if (validatedData.encryptSensitiveData === true) {
      // Run encryption in background
      dataRetentionService.applyEncryptionToExistingEmails(userId).catch(error => {
        logger.error('Failed to apply encryption to existing emails', {
          userId,
          error: (error as Error).message,
        });
      });
    }

    logger.info('Privacy settings updated', {
      userId,
      changes: validatedData,
    });

    return sendSuccess(res, settings, 'Privacy settings updated successfully');

  } catch (error) {
    if (error instanceof z.ZodError) {
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid privacy settings data', {
        errors: error.errors,
      });
    }

    logger.error('Failed to update privacy settings', {
      userId,
      error: (error as Error).message,
    });
    return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to update privacy settings');
  }
}));

/**
 * GET /api/privacy/data-stats
 * Get statistics about user's data
 */
router.get('/data-stats', catchAsync(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  try {
    const stats = await dataRetentionService.getUserDataStats(userId);

    return sendSuccess(res, stats, 'Data statistics retrieved successfully');

  } catch (error) {
    logger.error('Failed to get data statistics', {
      userId,
      error: (error as Error).message,
    });
    return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to retrieve data statistics');
  }
}));

/**
 * POST /api/privacy/cleanup-expired
 * Manually trigger cleanup of expired content for the user
 */
router.post('/cleanup-expired', catchAsync(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  try {
    // This would need to be modified to clean up only the current user's data
    // For now, we'll return a message about the global cleanup
    const stats = await dataRetentionService.cleanupExpiredContent();

    logger.info('Manual cleanup triggered by user', {
      userId,
      stats,
    });

    return sendSuccess(res, stats, 'Expired content cleanup completed');

  } catch (error) {
    logger.error('Failed to cleanup expired content', {
      userId,
      error: (error as Error).message,
    });
    return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to cleanup expired content');
  }
}));

/**
 * POST /api/privacy/encrypt-emails
 * Manually trigger encryption of user's unencrypted emails
 */
router.post('/encrypt-emails', catchAsync(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  try {
    const stats = await dataRetentionService.applyEncryptionToExistingEmails(userId);

    logger.info('Manual encryption triggered by user', {
      userId,
      stats,
    });

    return sendSuccess(res, stats, 'Email encryption completed');

  } catch (error) {
    logger.error('Failed to encrypt emails', {
      userId,
      error: (error as Error).message,
    });
    return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to encrypt emails');
  }
}));

/**
 * POST /api/privacy/purge-data
 * Purge user data (GDPR compliance)
 */
router.post('/purge-data', catchAsync(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  try {
    const validatedData = purgeDataSchema.parse(req.body);

    if (!validatedData.confirmPurge) {
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Data purge must be explicitly confirmed');
    }

    await dataRetentionService.purgeUserData(userId, validatedData.includeMetadata);

    logger.warn('User data purged', {
      userId,
      includeMetadata: validatedData.includeMetadata,
      userEmail: req.user!.email,
    });

    return sendSuccess(res, {
      purged: true,
      includeMetadata: validatedData.includeMetadata,
      timestamp: new Date().toISOString(),
    }, 'User data purged successfully');

  } catch (error) {
    if (error instanceof z.ZodError) {
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid purge data request', {
        errors: error.errors,
      });
    }

    logger.error('Failed to purge user data', {
      userId,
      error: (error as Error).message,
    });
    return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to purge user data');
  }
}));

/**
 * GET /api/privacy/export-data
 * Export user data (GDPR compliance)
 */
router.get('/export-data', catchAsync(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  try {
    // Get user's emails and settings
    const emails = await storage.getEmails(userId, 1000, 0); // Get up to 1000 emails
    const settings = await storage.getSettings(userId);
    const user = await storage.getUser(userId);

    // Decrypt encrypted content for export
    const decryptedEmails = await Promise.all(
      emails.map(async (email) => {
        if (email.isContentEncrypted) {
          return await dataRetentionService.decryptEmailContent(email);
        }
        return email;
      })
    );

    const exportData = {
      user: {
        id: user?.id,
        email: user?.email,
        name: user?.name,
        createdAt: user?.lastLogin, // Approximate creation date
      },
      settings: settings,
      emails: decryptedEmails.map(email => ({
        id: email.id,
        subject: email.subject,
        sender: email.sender,
        senderEmail: email.senderEmail,
        receivedAt: email.receivedAt,
        originalContent: email.originalContent,
        htmlContent: email.htmlContent,
        summary: email.summary,
        categories: email.categories,
        priority: email.priority,
        aiReply: email.aiReply,
        isRead: email.isRead,
        isArchived: email.isArchived,
        isImportant: email.isImportant,
      })),
      exportMetadata: {
        exportedAt: new Date().toISOString(),
        totalEmails: decryptedEmails.length,
        dataVersion: '1.0',
      },
    };

    logger.info('User data exported', {
      userId,
      emailCount: decryptedEmails.length,
    });

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="inboxzero-data-export-${userId}-${new Date().toISOString().split('T')[0]}.json"`);
    
    return res.json(exportData);

  } catch (error) {
    logger.error('Failed to export user data', {
      userId,
      error: (error as Error).message,
    });
    return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to export user data');
  }
}));

export default router; 