import { fireEvent, render, screen } from '@testing-library/react';
import type React from 'react';

// --------- mocks ---------
const mockUseAuth = jest.fn();
jest.mock('@/context/AuthContext', () => ({
  __esModule: true,
  useAuth: () => mockUseAuth(),
}));

const mockSignInWithGoogle = jest.fn();
jest.mock('@/lib/firebaseAuth', () => ({
  signInWithGoogle: () => mockSignInWithGoogle(),
  signInWithEmail: jest.fn(),
  signUpWithEmail: jest.fn(),
  resetPassword: jest.fn(),
}));

// Mock router
jest.mock('wouter', () => ({
  useLocation: () => ['/', jest.fn()],
}));

// Mock sub components to reduce render complexity
jest.mock('@/components/ui/LoadingScreen', () => ({
  __esModule: true,
  default: () => <div data-testid="loading">loading</div>,
}));

// ------------ import after mocks -------------
import LoginPage from '@/pages/login';

const renderPage = () => {
  return render(<LoginPage />);
};

describe('LoginPage', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('shows loading screen when auth is initializing', () => {
    mockUseAuth.mockReturnValue({ loading: true, error: null, user: null });
    renderPage();
    expect(screen.getByTestId('loading')).toBeInTheDocument();
  });

  it('displays backend error from AuthContext', () => {
    mockUseAuth.mockReturnValue({ loading: false, error: 'Backend boom', user: null });
    renderPage();
    expect(screen.getByText('Backend boom')).toBeInTheDocument();
  });

  it('invokes Google OAuth flow when Google button clicked', () => {
    mockUseAuth.mockReturnValue({ loading: false, error: null, user: null });
    renderPage();
    const btn = screen.getByRole('button', { name: /sign in with google/i });
    fireEvent.click(btn);
    expect(mockSignInWithGoogle).toHaveBeenCalled();
  });
}); 