/**
 * Client Logs Route
 *
 * Receives logs from client-side logger and integrates with server-side centralized logging
 */

import express, { type Request, type Response } from 'express';
import rateLimit from 'express-rate-limit';
import logger from '../lib/logger';

const router = express.Router();

// Rate limiting for log endpoints to prevent abuse
const logRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // Limit each IP to 100 log requests per minute
  message: 'Too many log requests from this IP',
  standardHeaders: true,
  legacyHeaders: false,
});

// Validate log entry structure
interface ClientLogEntry {
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  message: string;
  timestamp: string;
  context?: Record<string, any>;
  error?: {
    message: string;
    stack?: string;
    name?: string;
    code?: string;
  };
  component?: string;
  userId?: string;
  sessionId?: string;
}

function isValidLogEntry(data: any): data is ClientLogEntry {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.level === 'string' &&
    ['debug', 'info', 'warn', 'error', 'fatal'].includes(data.level) &&
    typeof data.message === 'string' &&
    typeof data.timestamp === 'string'
  );
}

/**
 * POST /api/logs/client
 * Receive client-side logs and forward to centralized logger
 */
router.post('/client', logRateLimit, (req: Request, res: Response): void => {
  try {
    const logEntry = req.body;

    // Validate log entry
    if (!isValidLogEntry(logEntry)) {
      res.status(400).json({
        success: false,
        error: 'Invalid log entry format',
      });
      return;
    }

    // Prepare context for server logger
    const context = {
      client: true,
      userAgent: req.headers['user-agent'],
      ip: req.ip,
      sessionId: logEntry.sessionId,
      component: logEntry.component,
      clientTimestamp: logEntry.timestamp,
      ...logEntry.context,
    };

    // Log to centralized logger based on level
    switch (logEntry.level) {
      case 'debug':
        logger.debug(`[Client] ${logEntry.message}`, context);
        break;
      case 'info':
        logger.info(`[Client] ${logEntry.message}`, context);
        break;
      case 'warn':
        logger.warn(`[Client] ${logEntry.message}`, context);
        break;
      case 'error':
      case 'fatal': {
        // Create Error object if error details are provided
        let error: Error | undefined;
        if (logEntry.error) {
          error = new Error(logEntry.error.message);
          error.name = logEntry.error.name || 'ClientError';
          if (logEntry.error.stack) {
            error.stack = logEntry.error.stack;
          }
          (error as any).code = logEntry.error.code;
        }

        logger.error(`[Client] ${logEntry.message}`, error, context);
        break;
      }
    }

    res.json({ success: true });
  } catch (error) {
    logger.error('Failed to process client log', error, {
      body: req.body,
      headers: req.headers,
    });

    res.status(500).json({
      success: false,
      error: 'Failed to process log entry',
    });
  }
});

/**
 * GET /api/logs/health
 * Health check for logging system
 */
router.get('/health', (_req: Request, res: Response): void => {
  res.json({
    success: true,
    timestamp: new Date().toISOString(),
    server: 'ready',
  });
});

export default router;
