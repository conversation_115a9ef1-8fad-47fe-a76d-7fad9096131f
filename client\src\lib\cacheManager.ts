/**
 * Client-side Cache Manager
 *
 * A lightweight caching system that provides an in-memory cache with LRU (Least Recently Used)
 * eviction policy and optional localStorage persistence with TTL (Time To Live) support.
 *
 * This manager is a generic utility and has NO knowledge of React Query or other data layers.
 */
import logger from './logger';

interface CacheOptions {
  useLocalStorage?: boolean;
  ttl?: number; // Time to live in seconds
}

interface CacheEntry<T> {
  value: T;
  timestamp: number;
  ttl: number | null;
}

const DEFAULT_MAX_SIZE = 500;

class CacheManager {
  private memoryCache: Map<string, CacheEntry<any>>;
  private readonly prefix: string;
  private readonly maxSize: number;

  private cacheHits = 0;
  private cacheMisses = 0;

  constructor(namespace = 'inboxzero', maxSize = DEFAULT_MAX_SIZE) {
    this.memoryCache = new Map();
    this.prefix = `${namespace}:`;
    this.maxSize = maxSize;

    this.loadFromLocalStorage();
    this.setupPeriodicCleanup();
  }

  public set<T>(key: string, value: T, options: CacheOptions = {}): void {
    const cacheKey = this.prefixKey(key);
    const entry: CacheEntry<T> = {
      value,
      timestamp: Date.now(),
      ttl: options.ttl !== undefined ? options.ttl : null,
    };

    this.memoryCache.set(cacheKey, entry);
    this.enforceMaxSize();

    if (options.useLocalStorage) {
      try {
        localStorage.setItem(cacheKey, JSON.stringify(entry));
      } catch (error) {
        logger.error('Failed to store cache item in localStorage', { error });
      }
    }
  }

  public get<T>(key: string, defaultValue?: T): T | undefined {
    const cacheKey = this.prefixKey(key);
    let entry = this.memoryCache.get(cacheKey);

    if (entry) {
      if (this.isExpired(entry)) {
        this.remove(key);
        this.cacheMisses++;
        return defaultValue;
      }
      // Refresh entry for LRU policy
      this.memoryCache.delete(cacheKey);
      this.memoryCache.set(cacheKey, entry);
      this.cacheHits++;
      return entry.value as T;
    }

    try {
      const storedValue = localStorage.getItem(cacheKey);
      if (storedValue) {
        entry = JSON.parse(storedValue) as CacheEntry<T>;
        if (this.isExpired(entry)) {
          localStorage.removeItem(cacheKey);
          this.cacheMisses++;
          return defaultValue;
        }
        // Load into memory for faster access and to apply LRU
        this.memoryCache.set(cacheKey, entry);
        this.enforceMaxSize();
        this.cacheHits++;
        return entry.value as T;
      }
    } catch (error) {
      logger.warn('Error retrieving from localStorage', { error });
    }

    this.cacheMisses++;
    return defaultValue;
  }

  public remove(key: string): void {
    const cacheKey = this.prefixKey(key);
    this.memoryCache.delete(cacheKey);
    try {
      localStorage.removeItem(cacheKey);
    } catch (error) {
      logger.warn('Error removing item from localStorage', { error });
    }
  }

  public has(key: string): boolean {
    const entry = this.memoryCache.get(this.prefixKey(key));
    return !!entry && !this.isExpired(entry);
  }

  public clear(includeLocalStorage = true): void {
    this.memoryCache.clear();
    if (includeLocalStorage) {
      try {
        for (let i = localStorage.length - 1; i >= 0; i--) {
          const key = localStorage.key(i);
          if (key?.startsWith(this.prefix)) {
            localStorage.removeItem(key);
          }
        }
      } catch (error) {
        logger.warn('Error clearing localStorage', { error });
      }
    }
  }

  public getCacheStats() {
    let memorySize = 0;
    try {
      this.memoryCache.forEach((entry) => {
        memorySize += JSON.stringify(entry).length;
      });
    } catch (e) {
      logger.warn('Could not stringify cache entry for size calculation', { error: e });
    }

    const totalRequests = this.cacheHits + this.cacheMisses;
    const hitRate = totalRequests > 0 ? this.cacheHits / totalRequests : 0;

    return {
      hitRate: Math.round(hitRate * 100) / 100,
      memorySize,
      itemCount: this.memoryCache.size,
      maxSize: this.maxSize,
    };
  }

  private enforceMaxSize(): void {
    while (this.memoryCache.size > this.maxSize) {
      const oldestKey = this.memoryCache.keys().next().value;
      if (oldestKey) {
        this.memoryCache.delete(oldestKey);
      }
    }
  }

  private loadFromLocalStorage(): void {
    try {
      const keys = Object.keys(localStorage);
      for (const key of keys) {
        if (key.startsWith(this.prefix)) {
          const value = localStorage.getItem(key);
          if (value) {
            try {
              const entry = JSON.parse(value);
              if (!this.isExpired(entry)) {
                this.memoryCache.set(key, entry);
              } else {
                localStorage.removeItem(key);
              }
            } catch {
              localStorage.removeItem(key);
            }
          }
        }
      }
      this.enforceMaxSize();
    } catch (error) {
      logger.warn('Error loading from localStorage', { error });
    }
  }

  private isExpired(entry: CacheEntry<any>): boolean {
    if (entry.ttl === null) return false;
    return Date.now() > entry.timestamp + entry.ttl * 1000;
  }

  private setupPeriodicCleanup(): void {
    // Prevent multiple intervals in development with Hot Module Replacement (HMR)
    if ((globalThis as any).__INBOXZERO_CACHE_CLEANUP_ID) {
      this.cleanupIntervalId = (globalThis as any).__INBOXZERO_CACHE_CLEANUP_ID;
      return;
    }

    this.cleanupIntervalId = setInterval(() => {
      this.memoryCache.forEach((entry, key) => {
        if (this.isExpired(entry)) {
          this.memoryCache.delete(key);
        }
      });

      if (Math.random() < 0.1) {
        try {
          const keys = Object.keys(localStorage);
          for (const key of keys) {
            if (key.startsWith(this.prefix)) {
              const value = localStorage.getItem(key);
              if (value) {
                try {
                  const entry = JSON.parse(value);
                  if (this.isExpired(entry)) {
                    localStorage.removeItem(key);
                  }
                } catch {
                  localStorage.removeItem(key);
                }
              }
            }
          }
        } catch (error) {
          logger.warn('Error cleaning localStorage', { error });
        }
      }
    }, 60000);

    // Persist interval id globally so reloaded modules reuse it
    (globalThis as any).__INBOXZERO_CACHE_CLEANUP_ID = this.cleanupIntervalId;
  }

  private prefixKey(key: string): string {
    return key.startsWith(this.prefix) ? key : `${this.prefix}${key}`;
  }

  // Keep a reference for potential future clearInterval usage
  private cleanupIntervalId: ReturnType<typeof setInterval> | undefined;
}

const cacheManager = new CacheManager();

export const CACHE_DURATIONS = {
  SHORT: 60, // 1 minute
  MEDIUM: 300, // 5 minutes
  LONG: 1800, // 30 minutes
  VERY_LONG: 86400, // 24 hours
};

export function getStaleTime(endpoint: string): number {
  if (endpoint.includes('/api/stats')) return CACHE_DURATIONS.MEDIUM * 1000;
  if (endpoint.includes('/api/emails/')) return 0;
  if (endpoint.includes('/api/emails')) return 0;
  return 0;
}

export { cacheManager };
export default cacheManager;
