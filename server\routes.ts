import type { Express } from 'express';
import express from 'express';
import { isAdmin } from './middleware/admin';
import { optionalAuth, requireAuth } from './middleware/simpleAuth';
import adminRoutes from './routes/admin-consolidated';
import clientLogsRoutes from './routes/client-logs';
// import diagnosticsRoutes from "./routes/diagnostics-consolidated";
import emailProviderRoutes from './routes/emailProviders';
import emailRoutes from './routes/emails';
import geminiRoutes from './routes/gemini';
import aiRoutes from './routes/ai';
import migrationRoutes from './routes/migrations-consolidated';
import monitoringRoutes from './routes/monitoring-consolidated';
import settingsRoutes from './routes/settings';
import statsRoutes from './routes/stats-routes';
import statusRoutes from './routes/status-consolidated';
import themeRoutes from './routes/theme';
import userRoutes from './routes/user';
import privacyRoutes from './routes/privacy-routes';

export function registerRoutes(app: Express): void {
  // Health & Status routes (no auth required)
  app.use('/api/health', statusRoutes);

  // Theme settings (mixed auth - GET public, PATCH requires auth)
  app.use('/api/theme', themeRoutes);

  // Authentication routes are now mounted centrally in `server/auth/index.ts`
  // app.use("/auth", authRoutes);

  // Apply optional auth to all API routes to set user context in logger
  app.use('/api', optionalAuth);

  // Core API routes (auth required)
  app.use('/api/user', requireAuth, userRoutes);
  app.use('/api/emails', requireAuth, emailRoutes);
  app.use('/api/stats', requireAuth, statsRoutes);
  app.use('/api/settings', requireAuth, settingsRoutes);
  app.use('/api/privacy', requireAuth, privacyRoutes);

  // Email provider management & token operations (auth required)
  app.use('/api/email-providers', requireAuth, emailProviderRoutes);

  // Client logging route (no auth required for logging)
  app.use('/api/logs', clientLogsRoutes);

  // System monitoring and diagnostic routes (auth required)
  app.use('/api', requireAuth, monitoringRoutes);

  // Admin routes (admin auth required)
  app.use('/api/admin', requireAuth, isAdmin, adminRoutes);

  // Migration & emergency routes (auth required for safety)
  app.use('/api', requireAuth, migrationRoutes);

  // AI service routes (auth required)
  app.use('/api/gemini', requireAuth, geminiRoutes);
  app.use('/api/ai', requireAuth, aiRoutes);

  // Legacy debug sample data endpoints
  // app.use("/api/debug", diagnosticsRoutes);
}
