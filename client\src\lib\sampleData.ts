/**
 * Sample Data Module
 *
 * This module provides sample data for the application when in test mode
 * or when no real data is available from the API.
 */

import type { Email, Settings, Stats, User } from '../types/email';

export const sampleEmails = [
  {
    id: 1,
    userId: 1,
    messageId: 'msg1',
    threadId: 'thread1',
    subject: 'Quarterly Report Due',
    snippet: 'Please submit your quarterly report by Friday.',
    sender: 'John Manager',
    senderEmail: '<EMAIL>',
    receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    isRead: false,
    isArchived: false,
    isReplied: false,
    isTrashed: false,
    isImportant: true,
    snoozedUntil: null,
    replyDate: null,
    replyId: null,
    categories: ['Work', 'Urgent'],
    priority: 'high',
    summary:
      'Your quarterly report is due this Friday. Please ensure all sections are completed with the latest data.',
    aiReply:
      'I will prepare the quarterly report and submit it by Friday. Thank you for the reminder.',
    originalContent:
      'Hello, Please submit your quarterly report by Friday. We need all the data to prepare for the board meeting next week. Thank you, <PERSON>',
    htmlContent:
      '<div>Hello,<br><br>Please submit your quarterly report by Friday. We need all the data to prepare for the board meeting next week.<br><br>Thank you,<br>John</div>',
    provider: 'gmail',
    labelIds: ['INBOX', 'IMPORTANT'],
    contentExpiresAt: null,
    lastAccessed: new Date(),
    isContentEncrypted: false,
    retentionDays: 30,
  },
  {
    id: 2,
    userId: 1,
    messageId: 'msg2',
    threadId: 'thread2',
    subject: 'Team lunch tomorrow',
    snippet: "Let's meet at the Italian restaurant at 12:30pm.",
    sender: 'Sarah Team',
    senderEmail: '<EMAIL>',
    receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
    isRead: true,
    isArchived: false,
    isReplied: false,
    isTrashed: false,
    isImportant: false,
    snoozedUntil: null,
    replyDate: null,
    replyId: null,
    categories: ['Work', 'Personal'],
    priority: 'medium',
    summary: 'Team lunch planned for tomorrow at the Italian restaurant at 12:30pm.',
    aiReply: "I'll be there! Looking forward to the team lunch tomorrow at 12:30pm.",
    originalContent:
      "Hi team, Let's meet at the Italian restaurant at 12:30pm tomorrow for our monthly team lunch. Please let me know if you can make it. Best, Sarah",
    htmlContent:
      "<div>Hi team,<br><br>Let's meet at the Italian restaurant at 12:30pm tomorrow for our monthly team lunch. Please let me know if you can make it.<br><br>Best,<br>Sarah</div>",
    provider: 'gmail',
    labelIds: ['INBOX'],
  },
  {
    id: 3,
    userId: 1,
    messageId: 'msg3',
    threadId: 'thread3',
    subject: 'URGENT: System Outage',
    snippet: 'Our main database is down. Need immediate assistance.',
    sender: 'Tech Support',
    senderEmail: '<EMAIL>',
    receivedAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    isRead: false,
    isArchived: false,
    isReplied: false,
    isTrashed: false,
    isImportant: true,
    snoozedUntil: null,
    replyDate: null,
    replyId: null,
    categories: ['Work', 'Urgent'],
    priority: 'high',
    summary:
      'Critical system outage affecting the main database. Immediate assistance required to restore services.',
    aiReply:
      "I'll look at the database issue right away. Have you checked the server logs? I'll be online in 5 minutes to help troubleshoot.",
    originalContent:
      'URGENT: Our main database is down. We need immediate assistance to restore service. Users are unable to access the system. Error log attached.',
    htmlContent:
      '<div><strong>URGENT:</strong> Our main database is down. We need immediate assistance to restore service. Users are unable to access the system. Error log attached.</div>',
    provider: 'gmail',
    labelIds: ['INBOX', 'IMPORTANT'],
  },
  {
    id: 4,
    userId: 1,
    messageId: 'msg4',
    threadId: 'thread4',
    subject: '50% off Spring Sale',
    snippet: "Don't miss our biggest spring sale! All items half off.",
    sender: 'Fashion Store',
    senderEmail: '<EMAIL>',
    receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
    isRead: true,
    isArchived: false,
    isReplied: false,
    isTrashed: false,
    isImportant: false,
    snoozedUntil: null,
    replyDate: null,
    replyId: null,
    categories: ['Promotions'],
    priority: 'low',
    summary: 'Spring sale promotion with 50% off all items. Limited time offer.',
    aiReply:
      "Thank you for letting me know about the sale. I'm not interested at this time, but please keep me updated on future promotions.",
    originalContent:
      "Spring Sale Alert! Don't miss our biggest spring sale ever! All items 50% off for a limited time only. Shop online or in-store. Use code SPRING50 at checkout.",
    htmlContent:
      "<div><h2>Spring Sale Alert!</h2><p>Don't miss our biggest spring sale ever! All items 50% off for a limited time only.</p><p>Shop online or in-store. Use code <strong>SPRING50</strong> at checkout.</p></div>",
    provider: 'gmail',
    labelIds: ['PROMOTIONS'],
  },
  {
    id: 5,
    userId: 1,
    messageId: 'msg5',
    threadId: 'thread5',
    subject: 'Weekend plans?',
    snippet: 'Hey, want to go hiking on Saturday?',
    sender: 'Michael Friend',
    senderEmail: '<EMAIL>',
    receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
    isRead: false,
    isArchived: false,
    isReplied: false,
    isTrashed: false,
    isImportant: false,
    snoozedUntil: null,
    replyDate: null,
    replyId: null,
    categories: ['Personal'],
    priority: 'medium',
    summary: 'Michael is inviting you to go hiking this Saturday.',
    aiReply: 'Hiking on Saturday sounds great! What time were you thinking of going and where?',
    originalContent:
      'Hey there, The weather is looking perfect this weekend. Want to go hiking on Saturday? I was thinking we could check out the new trail at Green Mountain. Let me know! Michael',
    htmlContent:
      '<div>Hey there,<br><br>The weather is looking perfect this weekend. Want to go hiking on Saturday? I was thinking we could check out the new trail at Green Mountain.<br><br>Let me know!<br>Michael</div>',
    provider: 'gmail',
    labelIds: ['INBOX', 'CATEGORY_PERSONAL'],
  },
  {
    id: 6,
    userId: 1,
    messageId: 'msg6',
    threadId: 'thread6',
    subject: 'Your invoice #2023-05',
    snippet: 'Your monthly invoice is attached. Payment due in 14 days.',
    sender: 'Accounting',
    senderEmail: '<EMAIL>',
    receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    isRead: true,
    isArchived: true,
    isReplied: true,
    isTrashed: false,
    isImportant: true,
    snoozedUntil: null,
    replyDate: new Date(Date.now() - 1000 * 60 * 60 * 23), // Replied 1 hour after receiving
    replyId: 'reply6',
    categories: ['Work'],
    priority: 'medium',
    summary: 'Monthly invoice with payment due in 14 days. Amount: $1,250.00',
    aiReply: 'Thank you for the invoice. I will process the payment this week.',
    originalContent:
      'Please find attached your monthly invoice #2023-05 for services rendered. The amount due is $1,250.00. Please remit payment within 14 days. Thank you for your business.',
    htmlContent:
      '<div>Please find attached your monthly invoice #2023-05 for services rendered.<br><br>The amount due is <strong>$1,250.00</strong>.<br><br>Please remit payment within 14 days.<br><br>Thank you for your business.</div>',
    provider: 'gmail',
    labelIds: ['INBOX', 'CATEGORY_PERSONAL', 'STARRED'],
  },
  {
    id: 7,
    userId: 1,
    messageId: 'msg7',
    threadId: 'thread7',
    subject: 'New Project Opportunity',
    snippet: "I'd like to discuss a potential new project with you.",
    sender: 'Lisa Client',
    senderEmail: '<EMAIL>',
    receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 3), // 3 hours ago
    isRead: false,
    isArchived: false,
    isReplied: false,
    isTrashed: false,
    isImportant: true,
    snoozedUntil: null,
    replyDate: null,
    replyId: null,
    categories: ['Work'],
    priority: 'high',
    summary:
      'A potential new project opportunity that may require your expertise. The client is interested in discussing details soon.',
    aiReply:
      "I'm very interested in discussing this new project opportunity. I have availability this week on Tuesday and Thursday afternoons for a call if that works for you.",
    originalContent:
      "Hi there, I've been following your work and I'd like to discuss a potential new project with you. We need someone with your expertise for a 3-month engagement starting next month. Would you be available for a call this week to discuss details? Best, Lisa",
    htmlContent:
      "<div>Hi there,<br><br>I've been following your work and I'd like to discuss a potential new project with you. We need someone with your expertise for a 3-month engagement starting next month.<br><br>Would you be available for a call this week to discuss details?<br><br>Best,<br>Lisa</div>",
    provider: 'gmail',
    labelIds: ['INBOX', 'IMPORTANT'],
  },
  {
    id: 8,
    userId: 1,
    messageId: 'msg8',
    threadId: 'thread8',
    subject: 'Family reunion update',
    snippet: 'Updated information about the family reunion next month.',
    sender: 'Uncle Bob',
    senderEmail: '<EMAIL>',
    receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 36), // 1.5 days ago
    isRead: true,
    isArchived: false,
    isReplied: false,
    isTrashed: false,
    isImportant: false,
    snoozedUntil: null,
    replyDate: null,
    replyId: null,
    categories: ['Personal'],
    priority: 'medium',
    summary:
      "The family reunion is scheduled for next month on the 15th at Uncle Bob's lake house. Please bring a dish to share.",
    aiReply:
      "Thanks for the update about the family reunion. I'll be there on the 15th and will bring my famous potato salad to share.",
    originalContent:
      "Hello family, Just a quick update on our family reunion next month. We'll be gathering at my lake house on the 15th starting at noon. Please bring a dish to share for our potluck lunch. Looking forward to seeing everyone! Uncle Bob",
    htmlContent:
      "<div>Hello family,<br><br>Just a quick update on our family reunion next month. We'll be gathering at my lake house on the 15th starting at noon.<br><br>Please bring a dish to share for our potluck lunch.<br><br>Looking forward to seeing everyone!<br>Uncle Bob</div>",
    provider: 'gmail',
    labelIds: ['INBOX', 'CATEGORY_PERSONAL'],
  },
];

export const sampleUser: Partial<User> = {
  id: 1,
  email: '<EMAIL>',
  name: 'Test User',
  picture: 'https://ui-avatars.com/api/?name=Test+User&background=0D8ABC&color=fff',
  accessToken: 'sample_access_token',
  refreshToken: 'sample_refresh_token',
  expiresAt: new Date(Date.now() + 3600 * 1000), // Expires in 1 hour
  provider: 'gmail',
  emailsProcessed: 124,
  repliesSent: 3,
  tier: 'free',
  replyTone: 'Professional',
  lastLogin: new Date(),
  lastReplyDate: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
  refreshAttempts: 0,
  lastTokenRefresh: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
  authErrorCount: 0,
  securityLevel: 'standard',
};

export const sampleSettings: Partial<Settings> = {
  id: 1,
  userId: 1,
  replyTone: 'Professional',
  displayName: 'Test User',
  customTone:
    'Thanks for your message! I appreciate you reaching out. I think we should tackle this step by step. Let me know if you need any additional information or have questions. Looking forward to working on this together.',
  privacyMode: false,
  notificationDigest: true,
  categories: {
    Work: true,
    Personal: true,
    Urgent: true,
    Promotions: true,
  },
  priorityColors: {
    high: '#FF4136',
    medium: '#FF851B',
    low: '#2ECC40',
  },
};

export const sampleStats: Stats = {
  totalEmails: 152,
  readEmails: 100,
  archivedEmails: 78,
  repliedEmails: 31,
  emailsProcessed: 124,
  categoryCounts: {
    Work: 67,
    Personal: 43,
    Urgent: 12,
    Promotions: 30,
  },
  priorityCounts: {
    high: 25,
    medium: 89,
    low: 38,
  },
  lastDayActivity: 8,
  lastWeekActivity: 42,
  lastMonthActivity: 124,
  streakDays: 3,
  averageResponseTime: 120,
};
