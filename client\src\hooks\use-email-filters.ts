import { useCallback, useMemo, useState } from 'react';
import type { Email, EmailFilters } from '@/types/email';

/**
 * Custom hook for email filtering and searching functionality
 */
export function useEmailFilters(emails: Email[]) {
  const defaultFilters: EmailFilters = {
    status: 'all',
    categories: [],
    priority: 'all',
    timeRange: 'all'
  };
  
  const [filters, setFilters] = useState<EmailFilters>(defaultFilters);
  const [searchQuery, setSearchQuery] = useState<string>('');

  /**
   * Apply filters to the email list
   */
  const filteredEmails = useMemo(() => {
    if (!emails) return [];

    return emails.filter((email) => {
      // Filter by status (read/unread/archived)
      if (filters.status !== 'all') {
        const isRead = email.isRead === true;
        const isArchived = email.isArchived === true;

        if (filters.status === 'read' && !isRead) return false;
        if (filters.status === 'unread' && isRead) return false;
        if (filters.status === 'archived' && !isArchived) return false;
      }

      // Filter by categories
      if (filters.categories && filters.categories.length > 0) {
        if (!email.categories) return false;
        if (!filters.categories.some((cat: string) => email.categories?.includes(cat))) return false;
      }

      // Filter by priority
      if (filters.priority !== 'all' && email.priority !== filters.priority) {
        return false;
      }

      // Filter by time range
      if (filters.timeRange !== 'all' && email.receivedAt) {
        const now = new Date();
        const receivedDate = new Date(email.receivedAt);

        // Today - within the last 24 hours
        if (filters.timeRange === 'today') {
          const yesterday = new Date(now);
          yesterday.setDate(now.getDate() - 1);
          if (receivedDate < yesterday) return false;
        }

        // This week - within the last 7 days
        else if (filters.timeRange === 'this_week') {
          const lastWeek = new Date(now);
          lastWeek.setDate(now.getDate() - 7);
          if (receivedDate < lastWeek) return false;
        }

        // This month - within the last 30 days
        else if (filters.timeRange === 'this_month') {
          const lastMonth = new Date(now);
          lastMonth.setDate(now.getDate() - 30);
          if (receivedDate < lastMonth) return false;
        }
      }

      // Apply search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSubject = email.subject?.toLowerCase().includes(query) || false;
        const matchesSender = email.sender?.toLowerCase().includes(query) || false;
        const matchesSnippet = email.snippet?.toLowerCase().includes(query) || false;
        const matchesEmail = email.senderEmail?.toLowerCase().includes(query) || false;

        return matchesSubject || matchesSender || matchesSnippet || matchesEmail;
      }

      return true;
    });
  }, [emails, filters, searchQuery]);

  /**
   * Clear all filters and search
   */
  const clearFilters = useCallback(() => {
    setFilters(defaultFilters);
    setSearchQuery('');
  }, []);

  /**
   * Check if any filters or search are active
   */
  const hasActiveFilters = useMemo(() => {
    return (
      searchQuery ||
      Object.values(filters).some((v) => (Array.isArray(v) ? v.length > 0 : v !== 'all'))
    );
  }, [filters, searchQuery]);

  return {
    filters,
    setFilters,
    searchQuery,
    setSearchQuery,
    filteredEmails,
    clearFilters,
    hasActiveFilters,
  };
}
