/**
 * Gemini API Service
 *
 * This service handles all interactions with the Google Generative AI APIs (Gemini).
 * It provides the following features:
 * - Email summarization
 * - Email categorization
 * - AI-generated email replies
 * - Unified email processing (combining the above)
 *
 * The service includes:
 * - Circuit breaker pattern for handling API failures
 * - Retry mechanism for transient errors
 * - Caching to reduce API calls
 * - Fallback responses when the API is unavailable
 */

import * as crypto from 'node:crypto';
import { GoogleGenAI } from '@google/genai';
import { getEnvVar } from '../lib/environmentValidator';
import logger from '../lib/logger';
import { protectApiCall } from '../utils/apiProtector';

// Configuration
const DEFAULT_GEMINI_MODEL = 'gemini-2.0-flash';
const EMAIL_CACHE_SIZE = 50; // Maximum number of cached results
const EMAIL_CACHE_EXPIRY = 30 * 60 * 1000; // 30 minutes in milliseconds

// Circuit breaker protection is now handled by protectApiCall function
// This provides unified protection across all Gemini operations

import { getCircuitState } from '../utils/apiProtector';

// Track if we've already logged the API key error to prevent console spam
let apiKeyErrorLogged = false;

/**
 * Interface for unified email processing results
 */
export interface UnifiedEmailProcessingResult {
  summary: string;
  categories: string[];
  reply: string;
  error?: string;
  cachedAt?: number; // Timestamp for caching
}

// Cache for recent email processing to reduce API calls
const emailProcessingCache = new Map<string, UnifiedEmailProcessingResult>();

/**
 * Cleans expired entries from the email processing cache
 */
function cleanEmailProcessingCache() {
  // Current time
  const now = Date.now();

  // Remove expired entries
  for (const [key, value] of emailProcessingCache.entries()) {
    if (value.cachedAt && now - value.cachedAt > EMAIL_CACHE_EXPIRY) {
      emailProcessingCache.delete(key);
    }
  }

  // If still too many entries, remove oldest ones
  if (emailProcessingCache.size > EMAIL_CACHE_SIZE) {
    // Get all entries sorted by timestamp (oldest first)
    const entries = Array.from(emailProcessingCache.entries()).sort(
      (a, b) => (a[1].cachedAt || 0) - (b[1].cachedAt || 0)
    );

    // Remove oldest entries until we're under the limit
    const entriesToRemove = entries.slice(0, entries.length - EMAIL_CACHE_SIZE);
    for (const [key] of entriesToRemove) {
      emailProcessingCache.delete(key);
    }
  }
}

/**
 * Structure for categorization result
 */
export interface CategoryResult {
  category: string;
  confidence: number;
}

/**
 * Test function for checking if the Gemini API is working
 * @returns Object with success status and message
 */
export async function testGeminiApi(): Promise<{
  success: boolean;
  message: string;
}> {
  const timer = logger.startTimer();
  const operationId = `test-${Date.now()}`;
  
  logger.ai.operationStart('api-test', operationId, { service: 'gemini' });

  try {
    // Check if API key is set
    const apiKey = getEnvVar('GEMINI_API_KEY');
    if (!apiKey) {
      const duration = timer();
      logger.ai.operationFail('api-test', operationId, new Error('API key not configured'), duration, {
        service: 'gemini',
        reason: 'missing_api_key'
      });
      return {
        success: false,
        message: 'Gemini API key is not set in environment variables',
      };
    }

    // Try to initialize the client
    const genAI = new GoogleGenAI({ apiKey });

    // Try to generate content
    const modelName = getEnvVar('GEMINI_MODEL') || DEFAULT_GEMINI_MODEL;
    
    logger.ai.apiCall('gemini', 'generateContent', 'POST', {
      model: modelName,
      testRequest: true
    });

    const response = await genAI.models.generateContent({
      model: modelName,
      contents: "Respond with 'Gemini API is working!' if you receive this message.",
    });
    
    const text = response.text || '';
    const duration = timer();
    
    logger.ai.apiResponse('gemini', 'generateContent', 200, duration, {
      model: modelName,
      responseLength: text.length
    });
    
    logger.ai.operationComplete('api-test', operationId, duration, {
      service: 'gemini',
      model: modelName,
      responseLength: text.length
    });

    return {
      success: true,
      message: `Gemini API test successful. Response: "${text.substring(0, 50)}..."`,
    };
  } catch (error) {
    const duration = timer();
    const errorMessage = String(error);
    
    logger.ai.operationFail('api-test', operationId, error, duration, {
      service: 'gemini',
      errorType: error instanceof Error ? error.name : 'unknown'
    });

    // Check if the error is related to authentication
    if (errorMessage.includes('UNAUTHENTICATED') || errorMessage.includes('401')) {
      return {
        success: false,
        message: 'Authentication failed with Gemini API. Please check your API key.',
      };
    }

    return {
      success: false,
      message: `Gemini API test failed: ${errorMessage.substring(0, 100)}...`,
    };
  }
}

// Function to initialize the Gemini client with configurable model
function getGeminiClient() {
  const apiKey = getEnvVar('GEMINI_API_KEY');
  if (!apiKey) {
    if (!apiKeyErrorLogged) {
      logger.error('GEMINI_API_KEY environment variable is not set', { service: 'gemini' });
      apiKeyErrorLogged = true;
    }
    throw new Error('GEMINI_API_KEY environment variable is not set');
  }

  // Get model from environment or use default
  const _modelName = getEnvVar('GEMINI_MODEL') || DEFAULT_GEMINI_MODEL;

  try {
    // Return the client directly
    return new GoogleGenAI({ apiKey });
  } catch (error) {
    logger.error('Failed to initialize Gemini client', { error: String(error), service: 'gemini' });
    throw error;
  }
}

/**
 * Extract text from HTML content for use with AI processing
 * This is important because HTML emails can be complex and need proper extraction
 *
 * @param content The content to process (may be HTML or plain text)
 * @returns Plain text extracted from the content
 */
export function extractTextFromHtml(content: string): string {
  if (!content) return '';

  try {
    // Check if content appears to be HTML
    const isHtml = /<\/?[a-z][\s\S]*>/i.test(content);

    if (!isHtml) {
      // If not HTML, just return the content with some basic cleaning
      return content
        .replace(/\r\n/g, '\n')
        .replace(/\n{3,}/g, '\n\n')
        .trim();
    }

    // Very basic HTML tag removal - in a server environment we don't have DOM access
    // A more robust solution would use a proper HTML parser like 'jsdom' or 'cheerio'
    let textContent = content
      // First remove scripts and style elements entirely
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, ' ')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, ' ')

      // Replace common block elements with newlines
      .replace(/<\/(?:p|div|h[1-6]|table|tr|blockquote|li|br)[^>]*>/gi, '\n')

      // Replace horizontal rules with a visual separator
      .replace(/<hr[^>]*>/gi, '\n---\n')

      // Convert list items and headings to maintain some structure
      .replace(/<li[^>]*>/gi, '• ')
      .replace(/<h[1-6][^>]*>/gi, '**')
      .replace(/<\/h[1-6][^>]*>/gi, '**\n')

      // Preserve links with their text and URL for context
      .replace(/<a[^>]*href=["']([^"']*)["'][^>]*>(.*?)<\/a>/gi, '$2 [$1]')

      // Remove all remaining tags
      .replace(/<[^>]*>/g, '')

      // Handle entities
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&ldquo;/g, '"')
      .replace(/&rdquo;/g, '"')
      .replace(/&rsquo;/g, "'")
      .replace(/&[a-zA-Z0-9#]+;/g, ' '); // Replace any remaining entities

    // Normalize spaces and tabs but preserve intentional newlines for readability
    textContent = textContent
      .replace(/[ \t]+/g, ' ') // Collapse consecutive spaces/tabs
      .replace(/\n\s+/g, '\n ') // Ensure single space after newline for readability
      .replace(/\n{3,}/g, '\n\n') // Limit consecutive newlines
      .trim();

    return textContent;
  } catch (error) {
    console.error('Error extracting text from content:', error);
    // Return original content on error, with basic cleanup
    return content
      .replace(/\r\n/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  }
}

/**
 * Validate and clean the email summary, checking for error patterns and content issues
 *
 * @param summary The raw summary to validate
 * @returns A clean, validated summary or error message
 */
function validateSummary(summary: string): string {
  // Check for null, undefined or empty summaries
  if (!summary || summary.length < 10) {
    return 'Summary unavailable';
  }

  // Check if the summary is already an error message
  if (summary === 'Summary unavailable - processing error detected') {
    return summary;
  }

  const summaryLower = summary.toLowerCase();

  // Only check for explicit self-identification or apology patterns
  // that clearly indicate the AI couldn't process the content
  if (
    summaryLower.startsWith('i apologize') ||
    summaryLower.startsWith('i cannot') ||
    summaryLower.startsWith('sorry, i')
  ) {
    return 'Summary unavailable - processing error detected';
  }

  // Handle excessively long summaries
  if (summary.length > 500) {
    return `${summary.substring(0, 497)}...`;
  }

  return summary;
}

function validateCategories(categories: string[]): string[] {
  if (!categories || categories.length === 0) {
    return ['Uncategorized'];
  }

  // Make sure all categories are valid
  const allowedCategories = [
    'Work',
    'Personal',
    'Finance',
    'Shopping',
    'Travel',
    'Social',
    'Urgent',
    'Promotions',
    'Updates',
    'Forums',
    'Medical',
    'Legal',
    'Family',
    'Events',
    'Uncategorized',
  ];

  const validCategories = categories.filter((category) => allowedCategories.includes(category));

  return validCategories.length > 0 ? validCategories : ['Uncategorized'];
}

function validateReply(reply: string, subject: string): string {
  if (!reply || reply.length < 20) {
    return `I received your email regarding "${subject}". I'll respond to you soon.`;
  }
  return reply;
}

// Helper function to handle Gemini API errors gracefully
// Enhanced with circuit breaker and retry mechanism for improved reliability
async function safeGeminiCall<T>(
  apiCallFn: () => Promise<T>,
  fallback: T,
  context: string
): Promise<T> {
  const operationId = `gemini-${context}-${Date.now()}`;
  
  logger.ai.debug('Starting protected API call', {
    operationId,
    context,
    service: 'gemini'
  });

  try {
    // Use the unified protectApiCall function for circuit breaker protection
    const result = await protectApiCall(apiCallFn, {
      operationId,
      retryCount: 2,
      retryDelay: 500,
      useExponentialBackoff: true,
      timeoutMs: 30000,
      fallbackValue: fallback,
      context: { operation: context },
      failureThreshold: 5,
      resetTimeout: 60000,
    });

    logger.ai.debug('Protected API call completed successfully', {
      operationId,
      context,
      service: 'gemini',
      usedFallback: result === fallback
    });

    return result;
  } catch (error) {
    logger.ai.error('Protected API call failed with unhandled error', error, {
      operationId,
      context,
      service: 'gemini'
    });
    
    // Return fallback on any unhandled errors
    return fallback;
  }
}

/**
 * Summarize an email using Gemini
 * @param emailContent The content of the email to summarize
 * @param isHtml Whether the content is HTML (needs extraction)
 * @returns A summary of the email
 */
export async function summarizeEmailWithGemini(
  emailContent: string,
  isHtml = false
): Promise<string> {
  // Ensure we have content to summarize
  if (!emailContent || emailContent.trim().length === 0) {
    return 'No content to summarize';
  }

  // Process HTML content if needed
  let processedContent = emailContent;
  if (isHtml || /<\/?[a-z][\s\S]*>/i.test(emailContent)) {
    processedContent = extractTextFromHtml(emailContent);
  }

  // Smart content truncation - preserve important parts
  const MAX_CONTENT_LENGTH = 8000;
  let sanitizedContent: string;

  if (processedContent.length > MAX_CONTENT_LENGTH) {
    // If content is too long, keep beginning, important middle parts, and end
    const beginLength = Math.floor(MAX_CONTENT_LENGTH * 0.5);
    const endLength = Math.floor(MAX_CONTENT_LENGTH * 0.3);
    const middleLength = MAX_CONTENT_LENGTH - beginLength - endLength;

    // Get sections
    const beginning = processedContent.substring(0, beginLength);
    const end = processedContent.substring(processedContent.length - endLength);

    // Extract potentially important middle content (look for keywords)
    const middleStart = Math.floor(processedContent.length * 0.4);
    const _middleEnd = Math.floor(processedContent.length * 0.6);
    const middle = processedContent.substring(middleStart, middleStart + middleLength);

    sanitizedContent = `${beginning}\n[...content truncated...]\n${middle}\n[...content truncated...]\n${end}`;
  } else {
    sanitizedContent = processedContent;
  }

  // Clean up the content
  sanitizedContent = sanitizedContent.replace(/[\r\n]+/g, ' ').trim();

  return await safeGeminiCall(
    async () => {
      const genAI = getGeminiClient();
      const prompt = `You are an expert email summarizer that extracts the most important information from emails.

      TASK: Create a clear, direct summary of the following email.

      EMAIL CONTENT:
      ${sanitizedContent}

      INSTRUCTIONS:
      1. Focus exclusively on factual content, key requests, deadlines, and actionable items
      2. Write in a direct, concise style with no introductory phrases like "this email is about" or "the sender states"
      3. Use action-oriented language focusing on concrete details (names, dates, numbers)
      4. Present information in the form of "X needs Y by Z date" or "Project deadline extended to April 15"
      5. Limit to 15-20 words maximum while maintaining critical meaning
      6. Never use phrases like "the email discusses" or "the sender mentions" - just present the actual information
      
      EXAMPLE FORMAT:
      ❌ "The sender is requesting feedback on the Q2 report by Friday"
      ✅ "Feedback needed on Q2 report by Friday"
      
      Your summary should be telegraphic, information-dense, and focus solely on the essential content.`;

      const modelName = getEnvVar('GEMINI_MODEL') || DEFAULT_GEMINI_MODEL;
      const response = await genAI.models.generateContent({
        model: modelName,
        contents: prompt,
      });

      const summaryText = response.text || '';
      const validatedSummary = validateSummary(summaryText || '');

      if (validatedSummary === 'Summary unavailable') {
      } else {
      }

      return validatedSummary;
    },
    'Summary unavailable',
    'summarization'
  );
}

/**
 * Summarize an email using Gemini
 * @param emailContent The content of the email to summarize
 * @param isHtml Whether the content is HTML (needs extraction)
 * @returns A summary of the email
 */
export async function summarizeEmail(
  emailContent: string,
  isHtml = false
): Promise<string> {
  // This function is being kept for potential future use where a different summarization
  // model might be tested, but for now, it's an alias for the Gemini version.
  return summarizeEmailWithGemini(emailContent, isHtml);
}

/**
 * Generate a reply to an email using Gemini
 * @param emailContent The content of the email to reply to
 * @param subject The subject of the email
 * @param sender The sender of the email
 * @param tone The tone to use for the reply
 * @param customTone A custom tone to use
 * @param isHtml Whether the content is HTML (needs extraction)
 * @returns A reply to the email
 */
export async function generateReplyWithGemini(
  emailContent: string,
  subject: string,
  sender: string,
  tone = 'professional',
  customTone?: string,
  isHtml = false
): Promise<string> {
  // Ensure we have content to reply to
  if (!emailContent || emailContent.trim().length === 0) {
    return `I received your email regarding "${subject}". I'll respond to you soon.`;
  }

  // Process HTML content if needed
  let processedContent = emailContent;
  if (isHtml || /<\/?[a-z][\s\S]*>/i.test(emailContent)) {
    processedContent = extractTextFromHtml(emailContent);
  }

  // Truncate the content
  const sanitizedContent = processedContent
    .substring(0, 5000)
    .replace(/[\r\n]+/g, ' ')
    .trim();
  const sanitizedSubject = subject ? subject.trim() : 'No Subject';
  const sanitizedSender = sender ? sender.trim() : 'Unknown Sender';

  // Use the custom tone if provided, otherwise use the specified tone
  const replyTone = customTone || tone || 'professional';

  return await safeGeminiCall(
    async () => {
      const genAI = getGeminiClient();
      const prompt = `You are an expert email assistant that helps users craft perfect replies.

      TASK: Generate a natural, conversational ${replyTone} reply to the following email.

      EMAIL SUBJECT: "${sanitizedSubject}"
      FROM: ${sanitizedSender}
      
      EMAIL CONTENT:
      ${sanitizedContent}

      INSTRUCTIONS:
      1. Write a natural, human-like reply that:
         - Sounds like it was written by a real person, not an AI
         - Flows naturally with varied sentence structure and transitions
         - Uses contractions (I'll, you're, we've) as appropriate
         - Is conversational yet professional without template-like phrases
         - Addresses specific content and questions in a personalized way
         - Includes specific details from the original email to show understanding
      
      2. Style guidelines:
         - DO NOT include a subject line (only the body of the reply)
         - End with an appropriate closing, using "[NAME]" as a placeholder instead of a signature
         - Focus on the tone being ${replyTone} throughout the response
         - Reply as if you are the recipient of the original email
      
      3. Content guidelines:
         - Answer questions from the original email if possible
         - If the email requires a decision, provide a tentative answer or ask for more information
         - For scheduling requests, suggest specific times or ask for preferences
         - For information requests, provide concise answers or explain when you'll follow up
         - For action items, acknowledge them clearly and indicate next steps
         
               Generate only the reply text, without any prefixes like "Reply:" or any explanation.`;

      const modelName = getEnvVar('GEMINI_MODEL') || DEFAULT_GEMINI_MODEL;
      const response = await genAI.models.generateContent({
        model: modelName,
        contents: prompt,
      });

      const replyText = response.text || '';
      const validatedReply = validateReply(replyText, sanitizedSubject);

      if (
        validatedReply ===
        `I received your email regarding "${sanitizedSubject}". I'll respond to you soon.`
      ) {
      } else {
      }

      return validatedReply;
    },
    `I received your email regarding "${sanitizedSubject}". I'll respond to you soon.`,
    'reply generation'
  );
}

/**
 * Regenerate a reply to an email using Gemini with additional context
 * @param emailContent The content of the email to reply to
 * @param subject The subject of the email
 * @param sender The sender of the email
 * @param tone The tone to use for the reply
 * @param previousReply The previous reply that was generated
 * @param customTone A custom tone to use
 * @param isHtml Whether the content is HTML (needs extraction)
 * @returns A newly generated reply to the email
 */
export async function regenerateReplyWithGemini(
  emailContent: string,
  subject: string,
  sender: string,
  tone: string,
  previousReply: string,
  customTone?: string,
  isHtml = false
): Promise<string> {
  // Ensure we have content to reply to
  if (!emailContent || emailContent.trim().length === 0) {
    return `I received your email regarding "${subject}". I'll respond to you soon.`;
  }

  // Process HTML content if needed
  let processedContent = emailContent;
  if (isHtml || /<\/?[a-z][\s\S]*>/i.test(emailContent)) {
    processedContent = extractTextFromHtml(emailContent);
  }

  // Truncate the content
  const sanitizedContent = processedContent
    .substring(0, 5000)
    .replace(/[\r\n]+/g, ' ')
    .trim();
  const sanitizedSubject = subject ? subject.trim() : 'No Subject';
  const sanitizedSender = sender ? sender.trim() : 'Unknown Sender';
  const sanitizedPreviousReply = previousReply ? previousReply.trim() : '';

  // Use the custom tone if provided, otherwise use the specified tone
  const replyTone = customTone || tone || 'professional';

  return await safeGeminiCall(
    async () => {
      const genAI = getGeminiClient();
      const prompt = `You are an expert email assistant that helps users craft perfect replies.

      TASK: Generate a new, improved version of the reply to the following email.

      EMAIL SUBJECT: "${sanitizedSubject}"
      FROM: ${sanitizedSender}
      
      EMAIL CONTENT:
      ${sanitizedContent}
      
      PREVIOUS REPLY (needs improvement):
      ${sanitizedPreviousReply}

      INSTRUCTIONS:
      1. Create a completely new reply that:
         - Has the same overall intent as the previous reply
         - Is more natural and conversational than the previous version
         - Uses a ${replyTone} tone throughout
         - Addresses any missed points from the original email
         - Improves clarity, structure, and flow
         - Sounds like it was written by a real person, not an AI
      
      2. Style guidelines:
         - DO NOT include a subject line (only the body of the reply)
         - End with an appropriate closing, using "[NAME]" as a placeholder instead of a signature
         - Focus on the tone being ${replyTone} throughout the response
         - Reply as if you are the recipient of the original email
      
      3. Content guidelines:
         - Answer questions from the original email if possible
         - If the email requires a decision, provide a tentative answer or ask for more information
         - For scheduling requests, suggest specific times or ask for preferences
         - For information requests, provide concise answers or explain when you'll follow up
         - For action items, acknowledge them clearly and indicate next steps
         
               Generate only the new reply text, without any prefixes like "Reply:" or any explanation.`;

      const modelName = getEnvVar('GEMINI_MODEL') || DEFAULT_GEMINI_MODEL;
      const response = await genAI.models.generateContent({
        model: modelName,
        contents: prompt,
      });

      const replyText = response.text || '';
      const validatedReply = validateReply(replyText, sanitizedSubject);

      if (
        validatedReply ===
        `I received your email regarding "${sanitizedSubject}". I'll respond to you soon.`
      ) {
      } else {
      }

      return validatedReply;
    },
    `I received your email regarding "${sanitizedSubject}". I'll respond to you soon.`,
    'reply regeneration'
  );
}

/**
 * Process an email with all AI features in a single call
 * Performs summarization, categorization, and reply generation with one API call
 * for improved performance and reduced API usage
 */
/**
 * Unified Email Processing
 *
 * Process an email with the Gemini API to generate a summary, categorize it, and create a reply.
 * This function combines all three operations into a single API call for efficiency and
 * implements circuit breaker, retry mechanisms, and caching for improved reliability.
 *
 * @param emailContent The full content of the email
 * @param subject The subject line of the email
 * @param sender The sender's name or email
 * @param tone The desired tone for the AI-generated reply (e.g., 'professional', 'friendly')
 * @param customTone Optional custom tone instruction to override the standard tones
 * @param isHtml Whether the email content is in HTML format (needs extraction)
 * @returns An object containing the summary, categories, and AI-generated reply
 */
export async function unifiedEmailProcessing(
  emailContent: string,
  subject: string,
  sender: string,
  tone = 'professional',
  customTone?: string,
  isHtml = false
): Promise<UnifiedEmailProcessingResult> {
  const timer = logger.startTimer();
  const operationId = `unified-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  
  logger.ai.operationStart('unified-email-processing', operationId, {
    service: 'gemini',
    contentLength: emailContent.length,
    subject: subject.substring(0, 50) + '...',
    sender: sender.substring(0, 30) + '...',
    tone,
    isHtml
  });

  // Create a cache key based on content hash + metadata
  const contentHash = crypto
    .createHash('sha256')
    .update(emailContent + subject + sender)
    .digest('hex');
  const cacheKey = `${contentHash}_${tone}`;

  // Check if we have a cached result
  if (emailProcessingCache.has(cacheKey)) {
    logger.ai.cacheHit(cacheKey, 'unified-email-processing', {
      operationId,
      service: 'gemini'
    });
    
    // Update cache timestamp by re-adding it
    const cachedResult = emailProcessingCache.get(cacheKey)!;
    emailProcessingCache.delete(cacheKey);
    emailProcessingCache.set(cacheKey, {
      ...cachedResult,
      cachedAt: Date.now(), // Update timestamp
    });
    
    const duration = timer();
    logger.ai.operationComplete('unified-email-processing', operationId, duration, {
      service: 'gemini',
      cacheHit: true,
      summary: cachedResult.summary.substring(0, 50) + '...',
      categoriesCount: cachedResult.categories.length
    });
    
    return cachedResult;
  }

  logger.ai.cacheMiss(cacheKey, 'unified-email-processing', {
    operationId,
    service: 'gemini'
  });

  // Default result with fallback values
  const result: UnifiedEmailProcessingResult = {
    summary: 'Summary unavailable',
    categories: ['Uncategorized'],
    reply: `I received your email regarding "${subject}". I'll respond to you soon.`,
    cachedAt: Date.now(), // Add timestamp
  };

  // Ensure we have content to process
  if (!emailContent || emailContent.trim().length === 0) {
    const duration = timer();
    const error = new Error('No content to process');
    logger.ai.operationFail('unified-email-processing', operationId, error, duration, {
      service: 'gemini',
      reason: 'empty_content'
    });
    result.error = 'No content to process';
    return result;
  }

  // Process HTML content if needed
  let processedContent = emailContent;
  if (isHtml || /<\/?[a-z][\s\S]*>/i.test(emailContent)) {
    logger.ai.contentProcessing('html', emailContent.length, 'text-extraction', {
      operationId,
      service: 'gemini'
    });
    processedContent = extractTextFromHtml(emailContent);
    logger.ai.debug('HTML content extracted', {
      originalLength: emailContent.length,
      extractedLength: processedContent.length,
      operationId
    });
  }

  // Clean cache periodically
  cleanEmailProcessingCache();

  // Prepare email content (truncate if needed)
  const MAX_CONTENT_LENGTH = 5000;
  const sanitizedContent = processedContent
    .substring(0, MAX_CONTENT_LENGTH)
    .replace(/[\r\n]+/g, ' ')
    .trim();

  const sanitizedSubject = subject ? subject.trim() : 'No Subject';
  const sanitizedSender = sender ? sender.trim() : 'Unknown Sender';

  // Use the custom tone if provided, otherwise use the specified tone
  const replyTone = customTone || tone || 'professional';

  logger.ai.contentProcessing('email', sanitizedContent.length, 'unified-processing', {
    operationId,
    service: 'gemini',
    subject: sanitizedSubject.substring(0, 30) + '...',
    tone: replyTone,
    truncated: processedContent.length > MAX_CONTENT_LENGTH
  });

  // Process with one API call instead of multiple with enhanced resilience
  const _circuitStateDetails = getCircuitState('gemini-unified email processing');

  const processedResult = await safeGeminiCall(
    async () => {
      const genAI = getGeminiClient();
      const modelName = getEnvVar('GEMINI_MODEL') || DEFAULT_GEMINI_MODEL;
      
      // Create a prompt that asks for all three outputs in a structured format
      const prompt = `You are an intelligent email assistant that helps users understand and respond to emails efficiently.

      TASK: Analyze the following email and provide three outputs in a structured JSON format.
      
      Email Subject: "${sanitizedSubject}"
      From: ${sanitizedSender}
      Email Content: ${sanitizedContent}
      
      DETAILED INSTRUCTIONS:
      
      1. SUMMARY: Create a direct, factual summary of key points, requests, and deadlines. Use action-oriented language without phrases like "the sender states" or "the email is about". Present in the form "X needs Y by Z date". Limit to 15-20 words maximum.

      2. CATEGORIES: Classify this email into 1-3 categories that best represent its content, purpose, and urgency.
         - Choose from: Work, Personal, Finance, Shopping, Travel, Social, Urgent, Promotions, Updates, Forums, Medical, Legal, Family, Events
         - If the email contains time-sensitive requests or deadlines, include "Urgent" as one category
         - If the email requires a business decision or action, include "Work" category
         - If the email involves billing, payments, or financial matters, include "Finance" category
      
      3. REPLY: Generate a natural, human-like ${replyTone} reply that:
         - Sounds like it was written by a real person, not an AI
         - Flows naturally with varied sentence structure and transitions
         - Uses contractions (I'll, you're, we've) as appropriate
         - Is conversational yet professional without template-like phrases
         - Addresses specific content and questions in a personalized way
         - Includes specific details from the original email to show understanding
         - DOES NOT include a subject line (only the body of the reply)
         - Ends with an appropriate closing, using "[NAME]" as a placeholder instead of a signature
      
      FORMAT: Return ONLY valid JSON with these fields: "summary", "categories" (as array), and "reply" with no additional text.
      
      EXAMPLE FORMAT:
      {
        "summary": "Timeline extended to June 15. Updated Phase 2 estimates needed by Friday.",
        "categories": ["Work", "Urgent"],
        "reply": "Hi John, Thanks for the update on the timeline. I've made a note of the June 15 extension - this gives us some much-needed breathing room for the final deliverables. I'll definitely have those Phase 2 estimates ready for you by Friday. Just wondering - would you prefer I include a detailed breakdown of the numbers, or just focus on the final totals? Let me know what works best for your presentation. Cheers, [NAME]"
      }`;

      logger.ai.promptValidation('unified-email-processing', prompt.length, true, {
        operationId,
        service: 'gemini',
        model: modelName
      });

      logger.ai.apiCall('gemini', 'generateContent', 'POST', {
        operationId,
        model: modelName,
        promptLength: prompt.length,
        contentLength: sanitizedContent.length
      });

      const apiTimer = logger.startTimer();
      const response = await genAI.models.generateContent({
        model: modelName,
        contents: prompt,
      });
      const apiDuration = apiTimer();
      
      const responseText = response.text || '';
      
      logger.ai.apiResponse('gemini', 'generateContent', 200, apiDuration, {
        operationId,
        model: modelName,
        responseLength: responseText.length
      });

      // Track token usage if available
      if (response.usageMetadata) {
        logger.ai.modelUsage(
          modelName,
          response.usageMetadata.promptTokenCount || 0,
          response.usageMetadata.candidatesTokenCount || 0,
          response.usageMetadata.totalTokenCount || 0,
          { operationId }
        );
      }

      try {
        // Try to parse the response as JSON
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);

        if (jsonMatch) {
          const parsedResponse = JSON.parse(jsonMatch[0]);

          logger.ai.responseValidation('unified-email-processing', true, undefined, {
            operationId,
            service: 'gemini',
            responseLength: responseText.length
          });

          // Extract and validate each field
          const summary = validateSummary(parsedResponse.summary || '');
          const categories = validateCategories(parsedResponse.categories || []);
          const reply = validateReply(parsedResponse.reply || '', sanitizedSubject);

          return {
            summary,
            categories,
            reply,
            cachedAt: Date.now(),
          };
        }
        // If we couldn't extract JSON, use fallbacks
        logger.ai.responseValidation('unified-email-processing', false, ['JSON extraction failed'], {
          operationId,
          service: 'gemini',
          responseText: responseText.substring(0, 200) + '...'
        });
        throw new Error('Invalid response format');
      } catch (parseError) {
        logger.ai.responseValidation('unified-email-processing', false, ['JSON parsing failed'], {
          operationId,
          service: 'gemini',
          parseError: parseError instanceof Error ? parseError.message : String(parseError),
          responseText: responseText.substring(0, 200) + '...'
        });

        // Try to extract parts manually using regex if JSON parsing failed
        const summary = responseText.match(/"summary"\s*:\s*"([^"]+)"/)
          ? responseText.match(/"summary"\s*:\s*"([^"]+)"/)?.[1] || result.summary
          : result.summary;

        // Try to extract categories using regex
        const categoriesMatch = responseText.match(/"categories"\s*:\s*\[(.*?)\]/);
        const categories = categoriesMatch
          ? categoriesMatch[1].split(',').map((c: string) => c.trim().replace(/"/g, ''))
          : result.categories;

        // Try to extract reply using regex
        const replyMatch = responseText.match(/"reply"\s*:\s*"([^"]+)"/);
        const reply = replyMatch ? replyMatch[1] : result.reply;

        return {
          summary: validateSummary(summary),
          categories: validateCategories(categories),
          reply: validateReply(reply, sanitizedSubject),
          error: 'JSON parsing failed, extracted fields manually',
          cachedAt: Date.now(),
        };
      }
    },
    result,
    'unified email processing'
  );

  const duration = timer();

  // Cache the result for future use
  logger.ai.cacheStore(cacheKey, 'unified-email-processing', EMAIL_CACHE_EXPIRY, {
    operationId,
    service: 'gemini'
  });
  
  emailProcessingCache.set(cacheKey, {
    ...processedResult,
    cachedAt: Date.now(),
  });

  logger.ai.operationComplete('unified-email-processing', operationId, duration, {
    service: 'gemini',
    cacheHit: false,
    summary: processedResult.summary.substring(0, 50) + '...',
    categoriesCount: processedResult.categories.length,
    hasError: !!processedResult.error
  });

  return processedResult;
}

/**
 * Explain the categorization of an email using Gemini
 * @param emailContent The content of the email
 * @param subject The subject of the email
 * @param categories The categories assigned to the email
 * @param isHtml Whether the content is HTML (needs extraction)
 * @returns An explanation of why the email was categorized as it was
 */
export async function explainCategorizationWithGemini(
  emailContent: string,
  subject: string,
  categories: string[],
  isHtml = false
): Promise<string> {
  // Process HTML content if needed
  let processedContent = emailContent;
  if (isHtml || /<\/?[a-z][\s\S]*>/i.test(emailContent)) {
    processedContent = extractTextFromHtml(emailContent);
  }

  return await safeGeminiCall(
    async () => {
      const genAI = getGeminiClient();
      const prompt = `The following email has been categorized as ${categories.join(', ')}.
      
      Subject: ${subject}
      
      Email Content:
      ${processedContent.substring(0, 5000)}
      
      Please explain in 2-3 sentences why this email was categorized this way, and what key indicators in the content support this categorization.`;

      const modelName = getEnvVar('GEMINI_MODEL') || DEFAULT_GEMINI_MODEL;
      const response = await genAI.models.generateContent({
        model: modelName,
        contents: prompt,
      });

      const explanation = response.text || '';

      return explanation;
    },
    `This email was categorized as ${categories.join(', ')} based on its content and subject line, which contain keywords and themes typically associated with these categories.`,
    'categorization explanation'
  );
}

/**
 * Generate a daily summary of emails using Gemini
 * @param emails Array of email objects with subject and summary
 * @returns A summary of the day's emails
 */
export async function generateDailySummaryWithGemini(
  emails: { subject: string; summary: string }[]
): Promise<string> {
  if (emails.length === 0) {
    return 'You have no emails today.';
  }

  // Create a string of all the email summaries
  const emailSummaries = emails
    .map((email, index) => `${index + 1}. "${email.subject}": ${email.summary}`)
    .join('\n');

  return await safeGeminiCall(
    async () => {
      const genAI = getGeminiClient();
      const prompt = `Based on the following list of emails (shown with their subjects and summaries), 
      create a concise daily briefing highlighting the most important information. 
      
      Email Summaries:
      ${emailSummaries}
      
      INSTRUCTIONS:
      1. Group similar emails when appropriate
      2. Prioritize urgent, time-sensitive, or important items first
      3. Use direct language without phrases like "you received" or "there are emails about"
      4. Format as bullet points for each major topic or deadline
      5. Focus exclusively on actionable information and deadlines
      6. Be direct and concise - highlight exactly what needs attention
      
      Daily Briefing:`;

      const modelName = getEnvVar('GEMINI_MODEL') || DEFAULT_GEMINI_MODEL;
      const response = await genAI.models.generateContent({
        model: modelName,
        contents: prompt,
      });

      const summary = response.text || '';

      return summary;
    },
    `You have ${emails.length} emails today. Review your inbox for details.`,
    'daily summary generation'
  );
}
