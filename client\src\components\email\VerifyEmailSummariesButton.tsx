import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Check, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useProviderStatus } from '@/hooks/use-provider-status';
import { useAuth } from '@/context/AuthContext';
import apiClient from '@/lib/apiClient';

/**
 * This component provides a button that triggers a comprehensive verification of all email summaries
 * and resets any with error-like content for reprocessing.
 *
 * It's designed to catch emails that have been incorrectly marked as "processed" but still
 * contain errors or failed to generate proper summaries.
 */
interface VerificationResult {
  success: boolean;
  found: number;
  reset: number;
}

export function VerifyEmailSummariesButton() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { gmailProvider } = useProviderStatus();
  
  // Check if Gmail is connected
  const isGmailConnected = gmailProvider?.isConnected || false;

  const verifyMutation = useMutation<
    { message: string; verified: number; errors: number },
    Error
  >({
    mutationFn: () => apiClient.post('/api/emails/summaries/verify'),
    onSuccess: (result) => {
      toast({
        title: 'Verification complete',
        description: `Verified ${result.verified} summaries, found ${result.errors} errors.`,
      });
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['emails', 'processing-status', user?.id] });
    },
    onError: (error) => {
      toast({
        title: 'Verification failed',
        description: error.message || 'Could not verify email summaries.',
        variant: 'destructive',
      });
    },
  });

  // Don't render if Gmail is not connected
  if (!isGmailConnected) {
    return null;
  }

  return (
    <div className="space-y-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => verifyMutation.mutate()}
        disabled={verifyMutation.isPending}
        className="w-full gap-2"
      >
        {verifyMutation.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : <Check className="h-4 w-4" />}
        {verifyMutation.isPending ? 'Verifying...' : 'Verify All Email Summaries'}
      </Button>

      {verifyMutation.data && (
        <div className="text-xs text-muted-foreground p-2 bg-secondary/30 rounded-md">
          <p>Verified {verifyMutation.data.verified} email summaries.</p>
          <p>Found {verifyMutation.data.errors} errors during verification.</p>
          {verifyMutation.data.errors > 0 && (
            <p className="text-destructive-foreground">
              Some emails may need attention.
            </p>
          )}
        </div>
      )}
    </div>
  );
}
