/// <reference types="node" />

/**
 * System Diagnostics Utility
 *
 * This module consolidates functionality from several diagnostic scripts:
 * - diagnostic.js
 * - server-debug.js
 * - server-startup-diagnostic.js
 *
 * It provides comprehensive system-level diagnostics for troubleshooting
 * server issues, database connectivity, and environment configuration.
 */

import { exec } from 'node:child_process';
import { createServer } from 'node:http';
import type { AddressInfo } from 'node:net';
import os from 'node:os';
import { promisify } from 'node:util';
import dotenv from 'dotenv';
import express from 'express';
// Corrected import path after moving other files
import { getDbConnection } from '../server/db';

// Load environment variables
dotenv.config();

const execAsync = promisify(exec);

// Color codes for console output
const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
};

// Logging functions
function log(message: string, color = COLORS.RESET) {
  // eslint-disable-next-line no-console
  console.log(color, message, COLORS.RESET);
}

function logSuccess(message: string) {
  log(`✓ ${message}`, COLORS.GREEN);
}

function logError(message: string) {
  log(`✗ ${message}`, COLORS.RED);
}

function logWarning(message: string) {
  log(`⚠ ${message}`, COLORS.YELLOW);
}

function logInfo(message: string) {
  log(`ℹ ${message}`, COLORS.BLUE);
}

function logHeader(message: string) {
  log(`\n=== ${message} ===`, COLORS.CYAN);
}

/**
 * Check system environment
 */
export function checkSystemEnvironment() {
  logHeader('System Environment');

  // Node.js information
  logInfo(`Node.js Version: ${process.version}`);
  logInfo(`Platform: ${process.platform}`);
  logInfo(`Architecture: ${process.arch}`);

  // Memory information
  const totalMemory = (os.totalmem() / 1024 / 1024 / 1024).toFixed(2);
  const freeMemory = (os.freemem() / 1024 / 1024 / 1024).toFixed(2);
  const usedMemory = (Number.parseFloat(totalMemory) - Number.parseFloat(freeMemory)).toFixed(2);

  logInfo(`Total Memory: ${totalMemory} GB`);
  logInfo(`Free Memory: ${freeMemory} GB`);
  logInfo(`Used Memory: ${usedMemory} GB`);

  // Local development environment
  logInfo(`PORT: ${process.env.PORT || '5000'}`);
  logInfo(`BASE_URL: ${process.env.BASE_URL || 'not set'}`);
  logInfo(`Database: ${process.env.DATABASE_URL ? 'configured' : 'not configured'}`);

  // Environment variables
  logHeader('Environment Variables');

  // Check important environment variables
  const envVars = [
    'NODE_ENV',
    'PORT',
    'DATABASE_URL',
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'SESSION_SECRET',
    'FIREBASE_SERVICE_ACCOUNT',
    'BASE_URL',
    'GEMINI_API_KEY',
    'OPENAI_API_KEY',
    'ENCRYPTION_KEY',
  ];

  for (const envVar of envVars) {
    if (process.env[envVar]) {
      // Mask sensitive values
      if (
        [
          'GOOGLE_CLIENT_SECRET',
          'SESSION_SECRET',
          'FIREBASE_SERVICE_ACCOUNT',
          'GEMINI_API_KEY',
          'OPENAI_API_KEY',
          'ENCRYPTION_KEY',
        ].includes(envVar)
      ) {
        logSuccess(`${envVar}: Set`);
      } else if (envVar === 'GOOGLE_CLIENT_ID') {
        const value = process.env[envVar]!;
        logSuccess(`${envVar}: ${value.substring(0, 10)}...${value.substring(value.length - 5)}`);
      } else if (envVar === 'DATABASE_URL') {
        const value = process.env[envVar]!;
        // Show only the protocol and hostname
        try {
          const url = new URL(value);
          logSuccess(`${envVar}: ${url.protocol}//${url.host}/...`);
        } catch (_e) {
          logWarning(`${envVar}: Set but not a valid URL`);
        }
      } else {
        logSuccess(`${envVar}: ${process.env[envVar]}`);
      }
    } else {
      if (['GOOGLE_CLIENT_ID', 'GOOGLE_CLIENT_SECRET', 'DATABASE_URL'].includes(envVar)) {
        logError(`${envVar}: Not set`);
      } else {
        logWarning(`${envVar}: Not set`);
      }
    }
  }
}

/**
 * Check for running processes on specific ports
 */
export async function checkPortAvailability(ports = [5000, 5001, 8080, 3000]) {
  logHeader('Port Availability');

  for (const port of ports) {
    try {
      // Create a temporary server to check if the port is available
      const tempApp = express();
      const server = createServer(tempApp);

      await new Promise<void>((resolve, reject) => {
        server.once('error', (err: NodeJS.ErrnoException) => {
          if (err.code === 'EADDRINUSE') {
            logError(`Port ${port} is in use`);
          } else {
            logError(`Error checking port ${port}: ${err.message}`);
          }
          reject(err);
        });

        server.once('listening', () => {
          const address = server.address() as AddressInfo;
          logSuccess(`Port ${address.port} is available`);
          server.close(() => resolve());
        });

        server.listen(port, '0.0.0.0');
      }).catch(() => {
        // Ignore errors; they're logged in the handlers
      });
    } catch (error) {
      logError(`Error checking port ${port}: ${error as string}`);
    }
  }

  // Also check for running processes that might be binding to these ports
  logInfo('\nChecking for processes using our target ports...');

  try {
    for (const port of ports) {
      try {
        // Use different commands based on OS
        let cmd = '';
        if (process.platform === 'win32') {
          cmd = `netstat -ano | findstr :${port}`;
        } else {
          cmd = `lsof -i :${port} || ss -tunlp | grep :${port} || netstat -tunlp 2>/dev/null | grep :${port}`;
        }

        const { stdout } = await execAsync(cmd);
        if (stdout.trim()) {
          logWarning(`Processes using port ${port}:`);
          log(stdout.trim());
        } else {
          logSuccess(`No processes found using port ${port}`);
        }
      } catch (_error) {
        // Ignore errors here, likely just means no processes found
        logSuccess(`No processes found using port ${port}`);
      }
    }
  } catch (error) {
    logError(`Error checking processes: ${error as string}`);
  }
}

/**
 * Test database connectivity
 */
export async function testDatabaseConnection() {
  logHeader('Database Connectivity');

  // Check if DATABASE_URL is set
  if (!process.env.DATABASE_URL) {
    logError('DATABASE_URL environment variable is not set');
    return false;
  }

  try {
    // Try a simple query
    logInfo('Testing database connection...');

    // Use the query client to run a simple test query
    const qc = await getDbConnection();
    const result = await qc`SELECT 1 as test`;

    if (result?.[0] && result[0].test === 1) {
      logSuccess('Database connection successful');

      // Try checking users table
      try {
        const usersResult = await qc`SELECT COUNT(*) as count FROM users`;
        const count = usersResult[0].count;
        logSuccess(`Users table exists with ${count} records`);
      } catch (_e) {
        logWarning('Users table does not exist or cannot be queried');
      }

      return true;
    }
    logError('Database connection failed - unexpected result');
    return false;
  } catch (error) {
    logError('Database connection failed', error as string);
    return false;
  }
}

/**
 * Test basic Express server startup
 */
export async function testExpressServer() {
  logHeader('Express Server Startup');

  try {
    const app = express();
    const server = app.listen(0); // Listen on a random free port
    const { port } = server.address() as AddressInfo;
    logSuccess(`Express server can start on a random port: ${port}`);
    await new Promise((resolve) => server.close(resolve));
    return true;
  } catch (error) {
    logError('Failed to start Express server', error as string);
    return false;
  }
}

/**
 * Main function to run all diagnostic checks
 */
export async function runFullDiagnostics() {
  logHeader('Starting Full System Diagnostics');

  checkSystemEnvironment();
  await checkPortAvailability();
  await testDatabaseConnection();
  await testExpressServer();

  logHeader('Diagnostics Complete');
}
