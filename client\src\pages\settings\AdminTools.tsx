import { CircuitBoard, Cpu, <PERSON><PERSON><PERSON>t, ShieldCheck } from 'lucide-react';
import { <PERSON> } from 'wouter';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { TabsContent } from '@/components/ui/tabs';

export function AdminToolsTab() {
  return (
    <TabsContent value="admin" className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShieldAlert className="h-5 w-5" />
            Admin Dashboard
          </CardTitle>
          <CardDescription>System administration tools and monitoring</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">System Monitoring</h3>
            <p className="text-sm text-muted-foreground">
              Monitor system health and performance metrics
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <Link href="/admin/memory-monitor">
                <Button variant="outline" className="w-full justify-start">
                  <Cpu className="mr-2 h-4 w-4" />
                  Memory Monitor
                </Button>
              </Link>
              <Link href="/admin/circuit-breakers">
                <Button variant="outline" className="w-full justify-start">
                  <CircuitBoard className="mr-2 h-4 w-4" />
                  Circuit Breakers
                </Button>
              </Link>
            </div>
          </div>
          <Separator />
          <div className="space-y-2">
            <h3 className="text-lg font-medium">System Maintenance</h3>
            <p className="text-sm text-muted-foreground">
              Perform system maintenance tasks and reset error states
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <Link href="/api/admin/reset-circuits" className="no-underline">
                <Button
                  variant="outline"
                  className="w-full justify-start bg-amber-100 dark:bg-amber-900/20 hover:bg-amber-200 dark:hover:bg-amber-800/30 border-amber-300 dark:border-amber-700"
                >
                  <CircuitBoard className="mr-2 h-4 w-4" />
                  Reset All Circuit Breakers
                </Button>
              </Link>
              <Link href="/api/admin/clear-error-cache" className="no-underline">
                <Button
                  variant="outline"
                  className="w-full justify-start bg-amber-100 dark:bg-amber-900/20 hover:bg-amber-200 dark:hover:bg-amber-800/30 border-amber-300 dark:border-amber-700"
                >
                  <ShieldCheck className="mr-2 h-4 w-4" />
                  Clear Error Caches
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  );
}

export default AdminToolsTab;
