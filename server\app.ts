/**
 * Express App Configuration for Testing
 *
 * This file exports a configured Express app without starting the server,
 * making it suitable for testing with supertest.
 */

import express from 'express';
import { emailDataService } from './services/emailDataService';
import { emailService } from './services/email-v2';

// Create Express app
const app = express();

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Trust proxy to get correct protocol (HTTP vs HTTPS) when behind a proxy
app.set('trust proxy', true);

// Mock authentication middleware for testing
app.use((req: any, res, next) => {
  req.user = { id: 1, email: '<EMAIL>' };
  next();
});

// Define the email routes directly for testing
app.get('/api/emails', async (req, res) => {
  try {
    const userId = req.user!.id;
    const {
      limit: limitStr = '50',
      offset: offsetStr = '0',
      view = 'list',
    } = req.query;
    const limit = Number.parseInt(limitStr as string, 10);
    const offset = Number.parseInt(offsetStr as string, 10);

    let emails;
    let estimatedDataReduction;

    // Use optimized service methods based on view type
    if (view === 'summary') {
      emails = await emailDataService.getEmailsForSummary(userId, { limit, offset });
      estimatedDataReduction = '70%';
    } else {
      // Default to list view
      emails = await emailDataService.getEmailsForList(userId, { limit, offset });
      estimatedDataReduction = '85%';
    }

    // Get total count from legacy service
    const { totalEmails } = await emailService.getEmails(userId, { limit: 1, page: 0 });

    res.json({
      emails,
      totalEmails,
      limit,
      hasMore: offset + limit < totalEmails,
      optimized: true,
      viewType: view === 'summary' ? 'summary' : 'list',
      estimatedDataReduction,
    });
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/emails/by-message-id/:messageId', async (req: any, res: any) => {
  try {
    const { messageId } = req.params;
    const userId = req.user!.id;
    const email = await emailDataService.getEmailForDetail(messageId, userId);

    if (!email) {
      return res.status(404).json({ error: 'Email not found' });
    }

    res.json(email);
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

export { app };
