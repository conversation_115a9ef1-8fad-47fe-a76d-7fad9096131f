import type { Settings } from '@shared/schema';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import apiClient from '@/lib/apiClient';

interface UseSettingsResult {
  settings: Settings | null | undefined;
  isLoading: boolean;
  error: Error | null;
  updateSettings: (newSettings: Partial<Settings>) => Promise<void>;
}

export function useSettings(): UseSettingsResult {
  const queryClient = useQueryClient();
  const [error, setError] = useState<Error | null>(null);

  // Fetch settings
  const { data: settings, isLoading } = useQuery<Settings | null, Error>({
    queryKey: ['/api/settings'],
    enabled:
      !window.location.pathname.includes('/login') && !window.location.pathname.includes('/signup'), // Prevent queries on auth pages
    refetchOnWindowFocus: false,
    retry: (failureCount, error) => {
      // Don't retry 401/403 errors (authentication issues)
      if (error && typeof error === 'object' && 'response' in error) {
        const status = (error as any).response?.status;
        if (status === 401 || status === 403) {
          return false;
        }
      }
      return failureCount < 1;
    },
  });

  // Create mutation for updating settings
  const mutation = useMutation({
    mutationFn: async (newSettings: Partial<Settings>) => {
      return apiClient.patch<Settings>('/api/settings', newSettings);
    },
    onSuccess: () => {
      // Invalidate the settings query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ['/api/settings'] });
    },
    onError: (error: unknown) => {
      setError(error as Error);
    },
  });

  // Update settings handler
  const updateSettings = async (newSettings: Partial<Settings>) => {
    try {
      await mutation.mutateAsync(newSettings);
    } catch (err) {
      console.error('Failed to update settings:', err);
    }
  };

  return {
    settings,
    isLoading,
    error,
    updateSettings,
  };
}
