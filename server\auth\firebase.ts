/**
 * Firebase Admin SDK Service
 *
 * This file initializes the Firebase Admin SDK for server-side
 * authentication and provides utility functions for token verification.
 */

import type { User } from '@shared/schema';
import { cert, type App as FirebaseApp, initializeApp } from 'firebase-admin/app';
import {
  type CreateRequest,
  type DecodedIdToken,
  getAuth,
  type UpdateRequest,
  type UserRecord,
} from 'firebase-admin/auth';
import { getEnvVar } from '../lib/environmentValidator';
import logger from '../lib/logger';
import { storage } from '../storage';

let isFirebaseInitialized = false;
let firebaseApp: FirebaseApp | null = null;

/**
 * Initializes the Firebase Admin SDK. This should be called once at application startup.
 * It uses the FIREBASE_SERVICE_ACCOUNT environment variable.
 */
export function initializeFirebase(): void {
  if (isFirebaseInitialized) {
    return;
  }

  const serviceAccountString = getEnvVar('FIREBASE_SERVICE_ACCOUNT');
  if (!serviceAccountString) {
    if (getEnvVar('NODE_ENV') === 'production') {
      logger.error(
        '[Firebase] FIREBASE_SERVICE_ACCOUNT is not set in a production environment. This is a fatal error.'
      );
      throw new Error('Firebase Admin SDK credentials are not configured.');
    }
    logger.warn(
      '[Firebase] FIREBASE_SERVICE_ACCOUNT is not set. Firebase features will be disabled in development.'
    );
    return;
  }

  try {
    // Handle both literal "\n" sequences and actual newlines by escaping them for JSON parsing
    const sanitizedString = serviceAccountString
      .replace(/\\n/g, '\\n') // keep already-escaped newlines intact
      .replace(/\n/g, '\\n'); // escape real newline characters

    let serviceAccount;
    if (sanitizedString.startsWith('"') && sanitizedString.endsWith('"')) {
      serviceAccount = JSON.parse(sanitizedString.slice(1, -1));
    } else {
      serviceAccount = JSON.parse(sanitizedString);
    }

    firebaseApp = initializeApp({
      credential: cert(serviceAccount as any),
    });

    isFirebaseInitialized = true;
    logger.info('[Firebase] Admin SDK initialized successfully.');
  } catch (error: any) {
    // Avoid logging the service account content to prevent secret leakage
    logger.error('[Firebase] Error initializing Admin SDK', {
      errorMessage: error.message,
      errorCode: error.code,
    });
    // In production, this should be a fatal error.
    if (getEnvVar('NODE_ENV') === 'production') {
      throw error;
    }
  }
}

function ensureInitialized(): void {
  if (!isFirebaseInitialized) {
    throw new Error(
      'Firebase Admin SDK has not been initialized. Call initializeFirebase() at startup.'
    );
  }
}

/**
 * Verifies a Firebase ID token.
 * Throws an error if the token is invalid, expired, or revoked.
 *
 * @param idToken - The Firebase ID token from the client.
 * @returns The decoded token payload.
 */
export async function verifyFirebaseToken(idToken: string): Promise<DecodedIdToken> {
  ensureInitialized();

  if (!idToken) {
    throw new Error('ID token must be a non-empty string.');
  }

  try {
    // checkRevoked = true is crucial for security to handle session revocation.
    const decodedToken = await getAuth(firebaseApp!).verifyIdToken(idToken, true);
    return decodedToken;
  } catch (error: any) {
    logger.warn('[Firebase] Token verification failed', {
      code: error.code,
      message: error.message,
    });
    // Re-throw a more generic error to avoid leaking implementation details.
    throw new Error('Invalid or expired authentication token.');
  }
}

/**
 * Verifies a Firebase ID token and returns the corresponding application user.
 * If the user does not exist in the local database, it creates a new one.
 *
 * @param token The Firebase ID token to verify.
 * @returns The application user.
 */
export async function verifyIdTokenAndGetUser(token: string): Promise<User> {
  ensureInitialized();
  const auth = getAuth(firebaseApp!);

  try {
    const decodedToken = await auth.verifyIdToken(token);
    const { uid, email, name, picture } = decodedToken;

    // Find user by Firebase UID first
    let user = await storage.getUserByFirebaseUid(uid);

    if (user) {
      logger.info(`Found existing user for Firebase UID: ${uid}`, { userId: user.id });

      // Update user's profile information to keep it fresh
      const updatedUser = await storage.updateUser(user.id, {
        name: name || user.name,
        picture: picture || user.picture,
      });

      return updatedUser || user;
    }

    // If not found by UID, try to find by email to link accounts
    if (email) {
      const usersByEmail = await storage.getUsersByEmail(email);
      if (usersByEmail.length > 0) {
        // For simplicity, link to the first user found with this email.
        // A more complex app might handle multiple accounts differently.
        user = usersByEmail[0];
        await storage.updateUser(user.id, {
          firebaseUid: uid,
          name: name || user.name,
          picture: picture || user.picture,
        });
        logger.info(`Linked Firebase UID ${uid} to existing user ${user.id} via email.`);
        return user;
      }
    }

    // If user doesn't exist by UID or email, create a new one
    logger.info(`Creating new user for Firebase UID: ${uid}`);
    const newUser = await storage.createUser({
      firebaseUid: uid,
      email: email || '', // email is optional in some Firebase configs
      name: name || null,
      picture: picture || null,
      provider: 'firebase',
    });

    logger.info(`Created new user ${newUser.id} for Firebase UID ${uid}.`);
    return newUser;
  } catch (error) {
    logger.error('Error verifying Firebase ID token:', {
      errorMessage: (error as Error).message,
      errorStack: (error as Error).stack,
    });
    throw new Error('Invalid Firebase token.');
  }
}

/**
 * Get user by Firebase UID
 */
export async function getUserByUid(uid: string): Promise<UserRecord> {
  try {
    ensureInitialized();
    const auth = getAuth(firebaseApp!);
    return await auth.getUser(uid);
  } catch (error) {
    logger.error(`Error getting user by UID ${uid}:`, error);
    throw error;
  }
}

/**
 * Create a new Firebase user
 */
export async function createUser(userData: CreateRequest): Promise<UserRecord> {
  try {
    ensureInitialized();
    const auth = getAuth(firebaseApp!);
    return await auth.createUser(userData);
  } catch (error) {
    logger.error('Error creating Firebase user:', error);
    throw error;
  }
}

/**
 * Update an existing Firebase user
 */
export async function updateUser(uid: string, userData: UpdateRequest): Promise<UserRecord> {
  try {
    ensureInitialized();
    const auth = getAuth(firebaseApp!);
    return await auth.updateUser(uid, userData);
  } catch (error) {
    logger.error(`Error updating Firebase user ${uid}:`, error);
    throw error;
  }
}

/**
 * Delete a Firebase user
 */
export async function deleteUser(uid: string): Promise<void> {
  try {
    ensureInitialized();
    const auth = getAuth(firebaseApp!);
    await auth.deleteUser(uid);
  } catch (error) {
    logger.error(`Error deleting Firebase user ${uid}:`, error);
    throw error;
  }
}

export default firebaseApp;
