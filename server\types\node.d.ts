// Extend the NodeJS namespace to include environment variables
declare namespace NodeJS {
  interface ProcessEnv {
    // Core Application Variables
    NODE_ENV: 'development' | 'production' | 'test';
    DATABASE_URL: string;

    // Google OAuth (Required for core functionality)
    GOOGLE_CLIENT_ID: string;
    GOOGLE_CLIENT_SECRET: string;

    // Firebase Service Account
    FIREBASE_SERVICE_ACCOUNT: string;

    // API Keys (Required for core functionality)
    OPENAI_API_KEY: string;

    // Optional OAuth Providers
    MICROSOFT_CLIENT_ID?: string;
    MICROSOFT_CLIENT_SECRET?: string;

    // Optional Server Configuration
    PORT?: string;
    REDIS_URL?: string;
    SESSION_SECRET?: string;
  }
}
