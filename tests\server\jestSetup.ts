/**
 * Global Jest setup for server-side test suites.
 *
 * 1. Ensures CommonJS versions of `express` and `express-session` expose a
 *    `default` export so that `import express from 'express'` works after
 *    ts-jest transpilation (which outputs CommonJS).
 * 2. Stubs the Winston-based logger used throughout the server so unit tests
 *    don't need the full transport stack and to avoid undefined log-level
 *    functions (`fatal`) in the validator.
 */

// -------- Patch express default export --------
const express = require('express');
if (!express.default) {
  // eslint-disable-next-line no-param-reassign
  express.default = express;
}

// -------- Patch express-session default export --------
const session = require('express-session');
if (!session.default) {
  // eslint-disable-next-line no-param-reassign
  session.default = session;
}

// -------- Patch supertest default export so `import request from 'supertest'` works --------
const supertest = require('supertest');
if (!supertest.default) {
  // eslint-disable-next-line no-param-reassign
  supertest.default = supertest;
}

// -------- Provide a lightweight mock for the server logger --------
jest.mock('@server/lib/logger', () => ({
  __esModule: true,
  default: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    fatal: jest.fn(),
  },
  updateLoggerContext: jest.fn(),
}));

// Attempt to patch the real logger instance (imported via relative path) so that any
// code that requires it directly still finds the expected methods.
try {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const realLogger = require('../../server/lib/logger');
  ['debug', 'info', 'warn', 'error', 'fatal'].forEach((level) => {
    if (typeof realLogger[level] !== 'function') {
      realLogger[level] = jest.fn();
    }
  });
} catch {
  // If the real logger cannot be resolved here (e.g., path differences in compiled TS), ignore.
}

// Provide a lightweight mock for the Redis service so tests that depend on it (e.g., gemini)
// don't fail when Redis hasn't been explicitly initialized. Individual test suites that need
// the real implementation can call `jest.dontMock('@server/services/redis')` before requiring.
jest.mock('@server/services/redis', () => {
  // Minimal in-memory stub matching the interface used by production code.
  const mockRedisClient = {
    hGetAll: jest.fn(),
    hIncrBy: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    incr: jest.fn(),
    del: jest.fn(),
    expire: jest.fn(),
    exists: jest.fn(),
    isReady: true,
  };

  return {
    __esModule: true,
    initializeRedis: jest.fn(),
    getRedisClient: jest.fn(() => mockRedisClient),
  };
});

// Provide a global mock for apiProtector to simplify tests and avoid circuit breaker complexity.
jest.mock('@server/utils/apiProtector', () => ({
  __esModule: true,
  protectApiCall: jest.fn(async (operation: any) => operation()),
  protectApiCallWithDetails: jest.fn(async (operation: any) => ({ result: await operation(), usedFallback: false, circuitOpen: false, durationMs: 1, retryCount: 0 })),
  resetCircuitForOperation: jest.fn(),
})); 