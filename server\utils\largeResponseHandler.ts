/// <reference types="node" />

/**
 * Large Response Handler
 *
 * Optimizes the handling of large API responses to reduce memory usage
 * by implementing streaming responses and pagination strategies.
 */

import { Transform, type TransformCallback } from 'node:stream';
import { pipeline } from 'node:stream/promises';
import type { Request, Response } from 'express';
import logger from '../lib/logger';
import { memoryManager } from './memoryManager';
import path from 'path';

/**
 * Options for streaming responses
 */
export interface StreamOptions {
  chunkSize?: number; // Size of each chunk in items
  transformFn?: (item: any) => any; // Optional transform function for each item
  totalCount?: number; // Total count if known
  contentType?: string; // Content type header
  memoryOptimized?: boolean; // Use lower memory in critical situations
}

/**
 * Process array data in chunks to avoid memory issues
 */
export class ChunkProcessor extends Transform {
  private buffer: any[] = [];
  private isFirstChunk = true;
  private isLastChunk = false;
  private processedCount = 0;

  constructor(
    private chunkSize: number,
    private transformFn?: (item: any) => any,
    private totalCount?: number
  ) {
    super({ objectMode: true });
  }

  _transform(chunk: any, _encoding: BufferEncoding, callback: TransformCallback): void {
    try {
      // Add to buffer
      this.buffer.push(chunk);
      this.processedCount++;

      // Check if this is the last item
      if (this.totalCount && this.processedCount >= this.totalCount) {
        this.isLastChunk = true;
      }

      // Process if buffer reached chunk size or this is the last item
      if (this.buffer.length >= this.chunkSize || this.isLastChunk) {
        this.processBuffer();
      }

      callback();
    } catch (error) {
      callback(error as Error);
    }
  }

  _flush(callback: TransformCallback): void {
    try {
      // Process any remaining items
      if (this.buffer.length > 0) {
        this.isLastChunk = true;
        this.processBuffer();
      } else if (this.isFirstChunk) {
        // If no items were processed at all, output an empty array
        this.push('[]');
      }

      callback();
    } catch (error) {
      callback(error as Error);
    }
  }

  private processBuffer(): void {
    // Apply transform function if provided
    const dataToSend = this.transformFn ? this.buffer.map(this.transformFn) : this.buffer;

    // Format as JSON with appropriate wrapping
    let output: string;

    if (this.isFirstChunk && this.isLastChunk) {
      // Single chunk, output complete array
      output = JSON.stringify(dataToSend);
    } else if (this.isFirstChunk) {
      // First chunk, start array
      output = `[${JSON.stringify(dataToSend).slice(1, -1)}`;
      this.isFirstChunk = false;
    } else if (this.isLastChunk) {
      // Last chunk, end array
      output = `,${JSON.stringify(dataToSend).slice(1)}`;
    } else {
      // Middle chunk, just the items with a comma prefix
      output = `,${JSON.stringify(dataToSend).slice(1, -1)}`;
    }

    // Send the chunk
    this.push(output);

    // Clear buffer to free memory
    this.buffer = [];
  }
}

/**
 * Stream an array as a JSON response to reduce memory usage
 *
 * @param res Express response object
 * @param dataSource Array or async generator of items to stream
 * @param options Streaming options
 */
export async function streamJsonResponse(
  res: Response,
  dataSource: any[] | AsyncIterable<any>,
  options: StreamOptions = {}
): Promise<void> {
  // Determine chunk size based on memory pressure
  const memoryStats = memoryManager.getMemoryStats();
  let chunkSize = options.chunkSize || 1000;

  // Adjust chunk size based on memory pressure
  if (options.memoryOptimized !== false) {
    if (memoryStats.pressureLevel === 'CRITICAL') {
      chunkSize = 50; // Tiny chunks when memory is critical
    } else if (memoryStats.pressureLevel === 'HIGH') {
      chunkSize = 200; // Small chunks when memory is high
    } else if (memoryStats.pressureLevel === 'MEDIUM') {
      chunkSize = 500; // Medium chunks when memory pressure is moderate
    }
  }

  // Set content type header
  res.setHeader('Content-Type', options.contentType || 'application/json');

  try {
    // Create processor for chunking the data
    const processor = new ChunkProcessor(chunkSize, options.transformFn, options.totalCount);

    // Set up pipeline
    if (Array.isArray(dataSource)) {
      // If data is an array, create a readable stream from it
      const { Readable } = await import('node:stream');
      const readableStream = Readable.from(dataSource);

      // Pipe through the processor to the response
      await pipeline(readableStream, processor, res);
    } else {
      // If data is an async iterable, create a readable stream from it
      const { Readable } = await import('node:stream');
      const readableStream = Readable.from(dataSource);

      // Pipe through the processor to the response
      await pipeline(readableStream, processor, res);
    }
  } catch (error) {
    logger.error('Error in streamJsonResponse:', error);

    // If headers haven't been sent, send an error response
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Error processing stream',
        message: (error as Error).message,
      });
    } else {
      // Otherwise just end the response
      res.end();
    }
  }
}

/**
 * Handle paginated results with memory optimization
 *
 * @param req Express request
 * @param res Express response
 * @param dataFetcher Function to fetch data for a given page/limit
 * @param options Options for pagination
 */
export async function handlePaginatedResponse(
  req: Request,
  res: Response,
  dataFetcher: (page: number, limit: number) => Promise<{ data: any[]; totalCount: number }>,
  options: {
    defaultLimit?: number;
    maxLimit?: number;
    wrap?: boolean;
  } = {}
): Promise<void> {
  try {
    // Get pagination parameters
    const page = Number.parseInt(req.query.page as string) || 1;
    let limit = Number.parseInt(req.query.limit as string) || options.defaultLimit || 20;
    const maxLimit = options.maxLimit || 100;

    // Adjust limit based on memory pressure
    const memoryStats = memoryManager.getMemoryStats();
    if (memoryStats.pressureLevel === 'CRITICAL') {
      // Force a small page size in critical memory conditions
      limit = Math.min(limit, 10);
    } else if (memoryStats.pressureLevel === 'HIGH') {
      // Reduce page size in high memory conditions
      limit = Math.min(limit, 20);
    } else if (memoryStats.pressureLevel === 'MEDIUM') {
      // Slightly reduce page size
      limit = Math.min(limit, maxLimit / 2);
    } else {
      // Normal conditions, just enforce max limit
      limit = Math.min(limit, maxLimit);
    }

    // Fetch data for this page
    const { data, totalCount } = await dataFetcher(page, limit);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Calculate memory usage for this request
    const responseSize = JSON.stringify(data).length;
    const responseSizeMB = responseSize / (1024 * 1024);

    logger.debug(
      `Pagination: page=${page}, limit=${limit}, items=${data.length}, ` +
        `totalPages=${totalPages}, responseSize=${responseSizeMB.toFixed(2)}MB`
    );

    // If response is large, use streaming
    if (responseSizeMB > 1 && data.length > 100) {
      logger.info(`Large paginated response (${responseSizeMB.toFixed(2)}MB), using streaming`);

      // If using wrapped response format
      if (options.wrap !== false) {
        // Send headers for pagination
        res.setHeader('X-Total-Count', totalCount.toString());
        res.setHeader('X-Total-Pages', totalPages.toString());
        res.setHeader('X-Current-Page', page.toString());
        res.setHeader('X-Has-Next-Page', hasNextPage.toString());
        res.setHeader('X-Has-Prev-Page', hasPrevPage.toString());

        // Start the response with the pagination envelope
        res.write(
          JSON.stringify({
            pagination: {
              page,
              limit,
              totalCount,
              totalPages,
              hasNextPage,
              hasPrevPage,
            },
            data: null, // Placeholder to be filled by stream
          }).replace('"data":null', '"data":')
        );

        // Stream just the array portion
        await streamJsonResponse(res, data, {
          memoryOptimized: true,
          contentType: 'application/json',
        });
      } else {
        // Simple array streaming
        await streamJsonResponse(res, data, {
          memoryOptimized: true,
          totalCount: data.length,
          contentType: 'application/json',
        });
      }
    } else {
      // Standard JSON response for smaller data
      if (options.wrap !== false) {
        res.json({
          pagination: {
            page,
            limit,
            totalCount,
            totalPages,
            hasNextPage,
            hasPrevPage,
          },
          data,
        });
      } else {
        res.json(data);
      }
    }
  } catch (error) {
    logger.error('Error in handlePaginatedResponse:', error);
    res.status(500).json({
      error: 'Error processing paginated response',
      message: (error as Error).message,
    });
  }
}

/**
 * Process a large array of items in batches to avoid memory issues
 *
 * @param items Array of items to process
 * @param processFn Function to process each batch
 * @param batchSize Size of each batch
 */
export async function processBatches<T, R>(
  items: T[],
  processFn: (batch: T[]) => Promise<R[]>,
  batchSize = 100
): Promise<R[]> {
  const results: R[] = [];
  const totalItems = items.length;

  // Adjust batch size based on memory pressure
  const memoryStats = memoryManager.getMemoryStats();
  if (memoryStats.pressureLevel === 'CRITICAL') {
    batchSize = 10; // Tiny batches when memory is critical
  } else if (memoryStats.pressureLevel === 'HIGH') {
    batchSize = 25; // Small batches when memory is high
  } else if (memoryStats.pressureLevel === 'MEDIUM') {
    batchSize = 50; // Medium batches when memory pressure is moderate
  }

  logger.debug(`Processing ${totalItems} items in batches of ${batchSize}`);

  for (let i = 0; i < totalItems; i += batchSize) {
    // Extract current batch
    const batch = items.slice(i, i + batchSize);

    // Process the batch
    const batchResults = await processFn(batch);

    // Add results to output
    results.push(...batchResults);

    // Log progress for large operations
    if (totalItems > 1000 && (i % 1000 === 0 || i + batchSize >= totalItems)) {
      const progress = Math.min(100, Math.round(((i + batch.length) / totalItems) * 100));
      logger.debug(`Batch processing ${progress}% complete (${i + batch.length}/${totalItems})`);
    }

    // Check memory pressure after each batch
    const currentMemoryStats = memoryManager.getMemoryStats();
    if (currentMemoryStats.pressureLevel === 'CRITICAL') {
      logger.warn('Critical memory pressure during batch processing, reducing batch size');
      batchSize = 10; // Reduce batch size for remaining batches
    }
  }

  return results;
}

export default {
  streamJsonResponse,
  handlePaginatedResponse,
  processBatches,
};
