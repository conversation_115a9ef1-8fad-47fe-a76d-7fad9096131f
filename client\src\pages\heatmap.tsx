import type React from 'react';
import { useEffect } from 'react';
import { useLocation } from 'wouter';
import EmailHeatmap from '@/components/email/EmailHeatmap';
import AppLayout from '@/components/layout/AppLayout';
import { useAuth } from '@/context/AuthContext';
import { Loader2 } from 'lucide-react';

const HeatmapPage: React.FC = () => {
  const { user, loading: isLoading } = useAuth();
  const [, setLocation] = useLocation();

  // Redirect to login if not authenticated - DEPRECATED
  // This logic should be handled by a <PrivateRoute> component in the router.
  // useEffect(() => {
  //   if (!isLoading && !user) {
  //     setLocation('/login');
  //   }
  // }, [user, isLoading, setLocation]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <p className="mt-3 text-muted-foreground">Loading your heatmap...</p>
        </div>
      </div>
    );
  }

  // This check is redundant if page is wrapped in <PrivateRoute>
  // if (!user) {
  //   return null; // Will redirect to login
  // }

  return (
    <AppLayout>
      <div className="p-4 md:p-6">
        <h1 className="text-2xl font-bold mb-4 text-foreground">Priority Heatmap</h1>
        <p className="text-muted-foreground mb-6">
          This visualization helps you identify which emails need your attention most urgently.
          Warmer colors (red, orange) indicate higher priority emails, while cooler colors
          (blue, green) represent lower priority ones.
        </p>
        <EmailHeatmap />
      </div>
    </AppLayout>
  );
};

export default HeatmapPage;
