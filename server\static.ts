import path from 'node:path';
import express, { type Express, type Request, type Response, type NextFunction } from 'express';
import logger from './lib/logger';

export function attachStaticHandlers(app: Express): void {
  const staticDir = path.resolve(process.cwd(), 'dist', 'client');
  app.use(express.static(staticDir, { maxAge: '1y', extensions: ['js', 'css'] }));

  // SPA fallback
  app.use((req: Request, res: Response, next: NextFunction) => {
    if (req.method !== 'GET' || req.path.startsWith('/api')) return next();
    res.sendFile(path.join(staticDir, 'index.html'));
  });

  logger.info('[STARTUP] Static assets served from ' + staticDir);
}
