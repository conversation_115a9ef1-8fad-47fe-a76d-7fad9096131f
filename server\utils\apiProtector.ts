/**
 * API Protection Utilities
 *
 * This module provides utility functions to protect API calls using circuit breakers
 * and other resilience patterns. It simplifies the use of circuit breakers and
 * makes it easy to protect any API call.
 */

import logger from '../lib/logger';
import { type CircuitBreakerOptions, circuitBreakerRegistry } from './circuitBreaker';

/**
 * Options for protecting an API call
 */
export interface ApiProtectionOptions<T> extends Partial<CircuitBreakerOptions> {
  /** Unique identifier for this protected operation */
  operationId: string;

  /** Number of retry attempts (default: 0) */
  retryCount?: number;

  /** Delay between retries in ms (default: 1000) */
  retryDelay?: number;

  /** Whether to use exponential backoff for retries (default: true) */
  useExponentialBackoff?: boolean;

  /** Value to return if operation fails and fallback is needed */
  fallbackValue?: T;

  /** Function to call to get a fallback value if operation fails */
  fallbackFn?: () => Promise<T>;

  /** Context information to include in logs */
  context?: Record<string, any>;

  /**
   * Overrides the `timeout` option in CircuitBreakerOptions.
   * @deprecated Use `timeout` from CircuitBreakerOptions directly.
   */
  timeoutMs?: number;
}

/**
 * Protected operation result with additional metadata
 */
export interface ProtectedResult<T> {
  /** The operation result */
  result: T;

  /** Whether the circuit is open */
  circuitOpen: boolean;

  /** Whether a fallback was used */
  usedFallback: boolean;

  /** Duration of the operation in ms */
  durationMs: number;

  /** Number of retries that were performed */
  retryCount: number;

  /** Any error that occurred */
  error?: Error;
}

/**
 * Protect an API call with a circuit breaker
 *
 * @param operation The API operation to protect
 * @param options Protection options
 * @returns The result of the operation
 *
 * @example
 * // Simple usage
 * const result = await protectApiCall(
 *   () => gmailApi.getMessages(userId),
 *   { operationId: 'gmail-get-messages' }
 * );
 *
 * @example
 * // Advanced usage with retries and fallback
 * const result = await protectApiCall(
 *   () => gmailApi.getMessages(userId),
 *   {
 *     operationId: 'gmail-get-messages',
 *     retryCount: 3,
 *     retryDelay: 1000,
 *     useExponentialBackoff: true,
 *     fallbackValue: [],
 *     context: { userId }
 *   }
 * );
 */
export async function protectApiCall<T>(
  operation: () => Promise<T>,
  options: ApiProtectionOptions<T>
): Promise<T> {
  const {
    operationId,
    retryCount = 0,
    retryDelay = 1000,
    useExponentialBackoff = true,
    fallbackValue,
    fallbackFn,
    context = {},
    timeoutMs, // Deprecated, but handled for backward compatibility
    ...circuitBreakerOptions
  } = options;
  // Get or create circuit breaker for this operation
  const circuitBreaker = circuitBreakerRegistry.getOrCreate({
    name: operationId,
    ...circuitBreakerOptions,
  });

  const operationWithRetries = async () => {
    let lastError: Error | null = null;
    for (let attempt = 0; attempt <= retryCount; attempt++) {
      try {
        if (attempt > 0) {
          const delay = useExponentialBackoff ? retryDelay * 2 ** (attempt - 1) : retryDelay;
          logger.debug(
            `Retrying operation ${operationId} (attempt ${attempt}/${retryCount}) after ${delay}ms`,
            { operationId, attempt, delay, context }
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
        const result = await operation();
        if (attempt > 0) {
          logger.info(`Operation ${operationId} succeeded after ${attempt} retries.`, {
            operationId,
            attempt,
            context,
          });
        }
        return result;
      } catch (error) {
        lastError = error as Error;
      }
    }
    throw lastError ?? new Error(`Operation ${operationId} failed after ${retryCount} retries.`);
  };

  try {
    const fallback =
      fallbackFn ??
      (fallbackValue !== undefined ? () => Promise.resolve(fallbackValue) : undefined);
    return await circuitBreaker.execute(operationWithRetries, fallback);
  } catch (error) {
    logger.error(`Operation ${operationId} failed definitively.`, {
      operationId,
      circuitState: (await circuitBreaker.getStateDetails()).state,
      error: (error as Error).message,
      context,
      ...circuitBreakerOptions,
    });
    throw error;
  }
}

/**
 * Protect an API call with a circuit breaker and return detailed result
 *
 * Use this when you need to know more about what happened during the operation
 * such as whether a fallback was used
 *
 * @param operation The API operation to protect
 * @param options Protection options
 * @returns Detailed result object including success status and metrics
 *
 * @example
 * const { result, circuitOpen, usedFallback } = await protectApiCallWithDetails(
 *   () => gmailApi.getMessages(userId),
 *   { operationId: 'gmail-get-messages', fallbackValue: [] }
 * );
 *
 * if (usedFallback) {
 *   logger.warn('Used fallback data for messages');
 * }
 */
export async function protectApiCallWithDetails<T>(
  operation: () => Promise<T>,
  options: ApiProtectionOptions<T>
): Promise<ProtectedResult<T>> {
  const startTime = Date.now();
  const result: ProtectedResult<T> = {
    result: null as unknown as T,
    circuitOpen: false,
    usedFallback: false,
    durationMs: 0,
    retryCount: 0,
  };

  const {
    operationId,
    retryCount = 0,
    retryDelay = 1000,
    useExponentialBackoff = true,
    fallbackValue,
    fallbackFn,
    context = {},
    timeoutMs, // Deprecated, but handled for backward compatibility
    ...circuitBreakerOptions
  } = options;

  // Get or create circuit breaker for this operation
  const circuitBreaker = circuitBreakerRegistry.getOrCreate({
    name: operationId,
    timeout: timeoutMs, // Pass timeout directly to circuit breaker
    resetTimeout: 60000,
    failureThreshold: 5,
    successThreshold: 2,
    ...circuitBreakerOptions,
  });

  // Flag to track if the circuit was opened before our operation
  const stateDetails = await circuitBreaker.getStateDetails();
  result.circuitOpen = stateDetails.state === 'OPEN';

  try {
    const fallback =
      fallbackFn ??
      (fallbackValue !== undefined ? () => Promise.resolve(fallbackValue) : undefined);
    result.result = await circuitBreaker.execute(async () => {
      let attempt = 0;
      let lastError: Error | null = null;

      while (attempt <= retryCount) {
        try {
          if (attempt > 0) {
            const delay = useExponentialBackoff ? retryDelay * 2 ** (attempt - 1) : retryDelay;
            logger.debug(
              `Retrying operation ${operationId} (attempt ${attempt}/${retryCount}) after ${delay}ms`,
              { operationId, attempt, delay, context }
            );
            await new Promise((resolve) => setTimeout(resolve, delay));
          }
          const result = await operation();
          if (attempt > 0) {
            logger.info(`Operation ${operationId} succeeded after ${attempt} retries.`, {
              operationId,
              attempt,
              context,
            });
          }
          return result;
        } catch (error) {
          lastError = error as Error;

          // Check if we should retry
          if (attempt < retryCount) {
            attempt++;
          } else {
            // No more retries
            throw (
              lastError || new Error(`Operation ${operationId} failed after ${retryCount} retries.`)
            );
          }
        }
      }

      // If we get here, all retries failed
      throw lastError || new Error(`Operation ${operationId} failed after ${retryCount} retries.`);
    }, fallback);
  } catch (error) {
    // Circuit is open or operation failed even with retries
    result.error = error as Error;

    // Handle fallbacks
    try {
      if (fallbackFn) {
        // Use the fallback function
        result.result = await fallbackFn();
        result.usedFallback = true;
      } else if (fallbackValue !== undefined) {
        // Use the fallback value
        result.result = fallbackValue;
        result.usedFallback = true;
      } else {
        // No fallback provided
        throw error;
      }
    } catch (fallbackError) {
      // Even the fallback failed
      logger.error(`Fallback for ${operationId} also failed: ${(fallbackError as Error).message}`, {
        operationId,
        error: fallbackError,
        context,
      });
      throw error; // Throw the original error
    }
  } finally {
    // Calculate duration
    result.durationMs = Date.now() - startTime;
    result.circuitOpen = (await circuitBreaker.getStateDetails()).state === 'OPEN';
  }

  return result;
}

/**
 * Reset a circuit breaker for a given operation
 * @param operationId The ID of the operation whose circuit should be reset
 */
export async function resetCircuitForOperation(operationId: string): Promise<void> {
  const breaker = circuitBreakerRegistry.get(operationId);
  if (breaker) {
    await breaker.forceReset();
  }
}

/**
 * Get the current state of a circuit breaker
 * @param operationId The operation ID
 * @returns The state of the circuit breaker
 */
export async function getCircuitState(operationId: string): Promise<any> {
  const circuit = circuitBreakerRegistry.get(operationId);
  if (circuit) {
    return await circuit.getStateDetails();
  }
  return null;
}

/**
 * Get the states of all registered circuit breakers
 * @returns A record of all circuit breaker states
 */
export async function getAllCircuitStates(): Promise<Record<string, any>> {
  return await circuitBreakerRegistry.getStates();
}
