import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON>, CircleCheck, Clock } from 'lucide-react';
import type React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

type ServiceStatus = 'operational' | 'degraded' | 'outage' | 'maintenance' | 'unknown';

interface Service {
  id: string;
  name: string;
  status: ServiceStatus;
  lastChecked?: Date;
  description?: string;
}

interface ServiceStatusIndicatorProps {
  services: Service[];
  onCheckStatus?: () => void;
  lastUpdated?: Date;
  showStatusBanner?: boolean;
  className?: string;
}

/**
 * Returns appropriate color and icon for a service status
 */
function getStatusProperties(status: ServiceStatus) {
  switch (status) {
    case 'operational':
      return {
        color: 'text-green-500',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        icon: CircleCheck,
        label: 'Operational',
      };
    case 'degraded':
      return {
        color: 'text-amber-500',
        bgColor: 'bg-amber-50',
        borderColor: 'border-amber-200',
        icon: AlertCircle,
        label: 'Degraded',
      };
    case 'outage':
      return {
        color: 'text-red-500',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        icon: CircleAlert,
        label: 'Outage',
      };
    case 'maintenance':
      return {
        color: 'text-blue-500',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
        icon: Clock,
        label: 'Maintenance',
      };
    default:
      return {
        color: 'text-gray-500',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
        icon: AlertCircle,
        label: 'Unknown',
      };
  }
}

/**
 * Status badge component for individual services
 */
const ServiceStatusBadge: React.FC<{ service: Service }> = ({ service }) => {
  const { color, icon: Icon, label } = getStatusProperties(service.status);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant="outline"
            className={`flex items-center space-x-1 ${color} hover:${color}`}
          >
            <Icon className="h-3 w-3" />
            <span>{service.name}</span>
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-xs">
            <p className="font-medium">
              {service.name}: {label}
            </p>
            {service.description && <p>{service.description}</p>}
            {service.lastChecked && (
              <p className="mt-1 text-gray-500">
                Last checked: {service.lastChecked.toLocaleTimeString()}
              </p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

/**
 * ServiceStatusIndicator component
 * Shows the operational status of various services the app depends on
 */
export const ServiceStatusIndicator: React.FC<ServiceStatusIndicatorProps> = ({
  services,
  onCheckStatus,
  lastUpdated,
  showStatusBanner = true,
  className = '',
}) => {
  // Get overall system status (worst status among all services)
  const getOverallStatus = (): ServiceStatus => {
    if (services.length === 0) return 'unknown';
    if (services.some((s) => s.status === 'outage')) return 'outage';
    if (services.some((s) => s.status === 'degraded')) return 'degraded';
    if (services.some((s) => s.status === 'maintenance')) return 'maintenance';
    if (services.every((s) => s.status === 'operational')) return 'operational';
    return 'unknown';
  };

  const overallStatus = getOverallStatus();
  const {
    bgColor,
    borderColor,
    color,
    icon: StatusIcon,
    label,
  } = getStatusProperties(overallStatus);

  // Only show status banner if there's an issue or explicitly requested
  if (overallStatus === 'operational' && !showStatusBanner) {
    return (
      <div className={`flex flex-wrap gap-2 ${className}`}>
        {services.map((service) => (
          <ServiceStatusBadge key={service.id} service={service} />
        ))}
      </div>
    );
  }

  return (
    <div className={className}>
      <Alert variant="default" className={`${bgColor} border ${borderColor} mb-4`}>
        <StatusIcon className={`h-4 w-4 ${color}`} />
        <AlertTitle className={color}>System Status: {label}</AlertTitle>
        <AlertDescription>
          <div className="mt-2 text-sm">
            {overallStatus !== 'operational' && (
              <p className="mb-2">
                {overallStatus === 'outage'
                  ? "We're experiencing issues with some services. Our team is working on a fix."
                  : overallStatus === 'degraded'
                    ? 'Some services are experiencing slowdowns or intermittent issues.'
                    : 'Scheduled maintenance is in progress. Some features may be unavailable.'}
              </p>
            )}

            <div className="flex flex-wrap gap-2 mt-3">
              {services.map((service) => (
                <ServiceStatusBadge key={service.id} service={service} />
              ))}
            </div>

            <div className="flex justify-between items-center mt-3 pt-2 border-t border-gray-200 text-xs text-gray-500">
              <div>
                {lastUpdated && <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>}
              </div>

              {onCheckStatus && (
                <Button variant="ghost" size="sm" className="h-7 text-xs" onClick={onCheckStatus}>
                  <BarChart2 className="h-3 w-3 mr-1" />
                  Refresh Status
                </Button>
              )}
            </div>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
};

/**
 * Example usage component with a minimal status indicator for the header/footer
 */
export const CompactServiceStatus: React.FC<{
  services: Service[];
  onClick?: () => void;
}> = ({ services, onClick }) => {
  const overallStatus = (): ServiceStatus => {
    if (services.length === 0) return 'unknown';
    if (services.some((s) => s.status === 'outage')) return 'outage';
    if (services.some((s) => s.status === 'degraded')) return 'degraded';
    if (services.some((s) => s.status === 'maintenance')) return 'maintenance';
    if (services.every((s) => s.status === 'operational')) return 'operational';
    return 'unknown';
  };

  const status = overallStatus();
  const { color, icon: StatusIcon, label } = getStatusProperties(status);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={`h-6 px-2 ${status !== 'operational' ? 'animate-pulse' : ''}`}
            onClick={onClick}
          >
            <StatusIcon className={`h-3 w-3 mr-1 ${color}`} />
            <span className={`text-xs ${color}`}>
              {status === 'operational' ? 'All Systems Normal' : 'System Issues'}
            </span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-xs p-1">
            <p className="font-medium">System Status: {label}</p>
            <div className="mt-1 space-y-1">
              {services.map((service) => (
                <div key={service.id} className="flex items-center">
                  <StatusIcon
                    className={`h-2 w-2 mr-1 ${getStatusProperties(service.status).color}`}
                  />
                  <span>
                    {service.name}: {getStatusProperties(service.status).label}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
