import path from 'node:path';
import { fileURLToPath } from 'node:url';
import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';
import mkcert from 'vite-plugin-mkcert';
import tsconfigPaths from 'vite-tsconfig-paths';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default defineConfig(({ mode }) => {
  const _env = loadEnv(mode, process.cwd(), '');
  const clientRoot = path.resolve(__dirname, 'client');

  return {
    plugins: [react(), tsconfigPaths(), mkcert()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'client/src'),
        '@lib': path.resolve(__dirname, 'client/src/lib'),
        '@hooks': path.resolve(__dirname, 'client/src/hooks'),
        '@components': path.resolve(__dirname, 'client/src/components'),
        '@context': path.resolve(__dirname, 'client/src/context'),
        '@shared': path.resolve(__dirname, 'shared'),
        '@assets': path.resolve(__dirname, 'attached_assets'),
      },
    },
    root: clientRoot,
    build: {
      outDir: path.resolve(__dirname, 'dist/public'),
      emptyOutDir: true,
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]',
          manualChunks: {
            vendor: ['react', 'react-dom'],
            ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-tabs'],
            query: ['@tanstack/react-query'],
            icons: ['lucide-react'],
          },
        },
      },
      chunkSizeWarningLimit: 1000,
      sourcemap: true,
    },
    server: {
      fs: {
        allow: ['..'],
      },
      proxy: {
        '/api': {
          target: 'https://localhost:5000',
          changeOrigin: true,
          secure: false,
        },
      },
      hmr: {
        port: 5173,
      },
    },
    envPrefix: 'VITE_',
    envDir: __dirname,
  };
});
