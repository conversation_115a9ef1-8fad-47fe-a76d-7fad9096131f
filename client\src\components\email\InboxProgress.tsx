import type React from 'react';
import { Progress } from '@/components/ui/progress';
import { useUserSettings } from '@/context/UserSettingsContext';

const InboxProgress: React.FC = () => {
  const { inboxProgress } = useUserSettings();

  return (
    <div className="px-3 py-2 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 flex flex-col">
      <div className="w-full">
        <div className="flex justify-between items-center mb-1">
          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
            Inbox Progress
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {inboxProgress.cleared} of {inboxProgress.total} cleared
          </span>
        </div>
        <Progress value={inboxProgress.percentage} className="h-2" />
      </div>
    </div>
  );
};

export default InboxProgress;
