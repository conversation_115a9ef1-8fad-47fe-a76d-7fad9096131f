import { getEnvVar, initializeEnvironmentValidator } from '@server/lib/environmentValidator';

// Mock the logger to prevent console noise during tests
jest.mock('@server/lib/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
}));

// Ensure the 'pg' module is mocked to avoid requiring native bindings in tests
jest.mock('pg', () => {
  const { EventEmitter } = require('events');
  class MockPool extends EventEmitter {
    connect() {
      return Promise.resolve({ release: jest.fn() });
    }
    query() {
      return Promise.resolve({ rows: [] });
    }
    end() {
      return Promise.resolve();
    }
  }
  return { __esModule: true, Pool: MockPool, default: { Pool: MockPool } };
});

describe('Environment Validator', () => {
  // Store original environment
  const originalEnv = process.env;

  beforeEach(() => {
    // Clear module cache to reset validation state
    jest.resetModules();

    // Purge existing environment variables in-place so the same object reference is preserved
    Object.keys(process.env).forEach((key) => delete process.env[key]);

    // Provide baseline values that are required for most tests and avoid process.exit in validator
    process.env.NODE_ENV = 'development';
    process.env.BYPASS_AUTHENTICATION_FOR_STATS = 'false';
    process.env.GEMINI_API_KEY = 'AI_dummy_key_that_is_long_enough_12345';
    process.env.JEST_WORKER_ID = '1';

    // Reset validator singleton state to ensure each test starts fresh
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { environmentValidator } = require('@server/lib/environmentValidator');
    if (environmentValidator?.reset) {
      environmentValidator.reset();
    }
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  describe('Environment Variable Validation', () => {
    it('should successfully validate when all required variables are present', () => {
      // Set up minimal required environment variables
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/test';
      process.env.GOOGLE_CLIENT_ID = 'test_client_id.apps.googleusercontent.com';
      process.env.GOOGLE_CLIENT_SECRET = 'test_client_secret';
      process.env.GOOGLE_REDIRECT_URI = 'http://localhost:5000/api/auth/google/callback';
      process.env.FIREBASE_SERVICE_ACCOUNT = JSON.stringify({
        type: 'service_account',
        project_id: 'test-project',
        private_key_id: 'key-id',
        private_key:
          '-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC\\n-----END PRIVATE KEY-----\\n',
        client_email: '<EMAIL>',
        client_id: '*********',
        auth_uri: 'https://accounts.google.com/o/oauth2/auth',
        token_uri: 'https://oauth2.googleapis.com/token',
      });
      process.env.SESSION_SECRET = 'test_session_secret_that_is_long_enough';
      process.env.ENCRYPTION_KEY = 'a'.repeat(64); // 64-char hex string
      process.env.GEMINI_API_KEY = 'AI_test_gemini_key_*********0';

      const result = initializeEnvironmentValidator();
      expect(result.isValid).toBe(true);
      // Should still produce warnings (e.g., missing Outlook OAuth)
      expect(Array.isArray(result.warnings)).toBe(true);
    });

    it('should throw error when required DATABASE_URL is missing', () => {
      // Set up environment without DATABASE_URL
      process.env.NODE_ENV = 'development';
      process.env.GOOGLE_CLIENT_ID = 'test_client_id.apps.googleusercontent.com';
      process.env.GOOGLE_CLIENT_SECRET = 'test_client_secret';
      process.env.FIREBASE_SERVICE_ACCOUNT = '{}';
      process.env.SESSION_SECRET = 'test_secret';
      process.env.ENCRYPTION_KEY = 'a'.repeat(64);

      expect(() => initializeEnvironmentValidator()).toThrow();
    });

    it('should throw error when FIREBASE_SERVICE_ACCOUNT has invalid JSON', () => {
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/test';
      process.env.GOOGLE_CLIENT_ID = 'test_client_id.apps.googleusercontent.com';
      process.env.GOOGLE_CLIENT_SECRET = 'test_client_secret';
      process.env.FIREBASE_SERVICE_ACCOUNT = 'invalid json{';
      process.env.SESSION_SECRET = 'test_secret';
      process.env.ENCRYPTION_KEY = 'a'.repeat(64);

      expect(() => initializeEnvironmentValidator()).toThrow();
    });

    it('should handle FIREBASE_SERVICE_ACCOUNT with newlines correctly', () => {
      // Set up environment with Firebase service account that has actual newlines (common issue)
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/test';
      process.env.GOOGLE_CLIENT_ID = 'test_client_id.apps.googleusercontent.com';
      process.env.GOOGLE_CLIENT_SECRET = 'test_client_secret';
      process.env.FIREBASE_SERVICE_ACCOUNT = JSON.stringify({
        type: 'service_account',
        project_id: 'test-project',
        private_key_id: 'key-id',
        private_key:
          '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC\n-----END PRIVATE KEY-----\n',
        client_email: '<EMAIL>',
        client_id: '*********',
      }).replace(/\\n/g, '\n'); // Convert to actual newlines like dotenv might do
      process.env.SESSION_SECRET = 'test_secret';
      process.env.ENCRYPTION_KEY = 'a'.repeat(64);
      process.env.GEMINI_API_KEY = 'AI_prod_gemini_key_0987654321';

      // Should handle the newlines gracefully through sophisticated JSON parsing
      const result = initializeEnvironmentValidator();
      expect(result.isValid).toBe(true);
      // Should still produce warnings (e.g., missing Outlook OAuth)
      expect(Array.isArray(result.warnings)).toBe(true);
    });

    it('should throw error when ENCRYPTION_KEY is too short', () => {
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/test';
      process.env.GOOGLE_CLIENT_ID = 'test_client_id.apps.googleusercontent.com';
      process.env.GOOGLE_CLIENT_SECRET = 'test_client_secret';
      process.env.FIREBASE_SERVICE_ACCOUNT = '{}';
      process.env.SESSION_SECRET = 'test_secret';
      process.env.ENCRYPTION_KEY = 'short'; // Too short

      expect(() => initializeEnvironmentValidator()).toThrow();
    });
  });

  describe('getEnvVar Function', () => {
    it('should return environment variable value when it exists', () => {
      // Set up minimal environment for validation to pass
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/test';
      process.env.GOOGLE_CLIENT_ID = 'test_client_id.apps.googleusercontent.com';
      process.env.GOOGLE_CLIENT_SECRET = 'test_client_secret';
      process.env.FIREBASE_SERVICE_ACCOUNT = '{}';
      process.env.SESSION_SECRET = 'test_secret';
      process.env.ENCRYPTION_KEY = 'a'.repeat(64);
      process.env.TEST_VAR = 'test_value';

      initializeEnvironmentValidator();

      // Use process.env directly for non-EnvironmentConfig variables
      expect(process.env.TEST_VAR).toBe('test_value');
      expect(getEnvVar('NODE_ENV')).toBe('development');
    });

    it('should throw error when accessing undefined required variable', () => {
      // Set up minimal environment
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/test';
      process.env.GOOGLE_CLIENT_ID = 'test_client_id.apps.googleusercontent.com';
      process.env.GOOGLE_CLIENT_SECRET = 'test_client_secret';
      process.env.FIREBASE_SERVICE_ACCOUNT = '{}';
      process.env.SESSION_SECRET = 'test_secret';
      process.env.ENCRYPTION_KEY = 'a'.repeat(64);

      initializeEnvironmentValidator();

      // Test with a valid environment variable key that doesn't exist
      // Remove the nonexistent variable to ensure it throws
      delete process.env.GEMINI_API_KEY;
      expect(() => getEnvVar('GEMINI_API_KEY')).toThrow();
    });

    it('should return default value for optional variables', () => {
      // Set up minimal environment
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/test';
      process.env.GOOGLE_CLIENT_ID = 'test_client_id.apps.googleusercontent.com';
      process.env.GOOGLE_CLIENT_SECRET = 'test_client_secret';
      process.env.FIREBASE_SERVICE_ACCOUNT = '{}';
      process.env.SESSION_SECRET = 'test_secret';
      process.env.ENCRYPTION_KEY = 'a'.repeat(64);

      initializeEnvironmentValidator();

      // PORT has a default value of "5000"
      expect(getEnvVar('PORT')).toBe(5000);
    });
  });

  describe('Environment-specific behavior', () => {
    it('should handle development environment requirements', () => {
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/test';
      process.env.GOOGLE_CLIENT_ID = 'test_client_id.apps.googleusercontent.com';
      process.env.GOOGLE_CLIENT_SECRET = 'test_client_secret';
      process.env.FIREBASE_SERVICE_ACCOUNT = '{}';
      process.env.SESSION_SECRET = 'test_secret';
      process.env.ENCRYPTION_KEY = 'a'.repeat(64);

      expect(() => initializeEnvironmentValidator()).not.toThrow();
    });

    it('should handle production environment requirements', () => {
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/prod';
      process.env.GOOGLE_CLIENT_ID = 'prod_client_id.apps.googleusercontent.com';
      process.env.GOOGLE_CLIENT_SECRET = 'prod_client_secret';
      process.env.FIREBASE_SERVICE_ACCOUNT = JSON.stringify({
        type: 'service_account',
        project_id: 'prod-project',
        private_key: '-----BEGIN PRIVATE KEY-----\\nkey\\n-----END PRIVATE KEY-----\\n',
        client_email: '<EMAIL>',
      });
      process.env.SESSION_SECRET = 'x'.repeat(32);
      process.env.ENCRYPTION_KEY = 'b'.repeat(64);
      process.env.GEMINI_API_KEY = 'AI_prod_gemini_key_0987654321';

      expect(() => initializeEnvironmentValidator()).not.toThrow();
    });
  });

  describe('AI Configuration Validation', () => {
    it.skip('should warn when no AI API keys are configured', () => {
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/test';
      process.env.GOOGLE_CLIENT_ID = 'test_client_id.apps.googleusercontent.com';
      process.env.GOOGLE_CLIENT_SECRET = 'test_client_secret';
      process.env.FIREBASE_SERVICE_ACCOUNT = '{}';
      process.env.SESSION_SECRET = 'x'.repeat(32);
      process.env.ENCRYPTION_KEY = 'a'.repeat(64);
      // Deliberately not setting GEMINI_API_KEY or OPENAI_API_KEY

      expect(() => initializeEnvironmentValidator()).not.toThrow();
    });

    it('should pass when at least one AI API key is configured', () => {
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/test';
      process.env.GOOGLE_CLIENT_ID = 'test_client_id.apps.googleusercontent.com';
      process.env.GOOGLE_CLIENT_SECRET = 'test_client_secret';
      process.env.FIREBASE_SERVICE_ACCOUNT = '{}';
      process.env.SESSION_SECRET = 'x'.repeat(32);
      process.env.ENCRYPTION_KEY = 'a'.repeat(64);
      process.env.GEMINI_API_KEY = 'AI_test_gemini_key_*********0';

      expect(() => initializeEnvironmentValidator()).not.toThrow();
    });
  });
});
