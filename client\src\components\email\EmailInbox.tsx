import { Loader2 } from 'lucide-react';
import type React from 'react';
import { lazy, Suspense, useEffect, useState } from 'react';
import { useEmailDetail } from '@/context/EmailDetailContext';
import { useIsMobile } from '@/hooks/use-mobile';

// Lazy load components for progressive loading
const EmailList = lazy(() => import('./EmailList'));
const EmailDetail = lazy(() => import('./EmailDetail'));
const MobileEmailDetail = lazy(() => import('./MobileEmailDetail'));

// Custom loading component for each section
const ListSkeleton = () => (
  <div className="w-full md:w-1/2 lg:w-2/5 border-r border-border bg-card flex flex-col h-full p-2 space-y-4">
    <div className="flex justify-between items-center">
      <div className="h-6 w-24 bg-muted rounded animate-pulse" />
      <div className="h-6 w-16 bg-muted rounded animate-pulse" />
    </div>
    {Array.from({ length: 8 }, (_, i) => `skeleton-${i}`).map((key) => (
      <div key={key} className="p-2 border border-border rounded-md">
        <div className="flex justify-between items-center">
          <div className="h-5 w-1/3 bg-muted rounded animate-pulse" />
          <div className="h-4 w-16 bg-muted rounded animate-pulse" />
        </div>
        <div className="h-4 w-2/3 bg-muted rounded mt-2 animate-pulse" />
        <div className="h-3 w-1/2 bg-muted rounded mt-2 animate-pulse" />
      </div>
    ))}
  </div>
);

const DetailSkeleton = () => (
  <div className="hidden md:flex md:w-1/2 lg:w-3/5 flex-col bg-card p-4 space-y-6">
    <div className="h-8 w-3/4 bg-muted rounded animate-pulse" />
    <div className="flex items-center space-x-3">
      <div className="h-10 w-10 rounded-full bg-muted animate-pulse" />
      <div>
        <div className="h-5 w-32 bg-muted rounded animate-pulse" />
        <div className="h-4 w-24 bg-muted rounded mt-2 animate-pulse" />
      </div>
    </div>
    <div className="space-y-3">
      <div className="h-4 w-full bg-muted rounded animate-pulse" />
      <div className="h-4 w-full bg-muted rounded animate-pulse" />
      <div className="h-4 w-3/4 bg-muted rounded animate-pulse" />
    </div>
  </div>
);

const EmailInbox: React.FC = () => {
  const { selectedEmail, selectEmail } = useEmailDetail();
  const isMobile = useIsMobile();
  const [_isLoaded, setIsLoaded] = useState(false);

  // Simulate progressive loading
  useEffect(() => {
    // Mark interface as loaded after a short delay
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleCloseModal = () => {
    selectEmail(null);
  };

  return (
    <div className="flex-1 flex overflow-hidden h-full w-full max-w-screen min-h-0">
      {/* Progressive loading with skeleton screens */}
      <Suspense fallback={<ListSkeleton />}>
        <EmailList className="basis-full md:basis-1/2 lg:basis-2/5 flex-shrink-0" />
      </Suspense>

      <Suspense fallback={<DetailSkeleton />}>
        <EmailDetail />
      </Suspense>

      {/* Mobile Email Detail Modal - Only render when needed */}
      {selectedEmail && isMobile && (
        <div className="md:hidden fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex email-modal-container">
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-0 sm:items-center">
              <div className="relative transform overflow-hidden rounded-t-lg sm:rounded-lg bg-card w-full sm:max-w-lg transition-all max-h-[90vh] flex flex-col">
                <div className="sticky top-0 bg-card border-b border-border flex items-center justify-between p-2 z-10">
                  <h3 className="text-sm font-semibold text-card-foreground truncate max-w-[70%]">
                    {selectedEmail.subject || '(No subject)'}
                  </h3>
                  <button
                    type="button"
                    className="rounded-md bg-card text-muted-foreground hover:text-foreground focus:outline-none p-1"
                    onClick={handleCloseModal}
                  >
                    <span className="sr-only">Close</span>
                    <svg
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      aria-label="close"
                    >
                      <title>Close</title>
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
                <div className="overflow-y-auto flex-1 email-detail-scroll">
                  <Suspense
                    fallback={
                      <div className="flex justify-center items-center h-full p-4">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    }
                  >
                    <MobileEmailDetail email={selectedEmail} onClose={handleCloseModal} />
                  </Suspense>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailInbox;
