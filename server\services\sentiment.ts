import { type PipelineType, pipeline } from '@xenova/transformers';
import logger from '../lib/logger';

// Type definitions for sentiment analysis results
export interface SentimentResult {
  label: string; // 'POSITIVE' or 'NEGATIVE'
  score: number; // Confidence score between 0 and 1
  allScores?: {
    // Optional all class scores
    POSITIVE: number;
    NEGATIVE: number;
  };
}

// Cache for the sentiment analysis pipeline to avoid reloading the model
let sentimentPipeline: any = null;

/**
 * Initializes the sentiment analysis pipeline
 * This can be called early in the application startup to pre-load the model
 */
export async function initSentimentAnalysis(): Promise<void> {
  if (!sentimentPipeline) {
    try {
      sentimentPipeline = await pipeline(
        'sentiment-analysis' as PipelineType,
        'Xenova/distilbert-base-uncased-finetuned-sst-2-english'
      );
    } catch (error) {
      logger.error('Failed to initialize sentiment analysis model', { 
        error: String(error), 
        service: 'sentiment' 
      });
      throw new Error('Failed to initialize sentiment analysis model');
    }
  }
}

/**
 * Analyzes the sentiment of a text
 *
 * @param text The text to analyze
 * @param includeAllScores Whether to include scores for all sentiment classes
 * @returns A SentimentResult object with the sentiment label and score
 */
export async function analyzeSentiment(
  text: string,
  includeAllScores = false
): Promise<SentimentResult> {
  try {
    if (!sentimentPipeline) {
      await initSentimentAnalysis();
    }

    // Clean and prepare the text
    const cleanedText = text.trim().slice(0, 512); // Model has a token limit

    // Get the sentiment prediction
    const options = includeAllScores ? { topk: null } : {};
    const result = await sentimentPipeline(cleanedText, options);

    // Handle the result
    if (!result || !result.length) {
      throw new Error('Sentiment analysis returned empty result');
    }

    const mainResult = result[0];

    // Format the response
    const sentimentResult: SentimentResult = {
      label: mainResult.label,
      score: mainResult.score,
    };

    // Include all scores if requested
    if (includeAllScores) {
      const positiveScore = result.find((r: any) => r.label === 'POSITIVE')?.score || 0;
      const negativeScore = result.find((r: any) => r.label === 'NEGATIVE')?.score || 0;

      sentimentResult.allScores = {
        POSITIVE: positiveScore,
        NEGATIVE: negativeScore,
      };
    }

    return sentimentResult;
  } catch (error) {
    logger.error('Sentiment analysis failed', { 
      error: String(error), 
      service: 'sentiment',
      textLength: text.length 
    });
    // Return a neutral default in case of error
    return {
      label: 'UNKNOWN',
      score: 0,
      allScores: includeAllScores ? { POSITIVE: 0, NEGATIVE: 0 } : undefined,
    };
  }
}

/**
 * Gets the sentiment polarity as a number between -1 (very negative) and 1 (very positive)
 *
 * @param text The text to analyze
 * @returns A number between -1 (negative) and 1 (positive)
 */
export async function getSentimentPolarity(text: string): Promise<number> {
  try {
    const result = await analyzeSentiment(text, true);
    if (result.label === 'UNKNOWN') {
      return 0;
    }

    // Convert to a scale from -1 to 1
    // If label is POSITIVE, use the positive score
    // If label is NEGATIVE, use the negative score as a negative value
    return result.label === 'POSITIVE' ? result.score : -result.score;
  } catch (error) {
    logger.error('Failed to get sentiment polarity', { 
      error: String(error), 
      service: 'sentiment',
      textLength: text.length 
    });
    return 0;
  }
}
