/**
 * Gmail API Service
 *
 * This module provides simple, stateless wrapper functions for interacting
 * with the Google Gmail API. It does not manage authentication state;
 * it expects a pre-authenticated OAuth2 client to be passed to its functions.
 */

import { type Auth, type gmail_v1, google } from 'googleapis';
import logger from '../lib/logger';

const gmail = google.gmail('v1');

/**
 * Gets the user's Gmail profile.
 * @param auth - An authenticated OAuth2Client instance.
 * @returns The user's Gmail profile object.
 */
export async function getProfile(auth: Auth.OAuth2Client): Promise<gmail_v1.Schema$Profile> {
  try {
    const response = await gmail.users.getProfile({ auth, userId: 'me' });
    return response.data;
  } catch (error) {
    logger.error('Failed to get Gmail profile', { error: (error as Error).message });
    throw new Error('Could not retrieve Gmail profile.');
  }
}

/**
 * Lists messages in the user's mailbox.
 * @param auth - An authenticated OAuth2Client instance.
 * @param params - Optional parameters for the message list query (e.g., maxResults, q).
 * @returns A list of message objects.
 */
export async function listMessages(
  auth: Auth.OAuth2Client,
  params: gmail_v1.Params$Resource$Users$Messages$List = { userId: 'me' }
): Promise<gmail_v1.Schema$Message[]> {
  try {
    const response = await gmail.users.messages.list({ auth, ...params });
    return response.data.messages || [];
  } catch (error) {
    logger.error('Failed to list Gmail messages', { error: (error as Error).message, params });
    throw new Error('Could not list Gmail messages.');
  }
}

/**
 * Gets a single message by its ID.
 * @param auth - An authenticated OAuth2Client instance.
 * @param messageId - The ID of the message to retrieve.
 * @returns The full message resource.
 */
export async function getMessage(
  auth: Auth.OAuth2Client,
  messageId: string
): Promise<gmail_v1.Schema$Message> {
  try {
    const response = await gmail.users.messages.get({
      auth,
      userId: 'me',
      id: messageId,
      format: 'full',
    });
    return response.data;
  } catch (error) {
    logger.error('Failed to get Gmail message', { error: (error as Error).message, messageId });
    throw new Error('Could not retrieve Gmail message.');
  }
}

// Add other simple, stateless Gmail API wrappers as needed...
