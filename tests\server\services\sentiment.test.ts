import { analyzeSentiment, getSentimentPolarity } from '@server/services/sentiment';

// Mock the transformers library to avoid downloading large models during tests
const mockPipeline = jest.fn();
jest.mock('@xenova/transformers', () => ({
  pipeline: jest.fn().mockImplementation(() => mockPipeline),
}));

describe('Sentiment Analysis Service', () => {
  beforeEach(() => {
    // Clear mock history before each test
    mockPipeline.mockClear();
  });

  describe('analyzeSentiment', () => {
    it('should return the sentiment with the highest score', async () => {
      const mockResult = [{ label: 'POSITIVE', score: 0.99 }];
      mockPipeline.mockResolvedValue(mockResult);

      const text = 'This is a wonderful and amazing product!';
      const result = await analyzeSentiment(text);

      expect(result.label).toBe('POSITIVE');
      expect(result.score).toBe(0.99);
      expect(mockPipeline).toHaveBeenCalledWith(text, {});
    });

    it('should include all scores when requested', async () => {
      const mockResult = [
        { label: 'POSITIVE', score: 0.99 },
        { label: 'NEGATIVE', score: 0.01 },
      ];
      mockPipeline.mockResolvedValue(mockResult);

      const text = 'This is a wonderful and amazing product!';
      const result = await analyzeSentiment(text, true);

      expect(result.label).toBe('POSITIVE');
      expect(result.score).toBe(0.99);
      expect(result.allScores).toEqual({
        POSITIVE: 0.99,
        NEGATIVE: 0.01,
      });
      expect(mockPipeline).toHaveBeenCalledWith(text, { topk: null });
    });

    it('should handle errors gracefully and return an UNKNOWN label', async () => {
      mockPipeline.mockRejectedValue(new Error('Model failed to load'));

      const text = 'Some text';
      const result = await analyzeSentiment(text);

      expect(result.label).toBe('UNKNOWN');
      expect(result.score).toBe(0);
    });

    it('should truncate text to the model limit', async () => {
      const longText = 'a '.repeat(500); // Create a string longer than 512 chars
      const truncatedText = longText.trim().slice(0, 512);

      const mockResult = [{ label: 'NEGATIVE', score: 0.8 }];
      mockPipeline.mockResolvedValue(mockResult);

      await analyzeSentiment(longText);

      expect(mockPipeline).toHaveBeenCalledWith(truncatedText, {});
    });
  });

  describe('getSentimentPolarity', () => {
    it('should return a positive score for a POSITIVE sentiment', async () => {
      const mockResult = [{ label: 'POSITIVE', score: 0.95 }];
      mockPipeline.mockResolvedValue(mockResult);

      const text = 'I love this!';
      const polarity = await getSentimentPolarity(text);

      expect(polarity).toBe(0.95);
    });

    it('should return a negative score for a NEGATIVE sentiment', async () => {
      const mockResult = [{ label: 'NEGATIVE', score: 0.88 }];
      mockPipeline.mockResolvedValue(mockResult);

      const text = 'I hate this!';
      const polarity = await getSentimentPolarity(text);

      expect(polarity).toBe(-0.88);
    });

    it('should return 0 if the sentiment analysis fails', async () => {
      mockPipeline.mockRejectedValue(new Error('Analysis failed'));

      const text = 'Some text';
      const polarity = await getSentimentPolarity(text);

      expect(polarity).toBe(0);
    });
  });
});
