#!/usr/bin/env tsx

import { config } from 'dotenv';
import { storage } from '../server/storage';

// Load environment variables
config();

async function checkUsers() {
  try {
    console.log('Checking current user state...\n');
    
    const allUsers = await storage.getAllUsers();
    console.log(`Total users: ${allUsers.length}\n`);
    
    // Group users by email
    const usersByEmail = new Map<string, typeof allUsers>();
    
    for (const user of allUsers) {
      if (!usersByEmail.has(user.email)) {
        usersByEmail.set(user.email, []);
      }
      usersByEmail.get(user.email)!.push(user);
    }
    
    // Show all users
    for (const [email, users] of usersByEmail) {
      console.log(`Email: ${email}`);
      for (const user of users) {
        console.log(`  User ID: ${user.id}`);
        console.log(`  Provider: ${user.provider}`);
        console.log(`  Firebase UID: ${user.firebaseUid || 'null'}`);
        console.log(`  Has Gmail Tokens: ${!!user.gmailTokens}`);
        console.log('  ---');
      }
      
      if (users.length > 1) {
        console.log(`  ⚠️  DUPLICATE USERS FOUND FOR ${email}`);
      }
      console.log('');
    }
    
  } catch (error) {
    console.error('Error checking users:', error);
  }
}

checkUsers(); 