import { Trash2 } from 'lucide-react';
import type React from 'react';
import { useEffect } from 'react';

import SimpleEmailList from '@/components/email/SimpleEmailList';
import AppLayout from '@/components/layout/AppLayout';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { useEmailList } from '@/context/EmailListContext';
import { useToast } from '@/hooks/use-toast';
import LoadingScreen from '@/components/ui/LoadingScreen';
import { defaultFilters } from '@/components/email/EmailFilter';

const TrashPage: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const { toast } = useToast();
  const { emails, isLoading, refreshEmails, setFilters, goToPage } = useEmailList();

  // Set filters for this page on mount and reset on unmount
  useEffect(() => {
    setFilters({ ...defaultFilters, status: 'trashed' });
    goToPage(1);

    // Reset filters when the component unmounts
    return () => {
      setFilters(defaultFilters);
    };
  }, [setFilters, goToPage]);


  const handleEmptyTrash = async () => {
    // This would be implemented with a call to your backend
    // to permanently delete all trashed emails
    toast({
      title: 'Feature in development',
      description: 'Empty trash functionality will be coming soon!',
      variant: 'default',
    });
  };

  if (authLoading || isLoading) {
    return <LoadingScreen message="Loading trashed emails..." />;
  }

  // The PrivateRoute wrapper will handle this.
  // if (!user) {
  //   return null;
  // }

  return (
    <AppLayout>
      <div className="h-full flex flex-col">
        <div className="trash-header border-b border-border p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Trash2 className="h-5 w-5 mr-2 text-muted-foreground" />
              <h1 className="text-lg sm:text-xl font-semibold text-foreground">Trash</h1>
              <div className="ml-2 text-xs sm:text-sm text-muted-foreground">
                {emails.length} messages
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refreshEmails()}
                disabled={isLoading}
                className="text-xs py-1 px-2 sm:px-3 h-8"
              >
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleEmptyTrash}
                className="text-xs py-1 px-2 sm:px-3 h-8 whitespace-nowrap"
              >
                Empty Trash
              </Button>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 overflow-auto">
          <SimpleEmailList
            emails={emails}
            isLoading={isLoading}
            emptyStateIcon={<Trash2 className="h-12 w-12 text-muted-foreground/30" />}
            emptyStateTitle="Your trash is empty"
            emptyStateDescription="Trashed emails will appear here for 30 days before being permanently deleted"
            onRefresh={() => refreshEmails()}
          />
        </div>
      </div>
    </AppLayout>
  );
};

export default TrashPage;
