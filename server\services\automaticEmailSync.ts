/**
 * Automatic Email Sync Service
 * 
 * This service runs in the background to automatically sync emails from Gmail
 * for all connected users every 60 seconds. It ensures the inbox stays up-to-date
 * without requiring manual sync actions.
 */

import logger from '../lib/logger';
import { storage } from '../storage';
import tokenService from './tokenService';
import { fetchGmailEmails, processEmailsWithAI } from './email-v2';

interface AutoSyncConfig {
  syncInterval: number; // milliseconds
  enabled: boolean;
  maxUsersPerCycle: number;
  maxEmailsPerUser: number;
}

class AutomaticEmailSyncService {
  private static readonly DEFAULT_CONFIG: AutoSyncConfig = {
    syncInterval: 60000, // 60 seconds
    enabled: true,
    maxUsersPerCycle: 50, // Limit to prevent overwhelming the system
    maxEmailsPerUser: 50, // Limit emails fetched per user per cycle
  };

  private config: AutoSyncConfig;
  private syncInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private lastSyncStats = {
    totalUsers: 0,
    successfulUsers: 0,
    failedUsers: 0,
    totalEmailsFetched: 0,
    lastSyncTime: new Date(),
  };

  constructor(config: Partial<AutoSyncConfig> = {}) {
    this.config = { ...AutomaticEmailSyncService.DEFAULT_CONFIG, ...config };
  }

  /**
   * Start the automatic email sync service
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('[AutoSync] Service is already running');
      return;
    }

    if (!this.config.enabled) {
      logger.info('[AutoSync] Service is disabled by configuration');
      return;
    }

    this.isRunning = true;
    logger.info('[AutoSync] Starting automatic email sync service', {
      syncInterval: this.config.syncInterval,
      maxUsersPerCycle: this.config.maxUsersPerCycle,
    });

    // Start the sync cycle
    this.syncInterval = setInterval(async () => {
      try {
        await this.performSyncCycle();
      } catch (error) {
        logger.error('[AutoSync] Error in sync cycle', {
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }, this.config.syncInterval);

    // Perform initial sync after a short delay
    setTimeout(() => {
      this.performSyncCycle().catch(error => {
        logger.error('[AutoSync] Error in initial sync cycle', {
          error: error instanceof Error ? error.message : String(error),
        });
      });
    }, 5000); // 5 second delay to allow server to fully start

    logger.info('[AutoSync] Service started successfully');
  }

  /**
   * Stop the automatic email sync service
   */
  stop(): void {
    if (!this.isRunning) {
      logger.warn('[AutoSync] Service is not running');
      return;
    }

    this.isRunning = false;
    
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    logger.info('[AutoSync] Service stopped');
  }

  /**
   * Perform a single sync cycle for all eligible users
   */
  private async performSyncCycle(): Promise<void> {
    const cycleStartTime = Date.now();
    const cycleId = `sync-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    
    logger.debug('[AutoSync] Starting sync cycle', { cycleId });

    try {
      // Get all users with Gmail connections
      const users = await this.getEligibleUsers();
      
      if (users.length === 0) {
        logger.debug('[AutoSync] No eligible users found for sync');
        return;
      }

      // Limit users per cycle to prevent overwhelming the system
      const usersToSync = users.slice(0, this.config.maxUsersPerCycle);
      
      logger.info('[AutoSync] Syncing emails for users', {
        cycleId,
        totalEligibleUsers: users.length,
        usersInThisCycle: usersToSync.length,
      });

      // Reset cycle stats
      let successfulUsers = 0;
      let failedUsers = 0;
      let totalEmailsFetched = 0;

      // Process users in parallel with controlled concurrency
      const concurrency = 5; // Process 5 users at a time
      for (let i = 0; i < usersToSync.length; i += concurrency) {
        const batch = usersToSync.slice(i, i + concurrency);
        
        const batchResults = await Promise.allSettled(
          batch.map(user => this.syncUserEmails(user, cycleId))
        );

        // Process results
        for (const result of batchResults) {
          if (result.status === 'fulfilled') {
            successfulUsers++;
            totalEmailsFetched += result.value;
          } else {
            failedUsers++;
            logger.warn('[AutoSync] User sync failed', {
              cycleId,
              error: result.reason,
            });
          }
        }
      }

      // Update stats
      this.lastSyncStats = {
        totalUsers: usersToSync.length,
        successfulUsers,
        failedUsers,
        totalEmailsFetched,
        lastSyncTime: new Date(),
      };

      const cycleDuration = Date.now() - cycleStartTime;
      logger.info('[AutoSync] Sync cycle completed', {
        cycleId,
        duration: cycleDuration,
        stats: this.lastSyncStats,
      });

    } catch (error) {
      const cycleDuration = Date.now() - cycleStartTime;
      logger.error('[AutoSync] Sync cycle failed', {
        cycleId,
        duration: cycleDuration,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Get users eligible for automatic email sync
   */
  private async getEligibleUsers(): Promise<Array<{ id: number; email: string; provider: string }>> {
    try {
      // Get all users who have Gmail tokens (regardless of provider)
      const users = await storage.getAllUsers();
      
      // Filter for users with Gmail connections - check for Gmail tokens instead of provider
      const eligibleUsers = users.filter(user => 
        user.gmailTokens && user.email
      );

      logger.debug('[AutoSync] Found eligible users', {
        totalUsers: users.length,
        eligibleUsers: eligibleUsers.length,
        eligibleUserIds: eligibleUsers.map(u => u.id),
      });

      return eligibleUsers;
    } catch (error) {
      logger.error('[AutoSync] Error getting eligible users', {
        error: error instanceof Error ? error.message : String(error),
      });
      return [];
    }
  }

  /**
   * Sync emails for a single user
   */
  private async syncUserEmails(
    user: { id: number; email: string; provider: string }, 
    cycleId: string
  ): Promise<number> {
    const userSyncStartTime = Date.now();
    
    try {
      logger.debug('[AutoSync] Syncing emails for user', {
        cycleId,
        userId: user.id,
        userEmail: user.email,
      });

      // Get valid access token for the user
      let accessToken;
      try {
        // Get the full user object from storage for token service
        const fullUser = await storage.getUserById(user.id);
        if (!fullUser) {
          logger.debug('[AutoSync] User not found in storage', {
            cycleId,
            userId: user.id,
            userEmail: user.email,
          });
          return 0;
        }
        
        accessToken = await tokenService.getValidAccessToken(fullUser);
      } catch (tokenError) {
        logger.debug('[AutoSync] Failed to get access token for user', {
          cycleId,
          userId: user.id,
          userEmail: user.email,
          error: tokenError instanceof Error ? tokenError.message : String(tokenError),
        });
        return 0;
      }
      
      if (!accessToken) {
        logger.debug('[AutoSync] No valid access token for user', {
          cycleId,
          userId: user.id,
          userEmail: user.email,
        });
        return 0;
      }

      // Fetch new emails from Gmail
      let fetchedEmails;
      try {
        fetchedEmails = await fetchGmailEmails(user.id, accessToken);
      } catch (fetchError) {
        logger.warn('[AutoSync] Failed to fetch emails for user', {
          cycleId,
          userId: user.id,
          userEmail: user.email,
          error: fetchError instanceof Error ? fetchError.message : String(fetchError),
        });
        throw fetchError;
      }
      
      if (fetchedEmails.length === 0) {
        logger.debug('[AutoSync] No new emails for user', {
          cycleId,
          userId: user.id,
          userEmail: user.email,
        });
        return 0;
      }

      // Queue AI processing for new emails (don't await - let it run in background)
      const emailIds = fetchedEmails.map(e => e.id).filter(Boolean) as number[];
      if (emailIds.length > 0) {
        processEmailsWithAI(user.id, emailIds, Math.min(emailIds.length, 10)).catch(err => {
          logger.warn('[AutoSync] Failed to queue AI processing for user', {
            cycleId,
            userId: user.id,
            userEmail: user.email,
            error: err instanceof Error ? err.message : String(err),
          });
        });
      }

      const userSyncDuration = Date.now() - userSyncStartTime;
      logger.info('[AutoSync] Successfully synced user emails', {
        cycleId,
        userId: user.id,
        userEmail: user.email,
        emailsFetched: fetchedEmails.length,
        emailsQueuedForAI: emailIds.length,
        duration: userSyncDuration,
      });

      return fetchedEmails.length;

    } catch (error) {
      const userSyncDuration = Date.now() - userSyncStartTime;
      logger.warn('[AutoSync] Failed to sync emails for user', {
        cycleId,
        userId: user.id,
        userEmail: user.email,
        duration: userSyncDuration,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  /**
   * Get current sync statistics
   */
  getStats() {
    return {
      isRunning: this.isRunning,
      config: this.config,
      lastSyncStats: this.lastSyncStats,
    };
  }

  /**
   * Update service configuration
   */
  updateConfig(newConfig: Partial<AutoSyncConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('[AutoSync] Configuration updated', { config: this.config });
  }
}

// Create and export a singleton instance
export const automaticEmailSyncService = new AutomaticEmailSyncService();

/**
 * Start the automatic email sync service
 */
export async function startAutomaticEmailSync(): Promise<void> {
  await automaticEmailSyncService.start();
}

/**
 * Stop the automatic email sync service
 */
export function stopAutomaticEmailSync(): void {
  automaticEmailSyncService.stop();
}

/**
 * Get sync service statistics
 */
export function getAutoSyncStats() {
  return automaticEmailSyncService.getStats();
} 