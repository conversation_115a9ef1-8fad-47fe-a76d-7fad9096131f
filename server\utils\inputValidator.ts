/**
 * Input Validation Utilities
 *
 * This module provides standardized input validation for API requests,
 * helping prevent injection attacks and ensuring data integrity.
 */

import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';
import { ValidationError } from './errorHandler';

// Common error messages
const ERROR_MESSAGES = {
  REQUIRED: 'This field is required',
  EMAIL_INVALID: 'Invalid email format',
  STRING_MIN: (min: number) => `Must be at least ${min} characters`,
  STRING_MAX: (max: number) => `Cannot exceed ${max} characters`,
  NUMBER_MIN: (min: number) => `Must be at least ${min}`,
  NUMBER_MAX: (max: number) => `Cannot exceed ${max}`,
  ARRAY_MIN: (min: number) => `Must contain at least ${min} items`,
  ARRAY_MAX: (max: number) => `Cannot contain more than ${max} items`,
  DATE_INVALID: 'Invalid date format',
  BOOLEAN_INVALID: 'Must be true or false',
  ENUM_INVALID: (values: string[]) => `Must be one of: ${values.join(', ')}`,
  UNEXPECTED_KEY: 'Unexpected property',
};

/**
 * Common schema patterns for reuse
 */
export const CommonSchemas = {
  // Email validation with common requirements
  email: z.string().email(ERROR_MESSAGES.EMAIL_INVALID).min(5).max(255),

  // URL validation with common requirements
  url: z.string().url().max(2048),

  // ID validation for common database ID patterns
  id: z.number().int().positive(),
  stringId: z.string().min(1).max(100),

  // Date validation for various formats
  date: z.string().refine((val) => !Number.isNaN(Date.parse(val)), ERROR_MESSAGES.DATE_INVALID),
  dateObject: z.date(),

  // Common string validations
  shortText: z.string().min(1).max(100),
  mediumText: z.string().min(1).max(500),
  longText: z.string().min(1).max(5000),

  // Basic types with constraints
  positiveNumber: z.number().positive(),
  nonNegativeNumber: z.number().nonnegative(),

  // Common optional types
  optionalShortText: z.string().max(100).optional(),
  optionalMediumText: z.string().max(500).optional(),
  optionalLongText: z.string().max(5000).optional(),

  // Common arrays
  stringArray: z.array(z.string()),
  numberArray: z.array(z.number()),

  // Common specific formats
  hexColor: z.string().regex(/^#([0-9A-F]{3}){1,2}$/i, 'Invalid hex color'),

  // Boolean with string conversion
  booleanFromString: z.preprocess(
    (val) => val === 'true' || val === '1' || val === true,
    z.boolean()
  ),
};

/**
 * Ready-made request schemas for common patterns like id params and
 * pagination. These can be used directly with the validateRequest
 * middleware to reduce boilerplate in route files.
 */
export const RequestSchemas = {
  idParam: z.object({ id: CommonSchemas.id }),
  userIdParam: z.object({ userId: CommonSchemas.id }),
  emailIdParam: z.object({ emailId: CommonSchemas.id }),
  pagination: createPaginationSchema(),
};

/**
 * Create a validation middleware function from a Zod schema
 * @param schema The Zod schema to validate against
 * @param source Where to look for data ('body', 'query', 'params')
 */
export function validateRequest(
  schema: z.ZodSchema<any>,
  source: 'body' | 'query' | 'params' = 'body'
) {
  return (req: Request, _res: Response, next: NextFunction) => {
    try {
      // Get data from the appropriate request property
      const data = req[source];

      // Validate the data against the schema
      const result = schema.safeParse(data);

      if (!result.success) {
        // Format validation errors for response
        const errors = result.error.errors.map((err) => ({
          path: err.path.join('.'),
          message: err.message,
        }));

        throw new ValidationError('Request validation failed', { errors });
      }

      // Replace the original data with the validated (and potentially transformed) data
      req[source] = result.data;

      next();
    } catch (error) {
      // Pass the validation error to the global error handler
      next(error);
    }
  };
}

/**
 * Create a schema for pagination parameters
 * @param defaultLimit Default number of items per page
 * @param maxLimit Maximum number of items per page
 */
export function createPaginationSchema(defaultLimit = 20, maxLimit = 100) {
  return z.object({
    page: z.preprocess(
      (val) => Number.parseInt(val as string, 10) || 1,
      z.number().int().positive().default(1)
    ),
    limit: z.preprocess((val) => {
      const limit = Number.parseInt(val as string, 10) || defaultLimit;
      return limit > maxLimit ? maxLimit : limit;
    }, z.number().int().positive().default(defaultLimit)),
    sortBy: z.string().optional(),
    order: z.enum(['asc', 'desc']).default('desc'),
  });
}

/**
 * Schema for email validation
 */
export const EmailSchemas = {
  // Schema for creating a new email
  create: z.object({
    userId: z.number().int().positive(),
    messageId: z.string().min(1).max(255),
    threadId: z.string().optional(),
    subject: z.string().max(500),
    snippet: z.string().max(1000).optional(),
    sender: z.string().max(255),
    senderEmail: z.string().email().max(255),
    receivedAt: z.date().or(
      z.string().refine((val) => !Number.isNaN(Date.parse(val)), {
        message: 'Invalid date format',
      })
    ),
    isRead: z.boolean().default(false),
    isArchived: z.boolean().default(false),
    categories: z.array(z.string()).optional(),
    priority: z.enum(['high', 'medium', 'low']).optional(),
    originalContent: z.string().max(100000),
    htmlContent: z.string().max(500000).optional(),
  }),

  // Schema for updating an existing email
  update: z.object({
    isRead: z.boolean().optional(),
    isArchived: z.boolean().optional(),
    categories: z.array(z.string()).optional(),
    priority: z.enum(['high', 'medium', 'low']).optional(),
    summary: z.string().max(1000).optional(),
    aiReply: z.string().max(5000).optional(),
  }),

  // Schema for email processing request
  process: z.object({
    priorityLevel: z.enum(['urgent', 'high', 'normal']).default('normal'),
    processingTypes: z
      .array(z.enum(['summary', 'categorization', 'reply']))
      .default(['summary', 'categorization', 'reply']),
  }),

  // Schema for batch email processing
  batchProcess: z.object({
    emailIds: z.array(z.number().int().positive()),
    priorityLevel: z.enum(['urgent', 'high', 'normal']).default('normal'),
    processingTypes: z
      .array(z.enum(['summary', 'categorization', 'reply']))
      .default(['summary', 'categorization', 'reply']),
  }),

  // Schema for filtering emails
  filter: z.object({
    status: z
      .enum(['all', 'read', 'unread', 'archived', 'trashed', 'important', 'snoozed'])
      .optional(),
    priority: z.enum(['all', 'high', 'medium', 'low']).optional(),
    category: z.string().optional(),
    timeRange: z.enum(['all', 'today', 'this_week', 'this_month']).optional(),
    dateFrom: z.string().datetime().optional(),
    dateTo: z.string().datetime().optional(),
    q: z.string().optional(), // For general search query
  }),

  // Schema for sending email replies
  reply: z.object({
    content: z.string().min(1).max(10000),
    useAiSuggestion: z.boolean().optional(),
    includeOriginal: z.boolean().optional(),
  }),
};

/**
 * Schema for user validation
 */
export const UserSchemas = {
  // Schema for creating a new user
  create: z.object({
    email: CommonSchemas.email,
    name: z.string().max(255).optional(),
    provider: z.string().max(50),
  }),

  // Schema for updating an existing user
  update: z.object({
    name: z.string().max(255).optional(),
    picture: z.string().max(1000).optional(),
    accessToken: z.string().max(5000).optional(),
    refreshToken: z.string().max(5000).optional(),
    expiresAt: z.date().optional(),
  }),

  // Schema for user login
  login: z.object({
    email: CommonSchemas.email,
    token: z.string().min(10),
  }),

  // Schema for validating Firebase auth tokens
  firebaseAuth: z.object({
    idToken: z.string().min(10),
  }),
};

/**
 * Schema for settings validation
 */
export const SettingsSchemas = {
  // Schema for creating or updating settings
  update: z.object({
    replyTone: z
      .enum(['professional', 'friendly', 'casual', 'formal', 'custom'])
      .default('professional'),
    customTone: z.string().max(500).optional(),
    privacyMode: z.boolean().default(false),
    notificationDigest: z.boolean().default(true),
    categories: z.record(z.string(), z.string()).optional(),
    priorityColors: z
      .object({
        high: z
          .string()
          .regex(/^#([0-9A-F]{3}){1,2}$/i)
          .optional(),
        medium: z
          .string()
          .regex(/^#([0-9A-F]{3}){1,2}$/i)
          .optional(),
        low: z
          .string()
          .regex(/^#([0-9A-F]{3}){1,2}$/i)
          .optional(),
      })
      .optional(),
    displayName: z.string().max(100).optional(),
    theme: z.enum(['light', 'dark', 'system']).optional(),
  }),
};
