{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    // Allow .test.ts files (override the exclude from the base tsconfig.json)
    // by not having an "exclude" or by having a more specific "include"
    "types": ["node", "jest"],
    "jsx": "react-jsx"
  },
  "include": [
    "**/*.test.ts",
    "**/*.spec.ts",
    "server/**/*",
    "shared/**/*",
    "tests/**/*"
  ],
  "exclude": [
    "node_modules",
    "build",
    "dist",
    "client/src/**/*"
  ]
}
