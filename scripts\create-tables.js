import 'dotenv/config';
import { sql } from 'drizzle-orm';
import { closeDatabase, db } from '../server/db.ts';

async function createTables() {
  try {
    console.log('Creating users table...');
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email TEXT NOT NULL UNIQUE,
        name TEXT,
        picture TEXT,
        role TEXT DEFAULT 'user' NOT NULL,
        access_token TEXT,
        refresh_token TEXT,
        expires_at TIMESTAMP,
        provider TEXT NOT NULL DEFAULT 'google',
        firebase_uid TEXT,
        gmail_tokens JSONB,
        emails_processed INTEGER DEFAULT 0,
        replies_sent INTEGER DEFAULT 0,
        tier TEXT DEFAULT 'free',
        reply_tone TEXT DEFAULT 'professional',
        last_login TIMESTAMP,
        last_reply_date TIMESTAMP,
        refresh_attempts INTEGER DEFAULT 0,
        last_token_refresh TIMESTAMP,
        auth_error_count INTEGER DEFAULT 0,
        security_level TEXT DEFAULT 'standard',
        token_invalid BOOLEAN DEFAULT false,
        last_token_error TEXT,
        last_api_error TEXT,
        last_connection_verified TIMESTAMP,
        token_update_status TEXT,
        token_status TEXT DEFAULT 'unknown',
        token_error_count INTEGER DEFAULT 0,
        token_error_time TIMESTAMP,
        token_last_refreshed TIMESTAMP
      )
    `);
    console.log('✅ Users table created successfully');

    console.log('Creating settings table...');
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS settings (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL UNIQUE,
        reply_tone TEXT DEFAULT 'professional',
        display_name TEXT,
        custom_tone TEXT,
        privacy_mode BOOLEAN DEFAULT false,
        notification_digest BOOLEAN DEFAULT true,
        categories JSONB,
        priority_colors JSONB,
        theme_mode TEXT DEFAULT 'system',
        accent_color TEXT DEFAULT 'indigo',
        border_radius INTEGER DEFAULT 6,
        data_retention_days INTEGER DEFAULT 30,
        allow_ai_processing BOOLEAN DEFAULT true,
        store_email_content BOOLEAN DEFAULT true,
        auto_delete_processed_emails BOOLEAN DEFAULT false,
        encrypt_sensitive_data BOOLEAN DEFAULT true,
        consent_version TEXT DEFAULT '1.0',
        consent_timestamp TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✅ Settings table created successfully');

    console.log('Creating emails table...');
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS emails (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        message_id TEXT NOT NULL,
        thread_id TEXT,
        subject TEXT,
        snippet TEXT,
        sender TEXT,
        sender_email TEXT,
        received_at TIMESTAMP,
        is_read BOOLEAN DEFAULT false,
        is_archived BOOLEAN DEFAULT false,
        is_replied BOOLEAN DEFAULT false,
        is_trashed BOOLEAN DEFAULT false,
        is_important BOOLEAN DEFAULT false,
        snoozed_until TIMESTAMP,
        reply_date TIMESTAMP,
        reply_id TEXT,
        summary TEXT,
        categories TEXT[],
        priority TEXT,
        ai_reply TEXT,
        original_content TEXT,
        html_content TEXT,
        provider TEXT,
        label_ids TEXT[],
        content_expires_at TIMESTAMP,
        last_accessed TIMESTAMP DEFAULT NOW(),
        is_content_encrypted BOOLEAN DEFAULT false,
        retention_days INTEGER DEFAULT 30
      )
    `);
    console.log('✅ Emails table created successfully');

    console.log('Creating achievements table...');
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS achievements (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        type TEXT NOT NULL,
        level INTEGER NOT NULL DEFAULT 1,
        icon TEXT NOT NULL,
        unlocked_at TIMESTAMP NOT NULL DEFAULT NOW(),
        progress INTEGER NOT NULL DEFAULT 0,
        max_progress INTEGER NOT NULL,
        is_complete BOOLEAN NOT NULL DEFAULT false
      )
    `);
    console.log('✅ Achievements table created successfully');

    console.log('Creating task_queue table...');
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS task_queue (
        id SERIAL PRIMARY KEY,
        task_type TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        priority INTEGER NOT NULL DEFAULT 1,
        data JSONB NOT NULL,
        result JSONB,
        error TEXT,
        retry_count INTEGER NOT NULL DEFAULT 0,
        max_retries INTEGER NOT NULL DEFAULT 3,
        last_attempt_at TIMESTAMP,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        scheduled_for TIMESTAMP,
        completed_at TIMESTAMP,
        locked_by TEXT,
        locked_at TIMESTAMP
      )
    `);
    console.log('✅ Task queue table created successfully');

    console.log('🎉 All tables created successfully!');
  } catch (error) {
    console.error('❌ Error creating tables:', error);
    throw error; // Re-throw to indicate failure
  }
}

createTables().finally(async () => {
  console.log('Closing database connection...');
  await closeDatabase();
});
