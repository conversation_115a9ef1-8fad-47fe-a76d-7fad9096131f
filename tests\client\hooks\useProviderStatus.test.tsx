import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

import { useProviderStatus } from '@/hooks/use-provider-status';
// Import the actual function so we can mock its implementation
import { getEmailProviderStatus } from '@/lib/emailProviders';

// Mock the entire module
jest.mock('@/lib/emailProviders', () => ({
  __esModule: true,
  // Make sure all exports from the original module are mocked
  ...jest.requireActual('@/lib/emailProviders'),
  getEmailProviderStatus: jest.fn(), // This is the function we will control
}));

// Mock the auth context
jest.mock('@/context/AuthContext', () => ({
  __esModule: true,
  useAuth: () => ({ user: { id: 1 }, loading: false }),
}));

// Cast the imported function to a Jest mock function type for type safety
const mockedGetEmailProviderStatus = getEmailProviderStatus as jest.Mock;

describe('Hooks/useProviderStatus', () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        // Disable retries for tests to get immediate results
        retry: false,
      },
    },
  });

  const wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  // Clear query cache and mock history after each test
  afterEach(() => {
    queryClient.clear();
    mockedGetEmailProviderStatus.mockClear();
  });

  it('should return provider data on successful fetch', async () => {
    const providersMock = [{ provider: 'google', isConnected: true }];
    mockedGetEmailProviderStatus.mockResolvedValue(providersMock);

    const { result } = renderHook(() => useProviderStatus(), { wrapper });

    await waitFor(() => expect(result.current.providers).toEqual(providersMock));
    expect(result.current.isError).toBe(false);
  });

  it('handles errors gracefully', async () => {
    // THIS IS THE KEY CHANGE: Create an error that the hook's `retry`
    // function will not attempt to retry.
    const error: any = new Error('Auth Failure');
    error.response = { status: 401 };

    mockedGetEmailProviderStatus.mockRejectedValue(error);

    const { result } = renderHook(() => useProviderStatus(), { wrapper });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error).toBe(error);
  });
});