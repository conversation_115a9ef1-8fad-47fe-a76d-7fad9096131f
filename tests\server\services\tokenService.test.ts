/**
 * TokenService tests
 *
 * Covers:
 * 1. Token parsing/validation, including encrypted storage.
 * 2. Expiration detection logic in validateTokens.
 * 3. Refresh token logic – success & missing refresh token scenario.
 * 4. Secure token storage (encrypt called when storing tokens).
 */

// Set minimal env vars required before importing TokenService & encryption util
process.env.ENCRYPTION_KEY = 'a'.repeat(64); // 32-byte hex key

import { encrypt } from '@server/utils/encryption';
import tokenService, { TokenObject } from '@server/services/tokenService';
import type { User } from '@shared/schema';

// ------------------------------
// Logger & <PERSON> mocks
// ------------------------------

jest.mock('@server/lib/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('@server/db', () => ({
  getDb: jest.fn(async () => ({
    // Mock query builder chain used in updateUserTokens
    update: () => ({
      set: () => ({ where: () => Promise.resolve() }),
    }),
  })),
}));

// Mock storage layer for refreshTokens internals
jest.mock('@server/storage', () => ({
  __esModule: true,
  storage: {
    updateUser: jest.fn(),
  },
}));

// Mock circuit-breaker wrapper to bypass actual Google call
const mockProtectResult = {
  result: {
    access_token: 'new-access',
    refresh_token: 'refresh-123',
    expiry_date: Date.now() + 3600 * 1000,
  },
  usedFallback: false,
  circuitOpen: false,
  durationMs: 5,
};

jest.mock('@server/utils/apiProtector', () => ({
  __esModule: true,
  protectApiCallWithDetails: jest.fn(async () => mockProtectResult),
  resetCircuitForOperation: jest.fn(),
}));

// Stub oauth2Client inside TokenService to avoid Google SDK requirement
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore – access private field for test
(tokenService as any).oauth2Client = {
  setCredentials: jest.fn(),
  refreshAccessToken: jest.fn(async () => ({ credentials: mockProtectResult.result })),
};

// ------------------------------
// Helpers
// ------------------------------
function makeUser(overrides: Partial<User> = {}): User {
  return {
    id: 1,
    email: '<EMAIL>',
    provider: 'google',
    role: 'user',
    // legacy fields
    accessToken: null,
    refreshToken: null,
    expiresAt: null,
    // meta counters
    emailsProcessed: 0,
    repliesSent: 0,
    tier: 'free',
    replyTone: 'professional',
    refreshAttempts: 0,
    authErrorCount: 0,
    tokenInvalid: false,
    tokenStatus: 'unknown',
    tokenErrorCount: 0,
    // nullable date fields
    lastLogin: null,
    lastReplyDate: null,
    lastTokenRefresh: null,
    lastConnectionVerified: null,
    tokenErrorTime: null,
    // other optional columns
    picture: null,
    name: null,
    gmailTokens: null,
    securityLevel: 'standard',
    lastTokenError: null,
    tokenUpdateStatus: null,
    firebaseUid: null,
    access_token: null as any, // not in schema but TS satisfaction
    ...overrides,
  } as unknown as User;
}

// ------------------------------
// Tests
// ------------------------------

describe('TokenService', () => {
  it('parses encrypted tokens correctly', () => {
    const rawTokens: TokenObject = {
      access_token: 'abc',
      refresh_token: 'def',
      expiry_date: Date.now() + 3600 * 1000,
    };
    const encrypted = encrypt(JSON.stringify(rawTokens));

    const user = makeUser({ gmailTokens: encrypted });

    const parsed = tokenService.parseTokens(user);
    expect(parsed).toEqual(rawTokens);
  });

  it('validateTokens detects expired vs valid tokens', () => {
    const validTokens: TokenObject = {
      access_token: 'valid',
      expiry_date: Date.now() + 7200 * 1000, // 2 hours
    };
    const expiredTokens: TokenObject = {
      access_token: 'expired',
      expiry_date: Date.now() - 1000, // past
    };

    const userValid = makeUser({ gmailTokens: JSON.stringify(validTokens) });
    const userExpired = makeUser({ gmailTokens: JSON.stringify(expiredTokens) });

    const validRes = tokenService.validateTokens(userValid);
    const expiredRes = tokenService.validateTokens(userExpired);

    expect(validRes.isValid).toBe(true);
    expect(validRes.isExpired).toBe(false);
    expect(validRes.needsRefresh).toBe(false);

    expect(expiredRes.isValid).toBe(false);
    expect(expiredRes.isExpired).toBe(true);
    expect(expiredRes.needsRefresh).toBe(true);
  });

  it('returns needsReauth when no refresh_token is available', async () => {
    const tokens: TokenObject = {
      access_token: 'x',
      expiry_date: Date.now() - 1000,
      // no refresh_token
    };
    const user = makeUser({ gmailTokens: JSON.stringify(tokens) });

    const result = await tokenService.refreshTokens(user);
    expect(result.success).toBe(false);
    expect(result.needsReauth).toBe(true);
  });

  it('successfully refreshes tokens when refresh_token exists', async () => {
    const tokens: TokenObject = {
      access_token: 'old-access',
      refresh_token: 'refresh-123',
      expiry_date: Date.now() - 1000,
    };
    const user = makeUser({ gmailTokens: JSON.stringify(tokens) });
    // Spy on private updateUserTokens to ensure secure storage path is taken
    const spyUpdate = jest.spyOn<any, any>(tokenService as any, 'updateUserTokens').mockResolvedValue(undefined);

    const result = await tokenService.refreshTokens(user);

    expect(result.success).toBe(true);
    expect(result.tokens?.access_token).toBe('new-access');
    expect(spyUpdate).toHaveBeenCalled();
  });

  it('encrypts tokens when storing them', async () => {
    const tokens: TokenObject = { access_token: 'sensitive' };
    await expect(tokenService.storeTokens(1, tokens)).resolves.not.toThrow();
  });
});
