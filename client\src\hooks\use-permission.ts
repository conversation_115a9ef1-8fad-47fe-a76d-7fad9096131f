// Emergency logout cleaner removed - using proper authentication flow
// import { handleAuthError } from "@/utils/emergency-logout-cleaner";
import { useRef, useState } from 'react';
import apiClient from '../lib/apiClient';
import { useToast } from './use-toast';

// Gmail specific scopes
export const GMAIL_SCOPES = {
  READONLY: 'https://www.googleapis.com/auth/gmail.readonly',
  MODIFY: 'https://www.googleapis.com/auth/gmail.modify',
  COMPOSE: 'https://www.googleapis.com/auth/gmail.compose',
  SEND: 'https://www.googleapis.com/auth/gmail.send',
  LABELS: 'https://www.googleapis.com/auth/gmail.labels',
  FULL: 'https://www.googleapis.com/auth/gmail.settings.basic',
  SETTINGS_SHARING: 'https://www.googleapis.com/auth/gmail.settings.sharing',
};

export function usePermission() {
  const [isRequesting, setIsRequesting] = useState(false);
  const { toast } = useToast();

  /**
   * Handle the redirection process for authentication
   */
  const redirectToAuth = (authUrl: string) => {
    // Store current location for returning after auth
    const currentPath = window.location.pathname;
    localStorage.setItem('returnToPath', currentPath);

    // Redirect to auth URL
    window.location.href = authUrl;
  };

  /**
   * Request additional Gmail permissions based on provided scopes
   * Will redirect the user to Google OAuth consent screen
   */
  const requestGmailPermission = async (scopes: string[]): Promise<boolean> => {
    if (isRequesting) {
      return false;
    }

    try {
      setIsRequesting(true);

      // Show feedback to user
      toast({
        title: 'Requesting Permissions',
        description: 'You will be redirected to Gmail to approve additional permissions.',
      });

      // Get auth URL for additional scopes
      const { authUrl } = await apiClient.post<{ authUrl: string }>(
        '/api/auth/google/request-scopes',
        { scopes }
      );

      // Safety check for valid URL
      if (!authUrl || !authUrl.includes('accounts.google.com')) {
        console.error('[Permission] Invalid auth URL received:', authUrl);
        toast({
          title: 'Authentication Error',
          description: 'Unable to connect to Google. Please try again later.',
          variant: 'destructive',
        });
        return false;
      }

      // Perform redirection
      redirectToAuth(authUrl);

      // We won't reach here due to redirect, but return true for completeness
      return true;
    } catch (error) {
      console.error('[Permission] Failed to request Gmail permissions:', error);
      toast({
        title: 'Permission Request Failed',
        description: 'Unable to request additional permissions. Please try again.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsRequesting(false);
    }
  };

  // Track when the last token refresh happened to prevent loops
  const lastTokenRefreshRef = useRef<number>(0);
  const TOKEN_REFRESH_COOLDOWN = 5000; // 5 seconds

  /**
   * Handle a token refresh request - completely re-authenticate the user
   */
  const handleTokenRefresh = async (): Promise<boolean> => {
    // Prevent rapid consecutive calls
    const now = Date.now();
    if (now - lastTokenRefreshRef.current < TOKEN_REFRESH_COOLDOWN) {
      return false;
    }

    // Check if we're on login page or user is not authenticated
    if (window.location.pathname.includes('/login') || window.location.pathname === '/') {
      return false;
    }

    try {
      // Update the refresh timestamp
      lastTokenRefreshRef.current = now;

      // User-friendly message
      toast({
        title: 'Session Expired',
        description: 'Your Gmail session has expired. Please sign in again.',
      });

      // Trigger the Google Auth flow for re-authentication
      const reauthUrl = new URL('/api/auth/google', window.location.origin);
      reauthUrl.searchParams.set('prompt', 'consent'); // Force prompt for permissions
      // Redirect the user to the re-authentication URL
      window.location.href = reauthUrl.toString();
      // Return a promise that never resolves, as the page will be redirected
      return new Promise(() => {});
    } catch (error) {
      console.error('[Permission] Token refresh failed:', error);

      // If it's a JSON parse error (HTML returned), redirect to login
      if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
        window.location.href = '/login';
        return false;
      }

      toast({
        title: 'Authentication Error',
        description: 'Unable to reconnect to Gmail. Try refreshing the page.',
        variant: 'destructive',
      });
      return false;
    }
  };

  /**
   * Simple authentication error handler
   */
  const handleAuthError = (errorMessage: string): boolean => {
    const lowerError = errorMessage.toLowerCase();
    if (
      lowerError.includes('401') ||
      lowerError.includes('unauthorized') ||
      lowerError.includes('authentication')
    ) {
      window.location.href = '/login';
      return true;
    }
    return false;
  };

  /**
   * Determine required scopes based on error message
   */
  const getScopesFromError = (errorMessage: string): string[] => {
    const lowerCaseError = errorMessage.toLowerCase();
    const scopesToRequest: string[] = [];

    if (
      lowerCaseError.includes('gmail.modify') ||
      lowerCaseError.includes('modify emails') ||
      lowerCaseError.includes('archive') ||
      lowerCaseError.includes('trash')
    ) {
      scopesToRequest.push(GMAIL_SCOPES.MODIFY);
    }

    if (
      lowerCaseError.includes('gmail.send') ||
      lowerCaseError.includes('send emails') ||
      lowerCaseError.includes('send message')
    ) {
      scopesToRequest.push(GMAIL_SCOPES.SEND);
    }

    if (
      lowerCaseError.includes('gmail.labels') ||
      lowerCaseError.includes('manage labels') ||
      lowerCaseError.includes('label')
    ) {
      scopesToRequest.push(GMAIL_SCOPES.LABELS);
    }

    if (
      lowerCaseError.includes('gmail.compose') ||
      lowerCaseError.includes('compose emails') ||
      lowerCaseError.includes('draft')
    ) {
      scopesToRequest.push(GMAIL_SCOPES.COMPOSE);
    }

    // If no specific scopes detected, return common scopes
    if (scopesToRequest.length === 0) {
      return [
        GMAIL_SCOPES.READONLY,
        GMAIL_SCOPES.MODIFY,
        GMAIL_SCOPES.SEND,
        GMAIL_SCOPES.LABELS,
      ];
    }

    return scopesToRequest;
  };

  /**
   * Parse an error message to determine if it's a permission or authentication issue,
   * and request the appropriate permissions if needed
   */
  const handlePermissionError = async (errorMessage: string): Promise<boolean> => {
    // Check for authentication errors first
    if (handleAuthError(errorMessage)) {
      return true; // Error was handled
    }

    const lowerCaseError = errorMessage.toLowerCase();

    // Check if error is related to token refresh failure
    if (
      lowerCaseError.includes('failed to refresh token') ||
      lowerCaseError.includes('invalid_grant') ||
      lowerCaseError.includes('token expired') ||
      lowerCaseError.includes('invalid credentials') ||
      lowerCaseError.includes('not authenticated')
    ) {
      // For token issues, use the dedicated token refresh handler
      return await handleTokenRefresh();
    }

    // Check if error is permission related
    if (
      lowerCaseError.includes('insufficient permission') ||
      lowerCaseError.includes('required scope') ||
      lowerCaseError.includes('permission_denied') ||
      lowerCaseError.includes('missing scope') ||
      lowerCaseError.includes('needs permission')
    ) {
      // Determine which scopes to request based on error message
      const scopesToRequest = getScopesFromError(errorMessage);
      return await requestGmailPermission(scopesToRequest);
    }

    // Not a permission error or couldn't determine how to handle
    return false;
  };

  return {
    isRequesting,
    requestGmailPermission,
    handlePermissionError,
    handleTokenRefresh,
    GMAIL_SCOPES,
  };
}
