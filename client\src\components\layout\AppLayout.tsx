import { AnimatePresence, motion } from 'framer-motion';
import React, { useId } from 'react';
import { Helmet } from 'react-helmet-async';
import { cn } from '@/lib/utils';
import AppHeader from './AppHeader';
import AppSidebar from './AppSidebar';

interface AppLayoutProps {
  children: React.ReactNode;
  fullWidth?: boolean;
  className?: string;
}

/**
 * AppLayout provides a consistent layout structure across all pages
 * with proper dark mode theming and smooth page transitions
 */
const AppLayout: React.FC<AppLayoutProps> = ({ children, fullWidth = false }) => {
  const mainContentId = useId();

  return (
    <>
      <Helmet>
        <body className="app-layout-active" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
        />
      </Helmet>
      <div className="bg-background min-h-screen flex flex-col max-h-screen w-full overflow-hidden max-w-screen">
        <AppHeader />
        <div className="flex flex-1 overflow-hidden w-full max-w-screen">
          <AppSidebar />
          <div className="flex flex-col flex-1 min-w-0">
            <AnimatePresence mode="wait">
              <motion.main
                key="main-content"
                initial={{ opacity: 0, y: 8 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -8 }}
                transition={{
                  duration: 0.15,
                  ease: 'easeInOut',
                }}
                className={`bg-background flex-1 overflow-y-hidden overflow-x-hidden
              relative scroll-smooth w-full main-content-container max-w-screen contain-content`}
                id={mainContentId}
                tabIndex={-1}
              >
                <div
                  className={cn(
                    'h-full w-full overflow-y-auto max-w-screen',
                    !fullWidth && 'px-4 py-2'
                  )}
                >
                  {children}
                </div>
              </motion.main>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </>
  );
};

export default React.memo(AppLayout);
