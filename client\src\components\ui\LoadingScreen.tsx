/**
 * LoadingScreen Component
 *
 * Displays a loading spinner with an optional message.
 * Used for indicating loading states throughout the app.
 */

import { Loader2 } from 'lucide-react';
import type React from 'react';

interface LoadingScreenProps {
  message?: string;
  fullScreen?: boolean;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = 'Loading...',
  fullScreen = true,
}) => {
  const containerClass = fullScreen
    ? 'fixed inset-0 flex flex-col items-center justify-center bg-background z-50'
    : 'flex flex-col items-center justify-center py-10';

  return (
    <div className={containerClass}>
      <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
      <p className="text-center text-muted-foreground">{message}</p>
    </div>
  );
};

export default LoadingScreen;
