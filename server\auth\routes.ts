/**
 * Unified Authentication Routes
 *
 * This file defines the single, authoritative set of routes for all
 * authentication and session management operations.
 */

import { type Request, type Response, Router } from 'express';
import { getGmailErrorPage, getGmailServerErrorPage } from '../lib/gmailErrorPages';
import logger from '../lib/logger';
import { ErrorCode, sendBadRequest, sendError, sendSuccess } from '../lib/standardizedResponses';
import { optionalAuth, requireAuth } from '../middleware/simpleAuth';
import type { TokenObject } from '../services/tokenService';
import tokenService from '../services/tokenService';
import { verifyIdTokenAndGetUser } from './firebase';
import { generateGoogleAuthUrl, handleGoogleCallback } from './google';

const router = Router();

// --- Test endpoint for debugging ---
if (process.env.NODE_ENV !== 'production') {
  router.get('/test', (_req: Request, res: Response): void => {
    res.json({
      success: true,
      message: 'Auth routes are working',
      timestamp: new Date().toISOString(),
    });
  });

  router.post('/test-post', (req: Request, res: Response): void => {
    res.json({
      success: true,
      message: 'Auth POST routes are working',
      body: req.body,
      timestamp: new Date().toISOString(),
    });
  });
}

// --- CSRF Protection ---
router.get('/csrf', (req: Request, res: Response): void => {
  // The csrf-sync middleware attaches the token as a property to the request object.
  res.json({ csrfToken: req.csrfToken });
});

// --- OAuth Flows ---

router.get('/google', optionalAuth, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    let needsConsent = true; // Default to true for security if user not found or tokens are invalid.

    if (req.user) {
      // User is logged in, check if they already have a refresh token.
      const tokens: TokenObject | null = tokenService.parseTokens(req.user);
      if (tokens?.refresh_token) {
        // If a refresh token exists, we don't need to force the consent screen again.
        needsConsent = false;
      }
    }

    const authUrl = await generateGoogleAuthUrl(req, { userId, promptConsent: needsConsent });

    // Check if this is an API request (expects JSON) or browser redirect
    if (req.headers.accept?.includes('application/json')) {
      sendSuccess(res, { authUrl });
      return;
    }
    logger.info('Redirecting to Google for OAuth.', { userId, needsConsent });
    res.redirect(authUrl);
  } catch (error) {
    logger.error('Failed to generate Google auth URL', { error: (error as Error).message });
    res.status(500).send('Failed to initiate authentication.');
  }
});

// Additional Google scopes endpoint
router.get(
  '/google/additional-scopes',
  requireAuth,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const authUrl = await generateGoogleAuthUrl(req, {
        userId: req.user?.id,
        promptConsent: true,
      });

      sendSuccess(res, { authUrl });
    } catch (error) {
      logger.error('Failed to generate additional scopes URL', { error: (error as Error).message });
      sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to generate authorization URL');
    }
  }
);

// Outlook OAuth endpoint
router.get('/outlook', optionalAuth, async (req: Request, res: Response): Promise<void> => {
  try {
    // For now, return a placeholder URL until Outlook OAuth is implemented
    const authUrl =
      'https://login.microsoftonline.com/common/oauth2/v2.0/authorize?scope=https://graph.microsoft.com/mail.read&response_type=code&client_id=placeholder';

    if (req.headers.accept?.includes('application/json')) {
      sendSuccess(res, { authUrl });
    } else {
      res.redirect(authUrl);
    }
  } catch (error) {
    logger.error('Failed to generate Outlook auth URL', { error: (error as Error).message });
    sendError(res, ErrorCode.INTERNAL_ERROR, 'Outlook OAuth not yet implemented');
  }
});

router.post('/firebase/verify', async (req: Request, res: Response): Promise<void> => {
  const { idToken } = req.body;

  if (!idToken) {
    sendBadRequest(res, 'Missing Firebase ID token.');
    return;
  }

  try {
    const user = await verifyIdTokenAndGetUser(idToken);

    // Establish a new session for the verified user
    req.session.userId = user.id;

    logger.info('Successfully verified Firebase token and established session.', {
      userId: user.id,
    });
    sendSuccess(res, { user }, 'Firebase token verified successfully.');
  } catch (error) {
    logger.error('Firebase token verification failed.', {
      error: error instanceof Error ? error.message : String(error),
    });
    sendError(res, ErrorCode.UNAUTHORIZED, 'Invalid or expired Firebase token.');
  }
});

// User registration endpoint
router.post('/register', async (req: Request, res: Response): Promise<void> => {
  try {
    const { idToken } = req.body;
    // The provider is implicitly 'firebase' for this registration flow.
    const user = await verifyIdTokenAndGetUser(idToken);

    // Establish session after successful registration/login
    req.session.userId = user.id;

    sendSuccess(
      res,
      {
        message: 'User authenticated successfully.',
        user: { id: user.id, email: user.email }, // Send back basic user info
      },
      'User authenticated successfully.'
    );
  } catch (error) {
    logger.error('Registration failed', { error });
    sendError(res, ErrorCode.OPERATION_FAILED, 'Registration or login failed.');
  }
});

router.get('/google/callback', async (req: Request, res: Response): Promise<void> => {
  try {
    const { code, state, error } = req.query;

    if (error) {
      const errorString = String(error);
      logger.warn('Google OAuth callback contained an error', { error: errorString });

      // Safely handle Google OAuth errors to prevent Reflected XSS
      const knownErrors: Record<string, string> = {
        access_denied: 'You have denied the permission request.',
        invalid_request: 'The request was malformed or missing parameters.',
        unauthorized_client: 'This application is not authorized for Google OAuth.',
        invalid_scope: 'One or more requested permissions are invalid.',
        server_error: 'Google encountered a temporary server error.',
        temporarily_unavailable: 'Google is temporarily unavailable. Please try again later.',
      };

      const errorTitle = knownErrors[errorString] ? errorString : 'unknown_error';
      const errorDescription =
        knownErrors[errorString] || 'An unknown error occurred during authentication.';

      res.status(403).send(getGmailErrorPage(errorTitle, errorDescription));
      return;
    }

    if (typeof code !== 'string' || typeof state !== 'string') {
      logger.warn('Google OAuth callback missing code or state.', {
        hasCode: !!code,
        hasState: !!state,
      });
      res
        .status(400)
        .send(
          getGmailErrorPage(
            'invalid_request',
            'Callback from Google was missing required parameters.'
          )
        );
      return;
    }

    const user = await handleGoogleCallback(req, code, state);

    if (!user) {
      // This case should ideally not be reached if findOrCreateUser is robust
      logger.error('User not found after Google callback.');
      res
        .status(500)
        .send(
          getGmailServerErrorPage(
            new Error('User provisioning failed after successful authentication.')
          )
        );
      return;
    }

    req.session.userId = user.id;
    logger.info('Successfully authenticated via Google callback.', { userId: user.id });

    // Redirect to the dashboard with a query parameter to trigger a client-side data refresh
    res.redirect('/dashboard?gmail_connected=true');
  } catch (error) {
    let errorMessage = 'An unknown error occurred during Google authentication.';
    let errorDetails: object | string = {};

    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'object' && error !== null) {
      // This is a common structure for Google API errors (GaxiosError)
      if ('response' in error && (error as any).response?.data) {
        errorDetails = (error as any).response.data;
        // Try to get a specific message from the Google error response
        const googleError = (errorDetails as any)?.error;
        const errorDescription = (errorDetails as any)?.error_description;
        if (googleError && errorDescription) {
          errorMessage = `Google Auth Error: ${googleError} - ${errorDescription}`;
        } else {
          errorMessage = `An unspecified error occurred while communicating with Google. Details: ${JSON.stringify(errorDetails)}`;
        }
      } else if ('message' in error) {
        errorMessage = String((error as any).message);
      } else {
        errorMessage = `An non-standard error object was thrown: ${JSON.stringify(error)}`;
      }
    } else {
      errorMessage = String(error);
    }

    logger.error('Google OAuth callback failed', {
      error: errorMessage,
      details: errorDetails,
    });

    const displayError = new Error(errorMessage);
    res.status(500).send(getGmailServerErrorPage(displayError));
  }
});

// --- Session & Token Management ---

router.post('/logout', requireAuth, (req: Request, res: Response): void => {
  const userId = req.session.userId;
  req.session.destroy((err) => {
    if (err) {
      logger.error('Failed to destroy session on logout.', { userId });
      sendError(res, ErrorCode.INTERNAL_ERROR, 'Logout failed due to a server error.');
      return;
    }
    res.clearCookie('inboxzero.sid');
    sendSuccess(res, { message: 'Logged out successfully.' }, 'Logged out successfully.');
  });
});

router.get('/status', requireAuth, (req: Request, res: Response): void => {
  // If the requireAuth middleware passes, the user is authenticated.
  // The user object is attached to the request by the auth middleware.
  sendSuccess(
    res,
    {
      isAuthenticated: true,
      user: req.user, // Return the full user object
    },
    'User is authenticated.'
  );
});

router.post('/refresh-token', requireAuth, async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await tokenService.refreshTokens(req.user!);
    if (result.success) {
      sendSuccess(res, { message: 'Token refreshed successfully.' });
    } else {
      const errorCode = result.needsReauth ? ErrorCode.UNAUTHORIZED : ErrorCode.OPERATION_FAILED;
      sendError(res, errorCode, result.error || 'Failed to refresh token.', {
        needsReauth: result.needsReauth,
      });
    }
  } catch (_error) {
    logger.error('Unhandled error during token refresh.', { userId: req.user?.id });
    sendError(res, ErrorCode.INTERNAL_ERROR, 'An unexpected error occurred during token refresh.');
  }
});

// Token status endpoint
router.get('/token-status', requireAuth, async (req: Request, res: Response): Promise<void> => {
  try {
    const tokens: TokenObject | null = tokenService.parseTokens(req.user!);
    const hasValidTokens = !!tokens?.access_token;
    const needsReauth = !tokens?.refresh_token;

    sendSuccess(res, {
      hasValidTokens,
      needsReauth,
      tokenExpiry: tokens?.expiry_date ? new Date(tokens.expiry_date) : null,
    });
  } catch (error) {
    logger.error('Error checking token status.', { userId: req.user?.id, error });
    sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to check token status.');
  }
});

// Fix token issues endpoint
router.post(
  '/fix-token-issues',
  requireAuth,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const result = await tokenService.refreshTokens(req.user!);
      if (result.success) {
        sendSuccess(res, { message: 'Token issues resolved successfully.' });
      } else {
        const errorCode = result.needsReauth ? ErrorCode.UNAUTHORIZED : ErrorCode.OPERATION_FAILED;
        sendError(res, errorCode, result.error || 'Failed to resolve token issues.', {
          needsReauth: result.needsReauth,
        });
      }
    } catch (_error) {
      logger.error('Unhandled error during token issue resolution.', { userId: req.user?.id });
      sendError(
        res,
        ErrorCode.INTERNAL_ERROR,
        'An unexpected error occurred while fixing token issues.'
      );
    }
  }
);

export default router;
