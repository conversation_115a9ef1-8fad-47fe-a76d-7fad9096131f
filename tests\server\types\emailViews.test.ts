/**
 * Email Views Types Tests
 * 
 * Tests the email view type definitions and utility functions
 * for data egress optimization.
 */

import {
  EmailViewType,
  EMAIL_FIELD_SELECTIONS,
  DATA_SIZE_REDUCTION,
  isEmailListItem,
  isEmailSummary,
  isEmailDetail,
  getOptimalViewType,
  type EmailListItem,
  type EmailSummary,
  type EmailDetail,
  type EmailMetadata,
  type EmailContent,
} from '@server/types/emailViews';

describe('Email View Types', () => {
  describe('EmailViewType enum', () => {
    it('should have all expected view types', () => {
      expect(EmailViewType.LIST).toBe('list');
      expect(EmailViewType.SUMMARY).toBe('summary');
      expect(EmailViewType.DETAIL).toBe('detail');
      expect(EmailViewType.METADATA).toBe('metadata');
      expect(EmailViewType.CONTENT).toBe('content');
    });
  });

  describe('EMAIL_FIELD_SELECTIONS', () => {
    it('should have field selections for all view types', () => {
      expect(EMAIL_FIELD_SELECTIONS).toHaveProperty(EmailViewType.LIST);
      expect(EMAIL_FIELD_SELECTIONS).toHaveProperty(EmailViewType.SUMMARY);
      expect(EMAIL_FIELD_SELECTIONS).toHaveProperty(EmailViewType.DETAIL);
      expect(EMAIL_FIELD_SELECTIONS).toHaveProperty(EmailViewType.METADATA);
      expect(EMAIL_FIELD_SELECTIONS).toHaveProperty(EmailViewType.CONTENT);
    });

    it('should exclude heavy content fields in LIST view', () => {
      const listSelection = EMAIL_FIELD_SELECTIONS[EmailViewType.LIST];
      
      expect(listSelection.id).toBe(true);
      expect(listSelection.messageId).toBe(true);
      expect(listSelection.subject).toBe(true);
      expect(listSelection.originalContent).toBe(false);
      expect(listSelection.htmlContent).toBe(false);
      expect(listSelection.summary).toBe(false);
      expect(listSelection.aiReply).toBe(false);
    });

    it('should include summary fields but exclude content in SUMMARY view', () => {
      const summarySelection = EMAIL_FIELD_SELECTIONS[EmailViewType.SUMMARY];
      
      expect(summarySelection.summary).toBe(true);
      expect(summarySelection.aiReply).toBe(true);
      expect(summarySelection.labelIds).toBe(true);
      expect(summarySelection.originalContent).toBe(false);
      expect(summarySelection.htmlContent).toBe(false);
    });

    it('should include all fields for DETAIL view', () => {
      const detailSelection = EMAIL_FIELD_SELECTIONS[EmailViewType.DETAIL];
      
      expect(detailSelection['*']).toBe(true);
    });

    it('should exclude user content fields in METADATA view', () => {
      const metadataSelection = EMAIL_FIELD_SELECTIONS[EmailViewType.METADATA];
      
      expect(metadataSelection.id).toBe(true);
      expect(metadataSelection.userId).toBe(true);
      expect(metadataSelection.provider).toBe(true);
      expect(metadataSelection.subject).toBe(false);
      expect(metadataSelection.snippet).toBe(false);
      expect(metadataSelection.originalContent).toBe(false);
      expect(metadataSelection.htmlContent).toBe(false);
    });

    it('should include content fields but exclude metadata in CONTENT view', () => {
      const contentSelection = EMAIL_FIELD_SELECTIONS[EmailViewType.CONTENT];
      
      expect(contentSelection.originalContent).toBe(true);
      expect(contentSelection.htmlContent).toBe(true);
      expect(contentSelection.subject).toBe(true);
      expect(contentSelection.isRead).toBe(false);
      expect(contentSelection.isArchived).toBe(false);
      expect(contentSelection.categories).toBe(false);
    });
  });

  describe('DATA_SIZE_REDUCTION', () => {
    it('should have reduction percentages for all view types', () => {
      expect(DATA_SIZE_REDUCTION[EmailViewType.LIST]).toBe(85);
      expect(DATA_SIZE_REDUCTION[EmailViewType.SUMMARY]).toBe(70);
      expect(DATA_SIZE_REDUCTION[EmailViewType.DETAIL]).toBe(0);
      expect(DATA_SIZE_REDUCTION[EmailViewType.METADATA]).toBe(90);
      expect(DATA_SIZE_REDUCTION[EmailViewType.CONTENT]).toBe(60);
    });

    it('should have realistic reduction percentages', () => {
      Object.values(DATA_SIZE_REDUCTION).forEach(reduction => {
        expect(reduction).toBeGreaterThanOrEqual(0);
        expect(reduction).toBeLessThanOrEqual(100);
      });
    });
  });

  describe('Type Guards', () => {
    const mockEmailListItem: EmailListItem = {
      id: 1,
      messageId: 'msg-1',
      subject: 'Test Subject',
      snippet: 'Test snippet',
      sender: 'Test Sender',
      senderEmail: '<EMAIL>',
      receivedAt: new Date(),
      isRead: false,
      isArchived: false,
      isTrashed: false,
      isImportant: false,
      isReplied: false,
      categories: ['work'],
      priority: 'normal',
      snoozedUntil: null,
      threadId: 'thread-1',
      provider: 'gmail',
    };

    const mockEmailSummary: EmailSummary = {
      ...mockEmailListItem,
      summary: 'Test summary',
      aiReply: 'Test AI reply',
      labelIds: ['INBOX'],
    };

    const mockEmailDetail: EmailDetail = {
      ...mockEmailSummary,
      userId: 1,
      originalContent: 'Original content',
      htmlContent: '<p>HTML content</p>',
      replyDate: null,
      replyId: null,
      contentExpiresAt: null,
      lastAccessed: new Date(),
      isContentEncrypted: false,
      retentionDays: 30,
    };

    describe('isEmailListItem', () => {
      it('should return true for valid EmailListItem', () => {
        expect(isEmailListItem(mockEmailListItem)).toBe(true);
      });

      it('should return false for invalid objects', () => {
        expect(isEmailListItem(null)).toBe(false);
        expect(isEmailListItem({})).toBe(false);
        expect(isEmailListItem({ id: 'not-a-number' })).toBe(false);
        expect(isEmailListItem({ messageId: 123 })).toBe(false);
      });
    });

    describe('isEmailSummary', () => {
      it('should return true for valid EmailSummary', () => {
        expect(isEmailSummary(mockEmailSummary)).toBe(true);
      });

      it('should return false for EmailListItem without summary', () => {
        expect(isEmailSummary(mockEmailListItem)).toBe(false);
      });

      it('should return false for invalid objects', () => {
        expect(isEmailSummary(null)).toBe(false);
        expect(isEmailSummary({})).toBe(false);
      });
    });

    describe('isEmailDetail', () => {
      it('should return true for valid EmailDetail', () => {
        expect(isEmailDetail(mockEmailDetail)).toBe(true);
      });

      it('should return false for objects without content fields', () => {
        expect(isEmailDetail(mockEmailListItem)).toBe(false);
        expect(isEmailDetail(mockEmailSummary)).toBe(false);
      });

      it('should return false for invalid objects', () => {
        expect(isEmailDetail(null)).toBe(false);
        expect(isEmailDetail({})).toBe(false);
      });
    });
  });

  describe('getOptimalViewType', () => {
    it('should return CONTENT for processing context', () => {
      const result = getOptimalViewType({ isProcessing: true });
      expect(result).toBe(EmailViewType.CONTENT);
    });

    it('should return METADATA for admin context', () => {
      const result = getOptimalViewType({ isAdminView: true });
      expect(result).toBe(EmailViewType.METADATA);
    });

    it('should return DETAIL when content is needed', () => {
      const result = getOptimalViewType({ needsContent: true });
      expect(result).toBe(EmailViewType.DETAIL);
    });

    it('should return SUMMARY when summary is needed', () => {
      const result = getOptimalViewType({ needsSummary: true });
      expect(result).toBe(EmailViewType.SUMMARY);
    });

    it('should return LIST for list view context', () => {
      const result = getOptimalViewType({ isListView: true });
      expect(result).toBe(EmailViewType.LIST);
    });

    it('should return SUMMARY as default', () => {
      const result = getOptimalViewType({});
      expect(result).toBe(EmailViewType.SUMMARY);
    });

    it('should prioritize contexts correctly', () => {
      // Processing should override other contexts
      expect(getOptimalViewType({ 
        isProcessing: true, 
        isAdminView: true, 
        needsContent: true 
      })).toBe(EmailViewType.CONTENT);

      // Admin should override content needs
      expect(getOptimalViewType({ 
        isAdminView: true, 
        needsContent: true, 
        needsSummary: true 
      })).toBe(EmailViewType.METADATA);

      // Content should override summary
      expect(getOptimalViewType({ 
        needsContent: true, 
        needsSummary: true, 
        isListView: true 
      })).toBe(EmailViewType.DETAIL);
    });
  });

  describe('Type Definitions', () => {
    it('should have correct EmailListItem structure', () => {
      const listItem: EmailListItem = {
        id: 1,
        messageId: 'msg-1',
        subject: 'Test',
        snippet: 'Test snippet',
        sender: 'Sender',
        senderEmail: '<EMAIL>',
        receivedAt: new Date(),
        isRead: false,
        isArchived: false,
        isTrashed: false,
        isImportant: false,
        isReplied: false,
        categories: [],
        priority: null,
        snoozedUntil: null,
        threadId: null,
        provider: null,
      };

      expect(listItem).toBeDefined();
      expect(typeof listItem.id).toBe('number');
      expect(typeof listItem.messageId).toBe('string');
    });

    it('should have correct EmailMetadata structure', () => {
      const metadata: EmailMetadata = {
        id: 1,
        messageId: 'msg-1',
        userId: 1,
        provider: 'gmail',
        receivedAt: new Date(),
        isRead: false,
        isArchived: false,
        isTrashed: false,
        isImportant: false,
        isReplied: false,
        categories: [],
        priority: null,
        contentExpiresAt: null,
        isContentEncrypted: false,
        retentionDays: 30,
      };

      expect(metadata).toBeDefined();
      expect(typeof metadata.userId).toBe('number');
      expect(typeof metadata.isContentEncrypted).toBe('boolean');
    });

    it('should have correct EmailContent structure', () => {
      const content: EmailContent = {
        id: 1,
        messageId: 'msg-1',
        subject: 'Test',
        originalContent: 'Original content',
        htmlContent: '<p>HTML content</p>',
        snippet: 'Snippet',
        sender: 'Sender',
        senderEmail: '<EMAIL>',
        receivedAt: new Date(),
      };

      expect(content).toBeDefined();
      expect(typeof content.originalContent).toBe('string');
      expect(typeof content.htmlContent).toBe('string');
    });
  });
});
