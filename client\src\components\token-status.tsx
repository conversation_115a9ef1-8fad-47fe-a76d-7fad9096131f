import { <PERSON><PERSON><PERSON><PERSON>cle, CheckCircle, Clock, ExternalLink, RefreshCw, Shield } from 'lucide-react';
import type React from 'react';
import { Link } from 'wouter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useProviderStatus } from '@/hooks/use-provider-status';
import { useToast } from '@/hooks/use-toast';
import type { EmailProviderStatus } from '@/lib/emailProviders';

// Token status interface
interface TokenStatus {
  provider: string;
  isConnected: boolean;
  hasValidToken: boolean;
  hasError: boolean;
  errorType?: string;
  errorMessage?: string;
  tokenExpiry?: string;
  lastRefresh?: string;
  circuitBreakerOpen?: boolean;
}

// Format date string to relative time
const formatRelativeTime = (dateString?: string) => {
  if (!dateString) return 'Unknown';

  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / 60000);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins} min${diffMins === 1 ? '' : 's'} ago`;

  const diffHours = Math.floor(diffMins / 60);
  if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;

  const diffDays = Math.floor(diffHours / 24);
  return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
};

const TokenStatusIndicator: React.FC<{ status: TokenStatus }> = ({ status }) => {
  // Show different indicators based on status
  if (status.circuitBreakerOpen) {
    return (
      <div className="flex items-center text-amber-500">
        <Shield className="w-4 h-4 mr-1" />
        <span className="text-sm">Circuit Open</span>
      </div>
    );
  }

  if (status.hasError) {
    return (
      <div className="flex items-center text-red-500">
        <AlertCircle className="w-4 h-4 mr-1" />
        <span className="text-sm">Error</span>
      </div>
    );
  }

  if (!status.isConnected) {
    return (
      <div className="flex items-center text-gray-500">
        <Clock className="w-4 h-4 mr-1" />
        <span className="text-sm">Not Connected</span>
      </div>
    );
  }

  return (
    <div className="flex items-center text-green-500">
      <CheckCircle className="w-4 h-4 mr-1" />
      <span className="text-sm">Connected</span>
    </div>
  );
};

const TokenStatusBadge: React.FC<{ status: TokenStatus }> = ({ status }) => {
  // Different badge styles based on status
  if (status.circuitBreakerOpen) {
    return (
      <Badge variant="outline" className="bg-amber-100">
        Circuit Tripped
      </Badge>
    );
  }

  if (status.hasError) {
    return (
      <Badge variant="outline" className="bg-red-100">
        Error
      </Badge>
    );
  }

  if (!status.isConnected) {
    return (
      <Badge variant="outline" className="bg-gray-100">
        Disconnected
      </Badge>
    );
  }

  return (
    <Badge variant="outline" className="bg-green-100">
      Connected
    </Badge>
  );
};

const TokenStatus: React.FC = () => {
  const { toast } = useToast();
  const { providers: data, isLoading, error, refetch } = useProviderStatus();

  // Handle manual refresh
  const handleRefresh = () => {
    refetch();
    toast({
      title: 'Refreshing token status',
      description: 'Checking connection status with email providers',
    });
  };

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Connection Status</CardTitle>
          <CardDescription>Checking your email provider connections...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-6">
          <RefreshCw className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card className="border-red-200">
        <CardHeader className="bg-red-50">
          <CardTitle className="text-lg">Connection Error</CardTitle>
          <CardDescription>Unable to check your email provider connections</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center text-red-600 mb-4">
            <AlertCircle className="h-5 w-5 mr-2" />
            <p className="text-sm">{error instanceof Error ? error.message : 'Unknown error'}</p>
          </div>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Transform raw provider data into TokenStatus objects
  const providers: TokenStatus[] = (data || []).map(
    (p: EmailProviderStatus): TokenStatus => ({
      provider: p.provider,
      isConnected: p.isConnected,
      hasValidToken: !p.tokenInvalid,
      hasError: !!p.lastApiError || !!p.tokenError,
      errorType: p.tokenError ? 'token' : p.lastApiError ? 'api' : undefined,
      errorMessage: p.tokenError || p.lastApiError || undefined,
      tokenExpiry: p.tokenExpires || undefined,
      lastRefresh: p.lastRefreshedAt || undefined,
      circuitBreakerOpen: p.middlewareContext?.circuitBreakerOpen || false,
    })
  );

  // No providers
  if (providers.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">No Email Providers</CardTitle>
          <CardDescription>You haven't connected any email providers yet</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Connect your email account to get started using Inbox Zero
          </p>
          <Link href="/settings">
            <a className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
              Connect Email Account
            </a>
          </Link>
        </CardContent>
      </Card>
    );
  }

  // Status summary
  const hasErrors = providers.some((p) => p.hasError);
  const hasCircuitBreakers = providers.some((p) => p.circuitBreakerOpen);
  const isAnyConnected = providers.some((p) => p.isConnected);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Email Provider Status</CardTitle>
            <CardDescription>Monitor the connection status of your email accounts</CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Quick status summary */}
        <div className="flex flex-wrap gap-2">
          <Badge variant={isAnyConnected ? 'default' : 'secondary'}>
            {providers.filter((p) => p.isConnected).length} Connected
          </Badge>
          {hasErrors && (
            <Badge variant="destructive">{providers.filter((p) => p.hasError).length} Errors</Badge>
          )}
          {hasCircuitBreakers && (
            <Badge variant="outline" className="bg-amber-100">
              Circuit Protection Active
            </Badge>
          )}
        </div>

        <Separator />

        {/* Individual provider status */}
        <div className="space-y-3">
          {providers.map((provider) => (
            <div
              key={provider.provider}
              className="flex items-center justify-between p-3 border rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <TokenStatusIndicator status={provider} />
                <div>
                  <h4 className="font-medium capitalize">{provider.provider}</h4>
                  <TokenStatusBadge status={provider} />
                </div>
              </div>

              <div className="text-right text-sm text-muted-foreground space-y-1">
                {provider.hasError && provider.errorMessage && (
                  <div className="text-red-600 max-w-xs">
                    <p className="font-semibold">Error: {provider.errorType || 'Unknown'}</p>
                    <p>{provider.errorMessage}</p>
                  </div>
                )}

                {provider.circuitBreakerOpen && (
                  <div className="text-amber-600">
                    <p className="font-semibold">Circuit breaker active</p>
                    <p>Protecting from repeated failures</p>
                  </div>
                )}

                {provider.isConnected && (
                  <div>
                    <p className="font-medium">
                      Token expires:{' '}
                      {provider.tokenExpiry
                        ? new Date(provider.tokenExpiry).toLocaleString()
                        : 'Unknown'}
                    </p>
                    <p>Last refresh: {formatRelativeTime(provider.lastRefresh)}</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>

      <CardFooter className="text-sm text-muted-foreground">
        <div className="flex items-center space-x-2">
          <ExternalLink className="w-4 h-4" />
          <span>Need help? Check the</span>
          <Link href="/settings" className="text-primary hover:underline">
            Email Settings
          </Link>
        </div>
      </CardFooter>
    </Card>
  );
};

export default TokenStatus;
