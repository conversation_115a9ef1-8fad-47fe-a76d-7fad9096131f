-- Merge User Accounts <PERSON>ript
-- This script merges user ID 3 (Gmail account) into user ID 1 (MSN account)
-- Run this in your database console (Supabase SQL Editor)

BEGIN;

-- Step 1: Check current state
SELECT 'Current state before merge:' as step;
SELECT id, email, name, provider, firebase_uid, created_at FROM users WHERE id IN (1, 3);
SELECT user_id, COUNT(*) as email_count FROM emails WHERE user_id IN (1, 3) GROUP BY user_id;

-- Step 2: Update all emails from user 3 to user 1
UPDATE emails
SET user_id = 1
WHERE user_id = 3;

-- Step 3: Update any settings/preferences from user 3 to user 1 (if they exist)
UPDATE settings
SET user_id = 1
WHERE user_id = 3;

-- Step 4: Update any achievements from user 3 to user 1 (if they exist)
UPDATE achievements
SET user_id = 1
WHERE user_id = 3;

-- Step 5: Update any task queue items from user 3 to user 1 (if they exist)
UPDATE task_queue
SET data = jsonb_set(data, '{userId}', '1'::jsonb)
WHERE data->>'userId' = '3';

-- Step 6: Merge Gmail tokens and other important data into user 1
UPDATE users
SET
  gmail_tokens = COALESCE(users.gmail_tokens, u3.gmail_tokens),
  access_token = COALESCE(users.access_token, u3.access_token),
  refresh_token = COALESCE(users.refresh_token, u3.refresh_token),
  expires_at = CASE
    WHEN u3.expires_at > users.expires_at OR users.expires_at IS NULL
    THEN u3.expires_at
    ELSE users.expires_at
  END,
  emails_processed = COALESCE(users.emails_processed, 0) + COALESCE(u3.emails_processed, 0),
  replies_sent = COALESCE(users.replies_sent, 0) + COALESCE(u3.replies_sent, 0),
  last_login = GREATEST(COALESCE(users.last_login, '1970-01-01'::timestamp), COALESCE(u3.last_login, '1970-01-01'::timestamp)),
  last_reply_date = GREATEST(COALESCE(users.last_reply_date, '1970-01-01'::timestamp), COALESCE(u3.last_reply_date, '1970-01-01'::timestamp)),
  last_connection_verified = GREATEST(COALESCE(users.last_connection_verified, '1970-01-01'::timestamp), COALESCE(u3.last_connection_verified, '1970-01-01'::timestamp)),
  token_last_refreshed = GREATEST(COALESCE(users.token_last_refreshed, '1970-01-01'::timestamp), COALESCE(u3.token_last_refreshed, '1970-01-01'::timestamp))
FROM (SELECT * FROM users WHERE id = 3) u3
WHERE users.id = 1;

-- Step 7: Delete the duplicate user record
DELETE FROM users WHERE id = 3;

-- Step 8: Verify the merge
SELECT 'Final state after merge:' as step;
SELECT id, email, name, provider, firebase_uid, emails_processed, replies_sent FROM users WHERE id = 1;
SELECT user_id, COUNT(*) as email_count FROM emails WHERE user_id = 1;
SELECT user_id, COUNT(*) as settings_count FROM settings WHERE user_id = 1;
SELECT user_id, COUNT(*) as achievements_count FROM achievements WHERE user_id = 1;

COMMIT;

-- If something goes wrong, you can rollback with:
-- ROLLBACK;
