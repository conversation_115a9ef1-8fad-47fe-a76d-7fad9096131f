const { drizzle } = require('drizzle-orm/postgres-js');
const { sql } = require('drizzle-orm');
const postgres = require('postgres');
require('dotenv').config();

async function fixArrayColumns() {
  console.log('🔧 Starting array column type fix...');

  // Create database connection
  const connectionString = process.env.DATABASE_URL;
  if (!connectionString) {
    console.error('❌ DATABASE_URL environment variable is required');
    process.exit(1);
  }

  const client = postgres(connectionString);
  const db = drizzle(client);

  try {
    console.log('📊 Checking current column types...');
    
    // Check current column types
    const columnInfo = await db.execute(sql`
      SELECT column_name, data_type, udt_name 
      FROM information_schema.columns 
      WHERE table_name = 'emails' 
      AND column_name IN ('categories', 'label_ids')
      ORDER BY column_name;
    `);
    
    console.log('Current column types:', columnInfo);

    console.log('🔄 Updating categories column to TEXT[]...');
    await db.execute(sql`
      ALTER TABLE emails 
      ALTER COLUMN categories TYPE TEXT[] 
      USING CASE 
        WHEN categories IS NULL THEN ARRAY[]::TEXT[]
        ELSE categories::TEXT[]
      END;
    `);

    console.log('🔄 Updating label_ids column to TEXT[]...');
    await db.execute(sql`
      ALTER TABLE emails 
      ALTER COLUMN label_ids TYPE TEXT[] 
      USING CASE 
        WHEN label_ids IS NULL THEN ARRAY[]::TEXT[]
        ELSE label_ids::TEXT[]
      END;
    `);

    console.log('🔄 Setting default values for array columns...');
    await db.execute(sql`
      ALTER TABLE emails 
      ALTER COLUMN categories SET DEFAULT ARRAY[]::TEXT[];
    `);

    await db.execute(sql`
      ALTER TABLE emails 
      ALTER COLUMN label_ids SET DEFAULT ARRAY[]::TEXT[];
    `);

    console.log('🔄 Updating existing NULL values to empty arrays...');
    await db.execute(sql`
      UPDATE emails 
      SET categories = ARRAY[]::TEXT[] 
      WHERE categories IS NULL;
    `);

    await db.execute(sql`
      UPDATE emails 
      SET label_ids = ARRAY[]::TEXT[] 
      WHERE label_ids IS NULL;
    `);

    console.log('📊 Verifying updated column types...');
    const updatedColumnInfo = await db.execute(sql`
      SELECT column_name, data_type, udt_name, column_default
      FROM information_schema.columns 
      WHERE table_name = 'emails' 
      AND column_name IN ('categories', 'label_ids')
      ORDER BY column_name;
    `);
    
    console.log('Updated column types:', updatedColumnInfo);

    console.log('✅ Array column types fixed successfully!');

  } catch (error) {
    console.error('❌ Error fixing array columns:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the fix
fixArrayColumns().catch((error) => {
  console.error('❌ Failed to fix array columns:', error);
  process.exit(1);
}); 