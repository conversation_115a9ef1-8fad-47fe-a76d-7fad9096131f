# Legacy Route Tracker

This document records API endpoints that are **retained only for backward-compatibility**.  Each entry lists:

* Why the route is still present
* The modern replacement (if any)
* Clean-up tasks required before removal
* Related code/tests to update

---

## 1. `PATCH /api/settings`

**Status:** Active (legacy) – will be deprecated once all clients migrate to the idempotent `PUT /api/settings` endpoint introduced in PR _Routes-CRUD-upgrade_.

### Rationale for Retaining
* The current SPA and mobile client still perform partial-update operations with a PATCH request.
* Third-party integrations (if any) may rely on the existing semantic of **partial update**.

### Modern Replacement
* `PUT /api/settings` – Performs a full-object update but accepts the **same payload schema** (`SettingsSchemas.update`). It is idempotent and aligns with REST conventions adopted elsewhere in the API.

### Tasks Before Removal
1. **Client migration**
   * Update `apiClient.ts` helper used by React Query mutations:
     ```ts
     await apiClient.put('/api/settings', data);
     ```
   * Remove legacy `patchSettings` mutation.
2. **Mobile / External Clients** – Verify no remaining PATCH calls (run grep or telemetry dashboard).
3. **E2E & Unit Tests** –
   * Replace any `PATCH /api/settings` calls with `PUT`.
   * Remove legacy-path tests once coverage for `PUT` is in place.
4. **Documentation** – Update README/API reference to advertise only `PUT`.
5. **Deprecation Window** – Announce in release notes; monitor logs for 14 days to ensure no inbound PATCH traffic.
6. **Code Cleanup**
   * Delete the `router.patch('/', …)` handler in `server/routes/settings.ts`.
   * Remove any conditional logic that differentiates between PATCH/PUT payloads (none today).

### Risks / Mitigations
* **Silent client breakage** – Roll out feature flag that logs but still handles PATCH with a warning for one release cycle.
* **Cache Invalidation** – Ensure React Query keys stay identical (`['settings']`) regardless of method; changing only the HTTP verb should not affect cache.

---

_Add additional legacy routes below as they are identified._ 