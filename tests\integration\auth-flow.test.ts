import request from "supertest";
import { eq } from "drizzle-orm";
import { app } from "../../server/index";
import { getDb } from "../../server/db";
import { users, type User } from "../../shared/schema";
import * as googleAuth from "../../server/auth/google";
import * as firebaseAuth from "../../server/auth/firebase";

// Use a supertest agent to maintain cookies across requests
const agent = request.agent(app);

// Mock external services
jest.mock("../../server/auth/google");
jest.mock("../../server/auth/firebase");

const mockedGoogleAuth = googleAuth as jest.Mocked<typeof googleAuth>;
const mockedFirebaseAuth = firebaseAuth as jest.Mocked<typeof firebaseAuth>;

describe("Integration: Auth Flows", () => {
  beforeEach(async () => {
    // Clean up database before each test
    const db = await getDb();
    await db.delete(users);
    // Clear mocks
    jest.clearAllMocks();
  });

  describe("Google OAuth Flow", () => {
    it("should redirect to Google for authentication", async () => {
      mockedGoogleAuth.generateGoogleAuthUrl.mockResolvedValueOnce(
        "https://accounts.google.com/o/oauth2/v2/auth?mock-params"
      );
      const res = await agent.get("/api/auth/google").expect(302);
      expect(res.header.location).toContain("accounts.google.com");
    });
    it("should handle callback, create user, and set session cookie", async () => {
      const mockUser = {
        id: 1,
        email: "<EMAIL>",
        name: "Test User",
        picture: null,
        accessToken: null,
        refreshToken: null,
        expiresAt: null,
        provider: "google",
        firebaseUid: null,
        googleId: "12345",
        role: "user",
        createdAt: new Date(),
        updatedAt: new Date(),
        emailVerified: null,
        image: null,
        accounts: [],
        sessions: [],
        authenticators: [],
        twoFactorEnabled: false,
        twoFactorSecret: null,
        backupCodes: null,
        lastLoginAt: null,
        loginCount: 0,
        isActive: true,
        preferences: null,
        metadata: null,
        gmailTokens: null,
        emailsProcessed: 0,
        repliesSent: 0,
        tier: "free",
        subscriptionId: null,
        subscriptionStatus: null,
        subscriptionCurrentPeriodEnd: null,
        subscriptionCancelAtPeriodEnd: false,
        stripeCustomerId: null,
        planId: null,
        planName: null,
        planPrice: null,
        planInterval: null,
        planFeatures: null,
        usageLimit: null,
        usageCount: 0,
        lastUsageReset: null,
      } as unknown as User;

      // Mock the function that gets the user profile from Google
      mockedGoogleAuth.handleGoogleCallback.mockResolvedValueOnce(mockUser);

      // Simulate the callback from Google
      const res = await agent
        .get("/api/auth/google/callback?code=mock_code&state=mock_state")
        .expect(302);

      // Should redirect to the frontend dashboard
      expect(res.header.location).toBe("/");

      // Check for the session cookie
      expect(res.header["set-cookie"]).toBeDefined();
      expect(res.header["set-cookie"][0]).toContain("connect.sid");

      // Verify user was created in the database
      const db = await getDb();
      const dbUser = await db.query.users.findFirst({
        where: (usersTable, { eq }) =>
          eq(usersTable.email, "<EMAIL>"),
      });
      expect(dbUser).toBeDefined();
      expect(dbUser?.name).toBe("Test User");
    });
  });

  describe("Firebase Token Verification Flow", () => {
    it("should create a session from a valid Firebase ID token", async () => {
      const mockFirebaseUser = {
        uid: "firebase-uid-123",
        email: "<EMAIL>",
        name: "Firebase User",
        picture: "https://example.com/firebase.png",
      };
      mockedFirebaseAuth.verifyIdTokenAndGetUser.mockResolvedValueOnce(
        mockFirebaseUser as any
      );

      const res = await agent
        .post("/api/auth/firebase/verify")
        .send({ idToken: "mock-firebase-token" })
        .expect(200);

      expect(res.body.email).toBe("<EMAIL>");
      expect(res.header["set-cookie"]).toBeDefined();

      // Verify user was created in the database
      const db = await getDb();
      const dbUser = await db.query.users.findFirst({
        where: (usersTable, { eq }) =>
          eq(usersTable.email, "<EMAIL>"),
      });
      expect(dbUser).toBeDefined();
    });
  });

  describe("Session Management and Logout", () => {
    it("should persist a session and allow access to protected routes, then log out successfully", async () => {
      // Step 1: Log in using Firebase to establish a session
      const mockFirebaseUser = {
        uid: "session.user.uid",
        email: "<EMAIL>",
        name: "Session User",
      };
      mockedFirebaseAuth.verifyIdTokenAndGetUser.mockResolvedValueOnce(
        mockFirebaseUser as any
      );

      await agent
        .post("/api/auth/firebase/verify")
        .send({ idToken: "mock-session-token" })
        .expect(200);

      // Step 2: Verify access to a protected route
      const statusRes = await agent.get("/api/auth/status").expect(200);
      expect(statusRes.body.isAuthenticated).toBe(true);
      expect(statusRes.body.user.email).toBe("<EMAIL>");

      // Step 3: Log out
      await agent.post("/api/auth/logout").expect(200);

      // Step 4: Verify access is now denied
      const finalStatusRes = await agent.get("/api/auth/status").expect(200);
      expect(finalStatusRes.body.isAuthenticated).toBe(false);
    });
  });
});
