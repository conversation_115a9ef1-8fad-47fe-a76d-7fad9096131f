import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Loader2, <PERSON>, Trophy } from 'lucide-react';
import AppLayout from '@/components/layout/AppLayout';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useAchievements } from '@/hooks/use-achievements';
import { useStats } from '@/hooks/use-stats';
import type { AchievementCategory } from '@/types/achievements';

export default function AchievementsPage() {
  const { achievementsData, isLoading } = useAchievements();
  const { stats } = useStats();

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">Loading achievements...</span>
        </div>
      </AppLayout>
    );
  }

  const calculateProgress = (category: AchievementCategory) => {
    if (!category.achievements || category.achievements.length === 0) return 0;
    const completed = category.achievements.filter((a) => a.achieved).length;
    const total = category.achievements.length;
    return Math.round((completed / total) * 100);
  };

  return (
    <AppLayout>
      <div className="container mx-auto py-6 max-w-4xl">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold text-foreground">Achievements</h1>
            <p className="text-muted-foreground">Track your progress and unlock rewards</p>
          </div>

          <Card className="w-full sm:w-auto bg-card">
            <CardContent className="p-4 flex items-center justify-between gap-4">
              <div className="flex items-center gap-2">
                <Trophy className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-sm font-medium text-foreground">Total Points</p>
                  <p className="text-2xl font-bold text-foreground">
                    {achievementsData?.totalPoints || 0}
                  </p>
                </div>
              </div>
              <Separator orientation="vertical" className="h-10" />
              <div>
                <p className="text-sm font-medium text-foreground">Level</p>
                <p className="text-2xl font-bold text-foreground">{achievementsData?.level || 1}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stats Overview */}
        <Card className="mb-6 bg-card">
          <CardHeader>
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              <CardTitle>Progress Overview</CardTitle>
            </div>
            <CardDescription>Your email management statistics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Emails Processed</span>
                  <span className="text-sm font-medium">{stats?.archivedEmails || 0}</span>
                </div>
                <Progress value={Math.min((stats?.archivedEmails || 0) / 2, 100)} />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Inbox Zero Achieved</span>
                  <span className="text-sm font-medium">
                    {stats?.inboxZeroCount || 0} times
                  </span>
                </div>
                <Progress value={Math.min((stats?.inboxZeroCount || 0) * 10, 100)} />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Response Rate</span>
                  <span className="text-sm font-medium">{stats?.responseRate || 0}%</span>
                </div>
                <Progress value={stats?.responseRate || 0} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="all" className="space-y-6">
          <TabsList className="bg-muted">
            <TabsTrigger value="all">All Achievements</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
            <TabsTrigger value="inprogress">In Progress</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-6">
            {achievementsData?.categories?.map((category) => (
              <div key={category.id} className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-foreground">{category.name}</h2>
                  <div className="flex items-center gap-2">
                    <Progress value={calculateProgress(category)} className="w-24" />
                    <span className="text-sm text-muted-foreground">
                      {category.achievements.filter((a) => a.achieved).length}/
                      {category.achievements.length}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {category.achievements.map((achievement) => (
                    <Card
                      key={achievement.id}
                      className={`bg-card ${achievement.achieved ? 'border-primary/50' : ''}`}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-base text-foreground">
                            {achievement.name}
                          </CardTitle>
                          {achievement.achieved ? (
                            <Badge variant="default" className="bg-primary">
                              <Check className="h-3 w-3 mr-1" />
                              Achieved
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-muted-foreground">
                              <Lock className="h-3 w-3 mr-1" />
                              Locked
                            </Badge>
                          )}
                        </div>
                        <CardDescription className="text-muted-foreground">
                          {achievement.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-muted-foreground">Progress</span>
                          <span className="font-medium text-foreground">
                            {achievement.progress?.current || 0} /{' '}
                            {achievement.progress?.target || 0}
                          </span>
                        </div>
                        <Progress
                          value={
                            ((achievement.progress?.current || 0) /
                              (achievement.progress?.target || 1)) *
                            100
                          }
                          className="mt-2"
                        />
                        <div className="mt-4 text-sm text-muted-foreground">
                          <Trophy className="h-4 w-4 inline mr-1 text-primary" />
                          <span>{achievement.points} points</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </TabsContent>

          <TabsContent value="completed" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievementsData?.categories?.flatMap((category) =>
                category.achievements
                  .filter((a) => a.achieved)
                  .map((achievement) => (
                    <Card key={achievement.id} className="bg-card border-primary/50">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-base text-foreground">
                            {achievement.name}
                          </CardTitle>
                          <Badge variant="default" className="bg-primary">
                            <Check className="h-3 w-3 mr-1" />
                            Achieved
                          </Badge>
                        </div>
                        <CardDescription className="text-muted-foreground">
                          {achievement.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="mt-2 text-sm text-muted-foreground">
                          <Trophy className="h-4 w-4 inline mr-1 text-primary" />
                          <span>{achievement.points} points</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="inprogress" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievementsData?.categories?.flatMap((category) =>
                category.achievements
                  .filter((a) => !a.achieved && a.progress?.current > 0)
                  .map((achievement) => (
                    <Card key={achievement.id} className="bg-card">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-base text-foreground">
                            {achievement.name}
                          </CardTitle>
                          <Badge variant="outline" className="text-muted-foreground">
                            In Progress
                          </Badge>
                        </div>
                        <CardDescription className="text-muted-foreground">
                          {achievement.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-muted-foreground">Progress</span>
                          <span className="font-medium text-foreground">
                            {achievement.progress?.current || 0} /{' '}
                            {achievement.progress?.target || 0}
                          </span>
                        </div>
                        <Progress
                          value={
                            ((achievement.progress?.current || 0) /
                              (achievement.progress?.target || 1)) *
                            100
                          }
                          className="mt-2"
                        />
                        <div className="mt-4 text-sm text-muted-foreground">
                          <Trophy className="h-4 w-4 inline mr-1 text-primary" />
                          <span>{achievement.points} points</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}
