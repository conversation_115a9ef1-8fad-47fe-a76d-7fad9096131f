import { ArrowLeft, Info, <PERSON>Q<PERSON><PERSON>, Refresh<PERSON>w, Shield } from 'lucide-react';
import { Link } from 'wouter';
import GmailConnectionStatus from '@/components/email/GmailConnectionStatus';
import AppLayout from '@/components/layout/AppLayout';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/context/AuthContext';

/**
 * Token Management Page
 *
 * This page provides comprehensive token management and diagnostics
 * for Gmail and other email provider connections.
 */
export default function TokenManagementPage() {
  const { user } = useAuth();

  // Check if user has Gmail connected
  const hasGmailConnected = user?.provider === 'google';

  // Function to reconnect Gmail
  const reconnectGmail = () => {
    // Create Google auth URL with the current URL as state for proper redirect
    const currentUrl = encodeURIComponent(window.location.href);
    const authUrl = `/api/google/auth?state=${currentUrl}`;

    // Redirect to the auth URL
    window.location.href = authUrl;
  };

  return (
    <AppLayout>
      <div className="container mx-auto py-6 max-w-4xl">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold mb-1 text-foreground">Token Management</h1>
            <p className="text-muted-foreground">Manage and troubleshoot your email connections</p>
          </div>
          <Link href="/settings">
            <Button variant="outline" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Settings
            </Button>
          </Link>
        </div>

        <Tabs defaultValue="connection" className="space-y-6">
          <TabsList className="bg-muted">
            <TabsTrigger value="connection">Connection Status</TabsTrigger>
            <TabsTrigger value="diagnostics">Diagnostics</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
          </TabsList>

          <TabsContent value="connection" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Gmail Connection Status</CardTitle>
                <CardDescription>View and manage your Gmail connection</CardDescription>
              </CardHeader>
              <CardContent>
                {hasGmailConnected ? (
                  <GmailConnectionStatus />
                ) : (
                  <div className="flex flex-col items-center justify-center py-6 text-center">
                    <MailQuestion className="h-16 w-16 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Gmail Account Connected</h3>
                    <p className="text-muted-foreground max-w-md mb-6">
                      You haven't connected a Gmail account yet. Connect your account to use Gmail
                      integration features.
                    </p>
                    <Button onClick={reconnectGmail}>Connect Gmail Account</Button>
                  </div>
                )}
              </CardContent>
              {hasGmailConnected && (
                <CardFooter className="border-t pt-6 flex flex-col items-start">
                  <h3 className="text-sm font-medium mb-3">Connection Actions</h3>
                  <div className="flex gap-3">
                    <Button
                      variant="secondary"
                      size="sm"
                      className="gap-2"
                      onClick={reconnectGmail}
                    >
                      <RefreshCw className="h-4 w-4" />
                      Reconnect Gmail
                    </Button>
                  </div>
                </CardFooter>
              )}
            </Card>
          </TabsContent>

          <TabsContent value="diagnostics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Connection Diagnostics</CardTitle>
                <CardDescription>Tools to diagnose and fix email connection issues</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>Troubleshooting Tips</AlertTitle>
                  <AlertDescription>
                    If you're experiencing connection issues, try these steps in order:
                    <ol className="list-decimal ml-5 mt-2 space-y-1">
                      <li>Reset the connection from the Connection Status tab</li>
                      <li>Reconnect your Gmail account</li>
                      <li>Check if your Google account has enabled third-party access</li>
                      <li>Contact support if issues persist</li>
                    </ol>
                  </AlertDescription>
                </Alert>

                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="errors">
                    <AccordionTrigger>Common Error Types</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-medium">Token Invalid</h4>
                          <p className="text-sm text-muted-foreground">
                            Your access token has expired or been revoked. Reconnect your Gmail
                            account.
                          </p>
                        </div>
                        <div>
                          <h4 className="font-medium">Rate Limiting</h4>
                          <p className="text-sm text-muted-foreground">
                            Too many requests to the Gmail API. Wait a few minutes and try again.
                          </p>
                        </div>
                        <div>
                          <h4 className="font-medium">Scope Changed</h4>
                          <p className="text-sm text-muted-foreground">
                            Gmail permissions have changed. Reconnect your Gmail account to accept
                            the latest permissions.
                          </p>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="circuit-breaker">
                    <AccordionTrigger>Circuit Breaker Information</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-3">
                        <p className="text-sm text-muted-foreground">
                          Inbox Zero uses a circuit breaker pattern to prevent multiple failed token
                          refresh attempts. If the system detects repeated failures, it will
                          temporarily stop trying to refresh your token to prevent additional
                          errors.
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Resetting your connection will clear the circuit breaker state and allow
                          token refresh to work again.
                        </p>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Security Information</CardTitle>
                <CardDescription>How your email tokens are secured</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-start gap-4">
                  <Shield className="h-10 w-10 text-primary flex-shrink-0" />
                  <div>
                    <h3 className="font-medium mb-1">Token Security</h3>
                    <p className="text-muted-foreground mb-3">
                      Inbox Zero employs industry-standard security measures to protect your email
                      access tokens:
                    </p>
                    <ul className="list-disc ml-5 space-y-2">
                      <li>Tokens are encrypted before being stored in our database</li>
                      <li>Your email content is never permanently stored on our servers</li>
                      <li>We use OAuth, which means we never see your actual email password</li>
                      <li>You can revoke access at any time from your Google account settings</li>
                    </ul>
                  </div>
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>How to Revoke Access</AlertTitle>
                  <AlertDescription>
                    You can revoke Inbox Zero's access to your Gmail account at any time by:
                    <ol className="list-decimal ml-5 mt-2 space-y-1">
                      <li>
                        Going to{' '}
                        <a
                          href="https://myaccount.google.com/permissions"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary hover:underline"
                        >
                          Google Account Permissions
                        </a>
                      </li>
                      <li>Finding "Inbox Zero" in the list of connected apps</li>
                      <li>Clicking "Remove Access"</li>
                    </ol>
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}
