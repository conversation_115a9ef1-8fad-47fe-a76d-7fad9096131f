/**
 * Lazy Loading Utilities
 *
 * Provides optimized utilities for component lazy loading with different
 * loading strategies to improve application performance.
 */

import { Loader2 } from 'lucide-react';
import type React from 'react';
import { lazy, Suspense, useEffect, useState } from 'react';

/**
 * Default loading component shown during lazy loading
 */
const DefaultLoadingComponent = () => (
  <div className="flex items-center justify-center h-full w-full min-h-[200px]">
    <Loader2 className="h-8 w-8 animate-spin text-primary" />
  </div>
);

/**
 * Options for customizing lazy loading behavior
 */
interface LazyLoadOptions {
  fallback?: React.ReactNode;
  errorBoundary?: React.ComponentType<any>;
  retry?: boolean;
  retryDelay?: number;
  timeout?: number;
}

/**
 * Standard lazy loading with Suspense
 * Use for components that should start loading immediately when imported
 */
export function lazyLoad<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
) {
  const {
    fallback = <DefaultLoadingComponent />,
    retry = true,
    retryDelay = 1000,
    timeout = 10000,
  } = options;

  const LazyComponent = lazy(() => {
    // Add retry logic for network failures
    if (retry) {
      return retryImport(importFunc, retryDelay, timeout);
    }
    return importFunc();
  });

  // Return a component that wraps the lazy-loaded one with Suspense
  return (props: React.ComponentProps<T>) => (
    <Suspense fallback={fallback}>
      <LazyComponent {...props} />
    </Suspense>
  );
}

/**
 * Deferred lazy loading - only starts loading after component is mounted
 * Use for low-priority components that aren't needed immediately
 */
export function deferredLazyLoad<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
) {
  const {
    fallback = <DefaultLoadingComponent />,
    retry = true,
    retryDelay = 1000,
    timeout = 10000,
  } = options;

  return (props: React.ComponentProps<T>) => {
    const [Component, setComponent] = useState<React.ComponentType<any> | null>(null);

    useEffect(() => {
      let isMounted = true;

      // Start loading after component mounts
      const loadComponent = async () => {
        try {
          // Add retry logic for network failures if enabled
          const module = retry
            ? await retryImport(importFunc, retryDelay, timeout)
            : await importFunc();

          // Only update state if component is still mounted
          if (isMounted) {
            setComponent(() => module.default);
          }
        } catch (error) {
          console.error('Error loading component:', error);
        }
      };

      loadComponent();

      return () => {
        isMounted = false;
      };
    }, [importFunc]);

    // Show loading indicator until component is loaded
    if (!Component) {
      return <>{fallback}</>;
    }

    // Render component once loaded
    return <Component {...props} />;
  };
}

/**
 * Retry a failed import with exponential backoff
 */
async function retryImport<T>(
  importFunc: () => Promise<T>,
  delay = 1000,
  timeout = 10000,
  maxRetries = 3
): Promise<T> {
  let retries = 0;
  const startTime = Date.now();

  // Create a function that will be called recursively
  const tryImport = async (): Promise<T> => {
    try {
      return await importFunc();
    } catch (error) {
      console.warn(`[LazyLoad] Import attempt ${retries + 1} failed:`, error);

      // Check if we've exceeded the timeout
      if (Date.now() - startTime > timeout) {
        console.error(`[LazyLoad] Import timed out after ${timeout}ms`);
        throw new Error(`Import timed out after ${timeout}ms`);
      }

      // Check if we've exceeded max retries
      if (retries >= maxRetries) {
        console.error(`[LazyLoad] Max retries (${maxRetries}) exceeded for import`);
        throw error;
      }

      // Exponential backoff
      const retryDelay = delay * 2 ** retries;

      // Wait before retrying
      await new Promise((resolve) => setTimeout(resolve, retryDelay));

      // Increment retry counter and try again
      retries++;
      return tryImport();
    }
  };

  return tryImport();
}
