/**
 * PrivateRoute Component
 *
 * Protects routes that require authentication.
 * Redirects to login page if user is not authenticated.
 */

import type React from 'react';
import { Redirect } from 'wouter';
import { useAuth } from '@/context/AuthContext';
import LoadingScreen from '../ui/LoadingScreen';

interface PrivateRouteProps {
  component: React.ComponentType<any>;
  [key: string]: any;
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({ component: Component, ...rest }) => {
  const { user, loading } = useAuth();

  // The loading screen should only show on initial app load when we don't know the auth state yet.
  // If we have a user object, we are authenticated, even if a background check is running.
  if (loading && !user) {
    return <LoadingScreen message="Verifying authentication..." />;
  }

  if (!user) {
    // To preserve the intended navigation path for post-login redirection,
    // we pass it as a query parameter.
    const redirectPath = encodeURIComponent(window.location.pathname + window.location.search);
    return <Redirect to={`/login?redirect=${redirectPath}`} />;
  }

  return <Component {...rest} />;
};

export default PrivateRoute;
