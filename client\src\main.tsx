import { createRoot } from 'react-dom/client';
import './index.css'; // Base styles
import App from './App';

// Start the authentication synchronization service
// authSyncManager.start(); // MOVED: This is now handled by AppInitializer.tsx

// The queryClient already has proper error handling and retry logic configured
// in ./lib/queryClient.ts, so we don't need to override it here

createRoot(document.getElementById('root')!).render(<App />);
