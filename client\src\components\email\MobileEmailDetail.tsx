import { format, formatDistanceToNow } from 'date-fns';
import { Archive, Clock, Trash2 } from 'lucide-react';
import type React from 'react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useEmailActions } from '@/hooks/use-email-actions';
import { useToast } from '@/hooks/use-toast';
import { processEmailHtml } from '@/utils/emailContentParser';
import type { Email } from '@/types/email';
import { CategoryBadge } from '../ui/badge-category';
import EmailReply from './EmailReply';
import './EmailContent.css';

interface MobileEmailDetailProps {
  email: Email;
  onClose: () => void;
}

const MobileEmailDetail: React.FC<MobileEmailDetailProps> = ({ email, onClose }) => {
  const { toast } = useToast();
  const { archiveEmail, isArchiving, trashEmail, isTrashing, snoozeEmail, isSnoozing } =
    useEmailActions();

  const handleArchive = () => {
    archiveEmail(email.messageId, {
      onSuccess: () => {
        toast({ title: 'Email archived' });
        onClose();
      },
    });
  };

  const handleTrash = () => {
    trashEmail(email.messageId, {
      onSuccess: () => {
        toast({ title: 'Email moved to trash' });
        onClose();
      },
    });
  };

  const handleSnooze = () => {
    // Default snooze time is 24 hours from now
    const snoozeUntil = new Date();
    snoozeUntil.setHours(snoozeUntil.getHours() + 24);
    snoozeEmail(
      { messageId: email.messageId, snoozeUntil },
      {
        onSuccess: () => {
          toast({ title: 'Email snoozed' });
          onClose();
        },
      }
    );
  };

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const getTimeDisplay = (dateString: string | Date | null) => {
    if (!dateString) return 'Unknown time';
    try {
      const date = typeof dateString === 'string' ? new Date(dateString) : dateString;

      // Check if date is valid
      if (Number.isNaN(date.getTime())) {
        return 'Invalid date';
      }

      const formattedTime = format(date, 'h:mm a');
      const relativeTime = formatDistanceToNow(date, { addSuffix: true });
      return `${formattedTime} (${relativeTime})`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date error';
    }
  };

  return (
    <div className="p-4">
      {/* Action buttons */}
      <div className="flex space-x-2 mb-4">
        <Button
          variant="outline"
          size="sm"
          className="flex-1"
          onClick={handleArchive}
          disabled={isArchiving}
        >
          <Archive className="h-4 w-4 mr-2" />
          Archive
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="flex-1"
          onClick={handleTrash}
          disabled={isTrashing}
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Delete
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="flex-1"
          onClick={handleSnooze}
          disabled={isSnoozing}
        >
          <Clock className="h-4 w-4 mr-2" />
          Snooze
        </Button>
      </div>

      {/* Email Content */}
      <div className="mb-4">
        <div className="flex space-x-2 mb-3">
          {email.categories &&
            Array.isArray(email.categories) &&
            email.categories.map((category) => (
              <CategoryBadge key={category} category={category} />
            ))}
          <span className="text-xs text-muted-foreground flex items-center">
            <Clock className="h-3 w-3 mr-0.5" />
            {getTimeDisplay(email.receivedAt)}
          </span>
        </div>

        <div className="flex items-center mb-4">
          <Avatar className="h-8 w-8">
            <AvatarFallback>{email.sender ? getInitials(email.sender) : '?'}</AvatarFallback>
          </Avatar>
          <div className="ml-2">
            <div className="flex items-center">
              <span className="font-medium text-sm text-foreground">
                {email.sender || 'Unknown sender'}
              </span>
            </div>
            <div className="text-xs text-muted-foreground">
              {email.senderEmail || email.sender || 'No email address'}
            </div>
          </div>
        </div>
      </div>

      <div className="bg-muted rounded-lg p-3 mb-4 border border-border">
        <div className="text-xs text-muted-foreground mb-1 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-3 w-3 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <title>Info</title>
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          AI Summary
        </div>
        <p className="text-xs text-foreground dark:text-foreground">
          {email.summary || 'Loading summary...'}
        </p>
      </div>

      <div className="prose prose-sm prose-invert max-w-none text-foreground mb-4">
        <div className="email-content-container relative w-full max-w-full overflow-hidden rounded-md border border-border p-3">
          {email.htmlContent ? (
            // Render HTML content if available
            // biome-ignore lint/security/noDangerouslySetInnerHtml: Content is sanitized by processEmailHtml function
            <div
              className="email-html-content"
              dangerouslySetInnerHTML={{
                __html: processEmailHtml(email.htmlContent),
              }}
            />
          ) : email.originalContent ? (
            // Fall back to original content with line breaks and minimal processing
            // biome-ignore lint/security/noDangerouslySetInnerHtml: Content is sanitized by processEmailHtml function
            <div
              className="email-html-content"
              dangerouslySetInnerHTML={{
                __html: processEmailHtml(email.originalContent.replace(/\n/g, '<br />')),
              }}
            />
          ) : (
            // Show loading skeleton if no content is available
            <div>
              <Skeleton className="h-3 w-full mb-2 bg-muted-foreground/20" />
              <Skeleton className="h-3 w-full mb-2 bg-muted-foreground/20" />
              <Skeleton className="h-3 w-3/4 mb-3 bg-muted-foreground/20" />
              <Skeleton className="h-3 w-full mb-2 bg-muted-foreground/20" />
              <Skeleton className="h-3 w-full mb-2 bg-muted-foreground/20" />
              <Skeleton className="h-3 w-5/6 mb-2 bg-muted-foreground/20" />
            </div>
          )}
        </div>
      </div>

      <EmailReply email={email} />
    </div>
  );
};

export default MobileEmailDetail;
