import { fireEvent, render, screen } from '@testing-library/react';
import EmailItem from '@/components/email/EmailItem';
import type { Email } from '@/types/email';
import '@testing-library/jest-dom';

const sampleEmail: Email = {
  id: 1,
  sender: 'Bob',
  subject: 'Hello',
  snippet: 'Body preview',
  receivedAt: new Date().toISOString(),
  read: false,
} as unknown as Email;

describe('Feature/EmailItem component', () => {
  it('displays sender and subject', () => {
    render(
      <EmailItem email={sampleEmail} isSelected={false} onClick={() => {}} />
    );
    expect(screen.getByText('Bob')).toBeInTheDocument();
    expect(screen.getByText('Hello')).toBeInTheDocument();
  });

  it('invokes onClick callback when clicked', () => {
    const handleClick = jest.fn();
    render(<EmailItem email={sampleEmail} isSelected={false} onClick={handleClick} />);
    fireEvent.click(screen.getByTestId('email-item'));
    expect(handleClick).toHaveBeenCalled();
  });

  it('shows unread indicator when email is unread', () => {
    render(<EmailItem email={sampleEmail} isSelected={false} onClick={() => {}} />);
    // unread dot has title="Unread email"
    expect(screen.getByTitle('Unread email')).toBeInTheDocument();
  });

  it('hides unread indicator when read', () => {
    const readEmail = { ...sampleEmail, read: true } as Email;
    render(<EmailItem email={readEmail} isSelected={false} onClick={() => {}} />);
    expect(screen.queryByTitle('Unread email')).not.toBeInTheDocument();
  });
}); 