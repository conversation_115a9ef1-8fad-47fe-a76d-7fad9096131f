/**
 * CSRF Middleware Test Suite
 *
 * This file contains tests for the CSRF protection middleware implemented
 * using the `csrf-sync` library.
 */
import type { Request, Response } from 'express';
import express from 'express';
import session from 'express-session';
import request from 'supertest';

// Mock the logger before importing the middleware
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};
jest.mock('@server/lib/logger', () => ({
  __esModule: true,
  default: mockLogger,
}));

// Now, import the middleware
import { initializeCsrfProtection } from '@server/middleware/csrf';

// We need a dummy secret for express-session
const SESSION_SECRET = 'test-secret';

describe('CSRF Protection Middleware (csrf-sync)', () => {
  let app: express.Express;
  // Using `any` to avoid strict type mismatch between supertest Agent/Test versions
  let agent: any;

  beforeEach(() => {
    app = express();

    // `csrf-sync` requires a session middleware.
    app.use(
      session({
        secret: SESSION_SECRET,
        resave: false,
        saveUninitialized: true,
      })
    );

    // Body parser to read POST bodies
    app.use(express.json());

    // Initialize the CSRF protection
    initializeCsrfProtection(app);

    // Create a dummy protected route for testing
    app.post('/protected-route', (_req, res) => {
      res.status(200).json({ message: 'Success' });
    });

    // Create a skipped route for testing
    app.post('/api/auth/register', (_req, res) => {
      res.status(200).json({ message: 'Registered' });
    });

    agent = request.agent(app);
  });

  it('should return a CSRF token from the /api/auth/csrf endpoint', async () => {
    const response = await agent.get('/api/auth/csrf');
    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('csrfToken');
    expect(typeof response.body.csrfToken).toBe('string');
  });

  it('should allow access to a protected route with a valid CSRF token', async () => {
    // 1. Get a token
    const tokenRes = await agent.get('/api/auth/csrf');
    const csrfToken = tokenRes.body.csrfToken;

    // 2. Make a request to the protected route with the token
    const response = await agent
      .post('/protected-route')
      .set('x-csrf-token', csrfToken)
      .send({ data: 'test' });

    expect(response.status).toBe(200);
    expect(response.body.message).toBe('Success');
  });

  it('should block access to a protected route without a CSRF token', async () => {
    const response = await agent.post('/protected-route').send({ data: 'test' });

    expect(response.status).toBe(403);
    expect(response.body.code).toBe('CSRF_ERROR');
  });

  it('should block access to a protected route with an invalid CSRF token', async () => {
    const response = await agent
      .post('/protected-route')
      .set('x-csrf-token', 'invalid-token')
      .send({ data: 'test' });

    expect(response.status).toBe(403);
    expect(response.body.code).toBe('CSRF_ERROR');
  });

  it('should skip CSRF protection for specified routes', async () => {
    // This route should be accessible without a CSRF token because it's in the skip list
    const response = await agent.post('/api/auth/register').send({ user: 'new-user' });

    expect(response.status).toBe(200);
    expect(response.body.message).toBe('Registered');
  });

  it('should not skip CSRF protection for GET requests (default behavior of csrf-sync)', async () => {
    // GET requests are ignored by default, so this should just work
    const getApp = express();
    initializeCsrfProtection(getApp);
    getApp.get('/protected-get', (_req: any, res: any) => res.status(200).send('OK'));

    const res = await (request as any)(getApp).get('/protected-get');
    expect(res.status).toBe(200);
  });
});
