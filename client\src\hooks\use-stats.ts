import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/apiClient';
import type { Stats } from '@/types/email';

/**
 * Custom hook for retrieving user email statistics.
 *
 * This hook leverages React Query for caching, retries, and error handling
 * while using the centralized `apiClient` for consistent API communication.
 */
export function useStats() {
  const {
    data: stats,
    isLoading: isStatsLoading,
    error: statsError,
    refetch: refetchStats,
  } = useQuery<Stats>({
    queryKey: ['/api/stats'],
    queryFn: () => apiClient.get<Stats>('/api/stats'),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
  });

  return {
    stats: stats ?? null,
    isStatsLoading,
    statsError,
    refetchStats,
  };
}
