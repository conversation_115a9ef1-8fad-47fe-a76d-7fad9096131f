console.log("************ SERVER IS RUNNING NEW CODE ************");
// Load environment variables first, before any other imports

import path from 'node:path';
import { fileURLToPath } from 'node:url';
import dotenv from 'dotenv';

// Get current file directory in ES module
const _filename = fileURLToPath(import.meta.url);
const _dirname = path.dirname(_filename);

// Load .env file from the root directory (more reliable than tsx --env-file)
dotenv.config({ path: path.join(_dirname, '..', '.env') });

// Only set NODE_ENV to development if it's not set at all (not even by cross-env)
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'development';
}

// Import logger after NODE_ENV is set
import logger, { loggerContextMiddleware } from './lib/logger';
import { patchDrizzleArrayMapping } from './lib/arrayFieldFix';

// Apply runtime patch for Drizzle array field mapping issue with RLS
patchDrizzleArrayMapping();

logger.info(`[STARTUP] Using NODE_ENV: ${process.env.NODE_ENV}`);

// Validate DATABASE_URL is present
if (!process.env.DATABASE_URL) {
  logger.error(
    '[ERROR] DATABASE_URL environment variable is not set. Please check your .env file contains the Supabase connection string'
  );
  process.exit(1);
} else {
  logger.info('[STARTUP] DATABASE_URL is configured');
}

import type { Server } from 'node:http';
import compression from 'compression';
import express, { type NextFunction, type Request, type Response } from 'express';
import { initializeAuth } from './auth';
import { initializeEnvironmentValidator } from './lib/environmentValidator';
import { registerRoutes } from './routes';
import startServer from './serverStartup';
import { initializeRedis } from './services/redis';
import { startDatabaseHealthMonitoring } from './utils/databaseHealthMonitor';
import { shutdown } from './utils/gracefulShutdown';
import { globalErrorHandler } from './utils/integrate';

let httpServer: Server;

// Define PORT
const PORT = process.env.PORT ? Number.parseInt(process.env.PORT, 10) : 5000;

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(compression());

// Add a simple health check route early
app.get('/api/health', (_req, res) => {
  res.json({
    status: 'ok',
    message: 'InboxZeroAI server is running',
    mode: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
  });
});

// Trust proxy to get correct protocol (HTTP vs HTTPS) when behind a proxy
app.set('trust proxy', true);

// Add logger context middleware to track requests
app.use(loggerContextMiddleware);

(async () => {
  logger.info(`[STARTUP] Running in ${process.env.NODE_ENV || 'development'} mode`);

  // Initialize environment validator after .env file is loaded
  initializeEnvironmentValidator();

  // Start the server early to allow Vite to hook into it in development
  httpServer = await startServer(app, PORT);

  // --- Middleware Order is CRITICAL here ---

  // 1. Initialize core services that don't depend on the database or auth
  // Firebase will be initialized in the auth module
  initializeRedis();

  // Initialize AI models for categorization
  try {
    const { initializeAllModels } = await import('./services/categorization');
    initializeAllModels(); // Fire-and-forget, let it load in the background
    logger.info('[STARTUP] AI model initialization started...');
  } catch (error) {
    logger.warn('[WARN] Failed to start AI model initialization:', {
      error: error instanceof Error ? error.message : String(error),
    });
  }

  // Initialize scheduled tasks for data retention and privacy
  try {
    const { scheduledTasksService } = await import('./services/scheduledTasks');
    scheduledTasksService.start();
    logger.info('[STARTUP] Scheduled tasks service started...');
  } catch (error) {
    logger.warn('[WARN] Failed to start scheduled tasks service:', {
      error: error instanceof Error ? error.message : String(error),
    });
  }

  // Initialize and start task queue processor for background AI processing
  try {
    const { initializeTaskQueue, startTaskQueueProcessor } = await import('./services/taskQueue');
    const { initializeEmailProcessors } = await import('./services/emailProcessor');
    
    // Initialize task queue configuration
    initializeTaskQueue({
      isProduction: process.env.NODE_ENV === 'production',
      isQuickStart: false,
    });
    
    // Initialize email processors (register task handlers)
    initializeEmailProcessors();
    
    // Start the task queue processor
    await startTaskQueueProcessor();
    logger.info('[STARTUP] Task queue processor started successfully');
  } catch (error) {
    logger.error('[ERROR] Failed to start task queue processor:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    // Don't exit - the system can work without background processing for debugging
  }

  // Initialize automatic email sync service
  try {
    const { startAutomaticEmailSync } = await import('./services/automaticEmailSync');
    await startAutomaticEmailSync();
    logger.info('[STARTUP] Automatic email sync service started...');
  } catch (error) {
    logger.warn('[WARN] Failed to start automatic email sync service:', {
      error: error instanceof Error ? error.message : String(error),
    });
  }

  // Initialize TokenService with OAuth2 configuration
  try {
    const { default: tokenService } = await import('./services/tokenService');
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
    const redirectUri = process.env.GOOGLE_REDIRECT_URI;

    if (!clientId || !clientSecret || !redirectUri) {
      throw new Error('Missing required OAuth2 environment variables: GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_REDIRECT_URI');
    }

    tokenService.initialize({
      clientId,
      clientSecret,
      redirectUri,
    });
    logger.info('[STARTUP] TokenService initialized successfully');
  } catch (error) {
    logger.error('[ERROR] Failed to initialize TokenService:', {
      error: error instanceof Error ? error.message : String(error),
    });
    // TokenService is critical for authentication, so we should exit
    process.exit(1);
  }

  // 2. Initialize database connection
  try {
    // The new `getDb` function handles its own initialization and health monitoring.
    // We can perform a test connection here to ensure it's ready.
    const { testConnection } = await import('./db');
    await testConnection();
    logger.info('[STARTUP] Database connection verified successfully');
    startDatabaseHealthMonitoring();
    logger.info('[STARTUP] Database health monitoring started');
  } catch (dbError) {
    logger.error('[ERROR] Failed to connect to the database during startup:', dbError);
    // In a real production scenario, you might want to exit here.
    // process.exit(1);
  }

  // 3. Initialize authentication, which sets up sessions and CSRF protection.
  // This MUST come before API routes that need protection.
  try {
    await initializeAuth(app);
    logger.info('[STARTUP] Authentication initialized successfully');
  } catch (error) {
    logger.fatal('[FATAL] Failed to initialize authentication. Server cannot start.', {
      error: error instanceof Error ? error.message : String(error),
    });
    process.exit(1);
  }

  // 4. Register API routes. These will now be protected by the auth middleware.
  // This MUST come BEFORE the frontend serving middleware.
  logger.info('[STARTUP] Registering API routes...');
  await registerRoutes(app);
  logger.info('[STARTUP] Routes registered successfully.');

  // 5. Serve static client assets in production
  if (process.env.NODE_ENV === 'production') {
    const { attachStaticHandlers } = await import('./static.js');
    attachStaticHandlers(app);
  }

  // 6. Register final error handlers.
  try {
    const { gmailErrorMiddleware } = await import('./utils/gmailErrorHandler');

    // Register error handlers. They are executed in order.
    // We wrap them in anonymous functions to ensure Express recognizes
    // them as error handlers via their 4-argument signature.
    app.use((error: any, req: Request, res: Response, next: NextFunction) => {
      gmailErrorMiddleware(error, req, res, next);
    });

    app.use((error: any, req: Request, res: Response, next: NextFunction) => {
      globalErrorHandler(error, req, res, next);
    });

    logger.info('[STARTUP] ✅ Global error handlers registered');
  } catch (error) {
    logger.error('[ERROR] Failed to setup global error handlers:', error);
  }

  // Setup signal handlers for graceful shutdown
  const signals = ['SIGINT', 'SIGTERM'];
  signals.forEach((signal) => {
    process.on(signal, () => {
      shutdown(httpServer, 0, signal);
    });
  });

  process.on('unhandledRejection', (reason, promise) => {
    const errorMessage = reason instanceof Error ? reason.message : String(reason);
    const errorCode = (reason as any)?.code;

    // Handle database connection errors more gracefully
    if (
      errorCode === 'CONNECTION_DESTROYED' ||
      errorMessage.includes('CONNECTION_DESTROYED') ||
      errorMessage.includes('connection terminated') ||
      errorMessage.includes('server closed the connection') ||
      errorMessage.includes('Health check timeout') ||
      errorMessage.includes('Database connection test timeout') ||
      errorMessage.includes('Database connection is not available') ||
      errorMessage.includes('Cannot read properties of null')
    ) {
      logger.warn('Database connection rejection (expected during slow connections/shutdown/restart):', {
        message: errorMessage,
        code: errorCode,
      });
      
      // Don't shutdown the server for expected database connection errors
      // These are common during database restarts, network issues, or high load
      return;
    }

    // For other critical errors, still shutdown gracefully
    logger.error('CRITICAL: UNHANDLED REJECTION DETECTED');
    logger.error('======================================');
    logger.error('Reason:', reason);
    logger.error('Promise:', promise);
    logger.info('[SHUTDOWN] Received unhandledRejection. Closing server gracefully.');
    
    shutdown(httpServer, 1, 'unhandledRejection');
  });

  process.on('uncaughtException', (error) => {
    console.error('CRITICAL: UNCAUGHT EXCEPTION DETECTED');
    console.error('======================================');
    console.error('Error:', error);
    console.error('======================================');
    shutdown(httpServer, 1, 'uncaughtException');
  });
})();

export { app, httpServer };
