/**
 * Stats Routes
 *
 * Provides endpoints for user statistics and analytics
 * Enhanced implementation with optimized queries and better error handling
 */

import { and, count, eq, gt } from 'drizzle-orm';
import { type Request, type Response, Router } from 'express';
import { emails } from '../../shared/schema';
import { getDb } from '../db';
import { getEnvVar } from '../lib/environmentValidator';
import logger from '../lib/logger';
import { catchAsync } from '../utils/errorHandler';

// Create router
const router = Router();

/**
 * Get user statistics
 * GET /api/stats
 */
router.get(
  '/',
  catchAsync(async (req: Request, res: Response) => {
    // Check for development-only auth bypass
    const userId =
      String(getEnvVar('BYPASS_AUTHENTICATION_FOR_STATS')) === 'true'
        ? (req.query.userId as string) || req.session?.userId || '1'
        : req.session?.userId;

    if (!userId) {
      logger.warn('Stats API access attempted without authentication');
      return res.status(401).json({
        success: false,
        message: 'Unauthorized - please log in',
      });
    }

    const userIdNum = Number(userId);
    if (Number.isNaN(userIdNum)) {
      logger.warn(`Invalid user ID format: ${userId}`);
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID format',
      });
    }

    try {
      const db = await getDb();
      if (String(getEnvVar('DEBUG_STATS_API')) === 'true') {
        logger.info(`[DEBUG] Fetching stats for user ${userId}`);
      } else {
        logger.debug(`Fetching stats for user ${userId}`);
      }

      // Optimize by running queries in parallel with correct field types
      const [totalEmailsResult, readEmailsResult, archivedEmailsResult, repliedEmailsResult] =
        await Promise.all([
          // Get email counts
          db
            .select({ count: count() })
            .from(emails)
            .where(eq(emails.userId, userIdNum)),

          db
            .select({ count: count() })
            .from(emails)
            .where(and(eq(emails.userId, userIdNum), eq(emails.isRead, true))),

          db
            .select({ count: count() })
            .from(emails)
            .where(and(eq(emails.userId, userIdNum), eq(emails.isArchived, true))),

          db
            .select({ count: count() })
            .from(emails)
            .where(and(eq(emails.userId, userIdNum), eq(emails.isReplied, true))),
        ]);

      // Use receivedAt field for recent activity as updatedAt doesn't exist
      const [lastDayActivityResult, lastWeekActivityResult, lastMonthActivityResult] =
        await Promise.all([
          db
            .select({ count: count() })
            .from(emails)
            .where(
              and(
                eq(emails.userId, userIdNum),
                gt(emails.receivedAt, new Date(Date.now() - 24 * 60 * 60 * 1000))
              )
            ),

          db
            .select({ count: count() })
            .from(emails)
            .where(
              and(
                eq(emails.userId, userIdNum),
                gt(emails.receivedAt, new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
              )
            ),

          db
            .select({ count: count() })
            .from(emails)
            .where(
              and(
                eq(emails.userId, userIdNum),
                gt(emails.receivedAt, new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
              )
            ),
        ]);

      // For categories, we need to extract from the categories array field
      // Since we can't directly group by array elements in SQL, we'll query all emails
      // and process the categories in JavaScript
      const emailsWithCategories = await db
        .select({
          id: emails.id,
          categories: emails.categories,
          priority: emails.priority,
        })
        .from(emails)
        .where(eq(emails.userId, userIdNum));

      // Process category counts
      const categoryCounts: Record<string, number> = {};
      const expectedCategories = [
        'updates',
        'promotions',
        'social',
        'purchases',
        'finance',
        'personal',
        'work',
      ];

      // Initialize all expected categories to 0
      expectedCategories.forEach((category) => {
        categoryCounts[category] = 0;
      });

      // Count occurrences of each category
      emailsWithCategories.forEach((email) => {
        if (email.categories && Array.isArray(email.categories)) {
          email.categories.forEach((category) => {
            if (category) {
              // Increment if it exists, otherwise initialize to 1
              categoryCounts[category] = (categoryCounts[category] || 0) + 1;
            }
          });
        }
      });

      // Process priority counts
      const priorityCounts: Record<string, number> = {
        high: 0,
        medium: 0,
        low: 0,
      };

      // Count occurrences of each priority
      emailsWithCategories.forEach((email) => {
        if (email.priority && Object.hasOwn(priorityCounts, email.priority)) {
          priorityCounts[email.priority]++;
        }
      });

      // Calculate response times (placeholder for now)
      // In a real implementation, this would calculate from actual response time data
      const totalEmails = Number(totalEmailsResult[0]?.count || 0);
      const averageResponseTime = totalEmails > 0 ? 7200 : 0; // Default to 2 hours if we have emails

      // Calculate streak days (placeholder for now)
      // In a real implementation, this would calculate from continuous usage patterns
      const streakDays = lastDayActivityResult[0]?.count ? 1 : 0;

      // Construct stats object matching the exact shape expected by the frontend
      const stats = {
        totalEmails: totalEmails,
        readEmails: Number(readEmailsResult[0]?.count || 0),
        archivedEmails: Number(archivedEmailsResult[0]?.count || 0),
        repliedEmails: Number(repliedEmailsResult[0]?.count || 0),
        emailsProcessed: totalEmails, // assuming all emails are processed
        categoryCounts: categoryCounts,
        priorityCounts: priorityCounts,
        lastDayActivity: Number(lastDayActivityResult[0]?.count || 0),
        lastWeekActivity: Number(lastWeekActivityResult[0]?.count || 0),
        lastMonthActivity: Number(lastMonthActivityResult[0]?.count || 0),
        streakDays: streakDays,
        averageResponseTime: averageResponseTime,
      };

      if (String(getEnvVar('DEBUG_STATS_API')) === 'true') {
        logger.info(`[DEBUG] Successfully fetched stats for user ${userId}. Stats:`, stats);
      } else {
        logger.debug(`Successfully fetched stats for user ${userId}`);
      }

      // Return the stats with a 200 OK status to ensure React Query recognizes the success
      return res.status(200).json(stats);
    } catch (error) {
      // Enhanced error logging with details
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      logger.error(`Error fetching stats for user ${userId}`, {
        error: errorMessage,
        stack: errorStack,
        userId,
      });

      // Return a more descriptive error with appropriate HTTP status
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch statistics',
        error: errorMessage,
        errorType: error instanceof Error ? error.constructor.name : 'Unknown',
        timestamp: new Date().toISOString(),
      });
    }
  })
);

export default router;
