import 'dotenv/config';
import { eq } from 'drizzle-orm';
import prompts from 'prompts';
import { closeDatabase, db } from '../server/db';
import logger from '../server/lib/logger';
import { users } from '../shared/schema';

const setAdmin = async () => {
  const emailArg = process.argv[2];

  if (!emailArg) {
    logger.error('Please provide a user email address as an argument.');
    process.exit(1);
  }

  logger.info(`Searching for user with email: ${emailArg}`);

  try {
    const user = await db.select().from(users).where(eq(users.email, emailArg)).limit(1);

    if (!user.length) {
      logger.error(`User with email "${emailArg}" not found.`);
      return;
    }

    const targetUser = user[0];
    logger.info('Found user:', {
      id: targetUser.id,
      email: targetUser.email,
      currentRole: targetUser.role,
    });

    const response = await prompts({
      type: 'confirm',
      name: 'confirm',
      message: `Are you sure you want to set this user as an ADMIN? (current role: ${targetUser.role})`,
      initial: false,
    });

    if (!response.confirm) {
      logger.info('Operation cancelled by user.');
      return;
    }

    logger.info("Updating user role to 'admin'...");
    const result = await db
      .update(users)
      .set({ role: 'admin' })
      .where(eq(users.id, targetUser.id))
      .returning({
        id: users.id,
        email: users.email,
        role: users.role,
      });

    if (result.length > 0) {
      logger.info('✅ Successfully updated user:', result[0]);
    } else {
      logger.error('Failed to update user. The user might have been deleted during the operation.');
    }
  } catch (error) {
    logger.error('An error occurred during the operation:', error);
  } finally {
    logger.info('Closing database connection.');
    await closeDatabase();
  }
};

setAdmin();
