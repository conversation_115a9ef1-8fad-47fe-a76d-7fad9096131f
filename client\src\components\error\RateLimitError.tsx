import { AlertTriangle, Clock } from 'lucide-react';
import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';

type RateLimitErrorProps = {
  message: string;
  retryAfter?: number;
  onRetry?: () => void;
};

export function RateLimitError({ message, retryAfter = 60, onRetry }: RateLimitErrorProps) {
  const [timeLeft, setTimeLeft] = React.useState(retryAfter);
  const [progressValue, setProgressValue] = React.useState(0);

  React.useEffect(() => {
    if (!retryAfter) return;

    setTimeLeft(retryAfter);
    const interval = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });

      setProgressValue((100 * (retryAfter - timeLeft + 1)) / retryAfter);
    }, 1000);

    return () => clearInterval(interval);
  }, [retryAfter, timeLeft]);

  return (
    <div className="mx-auto max-w-lg p-4">
      <Alert
        variant="destructive"
        className="mb-4 bg-destructive/10 border-destructive text-destructive dark:bg-destructive/20"
      >
        <AlertTriangle className="h-5 w-5" />
        <AlertTitle className="font-semibold">Rate Limit Exceeded</AlertTitle>
        <AlertDescription className="text-sm">{message}</AlertDescription>
      </Alert>

      {timeLeft > 0 && (
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>Please wait {timeLeft} seconds before trying again</span>
          </div>
          <Progress value={progressValue} className="h-2" />
        </div>
      )}

      {timeLeft === 0 && onRetry && (
        <Button onClick={onRetry} className="mt-4 w-full">
          Try Again
        </Button>
      )}
    </div>
  );
}

export default RateLimitError;
