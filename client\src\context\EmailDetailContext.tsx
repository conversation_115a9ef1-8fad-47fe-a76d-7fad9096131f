import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  createContext,
  type ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { usePermission } from '@/hooks/use-permission';
import { useToast } from '@/hooks/use-toast';
import apiClient from '@/lib/apiClient';
import type { Email } from '@/types/email';
import { useAuth } from './AuthContext';

interface EmailDetailContextType {
  selectedEmail: Email | null;
  isLoading: boolean;
  error: Error | null;
  selectEmail: (messageId: string | null) => void;
  archiveSelectedEmail: () => Promise<void>;
  sendReplyToSelected: (content: string) => Promise<void>;
  regenerateReplyForSelected: (tone: string) => Promise<string>;
  regenerateSummaryForSelected: () => Promise<string>;
  explainCategoriesForSelected: () => Promise<string>;
  isArchiving: boolean;
  isSendingReply: boolean;
  isRegeneratingReply: boolean;
  isRegeneratingSummary: boolean;
  isExplainingCategories: boolean;
}

const EmailDetailContext = createContext<EmailDetailContextType | undefined>(undefined);

// Use named function components to avoid Fast Refresh incompatibility
export function EmailDetailProvider({ children }: { children: ReactNode }) {
  const [selectedEmailMessageId, setSelectedEmailMessageId] = useState<string | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  // Determine if we're in test mode based on the email
  const isTestMode =
    user && (user.email === '<EMAIL>' || user.email === '<EMAIL>');
  const { handlePermissionError } = usePermission();

  // Use ref to track processing state instead of window pollution
  const processingEmailIds = useRef<Set<string>>(new Set());

  const updateEmailInCache = (messageId: string, newProps: Partial<Email>) => {
    const queryKey = ['emails', 'detail', messageId];
    // Optimistically update the email detail query
    queryClient.setQueryData<Email>(queryKey, (oldData) =>
      oldData ? { ...oldData, ...newProps } : undefined
    );

    // Optimistically update the email in any list queries
    queryClient
      .getQueryCache()
      .findAll({ queryKey: ['emails', user?.id] })
      .forEach((query) => {
        const previousData = queryClient.getQueryData<any>(query.queryKey);
        if (previousData?.emails) {
          queryClient.setQueryData(query.queryKey, {
            ...previousData,
            emails: previousData.emails.map((email: Email) =>
              email.messageId === messageId ? { ...email, ...newProps } : email
            ),
          });
        }
      });
  };

  // If email doesn't exist in query cache yet, try to find it
  const getSelectedEmailFromCache = useCallback(
    (messageId: string): Email | null => {
      if (!messageId) return null;

      // Try to get from cache - search through all email queries for this user
      const queries = queryClient.getQueryCache().findAll({
        queryKey: ['emails', user?.id || 'no-user'],
        exact: false,
      });

      for (const query of queries) {
        const cachedData = query.state.data as any;
        if (cachedData?.emails && Array.isArray(cachedData.emails)) {
          const email = cachedData.emails.find((e: Email) => e.messageId === messageId);
          if (email) {
            console.log('[EmailDetail] Found email in cache:', { messageId, emailId: email.id });
            return email;
          }
        }
      }

      console.log('[EmailDetail] Email not found in cache:', {
        messageId,
        queriesFound: queries.length,
      });
      return null;
    },
    [queryClient, user?.id]
  );

  // Fetch selected email details
  const {
    data: selectedEmail,
    isLoading,
    error,
  } = useQuery<Email | null>({
    queryKey: ['emails', 'detail', selectedEmailMessageId],
    queryFn: async () => {
      if (!selectedEmailMessageId) return null;

      // First try to get from cache
      const cachedEmail = getSelectedEmailFromCache(selectedEmailMessageId);
      if (cachedEmail) return cachedEmail;

      // If not in cache, fetch from API using message_id
      return apiClient.get<Email>(
        `/api/emails/by-message-id/${encodeURIComponent(selectedEmailMessageId)}`
      );
    },
    enabled: !!selectedEmailMessageId && !isTestMode,
    retry: 1,
    staleTime: 0, // Always consider data stale to ensure fresh fetches
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
  });

  const { mutateAsync: archiveEmailMutation, isPending: isArchiving } = useMutation<
    void,
    Error,
    number
  >({
    mutationFn: (id) => apiClient.post(`/api/emails/${id}/archive`),
    onSuccess: () => {
      toast({
        title: 'Email archived',
        description: 'The email has been successfully archived.',
      });
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['stats'] });
    },
  });

  const { mutateAsync: sendReplyMutation, isPending: isSendingReply } = useMutation<
    void,
    Error,
    { messageId: string; content: string }
  >({
    mutationFn: ({ messageId, content }) => apiClient.post(`/api/emails/by-message-id/${encodeURIComponent(messageId)}/reply`, { content }),
    onSuccess: (_, { messageId }) => {
      toast({
        title: 'Reply sent',
        description: 'Your reply has been successfully sent.',
      });
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['stats'] });
      queryClient.invalidateQueries({ queryKey: ['emails', 'detail', messageId] });
    },
  });

  const { mutateAsync: regenerateReplyMutation, isPending: isRegeneratingReply } = useMutation<
    string,
    Error,
    { messageId: string; tone: string }
  >({
    mutationFn: ({ messageId, tone }) => apiClient.post(`/api/ai/regenerate-reply/by-message-id/${encodeURIComponent(messageId)}`, { tone }),
  });

  const { mutateAsync: regenerateSummaryMutation, isPending: isRegeneratingSummary } = useMutation<
    string,
    Error,
    string,
    { previousDetailEmail: Email | null | undefined }
  >({
    mutationFn: (messageId: string) => apiClient.post(`/api/ai/regenerate-summary/by-message-id/${encodeURIComponent(messageId)}`),
    onMutate: async (messageId) => {
      await queryClient.cancelQueries({ queryKey: ['emails', 'detail', messageId] });
      const previousDetailEmail = queryClient.getQueryData<Email>(['emails', 'detail', messageId]);
      updateEmailInCache(messageId, { summary: 'Processing...' });
      return { previousDetailEmail };
    },
    onSuccess: (newSummary, messageId) => {
      toast({
        title: 'Summary regenerated',
        description: 'The AI summary has been updated successfully.',
      });
      updateEmailInCache(messageId, { summary: newSummary });
    },
    onError: (error, messageId, context) => {
      console.error(`Error regenerating summary for email ${messageId}:`, error);
      toast({
        title: 'Summary regeneration failed',
        description: 'Failed to update the email summary. Please try again.',
        variant: 'destructive',
      });
      if (context?.previousDetailEmail) {
        queryClient.setQueryData(['emails', 'detail', messageId], context.previousDetailEmail);
      } else {
        updateEmailInCache(messageId, { summary: 'Error generating summary' });
      }
    },
  });

  const { mutateAsync: explainCategoriesMutation, isPending: isExplainingCategories } = useMutation<
    string,
    Error,
    string
  >({
    mutationFn: (messageId: string) => apiClient.post(`/api/ai/explain-categories/by-message-id/${encodeURIComponent(messageId)}`),
  });

  // Function to select an email
  const selectEmail = useCallback(
    (messageId: string | null) => {
      console.log('[EmailDetail] Selecting email:', {
        messageId,
        isTestMode,
        userId: user?.id,
        userEmail: user?.email,
      });

      setSelectedEmailMessageId(messageId);

      // If we're in test mode or we have the email in cache, no need to fetch
      if (isTestMode || (messageId && getSelectedEmailFromCache(messageId))) {
        console.log('[EmailDetail] Using cached email or test mode');
        return;
      }

      // Otherwise, we'll let the query fetch the email
      console.log('[EmailDetail] Will fetch email from API');
    },
    [isTestMode, getSelectedEmailFromCache, user]
  );

  // Archive the selected email
  const archiveSelectedEmail = useCallback(async () => {
    if (!selectedEmail?.id) {
      toast({
        title: 'No email selected',
        description: 'Please select an email to archive.',
        variant: 'destructive',
      });
      return;
    }

    try {
      await archiveEmailMutation(selectedEmail.id);
    } catch (error) {
      // Check if it's a permission error
      if (error instanceof Error) {
        const wasHandled = await handlePermissionError(error.message);
        if (wasHandled) {
          // If permission handling was initiated, show a message
          toast({
            title: 'Additional permissions needed',
            description: 'Please complete the permission request to archive emails.',
          });
          return;
        }
      }

      // Re-throw if not handled
      throw error;
    }
  }, [selectedEmail?.id, archiveEmailMutation, handlePermissionError, toast]);

  // Send a reply to the selected email
  const sendReplyToSelected = useCallback(
    async (content: string) => {
      if (!selectedEmail?.messageId) {
        toast({
          title: 'No email selected',
          description: 'Please select an email to reply to.',
          variant: 'destructive',
        });
        return;
      }

      try {
        await sendReplyMutation({ messageId: selectedEmail.messageId, content });
      } catch (error) {
        // Check if it's a permission error
        if (error instanceof Error) {
          const wasHandled = await handlePermissionError(error.message);
          if (wasHandled) {
            // If permission handling was initiated, show a message
            toast({
              title: 'Additional permissions needed',
              description: 'Please complete the permission request to send replies.',
            });
            return;
          }
        }

        // Re-throw if not handled
        throw error;
      }
    },
    [selectedEmail?.messageId, sendReplyMutation, handlePermissionError, toast]
  );

  // Regenerate a reply for the selected email
  const regenerateReplyForSelected = useCallback(
    async (tone: string): Promise<string> => {
      if (!selectedEmail?.messageId) {
        toast({
          title: 'No email selected',
          description: 'Please select an email to generate a reply for.',
          variant: 'destructive',
        });
        return '';
      }

      return await regenerateReplyMutation({ messageId: selectedEmail.messageId, tone });
    },
    [selectedEmail?.messageId, regenerateReplyMutation, toast]
  );

  // Regenerate a summary for the selected email
  const regenerateSummaryForSelected = useCallback(async (): Promise<string> => {
    if (!selectedEmail?.messageId) {
      toast({
        title: 'No email selected',
        description: 'Please select an email to regenerate the summary for.',
        variant: 'destructive',
      });
      return '';
    }

    try {
      // Use ref to prevent multiple refresh operations for the same email
      if (processingEmailIds.current.has(selectedEmail.messageId)) {
        return '';
      }

      // Set the processing flag
      processingEmailIds.current.add(selectedEmail.messageId);

      try {
        const newSummary = await regenerateSummaryMutation(selectedEmail.messageId);

        // Clear the processing flag after a short delay
        setTimeout(() => {
          processingEmailIds.current.delete(selectedEmail.messageId);
        }, 2000);

        return newSummary;
      } catch (error) {
        // Clear the processing flag in case of error
        processingEmailIds.current.delete(selectedEmail.messageId);
        throw error;
      }
    } catch (error) {
      console.error('Error in regenerateSummaryForSelected:', error);
      throw error; // Re-throw to let the mutation error handler deal with it
    }
  }, [selectedEmail?.messageId, regenerateSummaryMutation, toast]);

  // Explain the categories for the selected email
  const explainCategoriesForSelected = useCallback(async (): Promise<string> => {
    if (!selectedEmail?.messageId) {
      toast({
        title: 'No email selected',
        description: 'Please select an email to explain categories for.',
        variant: 'destructive',
      });
      return '';
    }

    return await explainCategoriesMutation(selectedEmail.messageId);
  }, [selectedEmail?.messageId, explainCategoriesMutation, toast]);

  // If we're in test mode, use the cached email for preview
  const effectiveSelectedEmail =
    isTestMode && selectedEmailMessageId
      ? getSelectedEmailFromCache(selectedEmailMessageId)
      : selectedEmail || null;

  // Debug the effective selected email
  useEffect(() => {
    console.log('[EmailDetail] Effective selected email changed:', {
      selectedEmailMessageId,
      isTestMode,
      hasSelectedEmail: !!selectedEmail,
      hasEffectiveEmail: !!effectiveSelectedEmail,
      effectiveEmailId: effectiveSelectedEmail?.id,
      effectiveEmailSubject: effectiveSelectedEmail?.subject,
      timestamp: new Date().toISOString(),
    });
  }, [selectedEmailMessageId, isTestMode, selectedEmail, effectiveSelectedEmail]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      selectedEmail: effectiveSelectedEmail,
      isLoading,
      error: error as Error | null,
      selectEmail,
      archiveSelectedEmail,
      sendReplyToSelected,
      regenerateReplyForSelected,
      regenerateSummaryForSelected,
      explainCategoriesForSelected,
      isArchiving,
      isSendingReply,
      isRegeneratingReply,
      isRegeneratingSummary,
      isExplainingCategories,
    }),
    [
      effectiveSelectedEmail,
      isLoading,
      error,
      selectEmail,
      archiveSelectedEmail,
      sendReplyToSelected,
      regenerateReplyForSelected,
      regenerateSummaryForSelected,
      explainCategoriesForSelected,
      isArchiving,
      isSendingReply,
      isRegeneratingReply,
      isRegeneratingSummary,
      isExplainingCategories,
    ]
  );

  return <EmailDetailContext.Provider value={contextValue}>{children}</EmailDetailContext.Provider>;
}

// Use named function to avoid Fast Refresh incompatibility
export function useEmailDetail() {
  const context = useContext(EmailDetailContext);
  if (context === undefined) {
    throw new Error('useEmailDetail must be used within an EmailDetailProvider');
  }
  return context;
}
