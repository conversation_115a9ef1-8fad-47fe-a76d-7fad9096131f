import { Check, Filter } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import type { EmailFilters } from '@/types/email';

export const defaultFilters: EmailFilters = {
  categories: [],
  status: 'all',
  priority: 'all',
  timeRange: 'all',
};

export interface EmailFilterProps {
  onFilterChange: (filters: EmailFilters) => void;
  activeFilters: EmailFilters;
  isMobileView?: boolean;
}

const categoryOptions = ['Work', 'Personal', 'Urgent', 'Promotions'];
const statusOptions: { value: EmailFilters['status']; label: string }[] = [
  { value: 'all', label: 'All' },
  { value: 'read', label: 'Read' },
  { value: 'unread', label: 'Unread' },
  { value: 'archived', label: 'Archived' },
  { value: 'trashed', label: 'Trashed' },
  { value: 'important', label: 'Important' },
  { value: 'snoozed', label: 'Snoozed' },
];
const priorityOptions: { value: EmailFilters['priority']; label: string }[] = [
  { value: 'all', label: 'All Priorities' },
  { value: 'high', label: 'High Priority' },
  { value: 'medium', label: 'Medium Priority' },
  { value: 'low', label: 'Low Priority' },
];
const timeRangeOptions: { value: EmailFilters['timeRange']; label: string }[] = [
  { value: 'all', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'this_week', label: 'This Week' },
  { value: 'this_month', label: 'This Month' },
];

const EmailFilter: React.FC<EmailFilterProps> = ({
  onFilterChange,
  activeFilters,
  isMobileView = false,
}) => {
  const [open, setOpen] = useState(false);

  const handleCategoryToggle = (category: string) => {
    const newCategories = activeFilters.categories.includes(category)
      ? activeFilters.categories.filter((c) => c !== category)
      : [...activeFilters.categories, category];
    onFilterChange({ ...activeFilters, categories: newCategories });
  };

  const handleStatusChange = (
    status: 'all' | 'read' | 'unread' | 'archived' | 'trashed' | 'important' | 'snoozed'
  ) => {
    onFilterChange({ ...activeFilters, status });
  };

  const handlePriorityChange = (priority: 'all' | 'high' | 'medium' | 'low') => {
    onFilterChange({ ...activeFilters, priority });
  };

  const handleTimeRangeChange = (timeRange: 'all' | 'today' | 'this_week' | 'this_month') => {
    onFilterChange({ ...activeFilters, timeRange });
  };

  const resetFilters = () => {
    onFilterChange(defaultFilters);
  };

  // Calculate the total number of active filters
  const activeFilterCount = [
    activeFilters.categories.length > 0 ? 1 : 0,
    activeFilters.status !== 'all' ? 1 : 0,
    activeFilters.priority !== 'all' ? 1 : 0,
    activeFilters.timeRange !== 'all' ? 1 : 0,
  ].reduce((sum, val) => sum + val, 0);

  // For mobile sheet-like view, return a different UI structure
  if (isMobileView) {
    return (
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-3">
            <h3 className="text-sm font-medium">Categories</h3>
            <div className="flex flex-wrap gap-2">
              {categoryOptions.map((category) => (
                <Button
                  key={category}
                  variant={activeFilters.categories.includes(category) ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleCategoryToggle(category)}
                  className="h-8"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          <Separator className="my-4" />

          <div className="space-y-3">
            <h3 className="text-sm font-medium">Status</h3>
            <div className="grid grid-cols-2 gap-2">
              {statusOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={activeFilters.status === option.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleStatusChange(option.value)}
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>

          <Separator className="my-4" />

          <div className="space-y-3">
            <h3 className="text-sm font-medium">Priority</h3>
            <div className="grid grid-cols-2 gap-2">
              {priorityOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={activeFilters.priority === option.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePriorityChange(option.value)}
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>

          <Separator className="my-4" />

          <div className="space-y-3">
            <h3 className="text-sm font-medium">Time Range</h3>
            <div className="grid grid-cols-2 gap-2">
              {timeRangeOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={activeFilters.timeRange === option.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleTimeRangeChange(option.value as "all" | "today" | "this_week" | "this_month")}
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {activeFilterCount > 0 && (
          <div className="text-xs text-muted-foreground mb-2 flex items-center">
            <Filter className="h-3 w-3 mr-1" />
            {activeFilterCount} active filter{activeFilterCount !== 1 && 's'}
          </div>
        )}
      </div>
    );
  }

  // Normal desktop popover UI
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="p-1.5 relative">
          <Filter className="h-5 w-5" />
          {activeFilterCount > 0 && (
            <Badge
              variant="default"
              className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-[10px]"
            >
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="end">
        <Tabs defaultValue="category" className="w-full">
          <div className="flex items-center justify-between p-3 pb-0">
            <TabsList className="grid grid-cols-2">
              <TabsTrigger value="category">Categories</TabsTrigger>
              <TabsTrigger value="other">Filters</TabsTrigger>
            </TabsList>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-xs"
              onClick={resetFilters}
              disabled={activeFilterCount === 0}
            >
              Reset
            </Button>
          </div>
          <TabsContent value="category" className="mt-0 p-0">
            <Command>
              <CommandInput placeholder="Search categories..." />
              <CommandList>
                <CommandEmpty>No categories found.</CommandEmpty>
                <CommandGroup>
                  {categoryOptions.map((category) => (
                    <CommandItem
                      key={category}
                      onSelect={() => handleCategoryToggle(category)}
                      className="flex items-center justify-between p-2"
                    >
                      <span>{category}</span>
                      {activeFilters.categories.includes(category) && <Check className="h-4 w-4" />}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </TabsContent>
          <TabsContent value="other" className="mt-0 p-0">
            <Command>
              <CommandInput placeholder="Search filters..." />
              <CommandList>
                <CommandEmpty>No filters found.</CommandEmpty>
                <CommandGroup heading="Status">
                  {statusOptions.map((option) => (
                    <CommandItem
                      key={option.value}
                      onSelect={() => handleStatusChange(option.value)}
                    >
                      <Check
                        className={cn(
                          'mr-2 h-4 w-4',
                          activeFilters.status === option.value ? 'opacity-100' : 'opacity-0'
                        )}
                      />
                      {option.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
                <CommandSeparator />
                <CommandGroup heading="Priority">
                  {priorityOptions.map((option) => (
                    <CommandItem
                      key={option.value}
                      onSelect={() => handlePriorityChange(option.value)}
                    >
                      <Check
                        className={cn(
                          'mr-2 h-4 w-4',
                          activeFilters.priority === option.value ? 'opacity-100' : 'opacity-0'
                        )}
                      />
                      {option.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
                <CommandSeparator />
                <CommandGroup heading="Time Range">
                  {timeRangeOptions.map((option) => (
                    <CommandItem
                      key={option.value}
                      onSelect={() => handleTimeRangeChange(option.value as "all" | "today" | "this_week" | "this_month")}
                    >
                      <Check
                        className={cn(
                          'mr-2 h-4 w-4',
                          activeFilters.timeRange === option.value ? 'opacity-100' : 'opacity-0'
                        )}
                      />
                      {option.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </TabsContent>
        </Tabs>
        <div className="flex items-center justify-between p-3 border-t">
          <div className="text-sm text-muted-foreground">
            {activeFilterCount} active filter{activeFilterCount !== 1 && 's'}
          </div>
          <Button size="sm" onClick={() => setOpen(false)}>
            Apply Filters
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default EmailFilter;
