/**
 * Performance Monitoring Hook
 *
 * Provides performance monitoring capabilities for React components
 * including render time tracking, memory usage monitoring, and
 * performance metrics collection.
 *
 * Usage:
 * const { startTiming, endTiming, getMetrics } = usePerformanceMonitor('ComponentName');
 */

import React, { useCallback, useEffect, useRef } from 'react';
import { isProduction } from '../lib/environment';

interface PerformanceMetrics {
  componentName: string;
  renderCount: number;
  averageRenderTime: number;
  lastRenderTime: number;
  maxRenderTime: number;
  minRenderTime: number;
  totalRenderTime: number;
}

interface PerformanceMonitorReturn {
  startTiming: (operation?: string) => void;
  endTiming: (operation?: string) => number;
  getMetrics: () => PerformanceMetrics;
  resetMetrics: () => void;
  logMetrics: () => void;
}

const performanceStore = new Map<string, PerformanceMetrics>();

export const usePerformanceMonitor = (componentName: string): PerformanceMonitorReturn => {
  const startTimeRef = useRef<number | null>(null);
  const operationStartTimes = useRef<Map<string, number>>(new Map());

  // Initialize metrics for this component if not exists
  useEffect(() => {
    if (!performanceStore.has(componentName)) {
      performanceStore.set(componentName, {
        componentName,
        renderCount: 0,
        averageRenderTime: 0,
        lastRenderTime: 0,
        maxRenderTime: 0,
        minRenderTime: Number.POSITIVE_INFINITY,
        totalRenderTime: 0,
      });
    }
  }, [componentName]);

  const startTiming = useCallback((operation = 'render') => {
    const now = performance.now();
    if (operation === 'render') {
      startTimeRef.current = now;
    } else {
      operationStartTimes.current.set(operation, now);
    }
  }, []);

  const endTiming = useCallback(
    (operation = 'render') => {
      const now = performance.now();
      let duration = 0;

      if (operation === 'render' && startTimeRef.current !== null) {
        duration = now - startTimeRef.current;
        startTimeRef.current = null;

        // Update metrics
        const metrics = performanceStore.get(componentName);
        if (metrics) {
          metrics.renderCount++;
          metrics.lastRenderTime = duration;
          metrics.totalRenderTime += duration;
          metrics.averageRenderTime = metrics.totalRenderTime / metrics.renderCount;
          metrics.maxRenderTime = Math.max(metrics.maxRenderTime, duration);
          metrics.minRenderTime = Math.min(metrics.minRenderTime, duration);

          performanceStore.set(componentName, metrics);
        }
      } else {
        const startTime = operationStartTimes.current.get(operation);
        if (startTime !== undefined) {
          duration = now - startTime;
          operationStartTimes.current.delete(operation);
        }
      }

      return duration;
    },
    [componentName]
  );

  const getMetrics = useCallback((): PerformanceMetrics => {
    return (
      performanceStore.get(componentName) || {
        componentName,
        renderCount: 0,
        averageRenderTime: 0,
        lastRenderTime: 0,
        maxRenderTime: 0,
        minRenderTime: 0,
        totalRenderTime: 0,
      }
    );
  }, [componentName]);

  const resetMetrics = useCallback(() => {
    performanceStore.set(componentName, {
      componentName,
      renderCount: 0,
      averageRenderTime: 0,
      lastRenderTime: 0,
      maxRenderTime: 0,
      minRenderTime: Number.POSITIVE_INFINITY,
      totalRenderTime: 0,
    });
  }, [componentName]);

  const logMetrics = useCallback(() => {
    if (!isProduction()) {
      const metrics = getMetrics();
      if (metrics.renderCount > 0) {
        // Only log in development mode to avoid production console noise
        if (typeof console !== 'undefined' && console.group) {
          console.group(`📊 Performance Metrics: ${componentName}`);
          console.log(`Render Count: ${metrics.renderCount}`);
          console.log(`Average Render Time: ${metrics.averageRenderTime.toFixed(2)}ms`);
          console.log(`Last Render Time: ${metrics.lastRenderTime.toFixed(2)}ms`);
          console.log(`Max Render Time: ${metrics.maxRenderTime.toFixed(2)}ms`);
          console.log(`Min Render Time: ${metrics.minRenderTime.toFixed(2)}ms`);
          console.log(`Total Render Time: ${metrics.totalRenderTime.toFixed(2)}ms`);
          console.groupEnd();
        }
      }
    }
  }, [componentName, getMetrics]);

  return {
    startTiming,
    endTiming,
    getMetrics,
    resetMetrics,
    logMetrics,
  };
};

/**
 * Global performance monitoring utilities
 */
export const PerformanceMonitorUtils = {
  getAllMetrics: (): PerformanceMetrics[] => {
    return Array.from(performanceStore.values());
  },

  getComponentMetrics: (componentName: string): PerformanceMetrics | undefined => {
    return performanceStore.get(componentName);
  },

  clearAllMetrics: (): void => {
    performanceStore.clear();
  },

  logAllMetrics: (): void => {
    if (!isProduction() && typeof console !== 'undefined' && console.group) {
      console.group('📊 All Component Performance Metrics');
      performanceStore.forEach((metrics) => {
        if (metrics.renderCount > 0) {
          console.group(`${metrics.componentName}`);
          console.log(
            `Renders: ${metrics.renderCount}, Avg: ${metrics.averageRenderTime.toFixed(2)}ms`
          );
          console.groupEnd();
        }
      });
      console.groupEnd();
    }
  },

  getSlowComponents: (threshold = 50): PerformanceMetrics[] => {
    return Array.from(performanceStore.values())
      .filter((metrics) => metrics.averageRenderTime > threshold)
      .sort((a, b) => b.averageRenderTime - a.averageRenderTime);
  },
};

/**
 * React DevTools Performance Profiler Integration
 * 
 * NOTE: This hook is currently disabled to prevent infinite re-render loops.
 * The previous implementation caused components to re-render continuously
 * due to improper dependency management in useEffect.
 * 
 * To re-enable:
 * 1. Ensure proper dependency array management
 * 2. Use useCallback for all functions passed to useEffect
 * 3. Test thoroughly to prevent infinite loops
 */
export const useRenderProfiler = (componentName: string) => {
  const { getMetrics } = usePerformanceMonitor(componentName);

  // Profiler integration is disabled to prevent re-render loops
  // Future implementation should use React.Profiler API properly
  
  return { getMetrics };
};

/**
 * HOC for automatic performance monitoring
 * 
 * NOTE: This HOC is currently disabled to prevent infinite re-render loops.
 * The previous implementation caused components to re-render continuously
 * due to improper performance monitoring integration.
 * 
 * To re-enable:
 * 1. Implement proper React.Profiler integration
 * 2. Ensure monitoring doesn't trigger re-renders
 * 3. Use React.memo appropriately to prevent unnecessary renders
 */
export const withPerformanceMonitoring = <P extends Record<string, any>>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) => {
  const MonitoredComponent = (props: P): JSX.Element => {
    // Performance monitoring is disabled to prevent infinite loops
    // Future implementation should integrate with React DevTools Profiler
    return React.createElement(WrappedComponent, props);
  };

  MonitoredComponent.displayName = `withPerformanceMonitoring(${
    componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })`;

  return MonitoredComponent;
};
