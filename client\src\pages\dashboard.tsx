import { useQueryClient } from '@tanstack/react-query';
import { Alert<PERSON>riangle, Loader2, RefreshCw } from 'lucide-react';
import { lazy, Suspense, useEffect, useState, useRef } from 'react';
import { useLocation } from 'wouter';

import AppLayout from '@/components/layout/AppLayout';
import PageHeader from '@/components/layout/PageHeader';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/context/AuthContext';
import { useProviderStatus } from '@/hooks/use-provider-status';
import { toast } from '@/hooks/use-toast';

// Lazy-loaded components for code splitting
const EmailInbox = lazy(() => import('@/components/email/EmailInbox'));
const GmailConnectionPrompt = lazy(() => import('@/components/email/GmailConnectionPrompt'));
const EmailAccountsManager = lazy(() => import('@/components/email/EmailAccountsManager'));

const Dashboard = () => {
  const { user, loading } = useAuth();
  const [, setLocation] = useLocation();
  const [activeTab, setActiveTab] = useState<string>('inbox');
  const queryClient = useQueryClient();

  // Use centralized provider status hook to prevent polling conflicts
  const {
    providers = [],
    isLoading: isLoadingProviders,
    error: providerError,
    refetch: refetchProviders,
  } = useProviderStatus();

  // Handle the post-OAuth redirect from Google
  useEffect(() => {
    const handleRedirect = async () => {
      const searchParams = new URLSearchParams(window.location.search);
      // Only run if the param is present AND we have a user object
      if (searchParams.has('gmail_connected') && user?.id) {
        toast({
          title: 'Gmail Connected!',
          description: "We're fetching your latest account status.",
        });
        
        // Invalidate the query to mark it as stale
        await queryClient.invalidateQueries({ queryKey: ['emailProviderStatus', user.id] });
        
        // Explicitly refetch the query to get the latest status immediately
        await queryClient.refetchQueries({ queryKey: ['emailProviderStatus', user.id] });

        // Clean the URL to avoid refetching on every page refresh
        setLocation('/dashboard', { replace: true });
      }
    };

    handleRedirect();
  }, [user, queryClient, setLocation]); // Depend on the user object

  // Ensure providers is always an array to prevent .find errors - redundant but safe
  const providersArray = Array.isArray(providers) ? providers : [];

  // Check if any provider is connected (typically Gmail)
  const hasConnectedProvider =
    providersArray.length > 0 && providersArray.some((p) => p.isConnected);

  // Get Gmail specific connection info - check for both 'gmail' and 'google' provider names
  const gmailStatus = providersArray.find((p) => p.provider === 'gmail' || p.provider === 'google');
  const isGmailConnected = gmailStatus?.isConnected || false;

  useEffect(() => {
    // Prefetch some data when the prompt is shown
    queryClient.prefetchQuery({ queryKey: ['/api/emails', 'inbox'] });
  }, [queryClient]);

  // Retry loading provider status if there was an error
  const handleRetryProviderStatus = () => {
    refetchProviders();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <p className="mt-3 text-muted-foreground">Loading your account...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  // Loading providers with visual feedback about the process
  if (isLoadingProviders) {
    return (
      <AppLayout>
        <div className="container max-w-4xl py-8">
          <div className="text-center">
            <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
            <p className="mt-3 text-muted-foreground">Checking email account connections...</p>
            <p className="text-sm text-muted-foreground mt-2">This will only take a moment</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Handle provider error
  if (providerError) {
    return (
      <AppLayout>
        <div className="container max-w-4xl py-8">
          <Alert variant="destructive" className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Connection Error</AlertTitle>
            <AlertDescription>
              We encountered an error checking your email account connections. This might be a
              temporary issue.
            </AlertDescription>
          </Alert>

          <div className="flex justify-center mt-4">
            <Button onClick={handleRetryProviderStatus} className="mr-2">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry Connection
            </Button>
            <Button variant="outline" onClick={() => setActiveTab('accounts')}>
              Manage Email Accounts
            </Button>
          </div>
        </div>
      </AppLayout>
    );
  }

  // If user is logged in with Firebase but hasn't connected Gmail
  if (!hasConnectedProvider) {
    return (
      <AppLayout>
        <div className="container max-w-4xl py-8">
          <h1 className="text-2xl font-bold mb-6">Welcome to Inbox Zero</h1>
          <Alert className="mb-6">
            <AlertTitle>Connect your email account</AlertTitle>
            <AlertDescription>
              To get started, you'll need to connect your Gmail account. This allows Inbox Zero to
              help you manage your emails efficiently.
            </AlertDescription>
          </Alert>
          <Suspense
            fallback={
              <div className="flex justify-center items-center p-6 border rounded-lg bg-card">
                <Loader2 className="h-8 w-8 animate-spin text-primary mr-3" />
                <p>Loading connection interface...</p>
              </div>
            }
          >
            <GmailConnectionPrompt />
          </Suspense>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      {/* Main Content */}
      <div className="space-y-6">
        <PageHeader title="Dashboard" description="Your intelligent email overview." />
        <div className="flex h-full">
          <div className="flex-1 flex flex-col overflow-hidden">
            <main className="flex-1 overflow-y-auto p-6 max-w-screen contain-content h-full flex flex-col">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4">
                  <TabsTrigger
                    value="inbox"
                    className="flex-1 sm:flex-initial text-xs sm:text-sm h-7 sm:h-9 tabs-trigger"
                  >
                    Inbox
                  </TabsTrigger>
                  <TabsTrigger
                    value="accounts"
                    className="flex-1 sm:flex-initial text-xs sm:text-sm h-7 sm:h-9 tabs-trigger"
                  >
                    Email Accounts
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="inbox" className="h-full flex flex-col flex-1 min-h-0">
                  <Suspense fallback={<p>Loading Inbox...</p>}>
                    <div className="max-w-screen">
                      <EmailInbox />
                    </div>
                  </Suspense>
                </TabsContent>
                <TabsContent value="accounts" className="h-full flex flex-col">
                  <Suspense fallback={<p>Loading Accounts...</p>}>
                    <EmailAccountsManager />
                  </Suspense>
                </TabsContent>
              </Tabs>
            </main>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default Dashboard;
