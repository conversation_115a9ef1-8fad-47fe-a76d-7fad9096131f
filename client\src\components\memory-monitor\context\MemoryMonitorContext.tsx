import { type UseQueryResult, useQuery, useQueryClient } from '@tanstack/react-query';
import type React from 'react';
import { createContext, useCallback, useContext, useMemo, useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import apiClient from '@/lib/apiClient';

// Types
interface MemoryStats {
  rss: number;
  heapTotal: number;
  heapUsed: number;
  external: number;
  arrayBuffers: number;
  pressureLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  timestamp: string;
}

interface MemoryHistory {
  stats: MemoryStats[];
  leakDetection: {
    detected: boolean;
    confidence: number;
    details: string;
  };
}

interface PoolStats {
  total: number;
  active: number;
  idle: number;
  waiting: number;
  pressureLevel: string;
  poolMin: number;
  poolMax: number;
}

interface SystemResources {
  memory: {
    totalMB: number;
    freeMB: number;
    usedPercent: number;
  };
  cpu: {
    cores: number;
    loadAverage: number[];
    usagePercent: number;
  };
}

// Context
interface MemoryMonitorContextType {
  memoryStatsQuery: UseQueryResult<MemoryStats, unknown>;
  memoryHistoryQuery: UseQueryResult<MemoryHistory, unknown>;
  poolStatsQuery: UseQueryResult<PoolStats, unknown>;
  systemResourcesQuery: UseQueryResult<SystemResources, unknown>;
  autoRefresh: boolean;
  toggleAutoRefresh: () => void;
  triggerGarbageCollection: () => Promise<void>;
  refreshAll: () => void;
}

const MemoryMonitorContext = createContext<MemoryMonitorContextType | undefined>(undefined);

// Provider
export function MemoryMonitorProvider({ children }: { children: React.ReactNode }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [autoRefresh, setAutoRefresh] = useState(false);
  const refetchInterval = autoRefresh ? 5000 : false;

  const memoryStatsQuery = useQuery<MemoryStats>({
    queryKey: ['admin/memory/stats'],
    queryFn: () => apiClient.get('/admin/memory/stats'),
    refetchInterval,
    refetchOnWindowFocus: true,
  });

  const memoryHistoryQuery = useQuery<MemoryHistory>({
    queryKey: ['admin/memory/history'],
    queryFn: () => apiClient.get('/admin/memory/history'),
    refetchInterval: refetchInterval ? refetchInterval * 2 : false,
    refetchOnWindowFocus: true,
  });

  const poolStatsQuery = useQuery<PoolStats>({
    queryKey: ['admin/memory/pool-stats'],
    queryFn: () => apiClient.get('/admin/memory/pool-stats'),
    refetchInterval,
    refetchOnWindowFocus: true,
  });

  const systemResourcesQuery = useQuery<SystemResources>({
    queryKey: ['admin/system/resources'],
    queryFn: () => apiClient.get('/admin/system/resources'),
    refetchInterval: refetchInterval ? refetchInterval * 2 : false,
    refetchOnWindowFocus: true,
  });

  const toggleAutoRefresh = useCallback(() => {
    setAutoRefresh((prev) => {
      const newState = !prev;
      toast({
        title: newState ? 'Auto-refresh enabled' : 'Auto-refresh disabled',
        description: newState
          ? 'Dashboard will refresh every 5 seconds'
          : 'Dashboard will only update when manually refreshed',
        duration: 3000,
      });
      return newState;
    });
  }, [toast]);

  const triggerGarbageCollection = async () => {
    try {
      const data = await apiClient.post('/admin/memory/gc');
      toast({
        title: 'Garbage Collection Triggered',
        description: 'Memory cleanup has been initiated successfully.',
        duration: 3000,
      });
      setTimeout(() => refreshAll(), 1000);
    } catch (error: any) {
      toast({
        title: 'Operation Failed',
        description: error.message || 'Failed to trigger garbage collection',
        variant: 'destructive',
        duration: 5000,
      });
    }
  };

  const refreshAll = () => {
    queryClient.invalidateQueries({ queryKey: ['admin/memory/stats'] });
    queryClient.invalidateQueries({ queryKey: ['admin/memory/history'] });
    queryClient.invalidateQueries({ queryKey: ['admin/memory/pool-stats'] });
    queryClient.invalidateQueries({ queryKey: ['admin/system/resources'] });
  };

  const value = useMemo(
    () => ({
      memoryStatsQuery,
      memoryHistoryQuery,
      poolStatsQuery,
      systemResourcesQuery,
      autoRefresh,
      toggleAutoRefresh,
      triggerGarbageCollection,
      refreshAll,
    }),
    [
      memoryStatsQuery,
      memoryHistoryQuery,
      poolStatsQuery,
      systemResourcesQuery,
      autoRefresh,
      toggleAutoRefresh,
    ]
  );

  return <MemoryMonitorContext.Provider value={value}>{children}</MemoryMonitorContext.Provider>;
}

// Hook
export function useMemoryMonitor() {
  const context = useContext(MemoryMonitorContext);
  if (!context) {
    throw new Error('useMemoryMonitor must be used within a MemoryMonitorProvider');
  }
  return context;
}
