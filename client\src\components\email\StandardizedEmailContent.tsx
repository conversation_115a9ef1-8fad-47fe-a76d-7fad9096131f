import DOMPurify from 'dompurify';
import type React from 'react';
import { memo, useMemo, useState } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';

// Define interface for handling email images
interface EmailImage {
  url: string;
  alt: string;
  width?: number;
  height?: number;
  originalSize?: { width: number; height: number };
}

interface StandardizedEmailContentProps {
  html: string | null | undefined;
  className?: string;
  preserveImages?: boolean;
  maxImageWidth?: number;
  onImageLoad?: (image: EmailImage) => void;
  onExternalContentDetected?: (hasExternalContent: boolean) => void;
  showExternalContentWarnings?: boolean;
}

const ExternalContentWarning = memo(
  ({
    hasExternalContent,
    showExternalContentWarnings,
  }: {
    hasExternalContent: boolean;
    showExternalContentWarnings: boolean;
  }) => {
    if (!hasExternalContent || !showExternalContentWarnings) return null;

    return (
      <div className='external-content-warning'>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className='h-3.5 w-3.5 flex-shrink-0'
          viewBox='0 0 20 20'
          fill='currentColor'
          aria-hidden='true'
        >
          <title>Warning</title>
          <path
            fillRule='evenodd'
            d='M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z'
            clipRule='evenodd'
          />
        </svg>
        <span>This email contains external content that may track your activity</span>
      </div>
    );
  }
);
ExternalContentWarning.displayName = 'ExternalContentWarning';

/**
 * StandardizedEmailContent component
 *
 * Displays email content with consistent styling, regardless of the original HTML formatting.
 * Features:
 * - Sanitizes and normalizes HTML to prevent XSS and ensure consistent display
 * - Extracts and reformats images with lazy loading
 * - Handles links safely with proper security attributes
 * - Removes quoted content and signatures for clearer display
 * - Uses memoization for efficient rendering of repeated content
 * - Warns about external content for security
 */
const StandardizedEmailContent: React.FC<StandardizedEmailContentProps> = ({
  html,
  className = '',
  preserveImages = true,
  maxImageWidth = 600,
  onImageLoad,
  onExternalContentDetected,
  showExternalContentWarnings = true,
}) => {
  const [hasExternalContent, setHasExternalContent] = useState(false);
  const [loadedImages, setLoadedImages] = useState<Record<string, boolean>>({});
  const isMobile = useIsMobile();

  // Sanitize the HTML content using DOMPurify. This is critical for security.
  const sanitizedHtml = useMemo(() => {
    if (!html) return '';
    // Configure DOMPurify to be safe but allow for our use cases.
    const clean = DOMPurify.sanitize(html, {
      USE_PROFILES: { html: true }, // Base safe profile
      FORBID_TAGS: ['style', 'script'], // Disallow inline styles & scripts entirely
      // Block every attribute that starts with "on" (e.g. onclick) plus inline styles
      FORBID_ATTR: ['style', 'onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout', 'onfocus', 'onblur'],
    });

    if (onExternalContentDetected) {
      // Check for external content by looking for `src` attributes pointing to http/https.
      // DOMPurify doesn't track this for us, so we do a simple check on the original html.
      const hasExternal = /src\s*=\s*['"]https?:/i.test(html);
      setHasExternalContent(hasExternal);
      onExternalContentDetected(hasExternal);
    }

    return clean;
  }, [html, onExternalContentDetected]);

  // For now, we will not support complex image extraction and gallery features
  // as the primary goal is security and standardization. This simplifies the component
  // and removes potential complexities.
  const images: EmailImage[] = []; // This feature is disabled for now.

  // Handle image loading events
  const handleImageLoad = (image: EmailImage, dimensions: { width: number; height: number }) => {
    if (onImageLoad) {
      onImageLoad({
        ...image,
        originalSize: dimensions,
      });
    }

    setLoadedImages((prev) => ({
      ...prev,
      [image.url]: true,
    }));
  };

  return (
    <div
      className={`standardized-email-content ${isMobile ? 'standardized-email-mobile' : ''} ${className} w-full max-w-[100vw] overflow-hidden px-1 sm:px-2`}
    >
      {/* Email content */}
      {/* biome-ignore lint/security/noDangerouslySetInnerHtml: We are using DOMPurify to sanitize the HTML content before rendering */}
      <div
        className={`email-content-body w-full max-w-[100vw] overflow-x-hidden text-wrap ${isMobile ? 'text-xs leading-normal' : 'text-[11px] sm:text-xs md:text-sm leading-relaxed'} break-words pb-2`}
        dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
      />

      {/* External content warning if needed */}
      <ExternalContentWarning
        hasExternalContent={hasExternalContent}
        showExternalContentWarnings={showExternalContentWarnings ?? false}
      />

      {/* Image gallery for any extracted images - This is now disabled for security/simplicity */}
      {preserveImages && images.length > 0 && (
        <div className="email-content-images mt-2 sm:mt-3 w-full max-w-[100vw] overflow-hidden">
          {images.map((image, index) => (
            <div
              key={`${image.url}-${index}`}
              className="email-image-container my-2 overflow-hidden rounded-md bg-muted/30"
            >
              <img
                src={image.url}
                alt={image.alt || 'Email attachment'}
                className="email-content-image max-w-full h-auto mx-auto transition-opacity duration-300 opacity-100"
                style={
                  { maxWidth: `var(--max-image-width, ${maxImageWidth}px)` } as React.CSSProperties
                }
                loading="lazy"
                onLoad={(e) => {
                  const img = e.target as HTMLImageElement;
                  handleImageLoad(image, {
                    width: img.naturalWidth,
                    height: img.naturalHeight,
                  });
                }}
              />
              {!loadedImages[image.url] && (
                <div className="flex justify-center items-center h-20 animate-pulse">
                  <svg
                    className="w-6 h-6 text-muted-foreground"
                    fill="none"
                    viewBox="0 0 24 24"
                    aria-hidden="true"
                  >
                    <title>Loading</title>
                    <path
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M13.5 8.25V6.75M13.5 12V11.25M6 20.25h12A2.25 2.25 0 0020.25 18V6A2.25 2.25 0 0018 3.75H6A2.25 2.25 0 003.75 6v12A2.25 2.25 0 006 20.25z"
                    />
                  </svg>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Use memo to prevent unnecessary re-renders
export default memo(StandardizedEmailContent);
