import apiClient from './apiClient';
import type { AppError, ErrorCategory } from './errorHandler';

/**
 * Interface for error reporting configuration
 */
export interface ErrorReportingConfig {
  enabled: boolean;
  sampleRate: number;
  ignoredCategories?: ErrorCategory[];
  ignoredStatusCodes?: number[];
  endpoint?: string;
  additionalContext?: Record<string, any>;
  sanitizers?: Array<(data: any) => any>;
  consoleReporting: boolean;
}

/**
 * Default error reporting configuration
 */
export const defaultConfig: ErrorReportingConfig = {
  enabled: true,
  sampleRate: 1.0, // Report 100% of errors
  ignoredCategories: [],
  ignoredStatusCodes: [401, 403], // Don't report auth errors by default
  consoleReporting: true,
};

/**
 * Error reporting service that can be used to track and analyze errors
 */
class ErrorReportingService {
  private config: ErrorReportingConfig;
  private sessionId: string;
  private userId?: string | number;
  private userAgent: string;
  private errorCount: Record<string, number> = {};

  constructor(config: Partial<ErrorReportingConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    this.sessionId = this.generateSessionId();
    this.userAgent = typeof navigator !== 'undefined' ? navigator.userAgent : '';
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return (
      Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
    );
  }

  /**
   * Set the current user ID for error reports
   */
  public setUser(userId: string | number | undefined): void {
    this.userId = userId;
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<ErrorReportingConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Add additional context to all future error reports
   */
  public setAdditionalContext(context: Record<string, any>): void {
    this.config.additionalContext = {
      ...this.config.additionalContext,
      ...context,
    };
  }

  /**
   * Report an error to the configured endpoint
   */
  public reportError(error: Error | AppError, context: Record<string, any> = {}): void {
    if (!this.config.enabled) return;

    // Apply sampling - skip some errors randomly
    if (this.config.sampleRate < 1.0 && Math.random() > this.config.sampleRate) {
      return;
    }

    // Check if we should ignore this error
    if (this.shouldIgnoreError(error)) {
      return;
    }

    // Track error frequency
    const errorKey = this.getErrorKey(error);
    this.errorCount[errorKey] = (this.errorCount[errorKey] || 0) + 1;

    // Build the error report
    const appError = error as AppError;
    const report = {
      timestamp: new Date().toISOString(),
      message: error.message,
      name: error.name,
      stack: error.stack,
      category: appError.category || 'unknown',
      statusCode: appError.statusCode,
      details: appError.details,
      url: window.location.href,
      userAgent: this.userAgent,
      sessionId: this.sessionId,
      userId: this.userId,
      occurrence: this.errorCount[errorKey],
      context: {
        ...this.config.additionalContext,
        ...context,
      },
    };

    // Apply sanitizers to remove sensitive data
    const sanitizedReport = this.sanitizeReport(report);

    // Log to console if enabled
    if (this.config.consoleReporting) {
      console.error('[Error Report]', sanitizedReport);
    }

    // Send to remote endpoint if configured
    if (this.config.endpoint) {
      this.sendErrorReport(sanitizedReport);
    }
  }

  /**
   * Apply sanitizers to remove sensitive data
   */
  private sanitizeReport(report: any): any {
    if (!this.config.sanitizers || this.config.sanitizers.length === 0) {
      return report;
    }

    return this.config.sanitizers.reduce((sanitized, sanitizer) => sanitizer(sanitized), {
      ...report,
    });
  }

  /**
   * Generate a key to identify similar errors
   */
  private getErrorKey(error: Error): string {
    const appError = error as AppError;
    return `${error.name}:${error.message}:${appError.category || 'unknown'}:${appError.statusCode || 0}`;
  }

  /**
   * Check if an error should be ignored based on configuration
   */
  private shouldIgnoreError(error: Error): boolean {
    const appError = error as AppError;

    // Check ignored categories
    if (appError.category && this.config.ignoredCategories?.includes(appError.category)) {
      return true;
    }

    // Check ignored status codes
    if (appError.statusCode && this.config.ignoredStatusCodes?.includes(appError.statusCode)) {
      return true;
    }

    return false;
  }

  /**
   * Send error report to remote endpoint
   */
  private async sendErrorReport(report: any): Promise<void> {
    if (!this.config.endpoint) return;

    try {
      // Use the centralized apiClient for consistency, with keepalive for reliability
      // and skipping CSRF as it's a non-state-changing POST request.
      await apiClient.post(this.config.endpoint, report, {
        keepalive: true,
        skipCsrf: true,
        skipAuth: true, // Typically, error logging doesn't need to be authenticated
      });
    } catch (err) {
      // Don't report errors that happen during error reporting
      console.error('Error while sending error report:', err);
    }
  }
}

// Create a singleton instance
export const errorReporting = new ErrorReportingService();

/**
 * Create a sanitizer function that removes sensitive data from error reports
 */
export function createSanitizer(sensitiveKeys: string[]): (data: Record<string, any>) => any {
  return (data: Record<string, any>) => {
    if (!data || typeof data !== 'object') return data;

    const result: Record<string, any> = Array.isArray(data) ? [] : ({} as Record<string, any>);

    for (const key in data) {
      if (Object.hasOwn(data, key)) {
        // Check if this is a sensitive key
        if (
          sensitiveKeys.some((sensitiveKey) =>
            key.toLowerCase().includes(sensitiveKey.toLowerCase())
          )
        ) {
          (result as any)[key] = '[REDACTED]';
        } else if (typeof data[key] === 'object' && data[key] !== null) {
          // Recursively sanitize nested objects
          (result as any)[key] = createSanitizer(sensitiveKeys)(data[key] as Record<string, any>);
        } else {
          (result as any)[key] = data[key];
        }
      }
    }

    return result;
  };
}
