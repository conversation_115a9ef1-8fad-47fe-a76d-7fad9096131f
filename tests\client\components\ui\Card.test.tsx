import { render, screen } from '@testing-library/react';
import { Card } from '@/components/ui/card';
import '@testing-library/jest-dom';

describe('UI/Card component', () => {
  it('renders children content', () => {
    render(<Card>Hi there</Card>);
    expect(screen.getByText('Hi there')).toBeInTheDocument();
  });

  it('applies custom styling classes', () => {
    render(<Card className="shadow-lg" data-testid="card" />);
    expect(screen.getByTestId('card')).toHaveClass('shadow-lg');
  });

  it('includes base responsive classes', () => {
    render(<Card data-testid="card" />);
    const card = screen.getByTestId('card');
    expect(card).toHaveClass('rounded-lg');
    expect(card).toHaveClass('border');
  });
}); 