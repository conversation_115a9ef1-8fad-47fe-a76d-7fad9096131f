import { ArrowLef<PERSON> } from 'lucide-react';
import type React from 'react';
import { <PERSON> } from 'wouter';
import { cn } from '@/lib/utils';

interface PageHeaderProps {
  title: string;
  description?: string;
  backLink?: string;
  backLinkText?: string;
  className?: string;
  actions?: React.ReactNode;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  backLink,
  backLinkText = 'Back',
  className,
  actions,
}) => {
  return (
    <div
      className={cn(
        'flex flex-col sm:flex-row sm:items-center justify-between py-4 sm:py-6 px-4 sm:px-6',
        className
      )}
    >
      <div className="space-y-1 mb-4 sm:mb-0">
        {backLink && (
          <Link href={backLink}>
            <a className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 mb-2 -ml-2 text-muted-foreground hover:text-foreground">
              <ArrowLeft className="h-4 w-4 mr-1" />
              {backLinkText}
            </a>
          </Link>
        )}
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-foreground">{title}</h1>
        {description && (
          <p className="text-sm sm:text-base text-muted-foreground max-w-2xl">{description}</p>
        )}
      </div>

      {actions && <div className="flex items-center space-x-2">{actions}</div>}
    </div>
  );
};

export default PageHeader;
