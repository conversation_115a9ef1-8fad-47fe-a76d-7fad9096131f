import type React from 'react';
import { memo } from 'react';
import RegenerateSummaryButton from './RegenerateSummaryButton';

interface EmailSummaryProps {
  summary: string | null | undefined;
  messageId: string;
}

/**
 * Displays AI-generated summary of an email with different states
 * - Loading state
 * - Error state
 * - Processing state
 * - Success state
 */
const EmailSummary: React.FC<EmailSummaryProps> = ({ summary, messageId }) => {
  // Helper function to determine which state to display
  const getSummaryContent = () => {
    // No summary yet (initial load or empty state)
    if (!summary) {
      return (
        <div className="animate-pulse">
          <div className="h-3 bg-muted-foreground/20 rounded max-w-[90%] mb-2" />
          <div className="h-3 bg-muted-foreground/20 rounded max-w-[75%] mb-2" />
          <div className="h-3 bg-muted-foreground/20 rounded max-w-[60%]" />
        </div>
      );
    }

    // Processing state
    if (summary === 'Processing...') {
      return (
        <div className="flex items-center text-sm text-muted-foreground">
          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-r-transparent" />
          Processing email summary...
        </div>
      );
    }

    // Error states
    if (summary === 'Error generating summary' || summary === 'Summary unavailable') {
      return (
        <div className="flex items-start text-sm bg-warning/10 p-2 rounded border border-warning/20">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 text-warning shrink-0"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-label="warning"
          >
            <title>Warning</title>
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <span className="text-warning">
            Unable to generate summary. Click "Regenerate" to try again or view the email content
            below.
          </span>
        </div>
      );
    }

    // Success state - actual summary
    return <p className="text-sm text-foreground leading-relaxed">{summary}</p>;
  };

  return (
    <div className="bg-muted/60 hover:bg-muted transition-colors rounded-lg p-2 sm:p-3 mb-3 sm:mb-4 border border-border shadow-sm">
      <div className="text-xs text-muted-foreground mb-2 flex items-center justify-between">
        <div className="flex items-center">
          <div className="h-4 w-4 mr-1 flex items-center justify-center rounded-full bg-primary/20">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-2.5 w-2.5 text-primary"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-label="info"
            >
              <title>Info</title>
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <span className="font-medium">AI Summary</span>
        </div>
        <RegenerateSummaryButton messageId={messageId} />
      </div>

      {getSummaryContent()}
    </div>
  );
};

// Use memo with proper dependencies to prevent unnecessary re-renders
export default memo(EmailSummary, (prevProps, nextProps) => {
  return prevProps.summary === nextProps.summary && prevProps.messageId === nextProps.messageId;
});
