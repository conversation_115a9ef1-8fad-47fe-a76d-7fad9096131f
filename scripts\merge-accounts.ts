/**
 * Account Merger Script
 * Merges user ID 3 (Gmail account) into user ID 1 (MSN account)
 */

import { storage } from '../server/storage';
import logger from '../server/lib/logger';

async function mergeUserAccounts() {
  const sourceUserId = 3; // Gmail account
  const targetUserId = 1; // MSN account

  try {
    logger.info('Starting account merge process', { sourceUserId, targetUserId });

    // Step 1: Get both user records
    const sourceUser = await storage.getUser(sourceUserId);
    const targetUser = await storage.getUser(targetUserId);

    if (!sourceUser) {
      throw new Error(`Source user ${sourceUserId} not found`);
    }
    if (!targetUser) {
      throw new Error(`Target user ${targetUserId} not found`);
    }

    logger.info('Found users to merge', {
      sourceUser: { id: sourceUser.id, email: sourceUser.email },
      targetUser: { id: targetUser.id, email: targetUser.email }
    });

    // Step 2: Get all emails from source user
    const sourceEmails = await storage.getEmails(sourceUserId, 1000); // Get all emails
    logger.info(`Found ${sourceEmails.length} emails to migrate`);

    // Step 3: Update all emails to target user
    for (const email of sourceEmails) {
      await storage.updateEmail(email.id, { userId: targetUserId });
      logger.debug(`Migrated email ${email.id} to user ${targetUserId}`);
    }

    // Step 4: Merge tokens and settings
    const updatedUser = {
      ...targetUser,
      gmailTokens: targetUser.gmailTokens || sourceUser.gmailTokens,
      outlookTokens: targetUser.outlookTokens || sourceUser.outlookTokens,
      providerTokens: targetUser.providerTokens || sourceUser.providerTokens,
      lastSync: new Date(Math.max(
        new Date(targetUser.lastSync || 0).getTime(),
        new Date(sourceUser.lastSync || 0).getTime()
      ))
    };

    await storage.updateUser(targetUserId, updatedUser);
    logger.info('Updated target user with merged data');

    // Step 5: Delete source user
    await storage.deleteUser(sourceUserId);
    logger.info(`Deleted source user ${sourceUserId}`);

    // Step 6: Verify the merge
    const finalEmails = await storage.getEmails(targetUserId, 1000);
    logger.info('Account merge completed successfully', {
      targetUserId,
      finalEmailCount: finalEmails.length
    });

    return {
      success: true,
      migratedEmails: sourceEmails.length,
      finalEmailCount: finalEmails.length
    };

  } catch (error) {
    logger.error('Account merge failed', { error: error.message });
    throw error;
  }
}

// Run the merge if this script is executed directly
if (require.main === module) {
  mergeUserAccounts()
    .then((result) => {
      console.log('✅ Account merge completed:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Account merge failed:', error.message);
      process.exit(1);
    });
}

export { mergeUserAccounts };
