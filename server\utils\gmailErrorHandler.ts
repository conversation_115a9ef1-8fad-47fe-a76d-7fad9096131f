/**
 * Gmail API Error Handler
 *
 * This module provides specialized error handling for Gmail API errors,
 * with detailed user-friendly messages and recovery steps.
 */

import type { NextFunction, Request, Response } from 'express';
import logger from '../lib/logger';
import { type AppError, ExternalApiError } from './errorHandler';

// Common Gmail error codes and their user-friendly messages
export const GMAIL_ERROR_MESSAGES: Record<string, { message: string; recovery: string }> = {
  // Authentication errors
  '401': {
    message: 'Your Gmail access has expired or been revoked',
    recovery: 'Please reconnect your Gmail account to continue using the service',
  },
  '403': {
    message: "Inbox Zero doesn't have permission to access your Gmail",
    recovery: 'Try reconnecting with full permission scope',
  },

  // Rate limiting and quota issues
  '429': {
    message: 'Gmail request limit reached',
    recovery: 'Please wait a few minutes before trying again',
  },
  userRateLimitExceeded: {
    message: "You've made too many requests to Gmail",
    recovery: 'Please wait a few minutes and try again',
  },
  dailyLimitExceeded: {
    message: 'Daily Gmail API quota exceeded',
    recovery: 'Service will be restored automatically tomorrow',
  },
  quotaExceeded: {
    message: 'Gmail API quota exceeded',
    recovery: 'Please try again in a few hours',
  },

  // Token errors
  invalid_grant: {
    message: 'Your Gmail authorization is no longer valid',
    recovery: 'Please reconnect your Gmail account',
  },
  invalid_client: {
    message: 'OAuth client configuration issue',
    recovery: 'Please contact support for assistance',
  },
  invalid_request: {
    message: 'Invalid request to Gmail API',
    recovery: 'Please try refreshing the page and trying again',
  },

  // Common operational errors
  backendError: {
    message: 'Gmail service is currently experiencing issues',
    recovery: 'Please try again later',
  },
  connectionClosed: {
    message: 'Connection to Gmail was interrupted',
    recovery: 'Please check your network and try again',
  },
  connectionReset: {
    message: 'Connection to Gmail was reset',
    recovery: 'Please check your network and try again',
  },
  badGateway: {
    message: 'Gmail service is temporarily unavailable',
    recovery: 'Please try again later',
  },
  serviceUnavailable: {
    message: 'Gmail service is temporarily unavailable',
    recovery: 'Please try again in a few minutes',
  },

  // Last resort
  default: {
    message: 'Error communicating with Gmail',
    recovery: 'Please try refreshing the page or reconnecting your account',
  },
};

/**
 * Identify Gmail error type from error details
 */
export function identifyGmailError(error: any): {
  code: string;
  message: string;
  recovery: string;
} {
  // Default fallback
  let errorInfo = GMAIL_ERROR_MESSAGES.default;
  let code = 'unknown';

  // Extract helpful information from error object
  if (error) {
    // Check for HTTP status codes
    if (error.status || error.statusCode) {
      const statusCode = String(error.status || error.statusCode);
      if (GMAIL_ERROR_MESSAGES[statusCode]) {
        return {
          code: statusCode,
          ...GMAIL_ERROR_MESSAGES[statusCode],
        };
      }
    }

    // Check for Google API error response format
    if (error.response?.data?.error) {
      const googleError = error.response.data.error;

      // Check for structured error objects
      if (typeof googleError === 'object') {
        // Check detailed error reason
        const reason = googleError.errors?.[0]?.reason || googleError.reason;
        if (reason && GMAIL_ERROR_MESSAGES[reason]) {
          return {
            code: reason,
            ...GMAIL_ERROR_MESSAGES[reason],
          };
        }

        // Check status
        if (googleError.code && GMAIL_ERROR_MESSAGES[String(googleError.code)]) {
          return {
            code: String(googleError.code),
            ...GMAIL_ERROR_MESSAGES[String(googleError.code)],
          };
        }

        // Use message as last resort
        if (googleError.message) {
          code = 'api_error';
          errorInfo = {
            message: googleError.message,
            recovery: 'Please try again later or contact support if the issue persists',
          };
        }
      }
      // Simple string error
      else if (typeof googleError === 'string') {
        // Check if it's one of our known errors
        for (const [errorKey, errorDetails] of Object.entries(GMAIL_ERROR_MESSAGES)) {
          if (googleError.includes(errorKey)) {
            return {
              code: errorKey,
              ...errorDetails,
            };
          }
        }

        code = 'api_error';
        errorInfo = {
          message: googleError,
          recovery: 'Please try again later or contact support if the issue persists',
        };
      }
    }

    // Check for OAuth-specific errors
    if (error.response?.data?.error || error.error) {
      const oauthError = error.response?.data?.error || error.error;
      if (GMAIL_ERROR_MESSAGES[oauthError]) {
        return {
          code: oauthError,
          ...GMAIL_ERROR_MESSAGES[oauthError],
        };
      }
    }

    // Check error message as last resort
    if (error.message) {
      for (const [errorKey, errorDetails] of Object.entries(GMAIL_ERROR_MESSAGES)) {
        if (error.message.includes(errorKey)) {
          return {
            code: errorKey,
            ...errorDetails,
          };
        }
      }
    }
  }

  return { code, ...errorInfo };
}

/**
 * Create a user-friendly Gmail error
 */
export function createGmailError(error: any): AppError {
  const { code, message, recovery } = identifyGmailError(error);

  const userMessage = recovery ? `${message}. ${recovery}.` : message;

  // Create an external API error with the user-friendly message
  const apiError = new ExternalApiError(userMessage, {
    originalError: error,
    gmailErrorCode: code,
    recovery,
  });

  // Log the detailed error
  logger.error(`Gmail API Error (${code}): ${message}`, {
    gmailErrorCode: code,
    originalMessage: error?.message || 'Unknown error',
    statusCode: error?.status || error?.statusCode,
    responseData: error?.response?.data,
    stack: error?.stack,
  });

  return apiError;
}

/**
 * Gmail error handling middleware for Express
 */
export function gmailErrorMiddleware(error: any, req: Request, res: Response, next: NextFunction) {
  // Check if response has already been sent
  if (res.headersSent) {
    return next(error);
  }

  // Only handle Gmail API errors
  if (
    req.path.includes('/gmail') ||
    req.path.includes('/google') ||
    req.path.includes('/email-providers') ||
    (error.originalError &&
      (String(error.originalError.message).toLowerCase().includes('gmail') ||
        String(error.originalError.message).toLowerCase().includes('google')))
  ) {
    const gmailError = createGmailError(error);

    return res.status(gmailError.statusCode).json({
      success: false,
      error: {
        type: gmailError.type,
        message: gmailError.message,
        gmailErrorCode: gmailError.context?.gmailErrorCode,
        recovery: gmailError.context?.recovery,
      },
    });
  }

  // Pass to next error handler if not a Gmail error
  next(error);
}

/**
 * Check if an error is likely related to Gmail
 */
export function isGmailError(error: any): boolean {
  if (!error) return false;

  // Check various properties that might indicate a Gmail error
  return (
    (error.config?.url && String(error.config.url).includes('googleapis.com/gmail')) ||
    (error.message && String(error.message).toLowerCase().includes('gmail')) ||
    (error.response?.config?.url &&
      String(error.response.config.url).includes('googleapis.com/gmail')) ||
    error.response?.data?.error?.errors?.some(
      (e: any) => e.domain === 'global' && e.reason?.includes('gmail')
    )
  );
}

/**
 * Format Gmail error message with recovery instructions
 */
export function formatGmailErrorForUser(error: any): string {
  const { message, recovery } = identifyGmailError(error);
  return recovery ? `${message}. ${recovery}.` : message;
}
