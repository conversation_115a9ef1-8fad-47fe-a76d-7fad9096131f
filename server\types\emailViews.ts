/**
 * Email View Types for Data Egress Optimization
 * 
 * Defines different email data structures for different use cases
 * to minimize data transfer and improve performance.
 */

import type { Email } from '@shared/schema';

/**
 * Minimal email data for list views
 * Excludes heavy content fields to reduce data transfer
 */
export interface EmailListItem {
  id: number;
  messageId: string;
  subject: string | null;
  snippet: string | null;
  sender: string | null;
  senderEmail: string | null;
  receivedAt: Date | null;
  isRead: boolean;
  isArchived: boolean;
  isTrashed: boolean;
  isImportant: boolean;
  isReplied: boolean;
  categories: string[];
  priority: string | null;
  snoozedUntil: Date | null;
  threadId: string | null;
  provider: string | null;
}

/**
 * Email summary for preview/card views
 * Includes summary and AI reply but excludes full content
 */
export interface EmailSummary extends EmailListItem {
  summary: string | null;
  aiReply: string | null;
  labelIds: string[];
}

/**
 * Full email data for detail views
 * Includes all content fields
 */
export interface EmailDetail extends Email {
  // This is the complete Email type - no exclusions
}

/**
 * Email metadata only (for admin/analytics)
 * Minimal data for system operations
 */
export interface EmailMetadata {
  id: number;
  messageId: string;
  userId: number;
  provider: string | null;
  receivedAt: Date | null;
  isRead: boolean;
  isArchived: boolean;
  isTrashed: boolean;
  isImportant: boolean;
  isReplied: boolean;
  categories: string[];
  priority: string | null;
  contentExpiresAt: Date | null;
  isContentEncrypted: boolean;
  retentionDays: number | null;
}

/**
 * Email content only (for processing/AI operations)
 * Just the content fields needed for AI processing
 */
export interface EmailContent {
  id: number;
  messageId: string;
  subject: string | null;
  originalContent: string | null;
  htmlContent: string | null;
  snippet: string | null;
  sender: string | null;
  senderEmail: string | null;
  receivedAt: Date | null;
}

/**
 * Email view type enumeration
 */
export enum EmailViewType {
  LIST = 'list',           // For email list views
  SUMMARY = 'summary',     // For email preview/cards
  DETAIL = 'detail',       // For full email view
  METADATA = 'metadata',   // For admin/analytics
  CONTENT = 'content',     // For AI processing
}

type EmailFieldSelections = {
  [key in EmailViewType]: Record<string, boolean>;
};

export const EMAIL_FIELD_SELECTIONS: EmailFieldSelections = {
  [EmailViewType.LIST]: {
    id: true,
    messageId: true,
    subject: true,
    snippet: true,
    sender: true,
    senderEmail: true,
    receivedAt: true,
    isRead: true,
    isArchived: true,
    isTrashed: true,
    isImportant: true,
    isReplied: true,
    categories: true,
    priority: true,
    snoozedUntil: true,
    threadId: true,
    provider: true,
    // Exclude heavy content fields
    originalContent: false,
    htmlContent: false,
    summary: false,
    aiReply: false,
    labelIds: false,
  },
  [EmailViewType.SUMMARY]: {
    id: true,
    messageId: true,
    subject: true,
    snippet: true,
    sender: true,
    senderEmail: true,
    receivedAt: true,
    isRead: true,
    isArchived: true,
    isTrashed: true,
    isImportant: true,
    isReplied: true,
    categories: true,
    priority: true,
    snoozedUntil: true,
    threadId: true,
    provider: true,
    // summary fields
    summary: true,
    aiReply: true,
    labelIds: true,
    // Exclude heavy content fields
    originalContent: false,
    htmlContent: false,
  },
  [EmailViewType.DETAIL]: {
    '*': true,
  },
  [EmailViewType.METADATA]: {
    id: true,
    messageId: true,
    userId: true,
    provider: true,
    receivedAt: true,
    isRead: true,
    isArchived: true,
    isTrashed: true,
    isImportant: true,
    isReplied: true,
    categories: true,
    priority: true,
    contentExpiresAt: true,
    isContentEncrypted: true,
    retentionDays: true,
    // Exclude all content and user-facing fields
    subject: false,
    snippet: false,
    sender: false,
    senderEmail: false,
    originalContent: false,
    htmlContent: false,
    summary: false,
    aiReply: false,
  },
  [EmailViewType.CONTENT]: {
    id: true,
    messageId: true,
    subject: true,
    originalContent: true,
    htmlContent: true,
    snippet: true,
    sender: true,
    senderEmail: true,
    receivedAt: true,
    // Exclude metadata and status fields
    isRead: false,
    isArchived: false,
    isTrashed: false,
    isImportant: false,
    categories: false,
    priority: false,
    summary: false,
    aiReply: false,
  },
};

/**
 * Estimated data size reduction for each view type
 * Based on typical email content sizes
 */
export const DATA_SIZE_REDUCTION = {
  [EmailViewType.LIST]: 85,      // 85% smaller than full email
  [EmailViewType.SUMMARY]: 70,   // 70% smaller than full email
  [EmailViewType.DETAIL]: 0,     // Full size
  [EmailViewType.METADATA]: 90,  // 90% smaller than full email
  [EmailViewType.CONTENT]: 60,   // 60% smaller (no metadata)
} as const;

/**
 * Type guard functions
 */
export function isEmailListItem(email: any): email is EmailListItem {
  return !!(email && typeof email.id === 'number' && typeof email.messageId === 'string');
}

export function isEmailSummary(email: any): email is EmailSummary {
  return !!(isEmailListItem(email) && 'summary' in email);
}

export function isEmailDetail(email: any): email is EmailDetail {
  return !!(email && 'originalContent' in email && 'htmlContent' in email);
}

/**
 * Utility function to determine appropriate view type based on context
 */
export function getOptimalViewType(context: {
  isListView?: boolean;
  needsContent?: boolean;
  needsSummary?: boolean;
  isAdminView?: boolean;
  isProcessing?: boolean;
}): EmailViewType {
  if (context.isProcessing) return EmailViewType.CONTENT;
  if (context.isAdminView) return EmailViewType.METADATA;
  if (context.needsContent) return EmailViewType.DETAIL;
  if (context.needsSummary) return EmailViewType.SUMMARY;
  if (context.isListView) return EmailViewType.LIST;
  
  // Default to summary for balanced performance
  return EmailViewType.SUMMARY;
}
