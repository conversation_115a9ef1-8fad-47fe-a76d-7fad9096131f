/**
 * Inbox Zero - Error Handling System
 *
 * This module provides centralized error handling functions for the application.
 * It allows for consistent error reporting, logging, and user-friendly messages.
 */
import { toast } from '@/hooks/use-toast';

// --- START GMAIL SPECIFIC ---
// Common Gmail error codes and their user-friendly messages
const GMAIL_ERROR_MESSAGES: Record<string, { message: string; recovery: string }> = {
  '401': {
    message: 'Your Gmail access has expired.',
    recovery: 'Please reconnect your Gmail account.',
  },
  '403': {
    message: "Inbox Zero doesn't have permission to access your Gmail.",
    recovery: 'Try reconnecting with full permissions.',
  },
  '429': {
    message: 'Gmail request limit reached.',
    recovery: 'Please wait a few minutes before trying again.',
  },
  invalid_grant: {
    message: 'Your Gmail authorization is no longer valid.',
    recovery: 'Please reconnect your Gmail account.',
  },
  default: {
    message: 'An error occurred while communicating with Gmail.',
    recovery: 'Please try again or reconnect your account if the issue persists.',
  },
};
// --- END GMAIL SPECIFIC ---

// Define error categories for more specific handling
export enum ErrorCategory {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  SERVER = 'server',
  CLIENT = 'client',
  API_SERVICE = 'api_service',
  UNKNOWN = 'unknown',
}

// Error interface with additional metadata
export interface AppError extends Error {
  category?: ErrorCategory;
  statusCode?: number;
  retryable?: boolean;
  details?: Record<string, any>;
  originalError?: Error;
}

// Default error messages for different categories
const DEFAULT_ERROR_MESSAGES: Record<ErrorCategory, string> = {
  [ErrorCategory.NETWORK]:
    'Network connection problem. Please check your internet connection and try again.',
  [ErrorCategory.AUTHENTICATION]: 'Authentication error. Please log in again.',
  [ErrorCategory.AUTHORIZATION]: 'You do not have permission to perform this action.',
  [ErrorCategory.VALIDATION]: 'Invalid data provided. Please check your inputs and try again.',
  [ErrorCategory.SERVER]: 'Server error occurred. Our team has been notified.',
  [ErrorCategory.CLIENT]: 'An error occurred in the application. Please refresh the page.',
  [ErrorCategory.API_SERVICE]: 'External service error. Please try again later.',
  [ErrorCategory.UNKNOWN]: 'An unexpected error occurred. Please try again later.',
};

/**
 * Creates a typed AppError with additional context
 */
export function createAppError(
  message: string,
  category: ErrorCategory = ErrorCategory.UNKNOWN,
  options: Partial<Omit<AppError, 'message' | 'name' | 'category'>> = {}
): AppError {
  const error = new Error(message) as AppError;
  error.category = category;

  // Add any additional properties
  Object.assign(error, options);

  return error;
}

/**
 * Parses a generic error into a structured AppError.
 * This is a fallback for errors that don't originate from our apiClient.
 */
export function parseApiError(error: any): AppError {
  // If it's already an AppError, just return it.
  if (error?.category && error.message) {
    return error as AppError;
  }

  // For generic errors, wrap them as an UNKNOWN client-side error.
  const message = error?.message || DEFAULT_ERROR_MESSAGES[ErrorCategory.UNKNOWN];
  return createAppError(message, ErrorCategory.CLIENT, {
    originalError: error,
    retryable: false,
  });
}

/**
 * Gets a user-friendly message for an error
 */
export function getUserFriendlyMessage(error: AppError): string {
  if (error.message) {
    return error.message;
  }

  return error.category
    ? DEFAULT_ERROR_MESSAGES[error.category]
    : DEFAULT_ERROR_MESSAGES[ErrorCategory.UNKNOWN];
}

/**
 * Gets action guidance based on error category
 */
export function getErrorActionGuidance(error: AppError): string | null {
  switch (error.category) {
    case ErrorCategory.NETWORK:
      return 'Check your internet connection and refresh the page.';
    case ErrorCategory.AUTHENTICATION:
      return 'Please sign in again to continue.';
    case ErrorCategory.AUTHORIZATION:
      return 'If you believe this is incorrect, contact support.';
    case ErrorCategory.VALIDATION:
      return 'Review the highlighted fields and correct any errors.';
    case ErrorCategory.API_SERVICE:
      return 'Try again later or contact support if the issue persists.';
    default:
      return null;
  }
}

/**
 * Log error to console and send to error reporting service
 */
export function logError(error: AppError, context: Record<string, any> = {}): void {
  // For development - log to console
  console.error('[ERROR]', {
    message: error.message,
    category: error.category,
    statusCode: error.statusCode,
    details: error.details,
    context,
    stack: error.stack,
  });

  // Lazily import and use the error reporting service.
  import('./errorReporting')
    .then((reporterModule) => {
      reporterModule.errorReporting.reportError(error, context);
    })
    .catch((err) => {
      // This catch is for if the import() itself fails.
      console.error('Failed to load error reporting module:', err);
    });
}

/**
 * Display an error toast to the user
 */
export function showErrorToast(error: AppError): void {
  const message = getUserFriendlyMessage(error);
  const guidance = getErrorActionGuidance(error);

  toast({
    title: getErrorToastTitle(error),
    description: guidance ? `${message} ${guidance}` : message,
    variant: 'destructive',
    duration: 5000,
  });
}

/**
 * Get an appropriate title for the error toast
 */
function getErrorToastTitle(error: AppError): string {
  // --- START GMAIL SPECIFIC ---
  if (error.details?.service === 'gmail') {
    const code = error.details.gmailErrorCode;
    if (code === '401' || code === '403' || code === 'invalid_grant') {
      return 'Gmail Connection Issue';
    }
    if (code === '429') {
      return 'Gmail Rate Limit Reached';
    }
    return 'Gmail Error';
  }
  // --- END GMAIL SPECIFIC ---

  switch (error.category) {
    case ErrorCategory.NETWORK:
      return 'Connection Error';
    case ErrorCategory.AUTHENTICATION:
      return 'Authentication Error';
    case ErrorCategory.AUTHORIZATION:
      return 'Access Denied';
    case ErrorCategory.VALIDATION:
      return 'Validation Error';
    case ErrorCategory.SERVER:
      return 'Server Error';
    case ErrorCategory.API_SERVICE:
      return 'Service Unavailable';
    default:
      return 'Error';
  }
}

/**
 * Handle an error with consistent logging and user feedback
 */
export function handleError(
  error: any,
  context: Record<string, any> = {},
  showToast = true
): AppError {
  // Ensure we are always working with a structured AppError.
  let appError = parseApiError(error);

  // --- START GMAIL SPECIFIC ---
  // Check if this is a gmail-related error and enrich it.
  const isGmailError =
    appError.details?.service === 'gmail' || appError.message.toLowerCase().includes('gmail');

  if (isGmailError) {
    const gmailErrorCode =
      appError.details?.gmailErrorCode ||
      Object.keys(GMAIL_ERROR_MESSAGES).find((key) => appError.message.includes(key)) ||
      'default';

    const errorInfo = GMAIL_ERROR_MESSAGES[gmailErrorCode] || GMAIL_ERROR_MESSAGES.default;
    const enrichedMessage = `${errorInfo.message} ${errorInfo.recovery}`;

    appError = createAppError(enrichedMessage, ErrorCategory.API_SERVICE, {
      ...appError,
      details: { ...appError.details, service: 'gmail', gmailErrorCode },
    });
  }
  // --- END GMAIL SPECIFIC ---

  // Log the error
  logError(appError, context);

  // Show toast if requested
  if (showToast) {
    showErrorToast(appError);
  }

  // We must re-throw the error to allow for proper promise rejection handling
  // in TanStack Query, etc.
  throw appError;
}

/**
 * Wrap async functions with consistent error handling
 */
export function withErrorHandling<T>(
  fn: (...args: any[]) => Promise<T>,
  context: Record<string, any> = {},
  showToast = true
): (...args: any[]) => Promise<T> {
  return async (...args: any[]) => {
    try {
      return await fn(...args);
    } catch (error) {
      handleError(error, { ...context, args }, showToast);
      throw error; // Re-throw to allow component-specific handling
    }
  };
}
