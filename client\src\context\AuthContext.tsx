/**
 * Authentication Context
 *
 * This context provides authentication state and methods to the entire app.
 * It handles user login, logout, and session management.
 */

import type { User } from '@shared/schema';
import { onAuthStateChanged } from 'firebase/auth';
import type React from 'react';
import { createContext, useCallback, useContext, useEffect, useState, useMemo, useRef } from 'react';
import { useLocation } from 'wouter';
import apiClient from '@/lib/apiClient';
import { auth } from '@/lib/firebase';
import logger from '@/lib/logger';

interface AuthError {
  code: string;
  message: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  authError: AuthError | null;
  logout: () => Promise<void>;
  clearAuthError: () => void;
  refetchUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [authError, setAuthError] = useState<AuthError | null>(null);
  const [, navigate] = useLocation();
  
  // Add debouncing to prevent rapid auth state changes
  const lastAuthProcessTime = useRef(0);
  const authProcessingRef = useRef(false);
  const AUTH_DEBOUNCE_MS = 1000; // 1 second debounce

  // Stabilize the handleApiError callback
  const handleApiError = useCallback((err: any) => {
    const errorCode = err?.response?.data?.code;
    const errorMessage = err?.response?.data?.message;

    if (errorCode === 'TOKEN_EXPIRED' || errorCode === 'TOKEN_REFRESH_FAILED') {
      setAuthError({
        code: errorCode,
        message: errorMessage || 'Your session has expired or could not be refreshed.',
      });
    }
  }, []); // Empty dependency array since this function doesn't depend on any props or state

  // Stabilize the refetchUser callback
  const refetchUser = useCallback(async () => {
    setLoading(true);
    try {
      const data = await apiClient.get<{ isAuthenticated: boolean; user: User }>(
        '/api/auth/status'
      );
      if (data.isAuthenticated && data.user) {
        setUser(data.user);
      } else {
        setUser(null);
      }
    } catch (err) {
      handleApiError(err);
      setError('Failed to refresh user data.');
      setUser(null);
    } finally {
      setLoading(false);
    }
  }, [handleApiError]); // Only depend on handleApiError which is now stable

  // Stabilize the logout callback
  const logout = useCallback(async () => {
    setLoading(true);
    try {
      await apiClient.post('/api/auth/logout');
      setUser(null);
      navigate('/login', { replace: true });
    } catch (err) {
      handleApiError(err);
      setError('Logout failed. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [navigate, handleApiError]); // Stable dependencies

  // Stabilize the clearAuthError callback
  const clearAuthError = useCallback(() => {
    setAuthError(null);
  }, []); // No dependencies needed

  useEffect(() => {
    // Keep the logger's user context in sync with the auth state
    logger.setUserId(user?.id?.toString());
  }, [user]);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      const now = Date.now();
      
      // Debounce rapid auth state changes to prevent infinite loops
      if (now - lastAuthProcessTime.current < AUTH_DEBOUNCE_MS) {
        logger.debug('[AuthContext] Auth state change debounced to prevent rapid processing');
        return;
      }
      
      // Prevent concurrent auth processing
      if (authProcessingRef.current) {
        logger.debug('[AuthContext] Auth processing already in progress, skipping');
        return;
      }
      
      lastAuthProcessTime.current = now;
      authProcessingRef.current = true;

      if (firebaseUser) {
        setLoading(true);
        try {
          // When the Firebase user is detected, this is the single source of truth
          // for establishing our backend session and getting the app user details.
          const idToken = await firebaseUser.getIdToken(true);
          // Directly use apiClient to verify the token with the backend
          const user = await apiClient.post<User>('/api/auth/firebase/verify', { idToken });

          if (user) {
            setUser(user); // Set the user in the context
          } else {
            // This case indicates a server-side issue where the session wasn't created properly.
            setUser(null);
            setError(
              'Could not establish a server session. The user object was missing from the response.'
            );
          }
        } catch (err: any) {
          console.error('[AuthContext] Session establishment failed:', err);
          setError(err.message || 'An unknown error occurred during session setup.');
          setUser(null);
        } finally {
          setLoading(false);
          authProcessingRef.current = false;
        }
      } else {
        // If there's no Firebase user, we are logged out.
        setUser(null);
        setLoading(false);
        authProcessingRef.current = false;
      }
    });

    return () => unsubscribe();
  }, []); // Empty dependency array since this should only run once

  // Memoize the context value with only the actual state values, not the functions
  // The functions are already memoized with useCallback above
  const value = useMemo(
    () => ({ user, loading, error, authError, logout, clearAuthError, refetchUser }),
    [user, loading, error, authError, logout, clearAuthError, refetchUser]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export { AuthContext };
