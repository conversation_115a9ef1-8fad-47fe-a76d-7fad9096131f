import { useMutation } from '@tanstack/react-query';
import { format } from 'date-fns';
import { Bar<PERSON><PERSON>, Download, Printer, Share2 } from 'lucide-react';
import type React from 'react';
import { useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useEmailList } from '@/context/EmailListContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { useToast } from '@/hooks/use-toast';
import type { Email } from '@/types/email';

interface SummaryStats {
  totalReceived: number;
  totalProcessed: number;
  totalReplied: number;
  categoryCounts: Record<string, number>;
  timeSpent: number; // in minutes
}

// A new sub-component to render the progress bar without inline styles.
const CategoryProgressBar = ({ width, category }: { width: number; category: string }) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      ref.current.style.setProperty('--progress-width', `${width}%`);
    }
  }, [width]);

  const categoryClass =
    category === 'Urgent'
      ? 'bg-destructive'
      : category === 'Work'
        ? 'bg-primary'
        : category === 'Personal'
          ? 'bg-secondary-foreground'
          : category === 'Promotions'
            ? 'bg-warning'
            : 'bg-muted-foreground';

  return (
    <div ref={ref} className={`h-1.5 w-[var(--progress-width)] rounded-full ${categoryClass}`} />
  );
};

const DailyDigest: React.FC = () => {
  const { emails } = useEmailList();
  const { toast } = useToast();

  // Generate digest based on emails for today
  const generateDigestData = () => {
    const now = new Date();
    const filtered = emails.filter((email) => {
      if (!email.receivedAt) return false;
      const emailDate = new Date(email.receivedAt);
      return (
        emailDate.getDate() === now.getDate() &&
        emailDate.getMonth() === now.getMonth() &&
        emailDate.getFullYear() === now.getFullYear()
      );
    });

    return filtered;
  };

  // Calculate statistics from filtered emails
  const calculateStats = (filteredEmails: Email[]): SummaryStats => {
    const stats: SummaryStats = {
      totalReceived: filteredEmails.length,
      totalProcessed: filteredEmails.filter((e) => e.isRead || e.isArchived).length,
      totalReplied: 0, // In a real app, this would track emails you've replied to
      categoryCounts: {},
      timeSpent: Math.floor(filteredEmails.length * 2.5), // Rough estimate: 2.5 min per email
    };

    // Calculate category counts
    for (const email of filteredEmails) {
      if (email.categories && Array.isArray(email.categories)) {
        for (const category of email.categories) {
          if (!stats.categoryCounts[category]) {
            stats.categoryCounts[category] = 0;
          }
          stats.categoryCounts[category]++;
        }
      }
    }

    return stats;
  };

  const filteredEmails = generateDigestData();
  const stats = calculateStats(filteredEmails);

  // Calculate efficiency rate
  const efficiencyRate =
    stats.totalReceived > 0 ? Math.round((stats.totalProcessed / stats.totalReceived) * 100) : 0;

  // Email mutation to send yourself the digest
  const { mutate: emailDigest, isPending: isSending } = useMutation({
    mutationFn: async () => {
      // This would typically send an API request to email the digest
      await new Promise((resolve) => setTimeout(resolve, 1500)); // Simulate API call
      return true;
    },
    onSuccess: () => {
      toast({
        title: 'Digest sent',
        description: 'Your email digest has been sent to your inbox.',
      });
    },
    onError: () => {
      toast({
        title: 'Failed to send digest',
        description: 'There was an error sending your digest.',
        variant: 'destructive',
      });
    },
  });

  const isMobile = useIsMobile();

  return (
    <div className="p-2 sm:p-4 md:p-6 overflow-y-auto">
      <Card className="overflow-visible">
        <CardHeader className="pb-2 sm:pb-3">
          <div className="flex flex-col sm:flex-row justify-between sm:items-center space-y-2 sm:space-y-0">
            <div>
              <CardTitle className="text-base sm:text-lg">Summarize My Day</CardTitle>
              <CardDescription className="text-xs sm:text-sm">
                Daily email activity recap
              </CardDescription>
            </div>
            <div className="text-xs sm:text-sm text-muted-foreground">
              {format(new Date(), isMobile ? 'MMM d, yyyy' : 'EEEE, MMMM d, yyyy')}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4 sm:space-y-6 overflow-visible">
          <div className="space-y-4 sm:space-y-6">
            {/* Summary Header */}
            <Card className="bg-muted/30 border">
              <CardContent className="pt-4 sm:pt-6 p-3 sm:p-6">
                <div className="flex items-start space-x-3 sm:space-x-4">
                  <div className="bg-primary-muted p-1.5 sm:p-2 rounded-full flex-shrink-0">
                    <BarChart className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-sm sm:text-base font-medium mb-1 sm:mb-2">
                      Today's Email Activity
                    </h3>
                    <p className="text-xs sm:text-sm text-foreground/80 leading-relaxed">
                      You received {stats.totalReceived} emails today and processed{' '}
                      {stats.totalProcessed} of them, achieving a {efficiencyRate}% completion rate.
                      Great job keeping your inbox under control!
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Statistics Dashboard */}
            <div className="space-y-4 sm:space-y-6">
              {/* Stats Cards - 2x2 grid on mobile, 4x1 on desktop */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 sm:gap-4">
                <div className="p-3 sm:p-4 bg-primary-muted rounded-md border border-border">
                  <div className="text-lg sm:text-xl font-bold text-primary">
                    {stats.totalReceived}
                  </div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Emails Received</div>
                </div>
                <div className="p-3 sm:p-4 bg-success/20 rounded-md border border-border">
                  <div className="text-lg sm:text-xl font-bold text-success">
                    {stats.totalProcessed}
                  </div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Emails Processed</div>
                </div>

                <div className="p-3 sm:p-4 bg-secondary/50 rounded-md border border-border">
                  <div className="text-lg sm:text-xl font-bold text-secondary-foreground">
                    {efficiencyRate}%
                  </div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Completion Rate</div>
                </div>
                <div className="p-3 sm:p-4 bg-warning/20 rounded-md border border-border">
                  <div className="text-lg sm:text-xl font-bold text-warning">
                    ~{stats.timeSpent} min
                  </div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Time Saved</div>
                </div>
              </div>

              {/* Category Breakdown */}
              <div>
                <h3 className="text-xs sm:text-sm font-medium text-foreground/80 mb-2 sm:mb-3">
                  Categories Breakdown
                </h3>
                <div className="p-3 sm:p-4 bg-card rounded-md border border-border">
                  {Object.entries(stats.categoryCounts).length > 0 ? (
                    <div className="space-y-2 sm:space-y-3">
                      {Object.entries(stats.categoryCounts)
                        .sort((a, b) => b[1] - a[1])
                        .map(([category, count]) => (
                          <div key={category}>
                            <div className="flex justify-between items-center mb-1">
                              <span className="text-xs sm:text-sm text-foreground">{category}</span>
                              <span className="text-xs sm:text-sm font-medium text-foreground">
                                {count} ({Math.round((count / stats.totalReceived) * 100) || 0}
                                %)
                              </span>
                            </div>
                            <div className="w-full bg-muted rounded-full h-1.5">
                              <CategoryProgressBar
                                width={Math.round((count / stats.totalReceived) * 100) || 0}
                                category={category}
                              />
                            </div>
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="text-center text-xs sm:text-sm text-muted-foreground py-3">
                      No categorized emails today
                    </div>
                  )}
                </div>
              </div>

              {/* Action Buttons - Stack on mobile, flex on desktop */}
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-0 mt-3 sm:mt-4 pt-3 sm:pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => emailDigest()}
                  disabled={isSending}
                  className="w-full sm:w-auto justify-center"
                >
                  <Share2 className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-2" />
                  {isSending ? 'Sending...' : 'Email to me'}
                </Button>

                <div className="grid grid-cols-2 sm:flex sm:space-x-2 gap-2 sm:gap-0">
                  <Button variant="outline" className="w-full sm:w-auto justify-center">
                    <Printer className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                    {isMobile ? '' : 'Print'}
                  </Button>
                  <Button variant="outline" className="w-full sm:w-auto justify-center">
                    <Download className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                    {isMobile ? '' : 'Download'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DailyDigest;
