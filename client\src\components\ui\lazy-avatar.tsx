import { User } from 'lucide-react';
import type React from 'react';
import { memo, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';

interface LazyAvatarProps {
  src?: string | null;
  alt: string;
  fallback?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  fallbackClassName?: string;
}

/**
 * LazyAvatar component with optimized image loading
 * Shows a fallback or skeleton while the image loads
 */
const LazyAvatar: React.FC<LazyAvatarProps> = ({
  src,
  alt,
  fallback,
  size = 'md',
  className = '',
  fallbackClassName = '',
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Get size-specific classes
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
  }[size];

  // Handle successful load
  const handleLoad = () => {
    setIsLoaded(true);
  };

  // Handle load error
  const handleError = () => {
    setHasError(true);
  };

  // Get the fallback text (initials) from alt text
  const getFallbackText = () => {
    if (fallback) return fallback;

    // Generate initials from alt text
    return alt
      .split(' ')
      .map((name) => name[0])
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  return (
    <Avatar className={`${sizeClasses} ${className}`}>
      {/* Only render image if there's a src and no error */}
      {src && !hasError ? (
        <>
          {/* Show skeleton while loading */}
          {!isLoaded && <Skeleton className="absolute inset-0 rounded-full" />}

          {/* Avatar image with lazy loading */}
          <AvatarImage
            src={src}
            alt={alt}
            onLoad={handleLoad}
            onError={handleError}
            loading="lazy"
            className={isLoaded ? 'opacity-100' : 'opacity-0'}
          />
        </>
      ) : null}

      {/* Always render fallback - it will show when image is missing or fails to load */}
      <AvatarFallback className={fallbackClassName}>
        {getFallbackText() || <User className="h-4 w-4" />}
      </AvatarFallback>
    </Avatar>
  );
};

// Memoize to prevent unnecessary re-renders
export default memo(LazyAvatar);
