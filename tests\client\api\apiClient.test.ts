// @ts-nocheck

import { jest } from '@jest/globals';
import { act } from '@testing-library/react';

// @ts-nocheck

// ---------------------- Global fetch mock ----------------------
const mockFetch = jest.fn();
global.fetch = mockFetch as any;

// Helper to create fake Response
interface FakeResponse {
  ok: boolean;
  status: number;
  statusText: string;
  headers: {
    get: (name: string) => string | null;
  };
  json: () => Promise<any>;
  text: () => Promise<string>;
}

function createResponse(body: any, init?: Partial<FakeResponse>): FakeResponse {
  const status = init?.status ?? 200;
  const statusText = init?.statusText ?? 'OK';
  // Normalize header keys to lowercase for easier lookup
  const normalizedHeaders: Record<string, string> = {};
  const rawHeaders = init?.headers ?? { 'content-type': 'application/json' };
  for (const [key, value] of Object.entries(rawHeaders)) {
    normalizedHeaders[key.toLowerCase()] = value;
  }

  return {
    ok: status >= 200 && status < 300,
    status,
    statusText,
    // Minimal mock of the Headers interface used by apiClient (only .get is required)
    headers: {
      get: (name: string) => normalizedHeaders[name.toLowerCase()] ?? null,
    } as any,
    json: async () => body,
    text: async () => (typeof body === 'string' ? body : JSON.stringify(body)),
  } as FakeResponse;
}

// ---------------------- Firebase mocks -------------------------
const mockGetIdToken = jest.fn<Promise<string>, []>();
const mockCurrentUser = { getIdToken: mockGetIdToken } as any;
jest.mock('@/lib/firebase', () => ({
  __esModule: true,
  default: { currentUser: mockCurrentUser },
}));

// ---------------------- CSRF fetch mock ------------------------
const dummyCsrf = 'csrf-token-1234';
mockFetch.mockImplementation((url: unknown, init?: unknown) => {
  // CSRF endpoint
  if (url === '/api/auth/csrf') {
    return Promise.resolve(createResponse({ csrfToken: dummyCsrf }));
  }
  return Promise.resolve(createResponse({ ok: true }));
});

// ---------------------- Import client --------------------------
import apiClient, { clearCsrfTokenCache } from '@/lib/apiClient';

beforeEach(() => {
  jest.clearAllMocks();
  (mockGetIdToken as jest.Mock).mockResolvedValue('firebase-id-token');
  clearCsrfTokenCache();
});

describe('apiClient core behaviour', () => {
  it('injects Firebase ID token on authenticated request', async () => {
    await apiClient.get('/api/secure');

    // Only a single fetch call should be made for a GET request
    expect(mockFetch).toHaveBeenCalledTimes(1);
    const [, options] = mockFetch.mock.calls[0];
    expect((options as RequestInit)?.headers).toMatchObject({
      Authorization: 'Bearer firebase-id-token',
    });
  });

  it('retrieves and attaches CSRF token on mutation', async () => {
    await apiClient.post('/api/mutate', { data: 1 });

    // First fetch call should be to CSRF endpoint
    expect(mockFetch.mock.calls[0][0]).toBe('/api/auth/csrf');

    const [, options] = mockFetch.mock.calls[1];
    expect((options as RequestInit)?.headers).toMatchObject({ 'X-CSRF-Token': dummyCsrf });
  });

  it('retries failed network up to 2 times by default', async () => {
    let retryAttempts = 0;
    const failing: jest.Mock<Promise<FakeResponse>, [string, RequestInit?]> = jest.fn((url: string) => {
      if (url === '/api/retry') {
        if (retryAttempts === 0) {
          retryAttempts += 1;
          return Promise.resolve(
            createResponse({ error: 'server' }, { status: 500, statusText: 'Internal' })
          );
        }
        return Promise.resolve(createResponse({ ok: true }));
      }
      return Promise.resolve(createResponse({ ok: true }));
    });

    // First actual fetch (CSRF) should succeed, second (/api/retry) fails, third retries and succeeds
    mockFetch.mockImplementation((url: string, init?: RequestInit) => {
      if (url === '/api/auth/csrf') {
        return Promise.resolve(createResponse({ csrfToken: dummyCsrf }));
      }

      return failing(url, init);
    });

    const res = await apiClient.post('/api/retry', { x: 1 });
    expect(res.ok).toBeTruthy();
    // One failure + one success for /api/retry
    expect(failing).toHaveBeenCalledTimes(2);
  });

  it('throws on authentication failure (401)', async () => {
    mockFetch.mockImplementationOnce((url: string) => {
      if (url === '/api/auth/csrf') return Promise.resolve(createResponse({ csrfToken: dummyCsrf }));
      return Promise.resolve(createResponse({ message: 'unauth' }, { status: 401, statusText: 'Unauthorized' }));
    });

    await expect(apiClient.get('/api/secure')).rejects.toThrow('401');
  });
}); 