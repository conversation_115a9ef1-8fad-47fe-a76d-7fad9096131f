import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';
import { cn } from '@/lib/utils';

const statusIndicatorVariants = cva('inline-flex h-2 w-2 rounded-full', {
  variants: {
    variant: {
      success: 'bg-success',
      warning: 'bg-warning',
      error: 'bg-destructive',
      info: 'bg-info',
      neutral: 'bg-muted-foreground',
      processing: 'bg-warning animate-pulse-subtle',
    },
    size: {
      sm: 'h-1.5 w-1.5',
      md: 'h-2 w-2',
      lg: 'h-2.5 w-2.5',
    },
  },
  defaultVariants: {
    variant: 'neutral',
    size: 'md',
  },
});

export interface StatusIndicatorProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof statusIndicatorVariants> {
  label?: string;
}

const StatusIndicator = React.forwardRef<HTMLSpanElement, StatusIndicatorProps>(
  ({ className, variant, size, label, ...props }, ref) => {
    return (
      <span className={cn('relative inline-flex', className)} ref={ref} {...props}>
        <span className={cn(statusIndicatorVariants({ variant, size }))} aria-hidden="true" />
        {label && <span className="sr-only">{label}</span>}
      </span>
    );
  }
);

StatusIndicator.displayName = 'StatusIndicator';

export { StatusIndicator, statusIndicatorVariants };
