import type React from 'react';
import { <PERSON> } from 'wouter';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { dropdownActionItems, dropdownNavItems } from '@/config/navigation';
import { useAuth } from '@/context/AuthContext';
import { getUserInitials } from '@/lib/utils';

export function UserAccountMenu() {
  const { user, loading, logout } = useAuth();

  const handleLogout = async (e: React.MouseEvent) => {
    e.preventDefault();
    await logout();
  };

  if (loading) {
    return (
      <Button variant="ghost" size="sm" disabled>
        <LoadingSpinner size="sm" className="mr-2" />
        Loading...
      </Button>
    );
  }

  if (!user) {
    return (
      <Link href="/login">
        <a className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3">
          Sign In
        </a>
      </Link>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative h-8 w-8 rounded-full">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.picture || undefined} alt={user.name || user.email || 'User'} />
            <AvatarFallback>{getUserInitials(user.name || user.email || '')}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{user.name || 'User'}</p>
            <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {dropdownNavItems.map((item) => (
            <DropdownMenuItem key={item.path} asChild>
              <Link href={item.path}>
                <a className="w-full flex items-center">
                  <item.icon className="mr-2 h-4 w-4" />
                  <span>{item.name}</span>
                </a>
              </Link>
            </DropdownMenuItem>
          ))}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        {dropdownActionItems.map((action) => (
          <DropdownMenuItem key={action.path} asChild>
            {action.path === '/logout' ? (
              <a href="/logout" onClick={handleLogout} className="w-full flex items-center">
                <action.icon className="mr-2 h-4 w-4" />
                <span>{action.name}</span>
              </a>
            ) : (
              <Link href={action.path}>
                <a className="w-full flex items-center">
                  <action.icon className="mr-2 h-4 w-4" />
                  <span>{action.name}</span>
                </a>
              </Link>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
