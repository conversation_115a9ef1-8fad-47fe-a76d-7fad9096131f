import { Search, X } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface EmailSearchProps {
  onSearch: (query: string) => void;
  searchQuery: string;
  autoFocus?: boolean;
  className?: string;
}

const EmailSearch: React.FC<EmailSearchProps> = ({
  onSearch,
  searchQuery,
  autoFocus = false,
  className = '',
}) => {
  const [localQuery, setLocalQuery] = useState(searchQuery);
  const [isExpanded, setIsExpanded] = useState(!!searchQuery);

  // Update local query when the external searchQuery changes
  useEffect(() => {
    setLocalQuery(searchQuery);
    if (searchQuery) {
      setIsExpanded(true);
    }
  }, [searchQuery]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(localQuery);
  };

  const handleClear = () => {
    setLocalQuery('');
    onSearch('');
  };

  const _toggleSearch = () => {
    if (isExpanded && localQuery) {
      // If clearing the search
      handleClear();
    } else {
      // Toggle the search expansion
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <div
      className={cn(
        'transition-all duration-300 flex items-center',
        isExpanded ? 'w-full' : 'w-auto',
        className
      )}
    >
      {isExpanded ? (
        <form onSubmit={handleSearch} className="relative w-full">
          <Input
            type="text"
            placeholder="Search emails..."
            value={localQuery}
            onChange={(e) => setLocalQuery(e.target.value)}
            className="h-8 pl-8 pr-8 text-sm"
            autoFocus={autoFocus}
          />
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />

          {localQuery && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-2"
              onClick={handleClear}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Clear search</span>
            </Button>
          )}
        </form>
      ) : (
        <Button variant="ghost" size="sm" className="p-1.5" onClick={() => setIsExpanded(true)}>
          <Search className="h-5 w-5" />
          <span className="sr-only">Search emails</span>
        </Button>
      )}

      {isExpanded && !className.includes('flex-1') && (
        <Button
          variant="ghost"
          size="sm"
          className="ml-1 p-1.5"
          onClick={() => setIsExpanded(false)}
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close search</span>
        </Button>
      )}
    </div>
  );
};

export default EmailSearch;
