import { isEqual } from 'lodash-es';
import { useMemo, useRef } from 'react';

/**
 * Custom hook that only returns a new object when a deep comparison indicates the dependencies have changed
 * @param value The value to memoize
 * @param compare Function to compare previous and current values, defaults to deep equality check
 * @returns Memoized value that only changes when dependencies change based on deep comparison
 */
export function useMemoCompare<T>(value: T, compare = isEqual): T {
  // Ref for storing previous value
  const previousRef = useRef<T>(value);
  const previous = previousRef.current;

  // Use traditional memo to avoid unnecessary work if value is the same by reference
  const memoizedValue = useMemo(() => {
    // Only update ref if the values are not equal
    if (previous === undefined || !compare(previous, value)) {
      previousRef.current = value;
    }
    return previousRef.current;
  }, [value, compare, previous]);

  return memoizedValue;
}
