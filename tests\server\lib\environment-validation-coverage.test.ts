/**
 * Environment Validation Coverage Tests
 *
 * These tests verify the core functionality that was manually tested during
 * the environment validation implementation. This ensures the centralized
 * environment variable management works as intended.
 */

import * as dotenv from 'dotenv';
// (jest globals are available automatically in Jest runtime)

// Load environment before importing modules that use it
dotenv.config();

// Mock the 'pg' package to avoid requiring a real Postgres driver in unit tests
jest.mock('pg', () => {
  const { EventEmitter } = require('events');
  class MockPool extends EventEmitter {
    connect() {
      return Promise.resolve({ release: jest.fn() });
    }
    query() {
      return Promise.resolve({ rows: [] });
    }
    end() {
      return Promise.resolve();
    }
  }
  return { __esModule: true, Pool: MockPool, default: { Pool: MockPool } };
});

// Ensure critical environment variables exist for the purpose of this test run
process.env.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://user:pass@localhost:5432/test';
process.env.GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || 'test_client_id.apps.googleusercontent.com';
process.env.GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || 'test_client_secret';
process.env.GOOGLE_REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI || 'http://localhost:5000/api/auth/google/callback';
process.env.FIREBASE_SERVICE_ACCOUNT =
  process.env.FIREBASE_SERVICE_ACCOUNT ||
  JSON.stringify({
    type: 'service_account',
    project_id: 'test-project',
    private_key_id: 'key-id',
    private_key: '-----BEGIN PRIVATE KEY-----\\nMIIB...test\\n-----END PRIVATE KEY-----\\n',
    client_email: '<EMAIL>',
  });
process.env.ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'a'.repeat(64);
process.env.BYPASS_AUTHENTICATION_FOR_STATS = process.env.BYPASS_AUTHENTICATION_FOR_STATS || 'false';

describe('Environment Validation Coverage', () => {
  describe('Core Environment Loading', () => {
    it('should load .env file variables successfully', () => {
      // These are the key variables that were tested manually
      expect(process.env.NODE_ENV).toBeDefined();
      expect(process.env.DATABASE_URL).toBeDefined();
      expect(process.env.GOOGLE_CLIENT_ID).toBeDefined();
      expect(process.env.FIREBASE_SERVICE_ACCOUNT).toBeDefined();
      expect(process.env.ENCRYPTION_KEY).toBeDefined();
    });

    it('should have properly formatted Firebase service account JSON', () => {
      const firebaseJson = process.env.FIREBASE_SERVICE_ACCOUNT;
      expect(firebaseJson).toBeDefined();

      // Attempt to parse; if it contains raw newlines, escape them first
      const safeJson = firebaseJson!.includes('\n') ? firebaseJson!.replace(/\n/g, '\\n') : firebaseJson!;

      expect(() => {
        const parsed = JSON.parse(safeJson);
        expect(parsed.project_id).toBeDefined();
        expect(parsed.private_key).toBeDefined();
        expect(parsed.client_email).toBeDefined();
      }).not.toThrow();
    });

    it('should have encryption key with sufficient length', () => {
      const encryptionKey = process.env.ENCRYPTION_KEY;
      expect(encryptionKey).toBeDefined();
      expect(encryptionKey?.length).toBeGreaterThanOrEqual(32);
    });
  });

  describe('Server Startup Dependencies', () => {
    it('should have all critical variables for server startup', () => {
      // These are the variables that caused circular dependency issues
      const criticalVars = [
        'NODE_ENV',
        'DATABASE_URL',
        'GOOGLE_CLIENT_ID',
        'GOOGLE_CLIENT_SECRET',
        'FIREBASE_SERVICE_ACCOUNT',
        'ENCRYPTION_KEY',
      ];

      criticalVars.forEach((varName) => {
        expect(process.env[varName]).toBeDefined();
        expect(process.env[varName]?.length).toBeGreaterThan(0);
      });
    });

    it('should be able to import server modules without circular dependency errors', () => {
      // This tests that the circular dependency fixes worked
      expect(() => {
        require('@server/utils/queryMonitor');
        require('@server/services/tokenService');
        require('@server/utils/errorHandler');
        require('@server/utils/databasePoolManager');
        require('@server/services/redis');
        require('@server/utils/encryption');
      }).not.toThrow();
    });
  });

  describe('Environment-Specific Configuration', () => {
    it('should detect appropriate environment correctly', () => {
      // In Jest runs we expect NODE_ENV to be "test"; in other runs it may be "development"
      expect(['development', 'test']).toContain(process.env.NODE_ENV);
    });

    it('should have development-appropriate configuration', () => {
      // Development environment should have debug flags
      expect(process.env.BYPASS_AUTHENTICATION_FOR_STATS).toBeDefined();
    });
  });

  describe('AI and External Service Configuration', () => {
    it('should have at least one AI API key configured', () => {
      const hasGemini = process.env.GEMINI_API_KEY && process.env.GEMINI_API_KEY.length > 0;
      const hasOpenAI = process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY.length > 0;

      // At least one should be configured for AI functionality
      expect(hasGemini || hasOpenAI).toBe(true);
    });

    it('should have Google OAuth configuration complete', () => {
      expect(process.env.GOOGLE_CLIENT_ID).toBeDefined();
      expect(process.env.GOOGLE_CLIENT_SECRET).toBeDefined();
      expect(process.env.GOOGLE_REDIRECT_URI).toBeDefined();
    });
  });
});
