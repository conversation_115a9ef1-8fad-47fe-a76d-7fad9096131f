import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { Skeleton } from './skeleton';

interface ProgressiveImageProps {
  src: string;
  placeholderSrc?: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
}

export const ProgressiveImage: React.FC<ProgressiveImageProps> = ({
  src,
  placeholderSrc,
  alt,
  className,
  width,
  height,
}) => {
  const [imgSrc, setImgSrc] = useState(
    placeholderSrc ||
      'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"%3E%3C/svg%3E'
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);
  const ref = useRef<HTMLDivElement | HTMLImageElement>(null);

  useEffect(() => {
    // Reset states when src changes
    setIsLoading(true);
    setError(false);

    // Create new image to preload
    const img = new Image();
    img.src = src;

    img.onload = () => {
      setImgSrc(src);
      setIsLoading(false);
    };

    img.onerror = () => {
      setIsLoading(false);
      setError(true);
    };

    return () => {
      // Cancel image load on cleanup
      img.onload = null;
      img.onerror = null;
    };
  }, [src]);

  // Set width and height programmatically to avoid inline styles
  useEffect(() => {
    const element = ref.current;
    if (element) {
      if (width) element.style.setProperty('--width', `${width}px`);
      if (height) element.style.setProperty('--height', `${height}px`);
    }
  }, [width, height]);

  const sizeClass = 'w-[var(--width,100%)] h-[var(--height,auto)]';

  if (isLoading) {
    return <Skeleton className={cn(sizeClass, className)} />;
  }

  if (error) {
    return (
      <div
        ref={ref as React.Ref<HTMLDivElement>}
        className={cn('bg-muted flex items-center justify-center', sizeClass, className)}
      >
        <span className="text-muted-foreground text-sm">Failed to load image</span>
      </div>
    );
  }

  return (
    <img
      ref={ref as React.Ref<HTMLImageElement>}
      src={imgSrc}
      alt={alt}
      className={cn('transition-opacity duration-300', sizeClass, className)}
    />
  );
};
