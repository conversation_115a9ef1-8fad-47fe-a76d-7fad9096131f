import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertTriangle, LogIn, Refresh<PERSON>w, Server, Wifi } from 'lucide-react';
import type React from 'react';
import { useLocation } from 'wouter';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { type AppError, ErrorCategory } from '@/lib/errorHandler';

interface ErrorDisplayProps {
  error?: AppError | Error | null;
  title?: string;
  description?: string;
  showRetry?: boolean;
  onRetry?: () => void;
  className?: string;
}

/**
 * ErrorDisplay component
 * Displays user-friendly error messages with appropriate icons and actions
 */
export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  title,
  description,
  showRetry = true,
  onRetry,
  className = '',
}) => {
  const [, navigate] = useLocation();
  
  // Determine if it's an AppError with our extended properties
  const appError = error && 'category' in error ? (error as AppError) : null;
  const errorCategory = appError?.category || ErrorCategory.UNKNOWN;

  // Default messages if none provided
  const defaultTitle = getErrorTitle(errorCategory);
  const defaultDescription = error?.message || 'An unexpected error occurred.';

  // Icons based on error category
  const IconComponent = getErrorIcon(errorCategory);

  // Whether to show login button for auth errors
  const showLoginButton = errorCategory === ErrorCategory.AUTHENTICATION;

  const handleLoginRedirect = () => {
    navigate('/login');
  };

  const handlePageRefresh = () => {
    window.location.reload();
  };

  return (
    <div className={`rounded-md ${className}`}>
      <Alert variant="destructive" className="mb-4">
        <IconComponent className="h-4 w-4" />
        <AlertTitle>{title || defaultTitle}</AlertTitle>
        <AlertDescription className="mt-1">{description || defaultDescription}</AlertDescription>
      </Alert>

      <div className="flex flex-wrap gap-2 justify-start mt-3">
        {showRetry && onRetry && !showLoginButton && (
          <Button onClick={onRetry} variant="outline" size="sm" className="flex items-center">
            <RefreshCw className="mr-2 h-3.5 w-3.5" />
            Try Again
          </Button>
        )}

        {showLoginButton && (
          <Button
            onClick={handleLoginRedirect}
            variant="default"
            size="sm"
            className="flex items-center"
          >
            <LogIn className="mr-2 h-3.5 w-3.5" />
            Sign In
          </Button>
        )}

        {errorCategory === ErrorCategory.NETWORK && (
          <Button
            onClick={handlePageRefresh}
            variant="outline"
            size="sm"
            className="flex items-center"
          >
            <RefreshCw className="mr-2 h-3.5 w-3.5" />
            Refresh Page
          </Button>
        )}
      </div>
    </div>
  );
};

/**
 * Returns the appropriate error icon based on the error category
 */
function getErrorIcon(category: ErrorCategory) {
  switch (category) {
    case ErrorCategory.NETWORK:
      return Wifi;
    case ErrorCategory.AUTHENTICATION:
    case ErrorCategory.AUTHORIZATION:
      return LogIn;
    case ErrorCategory.SERVER:
    case ErrorCategory.API_SERVICE:
      return Server;
    case ErrorCategory.VALIDATION:
      return AlertTriangle;
    default:
      return AlertCircle;
  }
}

/**
 * Returns a user-friendly title based on the error category
 */
function getErrorTitle(category: ErrorCategory): string {
  switch (category) {
    case ErrorCategory.NETWORK:
      return 'Network Error';
    case ErrorCategory.AUTHENTICATION:
      return 'Authentication Required';
    case ErrorCategory.AUTHORIZATION:
      return 'Access Denied';
    case ErrorCategory.VALIDATION:
      return 'Invalid Data';
    case ErrorCategory.SERVER:
      return 'Server Error';
    case ErrorCategory.API_SERVICE:
      return 'Service Unavailable';
    case ErrorCategory.CLIENT:
      return 'Application Error';
    default:
      return 'Unexpected Error';
  }
}

/**
 * API Error component for React Query
 * Specifically designed to handle errors from API requests
 */
export const QueryErrorDisplay: React.FC<{
  error: unknown;
  refetch?: () => void;
}> = ({ error, refetch }) => {
  let appError: AppError;

  if (error instanceof Error) {
    // Convert regular Error to AppError if needed
    appError =
      'category' in error
        ? (error as AppError)
        : ({
            ...error,
            category: ErrorCategory.UNKNOWN,
            name: error.name || 'Error',
          } as AppError);
  } else {
    // Handle non-Error objects
    appError = {
      name: 'Error',
      message: 'An unknown error occurred',
      category: ErrorCategory.UNKNOWN,
    } as AppError;
  }

  return <ErrorDisplay error={appError} onRetry={refetch} showRetry={!!refetch} />;
};
