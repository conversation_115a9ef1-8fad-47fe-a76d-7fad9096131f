/**
 * Email Processing Service (Version 2)
 *
 * This service handles all email operations with a two-phase approach:
 * 1. First phase: Initial fetching of email data (fast, without AI processing)
 * 2. Second phase: Background AI processing (summarization, categorization, reply generation)
 */

import { Client } from '@microsoft/microsoft-graph-client';
import type { Email, InsertEmail, Settings } from '@shared/schema';
import { google } from 'googleapis';
import logger from '../lib/logger';
import { storage } from '../storage';
import { dataRetentionService } from './dataRetention';
// const categorizationService = optimizedCategorizationServiceFunction; // Reserved for future use
import { unifiedEmailProcessing } from './gemini';

// Constants for email status messages
const EMAIL_STATUS = {
  PROCESSING: 'Processing...',
  ERROR: 'Error generating summary',
  UNAVAILABLE: 'Summary unavailable',
  RATE_LIMITED: 'Summary unavailable (rate limited)',
  SHORT_MESSAGE: 'Short message',
};

// Constants for email categorization
const EMAIL_CATEGORIES = {
  PENDING: 'Pending',
  NOTIFICATION: 'Notification',
  UNCATEGORIZED: 'Uncategorized',
};

// Default reply template when AI cannot generate a reply
const DEFAULT_REPLY_TEMPLATE = (subject: string) =>
  `I received your email regarding "${subject}". I'll respond to you soon.`;

// summarizeEmailWithGemini and unifiedEmailProcessing provide AI capabilities. Their direct calls are used elsewhere when needed.

// Helper functions to reduce duplication
function shouldUpdateAIField(existingValue: string | null | undefined): boolean {
  return !existingValue || existingValue === EMAIL_STATUS.PROCESSING || existingValue === '';
}

function _formatPreviewText(text: string, maxLength = 30): string {
  if (!text) return '...';
  return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
}

/**
 * Two-phase Gmail email fetching:
 * Phase 1: Fast fetch of all emails without AI processing
 * Phase 2: Background AI processing only for visible emails (typically on page 1)
 *
 * This allows users to see their emails instantly while AI processing happens in the background
 */
export async function fetchGmailEmails(userId: number, accessToken: string) {
  try {
    // Create OAuth client with the access token
    const oauth2Client = new google.auth.OAuth2();
    oauth2Client.setCredentials({ access_token: accessToken });

    // Initialize Gmail API client
    const gmail = google.gmail({ version: 'v1', auth: oauth2Client });

    // OPTIMIZATION: Fetch user settings once at the beginning to avoid N+1 queries
    let userSettings: Settings | undefined;
    try {
      userSettings = await storage.getSettings(userId);
    } catch (settingsError) {
      logger.warn('Failed to fetch user settings, privacy features will be skipped', {
        userId,
        error: (settingsError as Error).message,
      });
    }

    // Get list of emails from inbox
    const response = await gmail.users.messages.list({
      userId: 'me',
      maxResults: 50, // Fetch up to 50 emails
      q: 'in:inbox newer_than:2d', // Only fetch inbox emails from the last 2 days to ensure we get fresh emails
    });

    if (!response.data.messages || response.data.messages.length === 0) {
      return [];
    }

    const emails = [];
    let processedCount = 0;

    // PHASE 1: Quickly fetch all emails with minimal processing (no AI)
    for (let i = 0; i < response.data.messages.length; i++) {
      const message = response.data.messages[i];

      if (!message.id) {
        continue;
      }

      // Check if email already exists in storage
      const existingEmail = await storage.getEmailByMessageId(userId, message.id);

      // If email exists and has already been properly processed with AI, just use the existing data
      if (
        existingEmail?.summary &&
        existingEmail.summary !== EMAIL_STATUS.PROCESSING &&
        existingEmail.summary !== ''
      ) {
        emails.push(existingEmail);
        processedCount++;
        continue; // Skip to next email
      }

      try {
        // Get detailed email data
        const emailData = await gmail.users.messages.get({
          userId: 'me',
          id: message.id,
          format: 'full',
        });

        if (!emailData.data) {
          continue;
        }

        // Extract header information
        const headers = emailData.data.payload?.headers || [];
        const subject =
          headers.find((h) => h.name?.toLowerCase() === 'subject')?.value || 'No Subject';
        const sender = headers.find((h) => h.name?.toLowerCase() === 'from')?.value || 'Unknown';
        const date = headers.find((h) => h.name?.toLowerCase() === 'date')?.value || '';

        // Extract email addresses
        let senderEmail = '';
        let senderName = sender;

        // Parse sender information
        const emailMatch = sender.match(/<([^>]+)>/);
        if (emailMatch && emailMatch.length > 1) {
          senderEmail = emailMatch[1];
          // Extract name part before email
          const namePart = sender.split('<')[0].trim();
          if (namePart) {
            senderName = namePart;
          }
        } else if (sender.includes('@')) {
          senderEmail = sender;
        }

        // Get message body
        let messageBody = '';
        let htmlContent = '';

        if (emailData.data.payload) {
          if (emailData.data.payload.body?.data) {
            // Plain text email
            messageBody = Buffer.from(emailData.data.payload.body.data, 'base64').toString('utf8');

            // For plain text emails, create a simple HTML version
            htmlContent = `<div>${messageBody.replace(/\n/g, '<br>')}</div>`;
          } else if (emailData.data.payload.parts && emailData.data.payload.parts.length > 0) {
            // Multi-part email - look for both plain text and HTML
            const plainTextPart = emailData.data.payload.parts.find(
              (part) => part.mimeType === 'text/plain' && part.body?.data
            );

            const htmlPart = emailData.data.payload.parts.find(
              (part) => part.mimeType === 'text/html' && part.body?.data
            );

            // Get HTML content first if available
            if (htmlPart?.body?.data) {
              htmlContent = Buffer.from(htmlPart.body.data, 'base64').toString('utf8');

              // If we have HTML but no plain text, extract plain text from HTML
              if (!plainTextPart || !plainTextPart.body?.data) {
                messageBody = htmlContent
                  .replace(/<[^>]*>/g, ' ')
                  .replace(/\s+/g, ' ')
                  .trim();
              }
            }

            // Get plain text if available
            if (plainTextPart?.body?.data) {
              messageBody = Buffer.from(plainTextPart.body.data, 'base64').toString('utf8');

              // If we have plain text but no HTML, create HTML from plain text
              if (!htmlContent) {
                htmlContent = `<div>${messageBody.replace(/\n/g, '<br>')}</div>`;
              }
            }
          }
        }

        // Use snippet if body extraction failed
        if (!messageBody && emailData.data.snippet) {
          messageBody = emailData.data.snippet;
        }

        // Parse received date
        // Ensure proper date parsing with validation
        let receivedAt;
        try {
          // Attempt to parse the date string
          if (date) {
            receivedAt = new Date(date);
            // Check if the date is valid
            if (Number.isNaN(receivedAt.getTime())) {
              receivedAt = new Date();
            }
          } else {
            receivedAt = new Date();
          }
        } catch (_dateError) {
          receivedAt = new Date();
        }

        // For Phase 1, use placeholder values for AI-dependent fields
        // These will be updated in Phase 2
        const summary = EMAIL_STATUS.PROCESSING;
        const categories: string[] = [EMAIL_CATEGORIES.PENDING];
        const aiReply = DEFAULT_REPLY_TEMPLATE(subject);

        // Create email entry for storage
        const emailEntry: Partial<Email> = {
          userId,
          messageId: message.id,
          threadId: emailData.data.threadId || message.id,
          subject,
          snippet: emailData.data.snippet || subject,
          sender: senderName,
          senderEmail,
          receivedAt,
          isRead: !emailData.data.labelIds?.includes('UNREAD'),
          isArchived: false, // Ensure email is not archived
          originalContent: messageBody,
          htmlContent: htmlContent || `<div>${messageBody.replace(/\n/g, '<br>')}</div>`, // Store HTML content
          labelIds: emailData.data.labelIds ?? [],
        };

        // Only update AI fields if they don't already exist with real content
        if (shouldUpdateAIField(existingEmail?.summary)) {
          emailEntry.summary = summary;
        }

        if (shouldUpdateAIField(existingEmail?.aiReply)) {
          emailEntry.aiReply = aiReply;
        }

        // Use existing categories if they are meaningful, otherwise set to pending
        if (!existingEmail?.categories || existingEmail.categories.includes(EMAIL_CATEGORIES.PENDING)) {
          emailEntry.categories = categories;
        }

        // Create or update the email in storage
        const newOrUpdatedEmail = await storage.createEmail({
          ...emailEntry,
          userId,
          messageId: emailEntry.messageId!,
          categories: (emailEntry.categories ?? []) as any,
          labelIds: (emailEntry.labelIds ?? []) as any,
        });

        // Apply privacy features: schedule content expiration and encryption
        // OPTIMIZATION: Use cached user settings to avoid N+1 queries
        if (userSettings) {
          try {
            // Schedule content expiration based on user settings
            await dataRetentionService.scheduleContentExpiration(newOrUpdatedEmail.id, userSettings);

            // Apply encryption if user has enabled it
            if (userSettings.encryptSensitiveData) {
              await dataRetentionService.encryptEmailContent(newOrUpdatedEmail);
            }
          } catch (privacyError) {
            logger.warn('Failed to apply privacy features to email', {
              emailId: newOrUpdatedEmail.id,
              error: (privacyError as Error).message,
            });
            // Don't fail the entire email processing for privacy feature errors
          }
        }

        emails.push(newOrUpdatedEmail);
        processedCount++;
      } catch (error) {
        logger.error(`Error processing email messageId: ${message.id}`, { error });
      }
    }

    logger.info(`Gmail fetch complete for userId: ${userId}. Processed ${processedCount} emails.`);

    return emails;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    if (errorMessage.includes('invalid_grant')) {
      logger.error('Invalid grant error during Gmail fetch, likely expired token.', { userId });
    }
    logger.error('Error fetching Gmail emails:', error);
    throw new Error('Failed to fetch emails from Gmail');
  }
}

/**
 * Two-phase Outlook email fetching:
 * Phase 1: Fast fetch of all emails without AI processing
 * Phase 2: Background AI processing only for visible emails (typically on page 1)
 */
export async function fetchOutlookEmails(userId: number, accessToken: string) {
  try {
    // Initialize Microsoft Graph client with the access token
    const client = Client.init({
      authProvider: (done) => {
        done(null, accessToken);
      },
    });

    // OPTIMIZATION: Fetch user settings once at the beginning to avoid N+1 queries
    let userSettings: Settings | undefined;
    try {
      userSettings = await storage.getSettings(userId);
    } catch (settingsError) {
      logger.warn('Failed to fetch user settings, privacy features will be skipped', {
        userId,
        error: (settingsError as Error).message,
      });
    }

    // Get messages from inbox - increased to 50 to get more emails
    const response = await client
      .api('/me/mailFolders/inbox/messages')
      .top(50)
      .select('id,subject,bodyPreview,receivedDateTime,from,body')
      .get();

    if (!response.value || response.value.length === 0) {
      return [];
    }

    const emails = [];
    let processedCount = 0;

    // PHASE 1: Process all emails quickly without AI analysis
    for (let i = 0; i < response.value.length; i++) {
      const message = response.value[i];

      // Check if email already exists in storage
      const existingEmail = await storage.getEmailByMessageId(userId, message.id);

      // If email exists and has already been processed with AI, just use the existing data
      if (
        existingEmail?.summary &&
        existingEmail.summary !== EMAIL_STATUS.PROCESSING &&
        existingEmail.summary !== ''
      ) {
        emails.push(existingEmail);
        processedCount++;
        continue; // Skip to next email
      }

      const sender = message.from.emailAddress.name;
      const senderEmail = message.from.emailAddress.address;
      const bodyContent = message.body.content;

      // For Outlook, the body content is already HTML, so store it directly
      // Also extract a plain text version by removing HTML tags
      const plainTextContent = bodyContent
        .replace(/<[^>]*>/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();

      // Validate and parse receivedDateTime
      let receivedAt;
      try {
        if (message.receivedDateTime) {
          receivedAt = new Date(message.receivedDateTime);
          // Check if date is valid
          if (Number.isNaN(receivedAt.getTime())) {
            receivedAt = new Date();
          }
        } else {
          receivedAt = new Date();
        }
      } catch (_dateError) {
        receivedAt = new Date();
      }

      // For Phase 1, use placeholder values for AI-dependent fields
      let summary = EMAIL_STATUS.PROCESSING;
      let categories: string[] = [EMAIL_CATEGORIES.PENDING];
      const aiReply = DEFAULT_REPLY_TEMPLATE(message.subject);

      if (bodyContent.length <= 20) {
        summary = EMAIL_STATUS.SHORT_MESSAGE;
        categories = [EMAIL_CATEGORIES.NOTIFICATION];
      }

      // Create the email data to save
      const emailEntry: Partial<Email> = {
        userId,
        messageId: message.id,
        threadId: message.conversationId || '',
        subject: message.subject,
        snippet: message.bodyPreview,
        sender,
        senderEmail,
        receivedAt,
        isRead: message.isRead,
        isArchived: false, // Ensure email is not archived
        originalContent: plainTextContent, // Store the plain text version
        htmlContent: bodyContent, // Store the HTML version directly
      };

      // Only update AI fields if they don't already exist with real content
      if (shouldUpdateAIField(existingEmail?.summary)) {
        emailEntry.summary = summary;
      }

      if (
        !existingEmail ||
        !existingEmail.categories ||
        existingEmail.categories.length === 0 ||
        (existingEmail.categories.length === 1 &&
          existingEmail.categories[0] === EMAIL_CATEGORIES.PENDING)
      ) {
        emailEntry.categories = categories;
      }

      if (!existingEmail || !existingEmail.aiReply) {
        emailEntry.aiReply = aiReply;
      }

      if (existingEmail) {
        // If email exists, just update it with the correct arguments
        await storage.updateEmail(existingEmail.id, emailEntry);
        emails.push({ ...existingEmail, ...emailEntry, id: existingEmail.id });
      } else {
        // Otherwise, create a new one, ensuring userId is included
        const newEmail = await storage.createEmail({
          ...emailEntry,
          userId,
          messageId: emailEntry.messageId!,
          categories: (emailEntry.categories ?? []) as any,
          labelIds: (emailEntry.labelIds ?? []) as any,
        });

        // Apply privacy features: schedule content expiration and encryption
        // OPTIMIZATION: Use cached user settings to avoid N+1 queries
        if (userSettings) {
          try {
            // Schedule content expiration based on user settings
            await dataRetentionService.scheduleContentExpiration(newEmail.id, userSettings);

            // Apply encryption if user has enabled it
            if (userSettings.encryptSensitiveData) {
              await dataRetentionService.encryptEmailContent(newEmail);
            }
          } catch (privacyError) {
            logger.warn('Failed to apply privacy features to email', {
              emailId: newEmail.id,
              error: (privacyError as Error).message,
            });
            // Don't fail the entire email processing for privacy feature errors
          }
        }

        emails.push(newEmail);
      }

      processedCount++;
    }

    logger.info(`Successfully processed ${processedCount} emails from Outlook`);
    return emails;
  } catch (error) {
    logger.error('Error fetching Outlook emails:', error);
    throw new Error('Failed to fetch emails from Outlook');
  }
}

/**
 * Process a batch of emails with AI (Phase 2)
 * This function is designed to be called in the background.
 * @param userId The ID of the user.
 * @param emailIds A specific list of email IDs to process.
 * @param limit The maximum number of emails to process from the list.
 */
export async function processEmailsWithAI(userId: number, emailIds: number[], limit = 10) {
  const timer = logger.startTimer();
  const batchId = `batch-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  
  logger.ai.operationStart('batch-email-processing', batchId, {
    userId,
    requestedEmailIds: emailIds.length,
    limit,
    service: 'email-processing'
  });

  const emailsToProcess = await storage.getEmailsByIds(emailIds.slice(0, limit), userId);

  if (emailsToProcess.length === 0) {
    const duration = timer();
    logger.ai.operationComplete('batch-email-processing', batchId, duration, {
      userId,
      processedCount: 0,
      reason: 'no_emails_found'
    });
    return;
  }

  logger.ai.info('Starting AI processing batch', {
    batchId,
    userId,
    emailCount: emailsToProcess.length,
    emailIds: emailsToProcess.map(e => e.id)
  });

  const processingPromises = emailsToProcess.map(async (email, index) => {
    const emailTimer = logger.startTimer();
    const emailOperationId = `${batchId}-email-${email.id}`;
    
    logger.ai.operationStart('email-ai-processing', emailOperationId, {
      batchId,
      emailId: email.id,
      userId,
      batchIndex: index,
      subject: email.subject?.substring(0, 50) + '...',
      contentLength: email.originalContent?.length || 0
    });

    try {
      // Use the unified processing function for efficiency
      const aiResult = await unifiedEmailProcessing(
        email.originalContent ?? '',
        email.subject ?? '',
        email.sender ?? 'Unknown'
      );

      // Update the email in the database with the new AI-generated data
      try {
        await storage.updateEmail(email.id, {
          summary: aiResult.summary,
          categories: aiResult.categories as any,
          aiReply: aiResult.reply,
        });
      } catch (dbError) {
        logger.email.error('Failed to update email in database after AI processing', dbError, email.id, userId, {
          batchId,
          subject: email.subject,
          errorType: 'database_update_error'
        });
        throw dbError; // Re-throw to be caught by outer catch block
      }

      const emailDuration = emailTimer();
      logger.ai.operationComplete('email-ai-processing', emailOperationId, emailDuration, {
        batchId,
        emailId: email.id,
        userId,
        summaryLength: aiResult.summary.length,
        categoriesCount: aiResult.categories.length,
        replyLength: aiResult.reply.length,
        hasError: !!aiResult.error
      });

      logger.email.debug('Successfully processed and updated email with AI data', email.id, userId, {
        batchId,
        summary: aiResult.summary.substring(0, 50) + '...',
        categories: aiResult.categories
      });
    } catch (error) {
      const emailDuration = emailTimer();
      logger.ai.operationFail('email-ai-processing', emailOperationId, error, emailDuration, {
        batchId,
        emailId: email.id,
        userId,
        errorType: error instanceof Error ? error.name : 'unknown',
        subject: email.subject?.substring(0, 50) + '...'
      });

      logger.email.error('Failed to process email with AI', error, email.id, userId, {
        batchId,
        subject: email.subject,
        contentLength: email.originalContent?.length || 0
      });

      // Optionally update the email with an error status
      try {
        await storage.updateEmail(email.id, {
          summary: EMAIL_STATUS.ERROR,
        });
      } catch (dbError) {
        logger.email.error('Failed to update email with error status in database', dbError, email.id, userId, {
          batchId,
          subject: email.subject,
          errorType: 'database_error_update_failed'
        });
        // Don't re-throw here as this is already in an error handler
      }
    }
  });

  await Promise.all(processingPromises);

  const batchDuration = timer();
  let successCount = emailsToProcess.length;
  
  // Count successful vs failed emails with proper error handling
  try {
    const failedEmailsCount = await Promise.all(
      emailsToProcess.map(async (email) => {
        try {
          const updated = await storage.getEmail(email.id);
          return updated?.summary === EMAIL_STATUS.ERROR ? 1 : 0;
        } catch (dbError) {
          logger.email.error('Failed to check email status after processing', dbError, email.id, userId, {
            batchId,
            errorType: 'database_status_check_error'
          });
          // Assume error if we can't check
          return 1;
        }
      })
    );
    
    const errorCount = failedEmailsCount.reduce((sum: number, count: number) => sum + count, 0);
    successCount = emailsToProcess.length - errorCount;
  } catch (error) {
    logger.email.error('Failed to calculate batch success metrics', error, undefined, userId, {
      batchId,
      errorType: 'batch_metrics_error'
    });
    // Use conservative estimate
    successCount = 0;
  }

  logger.ai.operationComplete('batch-email-processing', batchId, batchDuration, {
    userId,
    totalEmails: emailsToProcess.length,
    successCount,
    errorCount: emailsToProcess.length - successCount,
    avgProcessingTime: batchDuration / emailsToProcess.length
  });

  logger.info(`Finished AI processing for batch of ${emailsToProcess.length} emails`, {
    batchId,
    userId,
    duration: batchDuration,
    successCount,
    errorCount: emailsToProcess.length - successCount
  });
}

/**
 * Process emails that have been stuck in "Processing..." state for too long
 * This function should be called periodically to recover from processing failures
 *
 * @param userId User ID
 * @param timeThresholdMinutes Minimum time in minutes before considering an email stuck
 * @param limit Maximum number of emails to recover at once
 * @returns Number of emails recovered
 */
export async function recoverStuckEmails(
  userId: number,
  timeThresholdMinutes = 30,
  limit = 5
): Promise<number> {
  try {
    // Get all emails for this user with generous limit
    const allEmails = await storage.getEmails(userId, 200, 0);

    // Calculate the threshold timestamp
    const thresholdTime = new Date();
    thresholdTime.setMinutes(thresholdTime.getMinutes() - timeThresholdMinutes);

    // Find emails that have been processing for too long
    const stuckEmails = allEmails.filter(
      (email) =>
        // Emails with "Processing..." status that have timestamps older than the threshold
        (email.summary === EMAIL_STATUS.PROCESSING || !email.summary) &&
        email.receivedAt &&
        new Date(email.receivedAt) < thresholdTime
    );

    if (stuckEmails.length === 0) {
      return 0;
    }

    // Process up to the limit
    const emailsToRecover = stuckEmails.slice(0, limit);
    const emailIds = emailsToRecover.map((email) => email.id);

    if (emailIds.length > 0) {
      // Use the existing processing function but with increased priority (reduced delay)
      await processEmailsWithAI(userId, emailIds, limit);
    }
    return emailIds.length;
  } catch (error) {
    console.error('[ERROR] Failed to recover stuck emails:', error);
    return 0;
  }
}

// Basic implementations for replying and archiving emails

export async function sendGmailReply(
  userId: number,
  messageId: string,
  content: string,
  accessToken: string
) {
  const oauth2Client = new google.auth.OAuth2();
  oauth2Client.setCredentials({ access_token: accessToken });

  const gmail = google.gmail({ version: 'v1', auth: oauth2Client });

  const metadata = await gmail.users.messages.get({
    userId: 'me',
    id: messageId,
    format: 'metadata',
    metadataHeaders: ['Subject', 'From'],
  });

  const headers = metadata.data.payload?.headers || [];
  const subject = headers.find((h) => h.name?.toLowerCase() === 'subject')?.value || '';
  const from = headers.find((h) => h.name?.toLowerCase() === 'from')?.value || '';
  const threadId = metadata.data.threadId || messageId;

  const emailLines = [
    `To: ${from}`,
    `Subject: Re: ${subject}`,
    `In-Reply-To: <${messageId}>`,
    `References: <${messageId}>`,
    '',
    content,
  ];

  const raw = Buffer.from(emailLines.join('\r\n'))
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');

  await gmail.users.messages.send({
    userId: 'me',
    requestBody: { raw, threadId },
  });

  const email = await storage.getEmailByMessageId(userId, messageId);
  if (email) {
    await storage.markEmailAsRead(email.id);
  }

  return true;
}

export async function sendOutlookReply(
  userId: number,
  messageId: string,
  content: string,
  accessToken: string
) {
  const client = Client.init({
    authProvider: (done) => done(null, accessToken),
  });

  await client.api(`/me/messages/${messageId}/reply`).post({
    comment: content,
  });

  const email = await storage.getEmailByMessageId(userId, messageId);
  if (email) {
    await storage.markEmailAsRead(email.id);
  }

  return true;
}

export async function archiveGmailEmail(userId: number, messageId: string, accessToken: string) {
  const oauth2Client = new google.auth.OAuth2();
  oauth2Client.setCredentials({ access_token: accessToken });
  const gmail = google.gmail({ version: 'v1', auth: oauth2Client });

  await gmail.users.messages.modify({
    userId: 'me',
    id: messageId,
    requestBody: {
      removeLabelIds: ['INBOX'],
    },
  });

  const email = await storage.getEmailByMessageId(userId, messageId);
  if (email) {
    await storage.markEmailAsArchived(email.id);
  }

  return true;
}

export class EmailService {
  /**
   * Get a paginated and filtered list of emails for a user.
   * @param userId The ID of the user.
   * @param options Filtering and pagination options.
   * @returns A promise that resolves to the list of emails and pagination info.
   */
  async getEmails(
    userId: number,
    options: {
      page?: number;
      limit?: number;
      filters?: { archived?: boolean; important?: boolean; snoozed?: boolean; trashed?: boolean };
    }
  ): Promise<{ emails: Email[]; totalPages: number; totalEmails: number }> {
    const { page = 1, limit = 20, filters = {} } = options;
    const offset = (page - 1) * limit;

    const [emails, totalEmails] = await Promise.all([
      storage.getEmails(userId, limit, offset, filters),
      storage.getEmailCount(userId, filters),
    ]);

    const totalPages = Math.ceil(totalEmails / limit);

    // After fetching emails, trigger background AI processing for any that need it.
    const unprocessedEmailIds = emails
      .map((email) => {
        try {
          // Attempt to parse stringified JSON fields back into arrays
          const categories =
            typeof email.categories === 'string' ? JSON.parse(email.categories) : email.categories;
          const labelIds =
            typeof email.labelIds === 'string' ? JSON.parse(email.labelIds) : email.labelIds;
          return { ...email, categories, labelIds };
        } catch (e) {
          logger.warn(`Failed to parse JSON for email ${email.id}, using raw data.`);
          return email; // Return original email on parsing error
        }
      })
      .filter((email) => email.summary === EMAIL_STATUS.PROCESSING)
      .map((email) => email.id);

    if (unprocessedEmailIds.length > 0) {
      // Don't await this; let it run in the background.
      processEmailsWithAI(userId, unprocessedEmailIds);
    }

    return { emails, totalPages, totalEmails };
  }

  // Deprecated: getEmailById
  // Use getEmailByMessageId instead

  async getEmailByMessageId(userId: number, messageId: string): Promise<Email | null> {
    const email = await storage.getEmailByMessageId(userId, messageId);
    if (!email) {
      return null;
    }
    return email;
  }

  /**
   * Updates an email's state (e.g., archive, trash, important).
   * Ensures the user has ownership before updating.
   * @param userId The ID of the user.
   * @param messageId The ID of the email.
   * @param updates The fields to update.
   * @returns The updated email or null if not found/not owned.
   */
  async updateEmailState(
    userId: number,
    messageId: string,
    updates: Partial<Email>
  ): Promise<Email | null> {
    const email = await this.getEmailByMessageId(userId, messageId);
    if (!email) return null;
    const updatedEmail = await storage.updateEmailByMessageId(userId, messageId, updates);
    return updatedEmail ?? null;
  }

  /**
   * Create a new email owned by the given user.
   * Primarily used for testing or manual insertion – in production the
   * application relies on provider-specific sync tasks instead.
   * @param userId The owner of the email.
   * @param data   Partial email data. Must include at minimum `messageId` and `originalContent`.
   */
  async createEmail(userId: number, data: Partial<Email> & { messageId: string }): Promise<Email> {
    // Provide sane defaults for required fields.
    const insert: InsertEmail = {
      userId,
      messageId: data.messageId,
      threadId: data.threadId ?? data.messageId,
      subject: data.subject ?? '(no subject)',
      snippet: data.snippet ?? _formatPreviewText(data.originalContent || ''),
      sender: data.sender ?? 'unknown',
      senderEmail: data.senderEmail ?? '<EMAIL>',
      receivedAt: data.receivedAt ? new Date(data.receivedAt) : new Date(),
      isRead: data.isRead ?? false,
      isArchived: data.isArchived ?? false,
      isTrashed: false,
      isImportant: false,
      originalContent: data.originalContent ?? '',
      htmlContent: data.htmlContent ?? '',
      categories: data.categories ?? [],
      priority: data.priority ?? null,
      aiReply: data.aiReply ?? null,
      summary: data.summary ?? null,
      provider: data.provider ?? null,
      labelIds: data.labelIds ?? [],
      snoozedUntil: null,
      isReplied: false,
      replyDate: null,
      replyId: null,
    } as unknown as InsertEmail;

    return storage.createEmail(insert);
  }

  /**
   * Update an email by messageId (full update) – ensures ownership.
   */
  async updateEmailByMessageId(
    userId: number,
    messageId: string,
    updates: Partial<Email>
  ): Promise<Email | null> {
    const email = await this.getEmailByMessageId(userId, messageId);
    if (!email) return null;
    const updatedEmail = await storage.updateEmailByMessageId(userId, messageId, updates);
    return updatedEmail ?? null;
  }
}

export const emailService = new EmailService();
