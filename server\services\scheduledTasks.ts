/**
 * Scheduled Tasks Service
 * 
 * Handles background tasks for data retention, cleanup, and privacy compliance.
 * Runs periodic maintenance tasks to ensure data privacy and system health.
 */

import logger from '../lib/logger';
import { dataRetentionService } from './dataRetention';
import { cleanupOldTasks } from './taskQueue';

export interface ScheduledTaskConfig {
  dataCleanupInterval: number; // minutes
  taskQueueCleanupInterval: number; // minutes
  encryptionApplicationInterval: number; // minutes
  enableDataCleanup: boolean;
  enableTaskQueueCleanup: boolean;
  enableEncryptionApplication: boolean;
}

export class ScheduledTasksService {
  private static readonly DEFAULT_CONFIG: ScheduledTaskConfig = {
    dataCleanupInterval: 60, // Run every hour
    taskQueueCleanupInterval: 240, // Run every 4 hours
    encryptionApplicationInterval: 120, // Run every 2 hours
    enableDataCleanup: true,
    enableTaskQueueCleanup: true,
    enableEncryptionApplication: true,
  };

  private config: ScheduledTaskConfig;
  private intervals: NodeJS.Timeout[] = [];
  private isRunning = false;

  constructor(config: Partial<ScheduledTaskConfig> = {}) {
    this.config = { ...ScheduledTasksService.DEFAULT_CONFIG, ...config };
  }

  /**
   * Start all scheduled tasks
   */
  start(): void {
    if (this.isRunning) {
      logger.warn('Scheduled tasks are already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting scheduled tasks service', {
      config: this.config,
    });

    // Data cleanup task
    if (this.config.enableDataCleanup) {
      const dataCleanupInterval = setInterval(async () => {
        try {
          await this.runDataCleanup();
        } catch (error) {
          logger.error('Error in scheduled data cleanup task', {
            error: (error as Error).message,
          });
        }
      }, this.config.dataCleanupInterval * 60 * 1000);

      this.intervals.push(dataCleanupInterval);
      logger.info('Data cleanup task scheduled', {
        intervalMinutes: this.config.dataCleanupInterval,
      });
    }

    // Task queue cleanup task
    if (this.config.enableTaskQueueCleanup) {
      const taskQueueCleanupInterval = setInterval(async () => {
        try {
          await this.runTaskQueueCleanup();
        } catch (error) {
          logger.error('Error in scheduled task queue cleanup', {
            error: (error as Error).message,
          });
        }
      }, this.config.taskQueueCleanupInterval * 60 * 1000);

      this.intervals.push(taskQueueCleanupInterval);
      logger.info('Task queue cleanup scheduled', {
        intervalMinutes: this.config.taskQueueCleanupInterval,
      });
    }

    // Encryption application task
    if (this.config.enableEncryptionApplication) {
      const encryptionInterval = setInterval(async () => {
        try {
          await this.runEncryptionApplication();
        } catch (error) {
          logger.error('Error in scheduled encryption application', {
            error: (error as Error).message,
          });
        }
      }, this.config.encryptionApplicationInterval * 60 * 1000);

      this.intervals.push(encryptionInterval);
      logger.info('Encryption application task scheduled', {
        intervalMinutes: this.config.encryptionApplicationInterval,
      });
    }

    // Run initial cleanup immediately (with delay to allow system to start)
    setTimeout(() => {
      if (this.config.enableDataCleanup) {
        this.runDataCleanup().catch(error => {
          logger.error('Error in initial data cleanup', {
            error: (error as Error).message,
          });
        });
      }
    }, 30000); // 30 second delay
  }

  /**
   * Stop all scheduled tasks
   */
  stop(): void {
    if (!this.isRunning) {
      logger.warn('Scheduled tasks are not running');
      return;
    }

    this.isRunning = false;
    
    // Clear all intervals
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];

    logger.info('Stopped all scheduled tasks');
  }

  /**
   * Run data cleanup task
   */
  private async runDataCleanup(): Promise<void> {
    const startTime = Date.now();
    
    try {
      logger.info('Starting scheduled data cleanup');

      const stats = await dataRetentionService.cleanupExpiredContent();
      
      const duration = Date.now() - startTime;
      
      logger.info('Completed scheduled data cleanup', {
        stats,
        durationMs: duration,
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('Failed scheduled data cleanup', {
        error: (error as Error).message,
        durationMs: duration,
      });
    }
  }

  /**
   * Run task queue cleanup
   */
  private async runTaskQueueCleanup(): Promise<void> {
    const startTime = Date.now();
    
    try {
      logger.info('Starting scheduled task queue cleanup');

      // Clean up tasks older than 7 days
      const deletedCount = await cleanupOldTasks();
      
      const duration = Date.now() - startTime;
      
      logger.info('Completed scheduled task queue cleanup', {
        deletedTasks: deletedCount,
        durationMs: duration,
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('Failed scheduled task queue cleanup', {
        error: (error as Error).message,
        durationMs: duration,
      });
    }
  }

  /**
   * Run encryption application for users who have enabled it
   */
  private async runEncryptionApplication(): Promise<void> {
    const startTime = Date.now();
    
    try {
      logger.info('Starting scheduled encryption application');

      // This is a placeholder - in a real implementation, you would:
      // 1. Get all users who have encryption enabled
      // 2. Apply encryption to their unencrypted emails in batches
      // 3. Track progress and avoid overwhelming the system
      
      // For now, we'll just log that this task would run
      logger.info('Encryption application task completed (placeholder)', {
        note: 'This task would encrypt unencrypted emails for users with encryption enabled',
      });

      const duration = Date.now() - startTime;
      
      logger.info('Completed scheduled encryption application', {
        durationMs: duration,
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('Failed scheduled encryption application', {
        error: (error as Error).message,
        durationMs: duration,
      });
    }
  }

  /**
   * Get the current status of scheduled tasks
   */
  getStatus(): {
    isRunning: boolean;
    config: ScheduledTaskConfig;
    activeIntervals: number;
  } {
    return {
      isRunning: this.isRunning,
      config: this.config,
      activeIntervals: this.intervals.length,
    };
  }

  /**
   * Update configuration (requires restart to take effect)
   */
  updateConfig(newConfig: Partial<ScheduledTaskConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    logger.info('Updated scheduled tasks configuration', {
      newConfig: this.config,
      note: 'Restart required for changes to take effect',
    });
  }
}

// Export singleton instance
export const scheduledTasksService = new ScheduledTasksService(); 