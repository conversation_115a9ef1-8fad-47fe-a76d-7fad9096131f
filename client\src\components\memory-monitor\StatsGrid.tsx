import { formatDistanceToNow } from 'date-fns';
import type React from 'react';
import { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { useMemoryMonitor } from './context/MemoryMonitorContext';
import { getPressureLevelTextColor } from './utils';

export const StatsGrid: React.FC = () => {
  const { memoryStatsQuery, memoryHistoryQuery } = useMemoryMonitor();

  const { data: memoryStats, isLoading: isLoadingStats } = memoryStatsQuery;
  const { data: memoryHistory, isLoading: isLoadingHistory } = memoryHistoryQuery;

  const heapUsagePercent = useMemo(() => {
    if (!memoryStats) return 0;
    return (memoryStats.heapUsed / memoryStats.heapTotal) * 100;
  }, [memoryStats]);

  const lastUpdateTime = useMemo(() => {
    if (!memoryStats?.timestamp) return 'Never';
    return formatDistanceToNow(new Date(memoryStats.timestamp), {
      addSuffix: true,
    });
  }, [memoryStats?.timestamp]);

  const memoryLeakStatus = useMemo(() => {
    if (!memoryHistory?.leakDetection) {
      return { detected: false, confidence: 0, details: 'No data available' };
    }
    return memoryHistory.leakDetection;
  }, [memoryHistory]);

  const currentStatus = useMemo(() => {
    if (!memoryStats) return 'Unknown';
    return memoryStats.pressureLevel;
  }, [memoryStats]);

  if (isLoadingStats || isLoadingHistory) {
    return <StatsGridSkeleton />;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Memory Pressure</CardTitle>
          <CardDescription>Current server memory status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center h-24">
            <div className={`text-4xl font-bold ${getPressureLevelTextColor(currentStatus)}`}>
              {currentStatus}
            </div>
            <div className="text-sm text-muted-foreground mt-2">Last updated: {lastUpdateTime}</div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Heap Usage</CardTitle>
          <CardDescription>JS Heap Memory Allocation</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span>Usage</span>
              <span className="font-medium">
                {memoryStats
                  ? `${memoryStats.heapUsed}MB / ${memoryStats.heapTotal}MB`
                  : 'Loading...'}
              </span>
            </div>
            <Progress value={heapUsagePercent} className="h-2" />
            <div className="text-xs text-right text-muted-foreground">
              {heapUsagePercent.toFixed(1)}% used
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Total Process Memory</CardTitle>
          <CardDescription>Resident Set Size (RSS)</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center h-24">
            <div className="text-3xl font-bold">
              {memoryStats ? `${memoryStats.rss}MB` : 'Loading...'}
            </div>
            <div className="text-sm text-muted-foreground mt-2">Total allocated memory</div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Memory Leak Detection</CardTitle>
          <CardDescription>Based on usage patterns</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center h-24">
            <div
              className={`text-2xl font-bold ${
                memoryLeakStatus.detected ? 'text-red-500' : 'text-green-500'
              }`}
            >
              {memoryLeakStatus.detected ? 'Potential Leak Detected' : 'No Leaks Detected'}
            </div>
            <div className="text-sm text-muted-foreground mt-2">
              {memoryLeakStatus.detected
                ? `${memoryLeakStatus.confidence.toFixed(0)}% confidence`
                : 'Memory patterns look normal'}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const StatsGridSkeleton: React.FC = () => (
  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
    {[...Array(4)].map((_, i) => (
      <Card key={i}>
        <CardHeader className="pb-2">
          <Skeleton className="h-5 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center h-24 space-y-2">
            <Skeleton className="h-8 w-1/2" />
            <Skeleton className="h-4 w-1/3" />
          </div>
        </CardContent>
      </Card>
    ))}
  </div>
);

export default StatsGrid;
