import { type Request, type Response, Router } from 'express';
import logger from '../lib/logger';
// import { csrfProtection } from "../auth/securityIntegration";
import { storage } from '../storage';
import { catchAsync, NotFoundError } from '../utils/errorHandler';
import { SettingsSchemas, validateRequest } from '../utils/inputValidator';

const router = Router();

// Get the current user's settings
router.get(
  '/',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.session?.userId as number;

    // Enhanced logging for debugging auth issues
    logger.debug('[Settings] Auth check', {
      hasSession: !!req.session,
      userId: !!userId,
      hasUser: !!req.user,
    });

    if (!userId) {
      logger.debug('[Settings] No userId found in session');
      return res.status(401).json({
        success: false,
        message: 'Authentication required - no user session',
        code: 'NO_USER_SESSION',
      });
    }

    const settings = await storage.getSettings(userId);
    if (!settings) {
      // Create default settings instead of throwing error
      logger.info('[Settings] Creating default settings for user', { userId });
      const defaultSettings = await storage.createSettings({
        userId,
        replyTone: 'professional',
        privacyMode: false,
        notificationDigest: true,
        themeMode: 'system',

        borderRadius: 6,
      });
      return res.json({ success: true, settings: defaultSettings });
    }
    res.json({ success: true, settings });
  })
);

// Update the current user's settings
router.patch(
  '/',
  // csrfProtection,
  validateRequest(SettingsSchemas.update),
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.session?.userId as number;
    const updated = await storage.updateSettings(userId, req.body);
    if (!updated) {
      throw new NotFoundError('Settings not found');
    }
    res.json({ success: true, settings: updated });
  })
);

// PUT alias for full updates (idempotent)
router.put(
  '/',
  validateRequest(SettingsSchemas.update),
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.session?.userId as number;
    const updated = await storage.updateSettings(userId, req.body);
    if (!updated) {
      throw new NotFoundError('Settings not found');
    }
    res.json({ success: true, settings: updated });
  })
);

export default router;
