import { useMutation } from '@tanstack/react-query';
import { Loader2, Refresh<PERSON><PERSON>, XCircle } from 'lucide-react';
import { useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { useProviderStatus } from '@/hooks/use-provider-status';
import { toast } from '@/hooks/use-toast';
import {
  disconnectGmailAccount,
  type EmailProviderStatus,
  refreshGmailTokens,
} from '@/lib/emailProviders';
import { cn } from '@/lib/utils';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { EmailProviderStatusManager } from './EmailProviderStatusManager';

/**
 * Component to manage connected email accounts
 */
export function EmailAccountsManager() {
  const [selectedAccount, setSelectedAccount] = useState<EmailProviderStatus | null>(null);
  const {
    providers: providerData,
    isLoading: queryLoading,
    isError: queryError,
    refetch,
  } = useProviderStatus();

  const disconnectMutation = useMutation({
    mutationFn: () => disconnectGmailAccount(),
    onSuccess: () => {
      toast({
        title: 'Account Disconnected',
        description: 'Your Gmail account has been disconnected.',
        variant: 'default',
      });
      refetch();
    },
    onError: (error) => {
      console.error('Error disconnecting account:', error);
      toast({
        title: 'Disconnection Failed',
        description: 'There was a problem disconnecting your account. Please try again.',
        variant: 'destructive',
      });
    },
    onSettled: () => {
      setSelectedAccount(null);
    },
  });

  const refreshMutation = useMutation({
    mutationFn: () => refreshGmailTokens(),
    onSuccess: () => {
      toast({
        title: 'Tokens Refreshed',
        description: 'Your Gmail authentication has been refreshed.',
        variant: 'default',
      });
      refetch();
    },
    onError: (error) => {
      console.error('Error refreshing tokens:', error);
      toast({
        title: 'Refresh Failed',
        description:
          'There was a problem refreshing your authentication. Please try reconnecting your account.',
        variant: 'destructive',
      });
    },
    onSettled: () => {
      setSelectedAccount(null);
    },
  });

  const _handleReconnect = (provider: string) => {
    // Redirect to the appropriate auth endpoint
    // For now, we only have Google
    if (provider === 'google') {
      window.location.href = '/api/auth/google';
    } else {
      toast({
        title: 'Not Implemented',
        description: `Connecting to ${provider} is not yet supported.`,
        variant: 'destructive',
      });
    }
  };

  const handleDisconnect = async (_provider: string) => {
    // ... existing code ...
  };

  if (queryLoading) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6 flex justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (queryError) {
    return (
      <>
        <Alert variant="destructive" className="mb-4">
          <AlertTitle>Connection Error</AlertTitle>
          <AlertDescription>
            Failed to load your connected email accounts. The server might be experiencing issues.
          </AlertDescription>
        </Alert>
        <EmailProviderStatusManager
          onReconnected={refetch}
          showRetryButton={true}
          onManualRetry={refetch}
        />
      </>
    );
  }

  if (providerData.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Connected Accounts</CardTitle>
          <CardDescription>You don't have any email accounts connected yet.</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Connected Accounts</CardTitle>
        <CardDescription>Manage your connected email accounts here.</CardDescription>
      </CardHeader>
      <CardContent>
        {!queryLoading && !queryError && (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Provider</TableHead>
                <TableHead className="text-right">Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {providerData.map((provider: EmailProviderStatus) => (
                <TableRow key={provider.provider}>
                  <TableCell className="font-medium">{provider.email}</TableCell>
                  <TableCell className="capitalize">{provider.provider}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedAccount(provider);
                          refreshMutation.mutate();
                        }}
                        disabled={
                          refreshMutation.isPending && selectedAccount?.email === provider.email
                        }
                      >
                        <RefreshCw
                          className={cn('mr-2 h-4 w-4', {
                            'animate-spin':
                              refreshMutation.isPending &&
                              selectedAccount?.email === provider.email,
                          })}
                        />
                        Refresh
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDisconnect(provider.provider)}
                          >
                            <XCircle className="mr-2 h-4 w-4" />
                            Disconnect
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This will disconnect your {provider.email} account. You will need to
                              reconnect it to continue using it with InboxZero.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => disconnectMutation.mutate()}>
                              Disconnect
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}

export default EmailAccountsManager;
