// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Provide a global stub for the Firebase module used throughout the client so that
// consumer modules (AuthContext, apiClient, etc.) don't attempt to evaluate
// `import.meta.env` inside Jest which causes a SyntaxError.
jest.mock('@/lib/firebase', () => ({
  __esModule: true,
  auth: {},
  default: {},
}));

// Mock firebase/app basic
jest.mock('firebase/app', () => ({
  __esModule: true,
  initializeApp: jest.fn(() => ({})),
}));

// Extend firebase/auth mock to include getAuth and other noop fns
jest.mock('firebase/auth', () => {
  const noop = () => {};
  return {
    __esModule: true,
    getAuth: () => ({}),
    onAuthStateChanged: (_auth: any, cb: (user: null) => void) => {
      cb(null);
      return jest.fn();
    },
    signInWithEmailAndPassword: noop,
    createUserWithEmailAndPassword: noop,
    signOut: noop,
    updateProfile: noop,
    GoogleAuthProvider: jest.fn(),
    signInWithPopup: noop,
    EmailAuthProvider: { credential: jest.fn() },
    reauthenticateWithCredential: noop,
    sendPasswordResetEmail: noop,
    updateEmail: noop,
    updatePassword: noop,
  };
});

// Mock Radix Tooltip components to simple passthroughs to avoid JSX/Portal complexity in tests
jest.mock('@radix-ui/react-tooltip', () => {
  const PT = ({ children }: { children?: any }) => children || null;
  return {
    __esModule: true,
    // primitives re-exported under their Radix names
    Provider: PT,
    Root: PT,
    Trigger: PT,
    Content: PT,
  };
});
