/**
 * Gmail Provider Routes
 *
 * Handles provider-specific actions for Gmail, such as generating
 * authentication URLs and disconnecting the account.
 */
import { type Request, type Response, Router } from 'express';
import { generateGoogleAuthUrl } from '../../auth/google';
import logger from '../../lib/logger';
import { requireAuth } from '../../middleware/simpleAuth';
import tokenService from '../../services/tokenService';
import { storage } from '../../storage';
import { catchAsync } from '../../utils/errorHandler';

const router = Router();

// Disconnect a user's Gmail account
router.post(
  '/disconnect',
  requireAuth,
  catchAsync(async (req: Request, res: Response) => {
    if (!req.user?.id) {
      res.status(401).json({ error: 'Authentication required.' });
      return;
    }

    const userId = req.user.id;
    await storage.updateUser(userId, {
      gmailTokens: null,
      provider: 'firebase', // Revert to firebase as default
    });
    logger.info(`Gmail account disconnected for user ${userId}`);
    res.json({ success: true, message: 'Gmail account disconnected.' });
  })
);

// Get the status of the user's Gmail token
router.get(
  '/status',
  requireAuth,
  catchAsync(async (req: Request, res: Response) => {
    // In a real app, you'd get the user's ID from the session `req.user.id`
    // and fetch their token info from the database.
    // For this example, we'll use a mock or a direct service call.
    const tokenStatus = await tokenService.verifyConnection(req.user!);
    res.json(tokenStatus);
  })
);

// Force a refresh of the user's Gmail token
router.post(
  '/refresh',
  requireAuth,
  catchAsync(async (req: Request, res: Response) => {
    const result = await tokenService.refreshTokens(req.user!);
    if (result.success) {
      res.json({ success: true, message: 'Token refreshed successfully.' });
    } else {
      res.status(400).json({ success: false, message: result.error || 'Failed to refresh token.' });
    }
  })
);

// This endpoint is for connecting a Gmail account to an existing user
router.get('/connect', requireAuth, async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({ error: 'Authentication required.' });
      return;
    }
    // For connecting a new account, we always want to prompt for consent
    // to ensure we receive a refresh token.
    const authUrl = await generateGoogleAuthUrl(req, { userId: req.user.id, promptConsent: true });
    res.redirect(authUrl);
  } catch (error) {
    logger.error('Error generating Google auth URL for account connection', error);
    res.status(500).send('Failed to initiate Gmail connection.');
  }
});

export default router;
