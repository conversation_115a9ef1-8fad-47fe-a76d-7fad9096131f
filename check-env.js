#!/usr/bin/env node

import { dirname, join } from 'node:path';
import { fileURLToPath } from 'node:url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, '..', '.env') });

console.log('🔍 Environment Configuration Check\n');

const requiredEnv = [
  'NODE_ENV',
  'DATABASE_URL',
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET',
  'SESSION_SECRET',
  'FIREBASE_SERVICE_ACCOUNT',
  'GEMINI_API_KEY',
  'REDIS_URL',
];

const optionalEnv = ['PORT', 'ADMIN_EMAILS', 'SENTRY_DSN'];

let missingCount = 0;

console.log('📋 Required Variables:');
for (const varName of requiredEnv) {
  const value = process.env[varName];
  if (value) {
    if (varName === 'FIREBASE_SERVICE_ACCOUNT') {
      console.log(`✅ ${varName}: Set (content hidden)`);
    } else {
      console.log(`✅ ${varName}: ${value.substring(0, 30)}...`);
    }
  } else {
    console.log(`❌ ${varName}: NOT SET`);
    missingCount++;
  }
}

console.log('\n📋 Optional Variables:');
for (const varName of optionalEnv) {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: Set`);
  } else {
    console.log(`⚠️  ${varName}: NOT SET (using defaults)`);
  }
}

// Database URL validation
if (process.env.DATABASE_URL) {
  console.log('\n🔗 Database URL Analysis:');
  const dbUrl = process.env.DATABASE_URL;

  try {
    const url = new URL(dbUrl);
    console.log(`   - Protocol: ${url.protocol}`);
    console.log(`   - Host:     ${url.hostname}`);
    console.log(`   - Port:     ${url.port || 'default'}`);
    console.log(`   - Database: ${url.pathname.substring(1)}`);
    console.log(`   - Username: ${url.username}`);
    console.log(`   - Password: ${url.password ? '[HIDDEN]' : 'NOT SET'}`);

    if (url.hostname.includes('supabase.com')) {
      console.log('   ✅ Supabase configuration detected');
    }
  } catch (error) {
    console.log(`   ❌ Invalid DATABASE_URL format: ${error.message}`);
    missingCount++;
  }
}

console.log(
  `\n${missingCount > 0 ? '❌' : '✅'} Environment check ${missingCount > 0 ? 'failed' : 'passed'}`
);

if (missingCount > 0) {
  console.log(
    '\n💡 To fix issues, create a .env file in the root directory and set all required variables.'
  );
  process.exit(1);
} else {
  console.log('\n🚀 Environment appears to be properly configured!');
}
