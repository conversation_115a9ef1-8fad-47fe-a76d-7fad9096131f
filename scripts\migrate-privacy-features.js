import 'dotenv/config';
import { sql } from 'drizzle-orm';
import { closeDatabase, getDb } from '../server/db.ts';

async function migratePrivacyFeatures() {
  try {
    console.log('🔄 Starting privacy features migration...');
    
    const db = await getDb();

    // Add new columns to emails table
    console.log('Adding privacy fields to emails table...');
    await db.execute(sql`
      ALTER TABLE emails 
      ADD COLUMN IF NOT EXISTS content_expires_at TIMESTAMP,
      ADD COLUMN IF NOT EXISTS last_accessed TIMESTAMP DEFAULT NOW(),
      ADD COLUMN IF NOT EXISTS is_content_encrypted BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS retention_days INTEGER DEFAULT 30
    `);
    console.log('✅ Privacy fields added to emails table');

    // Add new columns to settings table
    console.log('Adding privacy fields to settings table...');
    await db.execute(sql`
      ALTER TABLE settings 
      ADD COLUMN IF NOT EXISTS data_retention_days INTEGER DEFAULT 30,
      ADD COLUMN IF NOT EXISTS allow_ai_processing BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS store_email_content BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS auto_delete_processed_emails BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS encrypt_sensitive_data BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS consent_version TEXT DEFAULT '1.0',
      ADD COLUMN IF NOT EXISTS consent_timestamp TIMESTAMP DEFAULT NOW(),
      ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT NOW(),
      ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT NOW()
    `);
    console.log('✅ Privacy fields added to settings table');

    // Create indexes for better performance
    console.log('Creating indexes for privacy features...');
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_emails_content_expires_at ON emails(content_expires_at) WHERE content_expires_at IS NOT NULL
    `);
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_emails_last_accessed ON emails(last_accessed)
    `);
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_emails_is_content_encrypted ON emails(is_content_encrypted) WHERE is_content_encrypted = true
    `);
    console.log('✅ Indexes created for privacy features');

    // Set default privacy settings for existing users
    console.log('Setting default privacy settings for existing users...');
    const result = await db.execute(sql`
      INSERT INTO settings (user_id, data_retention_days, allow_ai_processing, store_email_content, auto_delete_processed_emails, encrypt_sensitive_data, consent_version, consent_timestamp, created_at, updated_at)
      SELECT 
        id as user_id,
        30 as data_retention_days,
        true as allow_ai_processing,
        true as store_email_content,
        false as auto_delete_processed_emails,
        true as encrypt_sensitive_data,
        '1.0' as consent_version,
        NOW() as consent_timestamp,
        NOW() as created_at,
        NOW() as updated_at
      FROM users 
      WHERE id NOT IN (SELECT user_id FROM settings WHERE user_id IS NOT NULL)
      ON CONFLICT (user_id) DO UPDATE SET
        data_retention_days = COALESCE(settings.data_retention_days, 30),
        allow_ai_processing = COALESCE(settings.allow_ai_processing, true),
        store_email_content = COALESCE(settings.store_email_content, true),
        auto_delete_processed_emails = COALESCE(settings.auto_delete_processed_emails, false),
        encrypt_sensitive_data = COALESCE(settings.encrypt_sensitive_data, true),
        consent_version = COALESCE(settings.consent_version, '1.0'),
        consent_timestamp = COALESCE(settings.consent_timestamp, NOW()),
        updated_at = NOW()
    `);
    console.log('✅ Default privacy settings applied to existing users');

    // Update last_accessed for all existing emails
    console.log('Setting last_accessed timestamp for existing emails...');
    await db.execute(sql`
      UPDATE emails 
      SET last_accessed = COALESCE(received_at, NOW())
      WHERE last_accessed IS NULL
    `);
    console.log('✅ Last accessed timestamps updated for existing emails');

    // Set content expiration for existing emails based on user settings
    console.log('Setting content expiration for existing emails...');
    await db.execute(sql`
      UPDATE emails 
      SET 
        content_expires_at = received_at + INTERVAL '1 day' * COALESCE(s.data_retention_days, 30),
        retention_days = COALESCE(s.data_retention_days, 30)
      FROM settings s 
      WHERE emails.user_id = s.user_id 
        AND emails.content_expires_at IS NULL
        AND emails.original_content IS NOT NULL
    `);
    console.log('✅ Content expiration set for existing emails');

    console.log('🎉 Privacy features migration completed successfully!');

    // Display summary
    const emailCount = await db.execute(sql`SELECT COUNT(*) as count FROM emails`);
    const userCount = await db.execute(sql`SELECT COUNT(*) as count FROM users`);
    const settingsCount = await db.execute(sql`SELECT COUNT(*) as count FROM settings`);
    
    console.log('\n📊 Migration Summary:');
    console.log(`   📧 Total emails: ${emailCount[0]?.count || 0}`);
    console.log(`   👥 Total users: ${userCount[0]?.count || 0}`);
    console.log(`   ⚙️  Settings records: ${settingsCount[0]?.count || 0}`);
    console.log('\n✨ Privacy features are now active!');
    console.log('\nNext steps:');
    console.log('1. Users can now configure their privacy settings via /api/privacy/settings');
    console.log('2. Automatic data cleanup will run every hour');
    console.log('3. New emails will automatically have expiration scheduled');
    console.log('4. Encryption can be enabled per user in their privacy settings');

  } catch (error) {
    console.error('❌ Error during privacy features migration:', error);
    throw error;
  }
}

migratePrivacyFeatures().finally(async () => {
  console.log('Closing database connection...');
  await closeDatabase();
}); 