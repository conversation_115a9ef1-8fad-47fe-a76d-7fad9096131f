import type { ErrorInfo, ReactNode } from 'react';
import type { FallbackProps } from 'react-error-boundary';
import { ErrorBoundary as ReactErrorBoundary } from 'react-error-boundary';
import { Button } from '@/components/ui/button';
import { logError, parseApiError } from '@/lib/errorHandler';

// Default fallback UI, matches the previous class component's render
function _DefaultFallback({ error, resetErrorBoundary }: FallbackProps) {
  const errorMessage =
    error instanceof Error ? error.message : String(error ?? 'An unknown error occurred.');
  return (
    <div
      className="flex flex-col items-center justify-center min-h-[200px] p-6 border rounded-lg bg-background"
      role="alert"
    >
      <div className="text-destructive mb-4 text-lg font-semibold">Something went wrong</div>
      {error && (
        <pre className="text-sm text-destructive-foreground bg-destructive/10 p-2 rounded-md my-2">
          {errorMessage}
        </pre>
      )}
      <div className="mb-6 text-center text-muted-foreground">
        <p>We've encountered an unexpected error.</p>
        <p className="mt-1">Our team has been notified and we're working to fix it.</p>
      </div>
      <Button variant="default" onClick={resetErrorBoundary}>
        Try Again
      </Button>
    </div>
  );
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onReset?: () => void;
}

// Custom ErrorBoundary wrapper
const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({ children, fallback, onReset }) => {
  const handleOnError = (error: Error, info: ErrorInfo) => {
    const appError = parseApiError(error);
    logError(appError, { componentStack: info.componentStack });
  };

  const FallbackComponent = fallback ? () => <>{fallback}</> : () => fallback;

  return (
    <ReactErrorBoundary
      FallbackComponent={FallbackComponent}
      onError={handleOnError}
      onReset={onReset}
    >
      {children}
    </ReactErrorBoundary>
  );
};

export default ErrorBoundary;

/**
 * Higher-order component that wraps a component with an ErrorBoundary
 * Kept for backward compatibility.
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ErrorBoundaryProps, 'children'> = {}
): React.FC<P> {
  const WithErrorBoundary: React.FC<P> = (props) => (
    <ErrorBoundary {...options}>
      <Component {...props} />
    </ErrorBoundary>
  );

  // Set display name for debugging
  const displayName = Component.displayName || Component.name || 'Component';
  WithErrorBoundary.displayName = `withErrorBoundary(${displayName})`;

  return WithErrorBoundary;
}
