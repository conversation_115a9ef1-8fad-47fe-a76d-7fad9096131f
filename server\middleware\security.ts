/**
 * Security Middleware
 *
 * This module provides security middleware for the Express application:
 * - Rate limiting (global and route-specific)
 * - CSRF protection
 * - Security headers
 * - Error sanitization
 */

import type { NextFunction, Request, Response } from 'express';
import rateLimit from 'express-rate-limit';
import { v4 as uuidv4 } from 'uuid';
import { getEnvVar } from '../lib/environmentValidator';
import logger from '../lib/logger';

/**
 * Configuration for different rate limiters
 */
const RATE_LIMIT_CONFIG = {
  // General API limiter
  standard: {
    windowMs: 10 * 60 * 1000, // 10 minutes
    max: 200, // 200 requests per window
    message: {
      success: false,
      message: 'Too many requests, please try again later.',
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  },

  // More strict limiter for authentication-related endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 30, // 30 login attempts per window
    message: {
      success: false,
      message: 'Too many authentication attempts, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Very restrictive limiter for password reset, etc.
  sensitive: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10, // 10 requests per hour
    message: {
      success: false,
      message: 'Too many sensitive operations, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Admin-specific limits
  admin: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per window for admin operations
    message: {
      success: false,
      message: 'Too many admin operations, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
  },
};

/**
 * Create and export rate limiters
 */
export const standardLimiter = rateLimit(RATE_LIMIT_CONFIG.standard);
export const authLimiter = rateLimit(RATE_LIMIT_CONFIG.auth);
export const sensitiveLimiter = rateLimit(RATE_LIMIT_CONFIG.sensitive);
export const adminLimiter = rateLimit(RATE_LIMIT_CONFIG.admin);

/**
 * IP-based middleware to block known bad actors
 * This could be enhanced to check against IP blacklists or reputation services
 */
export const ipFilter = (_req: Request, _res: Response, next: NextFunction) => {
  // Implement IP blacklisting here if needed
  // const blockedIPs = ['0.0.0.0']; // Example only - replace with real bad IPs
  // if (blockedIPs.includes(req.ip)) {
  //   logger.warn(`Blocked request from blacklisted IP: ${req.ip}`);
  //   return res.status(403).json({ success: false, message: 'Access denied' });
  // }

  next();
};

/**
 * Apply security headers to all responses
 */
export const securityHeaders = (_req: Request, res: Response, next: NextFunction) => {
  // Set security-related HTTP headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  // The X-XSS-Protection header is deprecated and should not be used.
  // A strong Content-Security-Policy is the recommended replacement.
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  res.setHeader('Referrer-Policy', 'same-origin');

  // Content Security Policy
  // NOTE: 'unsafe-inline' has been removed from script-src and style-src.
  // This is a critical security improvement but may require frontend changes
  // to move inline styles and scripts to external files.
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self'; style-src 'self'; img-src 'self' data:; connect-src 'self'; font-src 'self'; object-src 'none'; media-src 'self'; frame-src 'self';"
  );

  next();
};

/**
 * Sanitize error responses to prevent information disclosure
 */
export const sanitizeErrors = (err: any, req: Request, res: Response, _next: NextFunction) => {
  // Log the full error for debugging
  logger.error('Server error:', err);

  // Determine status code (default to 500)
  const statusCode = err.statusCode || 500;

  // Only expose minimal error details in production
  const responseError = {
    success: false,
    message:
      getEnvVar('NODE_ENV') === 'production'
        ? 'An unexpected error occurred'
        : err.message || 'Unknown error',
    code: err.code || 'INTERNAL_ERROR',
  };

  // Add request ID for debugging if available
  if (req.headers['x-request-id'] || req.headers['x-trace-id']) {
    (responseError as any).requestId = req.headers['x-request-id'] || req.headers['x-trace-id'];
  }

  // Specific error types can have custom messages
  if (err.type === 'entity.parse.failed') {
    responseError.message = 'Invalid request data format';
    responseError.code = 'INVALID_REQUEST_FORMAT';
    return res.status(400).json(responseError);
  }

  // Return sanitized error response
  return res.status(statusCode).json(responseError);
};

/**
 * Set a unique request ID on each request
 */
export const setRequestId = (req: Request, res: Response, next: NextFunction) => {
  // Respect any existing request ID from upstream proxies or load balancers
  const existingId = req.headers['x-request-id'] || req.headers['x-trace-id'];
  const requestId = Array.isArray(existingId) ? existingId[0] : existingId;

  const finalRequestId = requestId || uuidv4();

  req.headers['x-request-id'] = finalRequestId;
  res.setHeader('X-Request-ID', finalRequestId);
  next();
};

/**
 * Apply all security middleware to an Express app
 */
export const applySecurityMiddleware = (app: any) => {
  // Apply middleware in the correct order: from most specific to most general.

  // 1. Set request ID and basic security headers for all responses.
  app.use(setRequestId);
  app.use(securityHeaders);

  // 2. Apply the strictest rate limiters to sensitive endpoints first.
  app.use(['/api/auth/reset-password', '/api/auth/update-password'], sensitiveLimiter);

  // 3. Apply specific limiters for auth and admin sections.
  app.use('/api/auth', authLimiter);
  app.use('/api/admin', adminLimiter);

  // 4. Apply the standard, more general rate limiter to all other API routes.
  app.use('/api', standardLimiter);

  // 5. Apply error sanitization middleware last to catch any errors from previous middleware.
  app.use(sanitizeErrors);

  logger.info('Security middleware initialized successfully');
};
