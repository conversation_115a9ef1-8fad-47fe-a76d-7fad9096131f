/**
 * CircuitBreaker Service (modern) – concise unit tests.
 *
 * Scenarios:
 * 1. CLOSED -> OPEN after consecutive failures >= threshold.
 * 2. OPEN rejects calls until resetTimeout elapsed, then transitions to HALF_OPEN.
 * 3. HALF_OPEN success moves to CLOSED; failure moves back to OPEN.
 */

import { CircuitBreaker, CircuitStateEnum } from '@server/utils/circuitBreaker';

// ----------------------------------
// Mock Redis client with minimal HGET/HSET/HINCRBY behaviour
// ----------------------------------

const redisStore: Record<string, Record<string, string>> = {};
function createMockRedis() {
  const client: any = {
    hGetAll: jest.fn(async (key: string) => redisStore[key] ?? {}),
    hSet: jest.fn(async (key: string, obj: Record<string, string>) => {
      redisStore[key] = { ...(redisStore[key] ?? {}), ...obj };
      return 1;
    }),
  };
  client.hIncrBy = jest.fn(async (key: string, field: string, by: number) => {
    const current = Number((redisStore[key]?.[field] ?? '0')) + by;
    await client.hSet(key, { [field]: String(current) });
    return current;
  });
  return client;
}

const mockRedisClient = createMockRedis();

jest.mock('@server/services/redis', () => ({
  __esModule: true,
  getRedisClient: () => mockRedisClient,
}));

// Silence logger
jest.mock('@server/lib/logger', () => ({
  __esModule: true,
  default: {
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('CircuitBreaker (modern implementation)', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.clearAllMocks();
    Object.keys(redisStore).forEach((k) => delete redisStore[k]);
  });
  afterEach(() => {
    jest.useRealTimers();
  });

  it('transitions CLOSED → OPEN after failure threshold', async () => {
    const breaker = new CircuitBreaker({ name: 'svc', failureThreshold: 2, resetTimeout: 10000 });

    // two failing executions
    await expect(breaker.execute(() => Promise.reject(new Error('fail')))).rejects.toThrow('fail');
    await expect(breaker.execute(() => Promise.reject(new Error('fail')))).rejects.toThrow('fail');

    const details = await breaker.getStateDetails();
    expect(details.state).toBe(CircuitStateEnum.OPEN);
  });

  it('OPEN rejects until resetTimeout then moves to HALF_OPEN', async () => {
    const breaker = new CircuitBreaker({ name: 'svc2', failureThreshold: 1, resetTimeout: 30000 });
    // trip it open
    await expect(breaker.execute(() => Promise.reject(new Error('x')))).rejects.toThrow('x');

    // should reject immediately while open
    await expect(breaker.execute(() => Promise.resolve('ok'))).rejects.toThrow();

    // advance time past timeout
    jest.advanceTimersByTime(30001);

    // Next execution should now be allowed and breaker in HALF_OPEN
    await expect(breaker.execute(() => Promise.resolve('ok'))).resolves.toBe('ok');
    const details = await breaker.getStateDetails();
    expect(details.state).not.toBe(CircuitStateEnum.OPEN);
  });

  it('HALF_OPEN success closes circuit; failure reopens', async () => {
    const breaker = new CircuitBreaker({ name: 'svc3', failureThreshold: 1, successThreshold: 1, resetTimeout: 10000 });
    // open it
    await expect(breaker.execute(() => Promise.reject(new Error('boom')))).rejects.toThrow('boom');

    jest.advanceTimersByTime(10001);
    // first execute success in half-open
    await expect(breaker.execute(() => Promise.resolve('yay'))).resolves.toBe('yay');
    let details = await breaker.getStateDetails();
    expect(details.state).toBe(CircuitStateEnum.CLOSED);

    // open again
    await expect(breaker.execute(() => Promise.reject(new Error('boom')))).rejects.toThrow();
    jest.advanceTimersByTime(10001);
    // failure in half-open triggers OPEN again
    await expect(breaker.execute(() => Promise.reject(new Error('fail')))).rejects.toThrow();
    details = await breaker.getStateDetails();
    expect(details.state).toBe(CircuitStateEnum.OPEN);
  });
}); 