import {
  Archive,
  Bar<PERSON>hart,
  Clock,
  FileText,
  HelpCircle,
  Inbox,
  LogOut,
  Settings,
  Star,
  Trash2,
  User as UserIcon,
} from 'lucide-react';

export interface NavItem {
  name: string;
  path: string;
  icon: React.ElementType;
  category: 'primary' | 'secondary' | 'utility' | 'dropdown';
  count?: number; // Optional count for badges
  adminOnly?: boolean; // For admin-specific routes
}

export const primaryNavItems: NavItem[] = [
  {
    name: 'Inbox',
    path: '/dashboard',
    icon: Inbox,
    category: 'primary',
  },
  {
    name: 'Priority Map',
    path: '/heatmap',
    icon: BarChart,
    category: 'primary',
  },
  {
    name: 'Daily Digest',
    path: '/digest',
    icon: FileText,
    category: 'primary',
  },
  {
    name: 'Archive',
    path: '/archive',
    icon: Archive,
    category: 'secondary',
  },
  {
    name: 'Trash',
    path: '/trash',
    icon: Trash2,
    category: 'secondary',
  },
  {
    name: 'Snoozed',
    path: '/snoozed',
    icon: Clock,
    category: 'secondary',
  },
  {
    name: 'Important',
    path: '/important',
    icon: Star,
    category: 'secondary',
  },
  {
    name: 'Settings',
    path: '/settings',
    icon: Settings,
    category: 'utility',
  },
];

export const secondaryNavItems: NavItem[] = [];

export const utilityNavItems: NavItem[] = [];

export const dropdownNavItems: NavItem[] = [
  {
    name: 'Settings',
    path: '/settings',
    icon: Settings,
    category: 'dropdown',
  },
  {
    name: 'Help & Tour',
    path: '#', // Placeholder for a help modal or page
    icon: HelpCircle,
    category: 'dropdown',
  },
];

export const dropdownActionItems: NavItem[] = [
  {
    name: 'Logout',
    path: '/logout',
    icon: LogOut,
    category: 'dropdown',
  },
];
