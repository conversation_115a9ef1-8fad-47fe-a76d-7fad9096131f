/**
 * Client-Side Logger
 *
 * Provides consistent logging interface for the client-side application.
 * Integrates with server-side centralized logging in production.
 * Sanitizes sensitive data and provides structured logging.
 */

import apiClient from '@/lib/apiClient';

// Environment detection
import { isDevelopment as isDev, isProduction as isProd } from './environment';

const isDevelopment = isDev();
const isProduction = isProd();

// Log levels
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal',
}

// Client-side log entry interface
interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  metadata?: Record<string, any>;
  error?: {
    message: string;
    stack?: string;
    name?: string;
    code?: string;
  };
  component?: string;
  userId?: string;
  sessionId?: string;
}

// Sensitive keys to sanitize
const SENSITIVE_KEYS = [
  'token',
  'password',
  'secret',
  'key',
  'apiKey',
  'credential',
  'auth',
  'authorization',
  'cookie',
  'session',
  'jwt',
  'access_token',
  'refresh_token',
];

/**
 * Sanitize potentially sensitive information from log data
 */
function sanitizeLogData(data: any): any {
  if (!data || typeof data !== 'object') {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(sanitizeLogData);
  }

  const sanitized: any = {};

  for (const [key, value] of Object.entries(data)) {
    const isSensitive = SENSITIVE_KEYS.some((sensitiveKey) =>
      key.toLowerCase().includes(sensitiveKey.toLowerCase())
    );

    if (isSensitive) {
      if (typeof value === 'string' && value.length > 4) {
        sanitized[key] = `${value.substring(0, 4)}****`;
      } else {
        sanitized[key] = '[REDACTED]';
      }
    } else if (typeof value === 'object') {
      sanitized[key] = sanitizeLogData(value);
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
}

/**
 * Extract error details safely
 */
function extractErrorDetails(error: unknown): LogEntry['error'] | undefined {
  if (!error) return undefined;

  if (error instanceof Error) {
    return {
      message: error.message,
      stack: isDevelopment ? error.stack : undefined,
      name: error.name,
      code: (error as any).code,
    };
  }

  return {
    message: String(error),
    name: 'UnknownError',
  };
}

/**
 * Send log to server-side logger in production
 */
async function sendToServer(logEntry: LogEntry): Promise<void> {
  if (!isProduction) return;

  try {
    // Send using centralised apiClient with timeout and retries disabled for logs
    await apiClient.post('/api/logs/client', logEntry, {
      timeout: 5000,
      retries: 0,
      // Logging may occur before auth state is established; allow skipAuth to prevent hard failure
      skipAuth: true,
      skipCsrf: true,
    });
  } catch (error) {
    // Silently fail - don't break the app if logging fails
    if (isDevelopment) {
      console.warn('[Logger] Failed to send log to server:', error);
    }
  }
}

/**
 * Format log message for console output
 */
function formatConsoleMessage(logEntry: LogEntry): string {
  const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
  let message = `${timestamp} [${logEntry.level.toUpperCase()}]`;

  if (logEntry.component) {
    message += ` [${logEntry.component}]`;
  }

  message += `: ${logEntry.message}`;

  return message;
}

/**
 * Create log entry
 */
function createLogEntry(
  level: LogLevel,
  message: string,
  metadata?: Record<string, any>,
  error?: unknown,
  component?: string,
  userId?: string
): LogEntry {
  const logEntry: LogEntry = {
    level,
    message,
    timestamp: new Date().toISOString(),
    sessionId: getSessionId(),
  };

  if (metadata) {
    logEntry.metadata = sanitizeLogData(metadata);
  }

  if (error) {
    logEntry.error = extractErrorDetails(error);
  }

  if (component) {
    logEntry.component = component;
  }

  // Add user ID if available
  if (userId) {
    logEntry.userId = userId;
  }

  return logEntry;
}

/**
 * Get session ID from sessionStorage or generate one
 */
function getSessionId(): string {
  if (typeof window === 'undefined') return 'ssr-session';

  let sessionId = sessionStorage.getItem('logger-session-id');
  if (!sessionId) {
    sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    sessionStorage.setItem('logger-session-id', sessionId);
  }
  return sessionId;
}

/**
 * Main logger class
 */
class ClientLogger {
  private isInitialized = false;
  private userId?: string;

  constructor() {
    this.initialize();
  }

  public setUserId(userId?: string) {
    this.userId = userId;
  }

  private initialize() {
    if (this.isInitialized) return;

    // Set up global error handlers
    if (typeof window !== 'undefined') {
      // Catch unhandled errors
      window.addEventListener('error', (event) => {
        this.error('Unhandled error', event.error, {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        });
      });

      // Catch unhandled promise rejections
      window.addEventListener('unhandledrejection', (event) => {
        this.error('Unhandled promise rejection', event.reason, {
          reason: typeof event.reason === 'string' ? event.reason : 'Non-string reason',
        });
      });
    }

    this.isInitialized = true;
  }

  debug(message: string, metadata?: Record<string, any>, component?: string) {
    this.log(LogLevel.DEBUG, message, metadata, undefined, component);
  }

  info(message: string, metadata?: Record<string, any>, component?: string) {
    this.log(LogLevel.INFO, message, metadata, undefined, component);
  }

  warn(message: string, metadata?: Record<string, any>, component?: string) {
    this.log(LogLevel.WARN, message, metadata, undefined, component);
  }

  error(message: string, error?: unknown, metadata?: Record<string, any>, component?: string) {
    this.log(LogLevel.ERROR, message, metadata, error, component);
  }

  fatal(message: string, error?: unknown, metadata?: Record<string, any>, component?: string) {
    this.log(LogLevel.FATAL, message, metadata, error, component);
  }

  private log(
    level: LogLevel,
    message: string,
    metadata?: Record<string, any>,
    error?: unknown,
    component?: string
  ) {
    // In development, log everything to the console for easier debugging
    if (isDevelopment) {
      const entry = createLogEntry(level, message, metadata, error, component, this.userId);
      const formattedMessage = formatConsoleMessage(entry);
      const details = {
        ...(entry.metadata && { metadata: entry.metadata }),
        ...(entry.error && { error: entry.error }),
      };

      switch (level) {
        case LogLevel.DEBUG:
          console.debug(formattedMessage, details);
          break;
        case LogLevel.INFO:
          console.info(formattedMessage, details);
          break;
        case LogLevel.WARN:
          console.warn(formattedMessage, details);
          break;
        case LogLevel.ERROR:
        case LogLevel.FATAL:
          console.error(formattedMessage, details);
          break;
        default:
          console.log(formattedMessage, details);
          break;
      }
    }

    // Only send errors and fatal logs to the server in production
    if (isProduction && (level === LogLevel.ERROR || level === LogLevel.FATAL)) {
      sendToServer(createLogEntry(level, message, metadata, error, component, this.userId));
    }
  }

  // --- Namespaced loggers for specific contexts ---

  auth = {
    loginSuccess: (userId: string, provider: string) => {
      this.info('User login successful', { userId, provider }, 'auth');
    },
    loginFailure: (error: unknown, provider: string) => {
      this.error('User login failed', error, { provider }, 'auth');
    },
    logout: (userId: string) => {
      this.info('User logged out', { userId }, 'auth');
    },
  };

  firebase = {
    initSuccess: () => {
      this.info('Firebase initialized successfully', undefined, 'firebase');
    },
    initFailure: (error: unknown) => {
      this.error('Firebase initialization failed', error, undefined, 'firebase');
    },
  };

  api = {
    request: (method: string, url: string, params?: Record<string, any>) =>
      this.debug(`API Request: ${method} ${url}`, { params }, 'API'),
    responseSuccess: (method: string, url:string, status: number) =>
      this.debug(`API Success: ${method} ${url}`, { status }, 'API'),
    responseError: (method: string, url: string, error: any) =>
      this.error(`API Error: ${method} ${url}`, error, { status: error.status }, 'API'),
  };
}

// Export singleton instance
const logger = new ClientLogger();
export default logger;
