import { AlertTriangle } from 'lucide-react';
import type React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface FormErrorProps {
  message?: string;
  errors?: Record<string, string[]>;
  className?: string;
}

/**
 * FormError component
 * Displays validation errors for form submissions
 */
export const FormError: React.FC<FormErrorProps> = ({ message, errors, className = '' }) => {
  // Don't render if no errors
  if (!message && (!errors || Object.keys(errors).length === 0)) {
    return null;
  }

  return (
    <Alert variant="destructive" className={`mb-4 ${className}`}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="mt-1">
        {message && <p className="font-medium">{message}</p>}

        {errors && Object.keys(errors).length > 0 && (
          <ul className="mt-2 text-sm list-disc pl-5 space-y-1">
            {Object.entries(errors).map(([field, errorMessages]) => (
              <li key={field}>
                <span className="font-medium">{formatFieldName(field)}:</span>{' '}
                {Array.isArray(errorMessages) ? errorMessages.join('. ') : String(errorMessages)}
              </li>
            ))}
          </ul>
        )}
      </AlertDescription>
    </Alert>
  );
};

/**
 * Helper to format field names for better readability
 */
function formatFieldName(fieldName: string): string {
  // Convert camelCase to Title Case with spaces
  const formatted = fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase());

  // Handle common field name abbreviations
  return formatted.replace(/Id /g, 'ID ').replace(/Url /g, 'URL ').replace(/Api /g, 'API ');
}

/**
 * ZodFormError component
 * Specialized version for handling Zod validation errors from react-hook-form
 */
export const ZodFormError: React.FC<{
  formErrors: Record<string, { message?: string } | undefined>;
  className?: string;
}> = ({ formErrors, className = '' }) => {
  if (!formErrors || Object.keys(formErrors).length === 0) {
    return null;
  }

  const formattedErrors: Record<string, string[]> = {};

  // Format errors from react-hook-form with Zod resolver
  for (const [field, error] of Object.entries(formErrors)) {
    if (error) {
      formattedErrors[field] = [error.message || 'Invalid input'];
    }
  }

  return (
    <FormError
      message="Please correct the following errors:"
      errors={formattedErrors}
      className={className}
    />
  );
};
