import type React from 'react';
import { memo, useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { Skeleton } from './skeleton';

interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  fallbackSrc?: string;
  onLoad?: () => void;
  onError?: () => void;
  loadingDelayMs?: number;
  threshold?: number;
}

/**
 * LazyImage component with IntersectionObserver-based loading
 *
 * Features:
 * - Only loads images when they enter the viewport
 * - Shows loading skeleton until image is loaded
 * - Handles loading errors with fallback
 * - Optimized with memoization to prevent unnecessary re-renders
 */
const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt = '',
  className = '',
  width,
  height,
  fallbackSrc = '',
  onLoad,
  onError,
  loadingDelayMs = 0,
  threshold = 0.1,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [shouldLoad, setShouldLoad] = useState(false);
  const [showSkeleton, setShowSkeleton] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Set up intersection observer to detect when image is in viewport
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    // If IntersectionObserver is available (most modern browsers)
    if (typeof IntersectionObserver !== 'undefined') {
      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting) {
            // Start loading the image
            setShouldLoad(true);

            // Clean up observer after intersection is detected
            if (containerRef.current && observerRef.current) {
              observerRef.current.unobserve(containerRef.current);
            }
          }
        },
        { threshold }
      );

      if (containerRef.current) {
        observerRef.current.observe(containerRef.current);
      }
    } else {
      // Fallback for browsers that don't support IntersectionObserver
      setShouldLoad(true);
    }

    // Optional delay for showing skeleton (prevents flickering for fast-loading images)
    if (loadingDelayMs > 0) {
      timeoutId = setTimeout(() => {
        setShowSkeleton(false);
      }, loadingDelayMs);
    }

    // Set width and height programmatically to avoid inline styles
    const element = containerRef.current;
    if (element) {
      if (width)
        element.style.setProperty('--width', typeof width === 'number' ? `${width}px` : width);
      if (height)
        element.style.setProperty('--height', typeof height === 'number' ? `${height}px` : height);
    }

    return () => {
      // Clean up
      if (observerRef.current && containerRef.current) {
        observerRef.current.unobserve(containerRef.current);
      }

      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [threshold, loadingDelayMs, width, height]);

  // Handlers for image loading and errors
  const handleLoad = () => {
    setIsLoaded(true);
    setShowSkeleton(false);
    if (onLoad) onLoad();
  };

  const handleError = () => {
    setHasError(true);
    setShowSkeleton(false);
    if (onError) onError();
  };

  // Decide which source to use based on loading state and errors
  const finalSrc = hasError ? fallbackSrc : shouldLoad ? src : '';

  const sizeClass = 'w-[var(--width,100%)] h-[var(--height,auto)]';

  return (
    <div ref={containerRef} className={cn('relative', sizeClass)}>
      {/* Show skeleton while loading */}
      {showSkeleton && !isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center overflow-hidden rounded">
          <Skeleton className={cn('h-full w-full', sizeClass)} />
        </div>
      )}

      {/* The actual image */}
      <img
        src={finalSrc}
        alt={alt}
        className={cn(
          className,
          sizeClass,
          isLoaded ? 'opacity-100' : 'opacity-0',
          'transition-opacity duration-300 ease-in'
        )}
        width={width}
        height={height}
        onLoad={handleLoad}
        onError={handleError}
        loading="lazy"
        {...props}
      />
    </div>
  );
};

export default memo(LazyImage);
