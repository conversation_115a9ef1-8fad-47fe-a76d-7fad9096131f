import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/context/AuthContext';
import { usePermission } from '@/hooks/use-permission';
import { useToast } from '@/hooks/use-toast';
import apiClient from '@/lib/apiClient';
import { EmailAction } from '@/lib/constants';
import type { Email } from '@/types/email';

/**
 * Custom hook for email-related actions
 * Separates API calls and state management from UI components
 */
export function useEmailActions() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { handlePermissionError } = usePermission();
  const { user } = useAuth();

  /**
   * Generic error handler for email actions with permission handling
   */
  const handleEmailActionError = async (error: unknown, actionName: string) => {
    if (error instanceof Error) {
      // First check if this is a permission or token-related error
      const wasHandled = await handlePermissionError(error.message);

      if (wasHandled) {
        // The permission handler already displayed a toast and set up redirection
        return;
      }

      // If not handled as a permission error, show appropriate error toast
      toast({
        title: `Failed to ${actionName}`,
        description: error.message,
        variant: 'destructive',
      });
    } else {
      // Unknown error type
      toast({
        title: `Failed to ${actionName}`,
        description: 'An unknown error occurred',
        variant: 'destructive',
      });
    }
  };

  const createEmailActionMutation = <TData = unknown, TVariables = number>(
    endpoint: (id: TVariables) => string,
    actionName: string,
    successMessage: string
  ) => {
    return useMutation<TData, Error, TVariables>({
      mutationFn: (variables: TVariables) => apiClient.post(endpoint(variables)),
      onSuccess: () => {
        // Invalidate all email-related queries
        queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
        queryClient.invalidateQueries({ queryKey: ['stats'] });
        toast({ title: 'Success', description: successMessage });
      },
      onError: (error) => handleEmailActionError(error, actionName),
    });
  };

  const archiveMutation = createEmailActionMutation<unknown, string>(
    (messageId) => `/api/emails/by-message-id/${encodeURIComponent(messageId)}/archive`,
    EmailAction.ARCHIVE,
    'Email archived successfully.'
  );

  const trashMutation = createEmailActionMutation<unknown, string>(
    (messageId) => `/api/emails/by-message-id/${encodeURIComponent(messageId)}/trash`,
    EmailAction.TRASH,
    'Email moved to trash.'
  );

  const markImportantMutation = useMutation<void, Error, { messageId: string; important: boolean }>({
    mutationFn: ({ messageId, important }) =>
      apiClient.post(`/api/emails/by-message-id/${encodeURIComponent(messageId)}/important`, { important }),
    onSuccess: (_, { important }) => {
      // Invalidate all email-related queries
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['stats'] });
      toast({
        title: 'Success',
        description: `Email marked as ${important ? 'important' : 'not important'}.`,
      });
    },
    onError: (error) => handleEmailActionError(error, EmailAction.MARK_IMPORTANT),
  });

  const snoozeMutation = useMutation<void, Error, { messageId: string; snoozeUntil: Date | null }>({
    mutationFn: ({ messageId, snoozeUntil }) =>
      apiClient.post(`/api/emails/by-message-id/${encodeURIComponent(messageId)}/snooze`, { snoozeUntil }),
    onSuccess: () => {
      // Invalidate all email-related queries
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['stats'] });
      toast({ title: 'Success', description: 'Email snoozed successfully.' });
    },
    onError: (error) => handleEmailActionError(error, EmailAction.SNOOZE),
  });

  const regenerateSummaryMutation = useMutation<
    string,
    Error,
    string,
    { previousEmail: Email | undefined }
  >({
    mutationFn: (messageId) => apiClient.post(`/api/ai/regenerate-summary/by-message-id/${encodeURIComponent(messageId)}`),
    onMutate: async (messageId) => {
      await queryClient.cancelQueries({ queryKey: ['emails', 'detail', messageId] });
      const previousEmail = queryClient.getQueryData<Email>(['emails', 'detail', messageId]);
      queryClient.setQueryData<Email>(['emails', 'detail', messageId], (old) =>
        old ? { ...old, summary: 'Processing...' } : old
      );
      return { previousEmail };
    },
    onSuccess: (newSummary, messageId) => {
      queryClient.setQueryData<Email>(['emails', 'detail', messageId], (old) =>
        old ? { ...old, summary: newSummary } : old
      );
      toast({ title: 'Summary regenerated', description: 'The AI summary has been updated.' });
    },
    onError: (err, messageId, context) => {
      if (context?.previousEmail) {
        queryClient.setQueryData(['emails', 'detail', messageId], context.previousEmail);
      }
      toast({
        title: 'Failed to regenerate summary',
        description: err.message,
        variant: 'destructive',
      });
    },
    onSettled: (_data, _error, messageId) => {
      queryClient.invalidateQueries({ queryKey: ['emails', 'detail', messageId] });
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
    },
  });

  // Send reply mutation
  const sendReplyMutation = useMutation({
    mutationFn: async (replyData: { messageId: string; content: string }) => {
      return apiClient.post(`/api/emails/by-message-id/${encodeURIComponent(replyData.messageId)}/reply`, {
        content: replyData.content,
      });
    },
    onSuccess: () => {
      // Invalidate all email-related queries
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['stats'] });
      toast({
        title: 'Success',
        description: 'Your reply has been sent.',
      });
    },
    onError: (error) => handleEmailActionError(error, 'send reply'),
  });

  const updateEmailWithNewSummary = (
    emails: Email[],
    messageId: string,
    newSummary: string
  ): Email[] => {
    return emails.map((email) =>
      email.messageId === messageId ? { ...email, summary: newSummary } : email
    );
  };

  const findEmailByMessageId = (emails: Email[], messageId: string): Email | undefined => {
    return emails.find((email) => email.messageId === messageId);
  };

  return {
    // Archive email
    archiveEmail: archiveMutation.mutateAsync,
    isArchiving: archiveMutation.isPending,

    // Trash email
    trashEmail: trashMutation.mutateAsync,
    isTrashing: trashMutation.isPending,

    // Mark as important
    markEmailAsImportant: markImportantMutation.mutateAsync,
    isMarkingImportant: markImportantMutation.isPending,

    // Snooze email
    snoozeEmail: snoozeMutation.mutateAsync,
    isSnoozing: snoozeMutation.isPending,

    // Summary regeneration
    regenerateSummary: regenerateSummaryMutation.mutateAsync,
    isRegeneratingSummary: regenerateSummaryMutation.isPending,

    // Send reply
    sendReply: sendReplyMutation.mutate,
    isSendingReply: sendReplyMutation.isPending,

    // Helper method to update an email summary in context
    updateEmailWithNewSummary,

    // Helper method to find an email by messageId
    findEmailByMessageId,
  };
}
