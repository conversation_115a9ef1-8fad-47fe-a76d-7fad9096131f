import { defineConfig } from 'drizzle-kit';

if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is required');
}

// Check if we're connecting to Supabase
const isSupabase = process.env.DATABASE_URL.includes('supabase.com');

export default defineConfig({
  out: './migrations',
  schema: './shared/schema.ts',
  dialect: 'postgresql',
  dbCredentials: {
    url: process.env.DATABASE_URL,
    // Enhanced SSL configuration for Supabase compatibility
    ssl: isSupabase
      ? {
          rejectUnauthorized: false,
          // Custom check function that always passes for Supabase
          checkServerIdentity: () => undefined,
        }
      : false,
  },
  verbose: process.env.NODE_ENV === 'development',
  strict: true,
});
