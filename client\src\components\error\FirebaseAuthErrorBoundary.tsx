/**
 * Firebase Authentication Error Boundary
 *
 * Catches Firebase auth-specific errors and provides user-friendly recovery options.
 * Specifically handles the _getRecaptchaConfig error and other Firebase initialization issues.
 */

import type React from 'react';
import type { ErrorInfo } from 'react';
import { type FallbackProps, ErrorBoundary as ReactErrorBoundary } from 'react-error-boundary';
import { useLocation } from 'wouter';
import { isDevelopment } from '@/lib/environment';
import logger from '@/lib/logger';
import { Button } from '../ui/button';
import { ErrorDisplay } from './ErrorDisplay';

interface Props {
  children: React.ReactNode;
}

function isFirebaseAuthError(error: unknown): error is Error {
  if (!(error instanceof Error)) {
    return false;
  }
  return (
    error.message.includes('_getRecaptchaConfig') ||
    error.message.includes('Firebase auth') ||
    error.message.includes('authentication system') ||
    error.message.includes('auth not initialized') ||
    !!error.stack?.includes('firebase/auth')
  );
}

function FallbackRenderer({ error, resetErrorBoundary }: FallbackProps) {
  const [, setLocation] = useLocation();

  if (!isFirebaseAuthError(error)) {
    // For non-Firebase errors, re-throw so a parent boundary can catch it.
    throw error;
  }

  const handleRefresh = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    setLocation('/');
    resetErrorBoundary();
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="max-w-md w-full">
        <ErrorDisplay
          title="Authentication System Error"
          description="There was a problem initializing the authentication system. This is usually a temporary issue."
          error={error}
          onRetry={resetErrorBoundary}
          showRetry={false}
        />
        <div className="flex flex-col space-y-2 mt-4">
          <Button onClick={resetErrorBoundary}>Try Again</Button>
          <Button onClick={handleRefresh} variant="outline">
            Refresh Page
          </Button>
          <Button onClick={handleGoHome} variant="ghost">
            Go to Homepage
          </Button>
        </div>

        {isDevelopment() && (
          <details className="mt-4 p-3 bg-muted rounded text-sm">
            <summary className="cursor-pointer font-medium">Technical Details</summary>
            <pre className="text-xs mt-1 whitespace-pre-wrap">{error.stack}</pre>
          </details>
        )}
      </div>
    </div>
  );
}

const FirebaseAuthErrorBoundary: React.FC<Props> = ({ children }) => {
  const handleOnError = (error: Error, info: ErrorInfo) => {
    if (isFirebaseAuthError(error)) {
      logger.error('FirebaseAuthErrorBoundary caught an error', error, {
        componentStack: info.componentStack,
      }, 'firebase');

      // Send analytics event if gtag is available (more robust type checking)
      if (typeof window !== 'undefined' && 'gtag' in window && typeof window.gtag === 'function') {
        window.gtag('event', 'exception', {
          description: `Firebase Auth Error: ${error.message}`,
          fatal: false,
        });
      }
    }
  };

  return (
    <ReactErrorBoundary fallbackRender={FallbackRenderer} onError={handleOnError}>
      {children}
    </ReactErrorBoundary>
  );
};

export default FirebaseAuthErrorBoundary;
