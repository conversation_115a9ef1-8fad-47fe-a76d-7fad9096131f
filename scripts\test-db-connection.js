#!/usr/bin/env node

import { dirname, join } from 'node:path';
import { fileURLToPath } from 'node:url';
import dotenv from 'dotenv';
import pg from 'pg';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, '..', '.env') });

const { Pool } = pg;

async function testDatabaseConnection() {
  console.log('🔍 Testing Supabase Database Connection\n');

  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  console.log('📋 Connection Details:');
  try {
    const url = new URL(process.env.DATABASE_URL);
    console.log(`   Host: ${url.hostname}`);
    console.log(`   Port: ${url.port || 5432}`);
    console.log(`   Database: ${url.pathname.substring(1)}`);
    console.log(`   Username: ${url.username}`);
    console.log(`   SSL: ${url.hostname.includes('supabase.com') ? 'Required' : 'Optional'}`);
  } catch (error) {
    console.error(`❌ Invalid DATABASE_URL format: ${error.message}`);
    process.exit(1);
  }

  // Create connection pool with Supabase-optimized settings
  const isSupabase = process.env.DATABASE_URL.includes('supabase.com');

  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: isSupabase
      ? {
          rejectUnauthorized: false,
          checkServerIdentity: () => undefined,
        }
      : false,
    max: 5, // Limit connections for testing
    connectionTimeoutMillis: 20000, // 20 second timeout
    idleTimeoutMillis: 60000,
    allowExitOnIdle: true,
  });

  try {
    console.log('\n🔄 Attempting to connect...');
    const client = await pool.connect();
    console.log('✅ Connection established successfully!');

    console.log('\n🔄 Testing basic query...');
    const result = await client.query('SELECT NOW() as current_time, version() as pg_version');
    console.log('✅ Query executed successfully!');
    console.log(`   Current time: ${result.rows[0].current_time}`);
    console.log(
      `   PostgreSQL version: ${result.rows[0].pg_version.split(' ')[0]} ${result.rows[0].pg_version.split(' ')[1]}`
    );

    console.log('\n🔄 Testing session table creation...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "test_session" (
        "sid" varchar NOT NULL,
        "sess" json NOT NULL,
        "expire" timestamp(6) NOT NULL,
        CONSTRAINT "test_session_pkey" PRIMARY KEY ("sid")
      )
    `);
    console.log('✅ Session table test successful!');

    // Clean up test table
    await client.query('DROP TABLE IF EXISTS "test_session"');

    client.release();
    console.log('\n🔄 Closing connection pool...');
    await pool.end();

    console.log('\n🎉 All database tests passed successfully!');
    console.log('✅ Your Supabase connection is working correctly.');
  } catch (error) {
    console.error('\n❌ Database connection test failed:');
    console.error(`   Error: ${error.message}`);

    if (error.code) {
      console.error(`   Code: ${error.code}`);
    }

    console.log('\n💡 Troubleshooting tips:');
    console.log('   1. Verify your Supabase project is active (not paused)');
    console.log('   2. Check your DATABASE_URL credentials are correct');
    console.log('   3. Ensure your network allows connections to Supabase');
    console.log('   4. Try accessing your Supabase dashboard to confirm the project status');
    await pool.end();
    process.exit(1);
  }
}

// Run the test
testDatabaseConnection().catch((error) => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
