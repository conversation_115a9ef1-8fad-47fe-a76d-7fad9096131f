import { <PERSON>ert<PERSON><PERSON>cle, Alert<PERSON>riangle, CheckCircle, Info, RefreshCw } from 'lucide-react';
import { useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
// Emergency auth reset button removed - using proper authentication flow
import { Spinner } from '@/components/ui/spinner';
import { useProviderStatus } from '@/hooks/use-provider-status';
import { useToast } from '@/hooks/use-toast';
import { forceReconnectGmail } from '@/lib/emailProviders';

/**
 * Gmail Connection Status Component
 *
 * This component checks the status of a user's Gmail connection by using the centralized `useProviderStatus` hook.
 * It provides a way to view connection status and reset it if needed.
 */
export function GmailConnectionStatus() {
  const { gmailProvider, isLoading, error: unknownError, refetch } = useProviderStatus();
  const [isResetting, setIsResetting] = useState(false);
  const { toast } = useToast();
  const error = unknownError as Error | null;

  // Handle reset connection button click
  const handleResetConnection = async () => {
    setIsResetting(true);
    try {
      await forceReconnectGmail();
      toast({ title: 'Connection reset', description: 'Successfully sent reset request.' });
      refetch();
    } catch (_error) {
      toast({ title: 'Error', description: 'Failed to reset connection.', variant: 'destructive' });
    } finally {
      setIsResetting(false);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="p-4 bg-muted/30 rounded-lg flex items-center justify-center space-x-2">
        <Spinner size="sm" />
        <span>Checking Gmail connection status...</span>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error checking Gmail connection</AlertTitle>
        <AlertDescription>
          {error?.message ||
            'There was a problem checking your Gmail connection. Please try again later.'}
        </AlertDescription>
      </Alert>
    );
  }

  // No data state
  if (!gmailProvider) {
    return (
      <Alert variant="warning">
        <Info className="h-4 w-4" />
        <AlertTitle>No Gmail Connection Found</AlertTitle>
        <AlertDescription>
          No Gmail connection information is available. Please try connecting your Gmail account.
        </AlertDescription>
      </Alert>
    );
  }

  // Extract status information from the provider
  const {
    isConnected,
    connectionStatus,
    tokenInvalid,
    tokenError,
    lastRefreshedAt,
    lastApiError,
    email,
  } = gmailProvider;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Gmail Connection Status</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={() => refetch()}
          disabled={isLoading || isResetting}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Connection status */}
      <Alert variant={isConnected ? 'default' : 'destructive'}>
        {isConnected ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
        <AlertTitle>{isConnected ? 'Connected' : 'Not Connected'}</AlertTitle>
        <AlertDescription>
          {isConnected
            ? `Your Gmail account (${email || '...'}) is connected and working properly.`
            : `Your Gmail connection is inactive. ${
                tokenInvalid
                  ? 'The authorization token is invalid.'
                  : 'Please reconnect your account.'
              }`}
        </AlertDescription>
      </Alert>

      {/* Connection details */}
      <div className="bg-muted/30 rounded-lg p-4 text-sm">
        <h4 className="font-medium mb-2">Connection Details</h4>
        <div className="grid grid-cols-2 gap-2">
          <div>Email Account:</div>
          <div>{email || 'Unknown'}</div>

          <div>Connection Status:</div>
          <div className="flex items-center">
            {isConnected ? (
              <span className="text-green-600 flex items-center">
                <CheckCircle className="h-3 w-3 mr-1" /> Active
              </span>
            ) : (
              <span className="text-red-600 flex items-center">
                <AlertCircle className="h-3 w-3 mr-1" /> {connectionStatus || 'Inactive'}
              </span>
            )}
          </div>

          <div>Token Status:</div>
          <div className="flex items-center">
            {!tokenInvalid ? (
              <span className="text-green-600 flex items-center">
                <CheckCircle className="h-3 w-3 mr-1" /> Valid
              </span>
            ) : (
              <span className="text-red-600 flex items-center">
                <AlertCircle className="h-3 w-3 mr-1" /> Invalid
              </span>
            )}
          </div>

          <div>Last Token Refresh:</div>
          <div>{lastRefreshedAt ? new Date(lastRefreshedAt).toLocaleString() : 'Never'}</div>

          {tokenError && (
            <>
              <div>Token Error:</div>
              <div className="text-red-600">{tokenError}</div>
            </>
          )}

          {lastApiError && (
            <>
              <div>API Error:</div>
              <div className="text-red-600">{lastApiError}</div>
            </>
          )}
        </div>
      </div>

      {/* Authentication limbo state warning */}
      {!isConnected && tokenInvalid && (
        <Alert variant="default" className="bg-amber-50 border-amber-200 text-amber-800">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Authentication State Inconsistency Detected</AlertTitle>
          <AlertDescription>
            <p>
              You may be experiencing an authentication state inconsistency where your account
              appears both connected and disconnected at the same time.
            </p>
            <div className="mt-4">
              <p className="font-semibold">Try this solution:</p>
              <ol className="list-decimal ml-6 mt-2 space-y-1">
                <li>First, try resetting the connection's circuit breaker below</li>
                <li>
                  After the reset, you may need to reconnect your Gmail account through settings.
                </li>
              </ol>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Action buttons for resolving the issue */}
      {!isConnected && tokenInvalid && (
        <div className="flex flex-col gap-2">
          {/* Circuit Breaker Reset Button */}
          <Button
            variant="secondary"
            onClick={handleResetConnection}
            disabled={isResetting}
            className="w-full"
          >
            {isResetting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Resetting Circuit Breaker...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset Connection
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
}

export default GmailConnectionStatus;
