#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to fix duplicate user accounts where G<PERSON> OAuth created separate users
 * instead of linking to existing Firebase users.
 * 
 * This script will:
 * 1. Find users with the same email address
 * 2. Identify Firebase vs Google provider users
 * 3. Merge Gmail tokens into the Firebase user
 * 4. Transfer any emails from the Google user to the Firebase user
 * 5. Delete the duplicate Google user
 */

import { config } from 'dotenv';
import { storage } from '../server/storage';
import logger from '../server/lib/logger';

// Load environment variables
config();

interface UserGroup {
  email: string;
  users: Array<{
    id: number;
    email: string;
    provider: string;
    firebaseUid: string | null;
    gmailTokens: any;
    hasEmails?: boolean;
  }>;
}

async function findDuplicateUsers(): Promise<UserGroup[]> {
  logger.info('Finding duplicate users...');
  
  const allUsers = await storage.getAllUsers();
  const usersByEmail = new Map<string, UserGroup>();
  
  for (const user of allUsers) {
    if (!user.email) continue;
    
    if (!usersByEmail.has(user.email)) {
      usersByEmail.set(user.email, {
        email: user.email,
        users: []
      });
    }
    
    usersByEmail.get(user.email)!.users.push({
      id: user.id,
      email: user.email,
      provider: user.provider,
      firebaseUid: user.firebaseUid,
      gmailTokens: user.gmailTokens,
    });
  }
  
  // Only return groups with multiple users
  return Array.from(usersByEmail.values()).filter(group => group.users.length > 1);
}

async function checkEmailCounts(duplicateGroups: UserGroup[]): Promise<void> {
  logger.info('Checking email counts for duplicate users...');
  
  for (const group of duplicateGroups) {
    for (const user of group.users) {
      try {
        const emails = await storage.getEmails(user.id, 1000, 0); // Get up to 1000 emails
        user.hasEmails = emails.length > 0;
        logger.info(`User ${user.id} (${user.provider}) has ${emails.length} emails`);
      } catch (error) {
        logger.error(`Error checking emails for user ${user.id}:`, error);
        user.hasEmails = false;
      }
    }
  }
}

async function mergeDuplicateUsers(duplicateGroups: UserGroup[]): Promise<void> {
  logger.info('Starting duplicate user merge process...');
  
  for (const group of duplicateGroups) {
    logger.info(`Processing duplicate group for email: ${group.email}`);
    
    // Find Firebase user (primary) and Google users (to be merged)
    const firebaseUser = group.users.find(u => u.provider === 'firebase' && u.firebaseUid);
    const googleUsers = group.users.filter(u => u.provider === 'google' && u.id !== firebaseUser?.id);
    
    if (!firebaseUser) {
      logger.warn(`No Firebase user found for email ${group.email}, skipping...`);
      continue;
    }
    
    if (googleUsers.length === 0) {
      logger.info(`No Google users to merge for email ${group.email}, skipping...`);
      continue;
    }
    
    logger.info(`Merging ${googleUsers.length} Google users into Firebase user ${firebaseUser.id}`);
    
    for (const googleUser of googleUsers) {
      try {
        // 1. Transfer Gmail tokens to Firebase user if the Google user has them
        if (googleUser.gmailTokens && !firebaseUser.gmailTokens) {
          logger.info(`Transferring Gmail tokens from user ${googleUser.id} to user ${firebaseUser.id}`);
          await storage.updateUser(firebaseUser.id, {
            gmailTokens: googleUser.gmailTokens
          });
        }
        
        // 2. Transfer emails from Google user to Firebase user
        if (googleUser.hasEmails) {
          logger.info(`Transferring emails from user ${googleUser.id} to user ${firebaseUser.id}`);
          const emails = await storage.getEmails(googleUser.id, 1000, 0);
          
          for (const email of emails) {
            // Update email to belong to Firebase user
            await storage.updateEmail(email.id, {
              userId: firebaseUser.id
            });
          }
          
          logger.info(`Transferred ${emails.length} emails from user ${googleUser.id} to user ${firebaseUser.id}`);
        }
        
        // 3. Delete the duplicate Google user
        logger.info(`Deleting duplicate Google user ${googleUser.id}`);
        // Note: You'll need to implement deleteUser method in storage if it doesn't exist
        await storage.deleteUser(googleUser.id);
        
        logger.info(`Successfully merged Google user ${googleUser.id} into Firebase user ${firebaseUser.id}`);
        
      } catch (error) {
        logger.error(`Error merging Google user ${googleUser.id}:`, error);
      }
    }
  }
}

async function main(): Promise<void> {
  try {
    logger.info('Starting duplicate user fix script...');
    
    // 1. Find duplicate users
    const duplicateGroups = await findDuplicateUsers();
    
    if (duplicateGroups.length === 0) {
      logger.info('No duplicate users found!');
      return;
    }
    
    logger.info(`Found ${duplicateGroups.length} groups of duplicate users:`);
    for (const group of duplicateGroups) {
      logger.info(`  ${group.email}: ${group.users.length} users`);
      for (const user of group.users) {
        logger.info(`    - User ${user.id}: provider=${user.provider}, firebaseUid=${user.firebaseUid ? 'yes' : 'no'}, gmailTokens=${user.gmailTokens ? 'yes' : 'no'}`);
      }
    }
    
    // 2. Check email counts
    await checkEmailCounts(duplicateGroups);
    
    // 3. Show summary before merging
    logger.info('\nSummary before merging:');
    for (const group of duplicateGroups) {
      const firebaseUser = group.users.find(u => u.provider === 'firebase' && u.firebaseUid);
      const googleUsers = group.users.filter(u => u.provider === 'google' && u.id !== firebaseUser?.id);
      
      if (firebaseUser && googleUsers.length > 0) {
        logger.info(`  ${group.email}:`);
        logger.info(`    Firebase user ${firebaseUser.id}: ${firebaseUser.hasEmails ? 'has emails' : 'no emails'}`);
        for (const googleUser of googleUsers) {
          logger.info(`    Google user ${googleUser.id}: ${googleUser.hasEmails ? 'has emails' : 'no emails'}, ${googleUser.gmailTokens ? 'has tokens' : 'no tokens'}`);
        }
      }
    }
    
    // 4. Perform the merge (uncomment when ready to execute)
    await mergeDuplicateUsers(duplicateGroups);
    
    logger.info('Script completed. User merge operations have been performed.');
    
  } catch (error) {
    logger.error('Script failed:', error);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
} 