/**
 * Advanced Error Handling Module
 *
 * This module provides a comprehensive approach to error handling across the application,
 * with standardized error classes, error reporting, recovery mechanisms, and
 * advanced API error handling features including:
 * - Circuit breaking for failing API endpoints
 * - Automatic retries with exponential backoff
 * - Detailed error tracking and metrics
 * - Smart recovery suggestions
 */

import logger from '../lib/logger';

// Environment detection - use direct access to avoid circular dependency during startup
const IS_PRODUCTION = process.env.NODE_ENV === 'production';

/**
 * Error types for categorizing errors across the application
 */
export enum ErrorType {
  // Validation errors
  VALIDATION = 'validation_error',
  INPUT_VALIDATION = 'input_validation_error',
  SCHEMA_VALIDATION = 'schema_validation_error',

  // Authentication/authorization errors
  AUTHENTICATION = 'authentication_error',
  AUTHORIZATION = 'authorization_error',
  TOKEN_EXPIRED = 'token_expired_error',
  TOKEN_INVALID = 'token_invalid_error',

  // Resource errors
  NOT_FOUND = 'not_found_error',
  RESOURCE_CONFLICT = 'resource_conflict_error',
  RESOURCE_GONE = 'resource_gone_error',

  // Rate limiting and quotas
  RATE_LIMIT = 'rate_limit_error',
  QUOTA_EXCEEDED = 'quota_exceeded_error',
  CONCURRENT_LIMIT = 'concurrent_limit_error',

  // External service errors
  EXTERNAL_API = 'external_api_error',
  EXTERNAL_DEPENDENCY = 'external_dependency_error',
  API_RESPONSE_ERROR = 'api_response_error',
  UPSTREAM_SERVICE = 'upstream_service_error',

  // Database errors
  DATABASE = 'database_error',
  DATABASE_CONNECTION = 'database_connection_error',
  DATABASE_QUERY = 'database_query_error',
  DATABASE_CONSTRAINT = 'database_constraint_error',

  // Performance and timeout errors
  TIMEOUT = 'timeout_error',
  PERFORMANCE = 'performance_error',

  // Configuration and environment errors
  CONFIGURATION = 'configuration_error',
  ENVIRONMENT = 'environment_error',

  // Fallback categories
  OPERATIONAL = 'operational_error',
  UNEXPECTED = 'unexpected_error',
}

/**
 * Recovery strategies for different error types
 */
export enum RecoveryStrategy {
  RETRY = 'retry', // Simple retry of the same operation
  RETRY_WITH_BACKOFF = 'retry_with_backoff', // Retry with exponential backoff
  REFRESH_AUTH = 'refresh_auth', // Refresh authentication and retry
  CIRCUIT_BREAK = 'circuit_break', // Stop trying for a while (circuit breaker)
  FALLBACK = 'fallback', // Use fallback mechanism or cached data
  DEGRADE = 'degrade', // Continue with degraded functionality
  MANUAL_INTERVENTION = 'manual_intervention', // Requires human intervention
  NONE = 'none', // No automatic recovery possible
}

/**
 * Maps error types to their default recovery strategies
 */
const DEFAULT_RECOVERY_STRATEGIES: Record<ErrorType, RecoveryStrategy> = {
  // Validation errors - generally not retryable
  [ErrorType.VALIDATION]: RecoveryStrategy.NONE,
  [ErrorType.INPUT_VALIDATION]: RecoveryStrategy.NONE,
  [ErrorType.SCHEMA_VALIDATION]: RecoveryStrategy.NONE,

  // Auth errors - may require token refresh
  [ErrorType.AUTHENTICATION]: RecoveryStrategy.REFRESH_AUTH,
  [ErrorType.AUTHORIZATION]: RecoveryStrategy.MANUAL_INTERVENTION,
  [ErrorType.TOKEN_EXPIRED]: RecoveryStrategy.REFRESH_AUTH,
  [ErrorType.TOKEN_INVALID]: RecoveryStrategy.REFRESH_AUTH,

  // Resource errors - might resolve with retries
  [ErrorType.NOT_FOUND]: RecoveryStrategy.NONE,
  [ErrorType.RESOURCE_CONFLICT]: RecoveryStrategy.RETRY,
  [ErrorType.RESOURCE_GONE]: RecoveryStrategy.NONE,

  // Rate limiting - use backoff
  [ErrorType.RATE_LIMIT]: RecoveryStrategy.RETRY_WITH_BACKOFF,
  [ErrorType.QUOTA_EXCEEDED]: RecoveryStrategy.CIRCUIT_BREAK,
  [ErrorType.CONCURRENT_LIMIT]: RecoveryStrategy.RETRY_WITH_BACKOFF,

  // External API errors - various strategies
  [ErrorType.EXTERNAL_API]: RecoveryStrategy.RETRY_WITH_BACKOFF,
  [ErrorType.EXTERNAL_DEPENDENCY]: RecoveryStrategy.FALLBACK,
  [ErrorType.API_RESPONSE_ERROR]: RecoveryStrategy.RETRY,
  [ErrorType.UPSTREAM_SERVICE]: RecoveryStrategy.CIRCUIT_BREAK,

  // Database errors - may need circuit breaking
  [ErrorType.DATABASE]: RecoveryStrategy.RETRY,
  [ErrorType.DATABASE_CONNECTION]: RecoveryStrategy.CIRCUIT_BREAK,
  [ErrorType.DATABASE_QUERY]: RecoveryStrategy.RETRY,
  [ErrorType.DATABASE_CONSTRAINT]: RecoveryStrategy.NONE,

  // Performance/timeout - may improve with retries
  [ErrorType.TIMEOUT]: RecoveryStrategy.RETRY_WITH_BACKOFF,
  [ErrorType.PERFORMANCE]: RecoveryStrategy.DEGRADE,

  // Config/env - generally not retryable
  [ErrorType.CONFIGURATION]: RecoveryStrategy.MANUAL_INTERVENTION,
  [ErrorType.ENVIRONMENT]: RecoveryStrategy.MANUAL_INTERVENTION,

  // Fallbacks
  [ErrorType.OPERATIONAL]: RecoveryStrategy.RETRY,
  [ErrorType.UNEXPECTED]: RecoveryStrategy.NONE,
};

/**
 * Advanced application error class with enhanced metadata
 */
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly context?: Record<string, any>;
  public readonly recoveryStrategy: RecoveryStrategy;
  public readonly timestamp: number;
  public readonly traceId?: string;
  public readonly retryable: boolean;
  public readonly apiEndpoint?: string;
  public readonly severity: 'low' | 'medium' | 'high' | 'critical';

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNEXPECTED,
    statusCode = 500,
    isOperational = true,
    options: {
      context?: Record<string, any>;
      recoveryStrategy?: RecoveryStrategy;
      traceId?: string;
      retryable?: boolean;
      apiEndpoint?: string;
      severity?: 'low' | 'medium' | 'high' | 'critical';
    } = {}
  ) {
    super(message);
    this.name = this.constructor.name;
    this.type = type;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.context = options.context || {};
    this.timestamp = Date.now();

    // Set recovery strategy - either from options or default for this error type
    this.recoveryStrategy = options.recoveryStrategy || DEFAULT_RECOVERY_STRATEGIES[type];

    // Set additional metadata
    this.traceId = options.traceId || generateTraceId();
    this.retryable =
      options.retryable !== undefined ? options.retryable : isRetryableError(type, statusCode);
    this.apiEndpoint = options.apiEndpoint;
    this.severity = options.severity || determineErrorSeverity(type, statusCode);

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Generate a user-friendly explanation for this error
   */
  getUserFriendlyMessage(): string {
    // Simple error message without technical details
    return sanitizeErrorMessage(this);
  }

  /**
   * Get recovery guidance text based on recovery strategy
   */
  getRecoveryGuidance(): string {
    switch (this.recoveryStrategy) {
      case RecoveryStrategy.RETRY:
        return 'Please try again.';
      case RecoveryStrategy.RETRY_WITH_BACKOFF:
        return 'Please try again after a short wait.';
      case RecoveryStrategy.REFRESH_AUTH:
        return 'Your session may have expired. Please log in again.';
      case RecoveryStrategy.CIRCUIT_BREAK:
        return 'This service is temporarily unavailable. Please try again later.';
      case RecoveryStrategy.FALLBACK:
        return "We're using a backup system. Some features may be limited.";
      case RecoveryStrategy.DEGRADE:
        return 'The service is operating with limited functionality.';
      case RecoveryStrategy.MANUAL_INTERVENTION:
        return 'Please contact support for assistance with this issue.';
      default:
        return '';
    }
  }

  /**
   * Serialize the error for API responses
   */
  toJSON() {
    return {
      type: this.type,
      message: this.message,
      statusCode: this.statusCode,
      traceId: this.traceId,
      recoveryGuidance: this.getRecoveryGuidance(),
      timestamp: this.timestamp,
    };
  }
}

/**
 * Input validation error
 */
export class ValidationError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, ErrorType.VALIDATION, 400, true, context);
  }
}

/**
 * Authentication error (not logged in)
 */
export class AuthenticationError extends AppError {
  constructor(message = 'Authentication required', context?: Record<string, any>) {
    super(message, ErrorType.AUTHENTICATION, 401, true, context);
  }
}

/**
 * Authorization error (no permission)
 */
export class AuthorizationError extends AppError {
  constructor(
    message = 'You do not have permission to perform this action',
    context?: Record<string, any>
  ) {
    super(message, ErrorType.AUTHORIZATION, 403, true, context);
  }
}

/**
 * Resource not found error
 */
export class NotFoundError extends AppError {
  constructor(message = 'Resource not found', context?: Record<string, any>) {
    super(message, ErrorType.NOT_FOUND, 404, true, context);
  }
}

/**
 * Rate limit exceeded error
 */
export class RateLimitError extends AppError {
  constructor(message = 'Rate limit exceeded', context?: Record<string, any>) {
    super(message, ErrorType.RATE_LIMIT, 429, true, context);
  }
}

/**
 * External API error
 */
export class ExternalApiError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, ErrorType.EXTERNAL_API, 502, true, context);
  }
}

/**
 * Database error
 */
export class DatabaseError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, ErrorType.DATABASE, 500, true, context);
  }
}

/**
 * Handle errors consistently across the application
 * @param error The error to handle
 * @param errorSource Where the error occurred
 */
export function handleError(error: unknown, errorSource: string): AppError {
  // Already an AppError, just log and return it
  if (error instanceof AppError) {
    logError(error, errorSource);
    return error;
  }

  // Convert to AppError based on type
  let appError: AppError;

  if (error instanceof Error) {
    // Try to categorize based on error message or name
    if (
      error.message.toLowerCase().includes('not found') ||
      error.message.toLowerCase().includes('404')
    ) {
      appError = new NotFoundError(error.message);
    } else if (
      error.message.toLowerCase().includes('permission') ||
      error.message.toLowerCase().includes('forbidden')
    ) {
      appError = new AuthorizationError(error.message);
    } else if (
      error.message.toLowerCase().includes('unauthorized') ||
      error.message.toLowerCase().includes('unauthenticated')
    ) {
      appError = new AuthenticationError(error.message);
    } else if (
      error.message.toLowerCase().includes('validation') ||
      error.message.toLowerCase().includes('invalid')
    ) {
      appError = new ValidationError(error.message);
    } else if (
      error.message.toLowerCase().includes('rate limit') ||
      error.message.toLowerCase().includes('too many requests')
    ) {
      appError = new RateLimitError(error.message);
    } else if (
      error.message.toLowerCase().includes('database') ||
      error.message.toLowerCase().includes('sql')
    ) {
      appError = new DatabaseError(error.message);
    } else if (
      error.message.toLowerCase().includes('api') ||
      error.message.toLowerCase().includes('external')
    ) {
      appError = new ExternalApiError(error.message);
    } else {
      // Generic application error
      appError = new AppError(error.message, ErrorType.UNEXPECTED, 500, false);
    }

    // Copy the stack trace
    appError.stack = error.stack;
  } else {
    // Unknown error type
    const errorMessage = typeof error === 'string' ? error : 'Unknown error occurred';
    appError = new AppError(errorMessage, ErrorType.UNEXPECTED, 500, false);
  }

  logError(appError, errorSource);
  return appError;
}

/**
 * Log an error with appropriate severity and context
 * @param error The error to log (now handles AppError or standard Error)
 * @param source The source of the error
 */
function logError(error: AppError | Error, source: string): void {
  const isAppError = error instanceof AppError;
  const severity = isAppError ? error.severity : 'error';

  const logObject: Record<string, any> = {
    source,
    error: {
      message: error.message,
      name: error.name,
      stack: error.stack,
    },
  };

  if (isAppError) {
    logObject.type = error.type;
    logObject.statusCode = error.statusCode;
    logObject.isOperational = error.isOperational;
    logObject.context = error.context;
    logObject.traceId = error.traceId;
  }

  // Map severity to appropriate logger method
  const logMethod = severity === 'critical' || severity === 'high' ? 'error' :
                   severity === 'medium' ? 'warn' :
                   severity === 'low' ? 'info' : 'error';

  logger[logMethod](`Error occurred in ${source}`, logObject);
}

/**
 * Create an Express-compatible error handler middleware with enhanced features
 */
export function errorHandlerMiddleware(
  options: {
    includeStack?: boolean;
    trackMetrics?: boolean;
    includeRecoveryGuidance?: boolean;
  } = {}
) {
  const {
    includeStack = !IS_PRODUCTION,
    trackMetrics = true,
    includeRecoveryGuidance = true,
  } = options;

  return (err: Error, req: any, res: any, next: any) => {
    if (res.headersSent) {
      return next(err);
    }

    const appError = handleError(err, 'express_middleware');

    if (trackMetrics) {
      errorMetrics.trackError(appError, req.path);
    }

    const responseBody: Record<string, any> = {
      type: appError.type,
      message: appError.message,
      traceId: appError.traceId,
    };

    // Add recovery guidance if enabled
    if (includeRecoveryGuidance && appError.getRecoveryGuidance()) {
      responseBody.recovery = appError.getRecoveryGuidance();
    }

    // Build enhanced response object
    const response: any = {
      success: false,
      error: responseBody,
    };

    // Include stack trace in development/testing
    if (includeStack && appError.stack) {
      response.error.stack = appError.stack.split('\n');
    }

    // Include additional debug info in non-production
    if (!IS_PRODUCTION) {
      response.error.debug = {
        errorType: appError.type,
        statusCode: appError.statusCode,
        recoveryStrategy: appError.recoveryStrategy,
        timestamp: new Date(appError.timestamp).toISOString(),
        severity: appError.severity,
      };

      // Include context in development only
      if (appError.context) {
        response.error.debug.context = appError.context;
      }
    }

    // Set response headers for better debugging
    res.set('X-Trace-ID', appError.traceId);
    res.set('X-Error-Type', appError.type);

    // Send enhanced error response
    res.status(appError.statusCode).json(response);
  };
}

/**
 * Global error handler middleware for Express
 * Provides backward compatibility with existing code
 */
export const globalErrorHandler = (err: Error, req: any, res: any, next: any) => {
  const handler = errorHandlerMiddleware();
  return handler(err, req, res, next);
};

/**
 * Async error handler for Express route handlers
 * Automatically catches errors and passes them to the next middleware
 */
export function asyncHandler(fn: Function) {
  return (req: any, res: any, next: any) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

// Alias for compatibility with existing code
export const catchAsync = asyncHandler;

/**
 * Create a function that will catch and handle errors from async functions
 * @param fn The async function to wrap
 * @param errorSource Source identifier for error logging
 */
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  errorSource: string
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
  return async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    try {
      return await fn(...args);
    } catch (error) {
      throw handleError(error, errorSource);
    }
  };
}

/**
 * Check if an external API error is likely a network error
 * @param error The error to check
 */
export function isNetworkError(error: unknown): boolean {
  if (!(error instanceof Error)) return false;

  const errorMessage = error.message.toLowerCase();

  return (
    errorMessage.includes('network') ||
    errorMessage.includes('connection') ||
    errorMessage.includes('timeout') ||
    errorMessage.includes('socket') ||
    errorMessage.includes('econnrefused') ||
    errorMessage.includes('econnreset') ||
    errorMessage.includes('unable to reach')
  );
}

/**
 * Centralized error metrics collection
 * Used for monitoring and alerting on error patterns
 */
class ErrorMetrics {
  private errorCounts: Map<string, number> = new Map(); // Counts by error type
  private endpointErrorCounts: Map<string, number> = new Map(); // Counts by endpoint
  private recentErrors: Array<{
    type: ErrorType;
    endpoint?: string;
    timestamp: number;
    statusCode: number;
    traceId: string;
  }> = [];

  private readonly MAX_RECENT_ERRORS = 100;

  // Count a new error occurrence
  public trackError(error: AppError, endpoint?: string): void {
    const { type, statusCode, traceId } = error;
    // Track overall error counts
    this.errorCounts.set(type, (this.errorCounts.get(type) || 0) + 1);

    // Track endpoint-specific errors
    if (endpoint) {
      this.endpointErrorCounts.set(endpoint, (this.endpointErrorCounts.get(endpoint) || 0) + 1);
    }

    // Record recent errors for detailed analysis
    if (this.recentErrors.length >= this.MAX_RECENT_ERRORS) {
      this.recentErrors.shift(); // Remove the oldest error
    }
    this.recentErrors.push({
      type,
      endpoint,
      timestamp: Date.now(),
      statusCode,
      traceId: traceId || '',
    });
  }

  // Get error stats for monitoring
  public getStats(): Record<string, any> {
    return {
      totalErrors: this.recentErrors.length,
      errorCountsByType: Object.fromEntries(this.errorCounts),
      errorCountsByEndpoint: Object.fromEntries(this.endpointErrorCounts),
    };
  }

  // Check for error patterns that may indicate a circuit breaker should trip
  public shouldTripCircuitBreaker(endpoint: string, timeWindowMs = 60000): boolean {
    const errorsInWindow = this.getErrorsForEndpoint(endpoint, timeWindowMs);
    // Basic logic: trip if more than 10 errors in the last minute
    return errorsInWindow.length > 10;
  }

  // Get errors for a specific endpoint
  public getErrorsForEndpoint(endpoint: string, timeWindowMs = 300000): Array<any> {
    const now = Date.now();
    return this.recentErrors.filter(
      (err) => err.endpoint === endpoint && now - err.timestamp <= timeWindowMs
    );
  }

  // Reset metrics (for testing)
  public reset(): void {
    this.errorCounts.clear();
    this.endpointErrorCounts.clear();
    this.recentErrors = [];
    logger.info('Error metrics have been reset.');
  }
}

// Singleton instance of error metrics
export const errorMetrics = new ErrorMetrics();

/**
 * Generate a trace ID for error tracking
 */
export function generateTraceId(): string {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 10);
  return `trace-${timestamp}${randomPart}`;
}

/**
 * Determine if an error should be considered retryable
 */
export function isRetryableError(type: ErrorType, statusCode: number): boolean {
  // Base on error type
  // ErrorType.NETWORK was removed as it's not defined in the enum.
  // Other types like TIMEOUT or EXTERNAL_API cover network-related retry scenarios.
  if (
    type === ErrorType.TIMEOUT ||
    type === ErrorType.RATE_LIMIT ||
    type === ErrorType.DATABASE_CONNECTION ||
    type === ErrorType.EXTERNAL_API ||
    type === ErrorType.API_RESPONSE_ERROR
  ) {
    return true;
  }

  // Base on status code
  if (statusCode >= 500 || statusCode === 429) {
    return true;
  }

  // Most 4xx errors are not retryable
  if (statusCode >= 400 && statusCode < 500) {
    return false;
  }

  return false;
}

/**
 * Determine error severity based on type and status code
 */
export function determineErrorSeverity(
  type: ErrorType,
  statusCode: number
): 'low' | 'medium' | 'high' | 'critical' {
  // Critical errors that need immediate attention
  if (
    type === ErrorType.DATABASE_CONNECTION ||
    type === ErrorType.ENVIRONMENT ||
    type === ErrorType.CONFIGURATION ||
    statusCode >= 500
  ) {
    return 'critical';
  }

  // High severity errors that affect functionality
  if (
    type === ErrorType.EXTERNAL_DEPENDENCY ||
    type === ErrorType.UPSTREAM_SERVICE ||
    type === ErrorType.DATABASE ||
    type === ErrorType.QUOTA_EXCEEDED ||
    statusCode === 429
  ) {
    return 'high';
  }

  // Medium severity errors that affect some users
  if (
    type === ErrorType.AUTHENTICATION ||
    type === ErrorType.AUTHORIZATION ||
    type === ErrorType.TOKEN_EXPIRED ||
    type === ErrorType.TOKEN_INVALID ||
    type === ErrorType.RATE_LIMIT ||
    (statusCode >= 400 && statusCode < 500)
  ) {
    return 'medium';
  }

  // Low severity errors that are expected or minor
  return 'low';
}

/**
 * Enhanced error tracking and reporting
 */
export function trackApiError(
  error: AppError,
  endpoint?: string,
  requestDetails?: Record<string, any>
): void {
  // Use the singleton error metrics instance
  errorMetrics.trackError(error, endpoint);

  logger.error(
    `API Error: ${error.message}`,
    {
      type: error.type,
      statusCode: error.statusCode,
      isOperational: error.isOperational,
      endpoint,
      traceId: error.traceId,
      context: error.context,
      request: requestDetails
        ? {
            method: requestDetails.method,
            url: requestDetails.url,
            ip: requestDetails.ip,
          }
        : undefined,
    },
    error
  );
}

/**
 * Helper to sanitize error messages for user-facing display
 * Prevents leaking sensitive information in error messages
 */
export function sanitizeErrorMessage(error: unknown): string {
  // If no error, return generic message
  if (!error) return 'An unexpected error occurred';

  // For AppErrors, we trust the message is already sanitized
  if (error instanceof AppError) return error.message;

  // For standard errors, sanitize the message
  if (error instanceof Error) {
    // Create a clean version of the message
    const message = error.message;

    // Check for potential sensitive info and sanitize if found
    if (
      message.includes('password') ||
      message.includes('token') ||
      message.includes('secret') ||
      message.includes('key') ||
      message.includes('credentials')
    ) {
      return 'An error occurred, but the details have been logged for security reasons';
    }

    // Remove any potential specific paths, IDs, etc.
    return message
      .replace(/\/[/\w.-]+/g, '[path]') // Replace file paths
      .replace(/\b[0-9a-f]{24}\b/g, '[id]') // Replace MongoDB IDs
      .replace(/\b\d{5,}\b/g, '[number]'); // Replace long numbers
  }

  // For other types, return generic message
  return 'An unexpected error occurred';
}

export function clearErrorCache(): void {
  errorMetrics.reset();
}
