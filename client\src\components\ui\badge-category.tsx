import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

type CategoryBadgeProps = {
  category: string;
  className?: string;
};

const getCategoryStyle = (category: string) => {
  // Map categories to their corresponding theme-aware styles - updated for visual appeal
  const categoryStyles: Record<string, string> = {
    Personal: 'bg-violet-100 text-violet-800 dark:bg-violet-900/30 dark:text-violet-300',
    Work: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300',
    Finance: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300',
    Shopping: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
    Travel: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
    Social: 'bg-fuchsia-100 text-fuchsia-800 dark:bg-fuchsia-900/30 dark:text-fuchsia-300',
    Newsletters: 'bg-slate-100 text-slate-800 dark:bg-slate-800/50 dark:text-slate-300',
    Updates: 'bg-sky-100 text-sky-800 dark:bg-sky-900/30 dark:text-sky-300',
    Promotions: 'bg-rose-100 text-rose-800 dark:bg-rose-900/30 dark:text-rose-300',
    Productivity: 'bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300',
  };

  return (
    categoryStyles[category] || 'bg-gray-100 text-gray-800 dark:bg-gray-800/50 dark:text-gray-300'
  );
};

export function CategoryBadge({ category, className }: CategoryBadgeProps) {
  return (
    <Badge
      variant="outline"
      className={cn('rounded-md font-normal border-0', getCategoryStyle(category), className)}
    >
      {category}
    </Badge>
  );
}

export default CategoryBadge;
