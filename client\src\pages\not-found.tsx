import { Arrow<PERSON><PERSON><PERSON>, Home, Frown } from 'lucide-react';
import type React from 'react';
import { useLocation, Link } from 'wouter';
import { Button } from '@/components/ui/button';
import AppLayout from '@/components/layout/AppLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

/**
 * NotFound page component
 * Displays a user-friendly 404 error page when routes don't match
 */
const NotFound: React.FC = () => {
  const [, navigate] = useLocation();

  return (
    <AppLayout>
      <div className="flex items-center justify-center h-full p-4">
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10 mb-4">
              <Frown className="h-10 w-10 text-destructive" />
            </div>
            <CardTitle className="text-2xl font-bold">Page Not Found</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <p className="text-muted-foreground">
              Sorry, we couldn't find the page you're looking for. It might have been moved,
              deleted, or doesn't exist.
            </p>

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link href="/">
                <Button variant="default" className="w-full sm:w-auto">
                  <Home className="mr-2 h-4 w-4" />
                  Go to Home
                </Button>
              </Link>

              <Button
                onClick={() => window.history.back()}
                variant="outline"
                className="w-full sm:w-auto"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default NotFound;
