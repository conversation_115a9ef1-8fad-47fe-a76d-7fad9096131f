import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';
import { cn } from '@/lib/utils';

const spinnerVariants = cva('inline-block rounded-full border-current animate-spin-slow', {
  variants: {
    size: {
      xs: 'h-3 w-3 border-[1.5px]',
      sm: 'h-4 w-4 border-2',
      md: 'h-6 w-6 border-2',
      lg: 'h-8 w-8 border-[3px]',
      xl: 'h-10 w-10 border-4',
    },
    variant: {
      default: 'text-primary/50 border-r-primary',
      secondary: 'text-secondary/50 border-r-secondary',
      muted: 'text-muted-foreground/30 border-r-muted-foreground/60',
      inverted: 'text-background/20 border-r-background',
    },
  },
  defaultVariants: {
    size: 'md',
    variant: 'default',
  },
});

export interface LoadingSpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  label?: string;
}

const LoadingSpinner = React.forwardRef<HTMLDivElement, LoadingSpinnerProps>(
  ({ className, size, variant, label = 'Loading...', ...props }, ref) => {
    return (
      <div
        className={cn('inline-flex items-center justify-center', className)}
        ref={ref}
        {...props}
        role="status"
      >
        <div className={cn(spinnerVariants({ size, variant }))} aria-hidden="true" />
        <span className="sr-only">{label}</span>
      </div>
    );
  }
);

LoadingSpinner.displayName = 'LoadingSpinner';

export { LoadingSpinner, spinnerVariants };
