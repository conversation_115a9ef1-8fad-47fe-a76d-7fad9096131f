{"include": ["client/src/**/*", "shared/**/*", "server/**/*", "tests/**/*"], "exclude": ["node_modules", "build", "dist"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "module": "ESNext", "strict": true, "useUnknownInCatchVariables": true, "lib": ["esnext", "dom", "dom.iterable"], "jsx": "react-jsx", "skipLibCheck": true, "isolatedModules": true, "allowImportingTsExtensions": true, "moduleResolution": "bundler", "baseUrl": ".", "types": ["node"], "downlevelIteration": true, "typeRoots": ["./node_modules/@types", "./server/types"], "paths": {"@/*": ["./client/src/*"], "@lib/*": ["./client/src/lib/*"], "@hooks/*": ["./client/src/hooks/*"], "@components/*": ["./client/src/components/*"], "@context/*": ["./client/src/context/*"], "@shared/*": ["./shared/*"], "@server/*": ["./server/*"]}}}