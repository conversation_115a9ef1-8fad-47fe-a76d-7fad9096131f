/**
 * Admin Authentication Middleware
 *
 * This middleware checks if a user has admin privileges
 */

import type { NextFunction, Request, Response } from 'express';
import type { User } from '../../shared/schema';
import logger from '../lib/logger';

/**
 * Check if a user is an admin by checking their role.
 *
 * NOTE: This implementation requires a `role` field on the `User` model
 * in the database. A database migration is needed to add this field.
 * For example: `ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT 'user';`
 */
async function checkAdminAccess(user: User | undefined): Promise<boolean> {
  if (!user) {
    return false;
  }

  try {
    // Explicitly check that role is the non-null string 'admin'
    if (user.role && user.role === 'admin') {
      return true;
    }

    return false;
  } catch (error) {
    logger.error('Error checking admin access:', {
      userId: user.id,
      errorMessage: (error as Error).message,
      errorStack: (error as Error).stack,
    });
    return false;
  }
}

/**
 * Middleware to check if the user is an admin
 */
export const isAdmin = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // req.user is populated by the authentication middleware (e.g., simpleAuth)
    const user = req.user;

    if (!user) {
      logger.warn('Admin check failed - No authenticated user found in request');
      res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
      return;
    }

    // Check if user has admin access
    const hasAdminAccess = await checkAdminAccess(user);

    if (!hasAdminAccess) {
      logger.warn(`Admin access denied for user ID ${user.id}`);
      res.status(403).json({
        success: false,
        message: 'Admin access required',
      });
      return;
    }

    // User is an admin, proceed to next middleware
    next();
  } catch (error) {
    logger.error('Error in admin middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during admin check',
    });
  }
};
